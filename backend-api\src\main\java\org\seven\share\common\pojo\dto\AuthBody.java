package org.seven.share.common.pojo.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: AuthBody
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/1
 */
@Data
public class AuthBody implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String usertoken;

    private String carid;

    private String nodeType;

    private Integer planType;
}

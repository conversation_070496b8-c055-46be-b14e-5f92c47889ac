{"resetPassword": {"title": "Reset Password", "username": "Username", "email": "Email", "code": "Verification Code", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "getVerificationCode": "Get Verification Code", "reset": "Reset Password", "resetting": "Resetting...", "backToLogin": "Back to Login", "errors": {"usernameRequired": "Please enter your username", "emailRequired": "Please enter your email", "emailInvalid": "Please enter a valid email address", "codeRequired": "Please enter the verification code", "codeLength": "The verification code should be 6 characters long", "newPasswordRequired": "Please enter your new password", "newPasswordMinLength": "Password must be at least 6 characters long", "confirmPasswordRequired": "Please confirm your new password", "passwordMismatch": "The two passwords do not match", "emailMissing": "Please enter your email address first", "codeSent": "Verification code sent successfully", "passwordResetSuccess": "Password reset successfully"}}, "register": {"title": "Create New Account", "username": "Username", "email": "Email", "code": "Verification Code", "password": "Password", "confirmPassword": "Confirm Password", "getVerificationCode": "Get Verification Code", "register": "Register", "registering": "Registering...", "inviter": "Please enter the invitation code", "alreadyHaveAccount": "Already have an account?", "login": "<PERSON><PERSON>", "errors": {"usernameRequired": "Please enter your username", "usernameLength": "Length should be between 3 and 20 characters", "emailRequired": "Please enter your email", "emailInvalid": "Please enter a valid email address", "codeRequired": "Please enter the verification code", "codeLength": "The verification code should be 6 characters long", "passwordRequired": "Please enter your password", "passwordMinLength": "Password must be at least 6 characters long", "confirmPasswordRequired": "Please confirm your password", "passwordMismatch": "The two passwords do not match", "emailMissing": "Please enter your email address first", "codeSent": "Verification code sent successfully", "codeFailed": "Verification code sent successfully Failed", "registrationSuccess": "Registration successful", "inviterRequired": "Please enter the invitation code"}}, "login": {"title": "Login Your Account", "accountLogin": "Account and Password", "authCodeLogin": "Authorization Code", "enterAuthCode": "Enter Authorization Code", "username": "Username or Email", "code": "Code", "password": "Password", "rememberMe": "Remember Password", "dologin": "<PERSON><PERSON>", "loging": "Logining...", "registerNewAccount": "New User? Sign up", "forgot_password": "Forgot Password", "loginSuccess": "Login Successful", "otherLoginMethods": "OR", "errors": {"usernameRequired": "Please enter your username or email", "codeRequired": "Please enter verification code", "passwordRequired": "Please enter your password", "codeError": "Please check your verification code", "authCodeRequired": "Please enter authorization code"}}, "changePassword": {"success": "Change Password Successful", "email": "Email", "userName": "User Name", "expirationDateExpired": "VIP Expired Date", "plusExpirationDateExpired": "SVIP Expired Date", "claudeExpirationDateExpired": "<PERSON> Expired Date", "claudeProExpirationDateExpired": "<PERSON> Expired Date", "affCode": "Aff Code", "membershipPlan": "Membership Plan", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "perLimit": "<PERSON>", "claudePerLimit": "<PERSON>"}, "sidebar": {"dashboard": "Dashboard", "shop": "Online Shop", "buy": "Buy Keys", "redeem": "<PERSON><PERSON><PERSON>", "promotion": "Promotion", "announcements": "Announcements", "instructions": "Instructions", "adminPanel": "Admin Panel"}, "auth": {"loginPrompt": "Feature unavailable. Go to login?", "notLoggedIn": "Not Logged In", "registerPrompt": "Feature unavailable. Go to register?"}, "announcements": {"claude": {"title": "Claude Service Announcement"}, "grok": {"title": "Grok Service Announcement"}, "draw": {"title": "AI Drawing Service Announcement"}, "gpt": {"title": "GPT Service Announcement"}}, "common": {"announcement": "Announcement", "login": "<PERSON><PERSON>", "register": "Register", "confirm": "Confirm", "cancel": "Cancel", "close": "Close", "save": "Save", "edit": "Edit", "delete": "Delete", "download": "Download", "upload": "Upload", "loading": "Loading...", "noData": "No Data", "success": "Success", "error": "Error", "warning": "Warning", "tip": "Tip", "personalCenter": "Personal Center", "noValidNodes": "No valid nodes available, please contact administrator", "officialStatus": "Official Status", "draw": "Drawing", "welcome": "Welcome", "unit": {"piece": "piece", "times": "times"}}, "mobile": {"dailyCheckIn": "Daily Check-in", "notifications": "Notifications", "lightMode": "Light Mode", "darkMode": "Dark Mode", "language": "Language", "more": "More"}, "userGuide": {"title": "Welcome to AI Service Platform", "subtitle": "Please follow these steps to start using our AI services", "steps": {"selectModel": {"title": "Select Model", "description": "Click the dropdown menu on the left to choose your desired AI model"}, "checkStatus": {"title": "Check Status", "description": "The center displays official service status to ensure normal operation"}, "selectNode": {"title": "Select Node", "description": "Choose an appropriate service node from the list below"}}, "announcementTip": "Click the announcement button on the right to view latest notifications and usage instructions"}, "modelSelector": {"title": "Select Model", "subtitle": "Choose the AI model you want to use", "loading": "Loading models...", "noModels": "No valid model configurations found", "loadingModels": "Loading available models..."}, "userProfile": {"validUntil": "<PERSON>id <PERSON>", "noPlan": "No Plan Purchased", "newPassword": "New Password", "enterNewPassword": "Enter new password", "newPasswordRequired": "Please enter new password", "passwordLengthError": "Password must be at least 6 characters", "confirmPasswordRequired": "Please confirm new password", "passwordMismatch": "Passwords do not match", "benefits": "Benefits", "changePassword": "Change Password", "currentPassword": "Current Password", "verified": "Verified", "logout": "Logout", "cancel": "Cancel", "save": "Save", "unVerified": "UnVerified", "bindEmail": "Bind <PERSON><PERSON>", "noEmail": "No email bound", "saving": "Saving...", "enterCurrentPassword": "Enter current password", "enterConfirmPassword": "Enter confirm password", "confirmPassword": "Confirm Password", "paymentHistory": "Recent Payment History", "noPaymentHistory": "No Payment History", "paymentDate": "Payment Date", "productName": "Product Name", "amount": "Amount", "status": "Status", "success": "Success", "pending": "Pending", "failed": "Failed", "tradeNo": "Trade No", "limits": "Limits", "perSecond": "Per Second", "perMinute": "Per <PERSON>", "perHour": "Per Hour", "perThreeHour": "Per Three Hour", "perFiveHour": "Per Five Hour", "perDay": "Per Day", "perWeek": "Per Week", "perMonth": "Per Month", "perYear": "Per Year", "noLimit": "No Limit", "useBan": "Use Ban"}, "shop": {"title": "Online Store", "loading": "Loading products...", "days": "Days", "dailyPrice": "Daily Price", "perImagePrice": "Per Image", "moreFeatures": "More Features", "showLess": "Show Less", "hotSale": "Hot Sale", "recommended": "Recommended", "exclusive": "Exclusive", "basicFeatures": "Basic Features Included", "categories": {"all": "All"}, "noProducts": {"title": "No Products Available", "description": "The administrator has not yet listed any products. Please check back later or contact customer service for details.", "refresh": "Refresh Page", "clearFilters": "Clear Filters"}, "error": {"title": "Loading Failed", "fetchFailed": "Failed to fetch product information, please try again later"}, "defaultFeatures": {"chatgpt": "ChatGPT 4.0 Access", "claude": "Claude 3.5 Access", "grok": "Grok 3.0 Access", "chatgptClaude": "ChatGPT + Claude Dual Models", "chatgptGrok": "ChatGPT + Grok Combo", "claudeGrok": "Claude + Grok Combo", "allModels": "All AI Models Access", "aiDrawing": "AI Drawing Features", "webAccess": "Web Platform Access", "mobileApp": "Mobile Application", "realTimeInfo": "Real-time Information", "apiAccess": "API Interface Access", "multipleStyles": "Multiple Art Styles", "hdGeneration": "HD Image Generation", "commercialLicense": "Commercial License", "basicSupport": "Basic Customer Support", "prioritySupport": "Priority Customer Support", "premiumSupport": "Dedicated Customer Support", "basicService": "Basic Service Features"}, "packages": {"basic": {"name": "Basic", "description": "Essential AI services for individual users", "features": {"chatgpt": "ChatGPT 4.0 Access", "basicSupport": "Basic Customer Support", "webAccess": "Web Platform Access", "mobileApp": "Mobile Application"}}, "professional": {"name": "Professional", "description": "Comprehensive AI solution for professionals", "features": {"allModels": "ChatGPT + Claude Dual Models", "prioritySupport": "Priority Customer Support", "advancedFeatures": "Advanced Features Access", "apiAccess": "API Interface Access", "teamCollaboration": "Team Collaboration"}}, "enterprise": {"name": "Enterprise", "description": "Full-featured AI service package for enterprises", "features": {"allModels": "All AI Models Access", "unlimitedUsage": "Unlimited Usage", "premiumSupport": "Dedicated Customer Support", "customIntegration": "Custom Integration", "advancedAnalytics": "Advanced Analytics", "aiDrawing": "AI Drawing Features"}}}, "duration": {"week": "7 Days", "month": "30 Days", "quarter": "90 Days", "year": "365 Days"}, "discount": {"save15": "Save 15%", "save30": "Save 30%"}, "payment": {"title": "Order Confirmation", "orderInfo": "Order Information", "package": "Package Name", "amount": "Payment Amount", "originalPrice": "Original Price", "discountPrice": "Discount Price", "validDays": "Valid Days", "usageLimit": "Usage Limit", "days": "days", "scanToPay": "Please scan with {payMethod} to pay", "scanTip": "Open the corresponding app and scan the QR code to complete payment", "selectPayment": "Select Payment Method", "processing": "Processing...", "confirmPay": "Confirm Payment", "close": "Close", "coupon": "Coupon", "cancel": "Cancel", "confirm": "Confirm", "confirmClose": {"title": "Confirm Close", "message": "Order has been created and will remain even after closing. Are you sure you want to close the payment page?", "confirm": "Close Anyway", "cancel": "Continue Payment"}, "methods": {"alipay": "Alipay", "usdt": "USTD", "wechat": "WeChat"}}, "coupon": {"title": "Coupon", "inputLabel": "Coupon Code", "placeholder": "Please enter coupon code (optional)", "confirm": "Confirm", "cancel": "Cancel"}, "subscribe": "Subscribe Now", "errors": {"noPayment": "Administrator has not configured payment methods", "fetchError": "Failed to fetch data"}, "login": {"title": "Not Logged In", "message": "Feature unavailable, go to login?", "confirm": "<PERSON><PERSON>", "cancel": "Cancel"}}, "renewNow": "Renew Now", "promotion": {"title": "Promotion Information", "description": "This records your invitation records and commission information"}, "confirm": "Confirm", "cancel": "Cancel", "notification": {"title": "Notification", "read": "Read"}, "card_redeem_title": "Card Code Redemption", "card_redeem_subtitle": "Here you can redeem your card code", "redeem_card_code": "Enter Card Code", "redeem_button_text": "Redeem Now", "redeem_in_progress": "Redeeming...", "redeem_success": "Redemption Successful!", "redeem_warning": "Please enter a card code", "instructions_title": "Instructions:", "instructions_list_first": "Enter the card code you received", "instructions_list_second": "Click 'Redeem Now' to redeem", "instructions_list_third": "Once successful, the system will automatically recharge your account with the corresponding benefits", "instructions_list_fourth": "If you encounter any issues, please contact customer service", "keyCodePurchase": {"title": "Key Code Purchase", "description": "Here you can purchase new key codes", "instructions": "Instructions"}, "redeem_time": "Redeem Time:", "redeem_loading": "Loading...", "redemption_history": "Latest Redemption History", "no_redemption_history": "No redemption record", "instructions": {"title": "Instructions", "description": "Detailed instructions and help documents are provided here"}, "promotionQRCode": "Promotion QR Code", "inviteLinkText": "Share the QR code or copy the invitation link to invite friends. When they make a purchase, you will earn", "withdrawCash": "cash rewards", "withdrawThreshold": "Withdrawable immediately after reaching {amount} .", "cashbackForPaidUsers": "Admin has enabled cashback for paid users only. Cashback amounts will only be calculated for referrals made by paid users.", "totalOrders": "Total Promotion Orders", "totalAmount": "Total Promotion Amount", "withdrawableAmount": "<PERSON><PERSON><PERSON><PERSON> Amount", "withdrawnAmount": "Total Withdrawn Amount", "copyInviteLink": "Copy Invite Link", "withdrawNow": "Withdraw Now", "inviteRecord": "Invite Records", "withdrawRecord": "Withdrawal Records", "table": {"index": "Index", "noData": "No Data", "username": "Username", "email": "Email", "memberType": "Membership Type", "inviteTime": "Invite Time", "withdrawAmount": "<PERSON><PERSON><PERSON> Amount", "withdrawTime": "<PERSON><PERSON>wal Time", "approvalStatus": "Approval Status", "approvalResult": "A<PERSON><PERSON><PERSON> Result", "types": {"plus": "Premium Member", "normal": "Regular Member"}, "statuses": {"approved": "Approved", "pending": "Pending", "rejected": "Rejected", "unknown": "Unknown Status"}}, "pagination": {"prev": "Previous", "next": "Next"}, "announcement": {"title": "Site Announcements", "description": "You can find the latest announcements here.", "iframeTitle": "Site Announcements"}, "status": {"idle": "Idle", "busy": "Busy", "offline": "Offline", "unknown": "Unknown", "operational": "Operational", "degraded_performance": "Degraded Performance", "partial_outage": "Partial Outage", "major_outage": "Major Outage", "maintenance": "Under Maintenance"}, "recommendations": {"recommended": "Recommended", "available": "Available"}, "loading": {"enteringChat": "Entering chat...", "loadingData": "Loading data...", "switchingModel": "Switching model...", "connectingService": "Connecting to service...", "loadingInstances": "Loading instance information...", "connectingToService": "Connecting to {model} service...", "loadingModelInstances": "Loading {model} instance information..."}, "errors": {"getCarListFailed": "Failed to get car list", "selectCarFailed": "Failed to select car", "nodeError": "This node is abnormal, please change node.", "noBackupAccess": "You don't have permission to use the backup site, please recharge and try again", "switchModelFailed": "Failed to switch model", "switchModelError": "An error occurred while switching models, please try again", "initializationFailed": "Initialization failed", "loadDataFailed": "Failed to load data", "parseBackupSitesFailed": "Failed to parse backup sites", "parseNodeOrderFailed": "Failed to parse node order", "requestTimeout": "Request timeout", "fetchOpenAIStatusFailed": "Failed to fetch OpenAI status", "fetchClaudeStatusFailed": "Failed to fetch Claude status", "asyncFetchOpenAIStatusFailed": "Async fetch OpenAI status failed", "asyncFetchClaudeStatusFailed": "Async fetch Claude status failed", "timedFetchOpenAIStatusFailed": "Timed fetch OpenAI status failed", "timedFetchClaudeStatusFailed": "Timed fetch Claude status failed", "cannotGetOpenAIStatus": "Cannot get OpenAI status", "cannotGetClaudeStatus": "Cannot get Claude status", "claudeComponentNotFound": "Claude.ai component not found"}, "login-warn": {"notLoggedIn": "Not logged in", "functionUnavailable": "Function unavailable, go to login?", "loginButton": "<PERSON><PERSON>", "cancelButton": "Cancel"}, "withdrawDialog": {"title": "Apply for Withdrawal", "amount": "<PERSON><PERSON><PERSON> Amount", "amountPlaceholder": "Enter withdrawal amount", "password": "Login Password", "passwordPlaceholder": "Enter your login password", "contact": "Contact Information", "contactPlaceholder": "Enter contact information", "qrcode": "Payment QR Code", "qrcodePlaceholder": "Upload payment QR code", "uploadError": "Please upload a payment QR code", "cancel": "Cancel", "confirm": "Confirm", "fileError": "File upload failed: {error}", "fileLimitError": "Only one file can be uploaded", "extractAll": "Extract All"}, "tooltip": {"profile": "Profile <PERSON>er", "signIn": "Sign In", "notification": "Notifications", "language": "Change Language", "darkMode": "Toggle Dark Mode"}, "noValidNodes": "No valid nodes found", "paymentSuccess": "Payment Success", "signIn": {"title": "Daily Sign-in", "signIn": "Sign In", "signed": "Signed", "success": "Sign-in successful!", "failed": "Sign-in failed", "alreadySignedIn": "You have already signed in today", "continuousDays": "Continuous Sign-in: {days} days", "signInNow": "Sign In Now", "signingIn": "Signing in..."}, "all": "All", "request": {"configError": "Request configuration error", "loginExpired": "Login information expired, please log in again", "operationFailed": "Operation failed", "systemError": "System error", "authExpired": "Authorization information has expired, please contact the administrator", "noPermission": "You do not have permission to access this resource", "serverError": "Internal server error", "connectionError": "Connection error {status}", "networkError": "Network error, please check your network connection", "timeout": "Request timeout, please try again later"}, "instances": {"title": "AI Model Instances", "selectModel": "Select Model", "noInstances": "No instances available", "instanceInfo": "Instance Information", "accountType": "Account Type", "carId": "Car ID", "usage": "Usage", "enterChat": "<PERSON><PERSON>", "models": {"chatgpt": "ChatGPT", "claude": "<PERSON>", "grok": "Grok", "grokSuper": "Grok Super", "sass": "SASS", "draw": "AI Drawing"}, "types": {"free": "Free", "4o": "Mini", "plus": "Plus", "team": "Team", "pro": "Pro", "standard": "Standard", "max": "Max", "claude37": "4.0"}, "drawPanel": {"title": "AI Drawing", "close": "Close"}, "descriptions": {"chatgpt": "Intelligent conversational assistant supporting multi-turn dialogue and complex task processing", "claude": "High-quality AI assistant excelling in analysis and creative tasks", "grok": "Real-time information AI with access to latest data", "gemini": "Google AI model with strong multimodal understanding capabilities", "ai绘图": "Professional AI image generation tool supporting multiple artistic styles", "default": "Powerful AI assistant providing intelligent services"}}, "draw": {"title": "AI Drawing", "announcement": "Drawing Announcement", "quota": {"title": "Drawing Quota", "used": "Used", "remaining": "Remaining", "total": "Total", "resetTime": "Reset Time", "notSet": "Not Set", "today": "Today", "tomorrow": "Tomorrow", "daysLater": "{days} days later", "dateFormat": "{month}/{day}/{year}", "fetchError": "Failed to fetch drawing quota"}, "tabs": {"generate": "Generate Image", "edit": "Edit Image", "history": "Image History", "record": "Usage Record"}, "editor": {"uploadImage": "Upload Image", "dragDropUpload": "Drag and drop or click to upload image", "supportedFormats": "Supports JPG, PNG, WEBP, JPEG formats, max size 10MB.", "uploadedCount": "Uploaded {count} images", "imagePreview": "Image Preview", "localRedraw": "Local Redraw", "originalImage": "Original Image", "clickToAddRedraw": "Click to add local redraw", "markedRedrawArea": "Marked redraw area", "modifyRedrawArea": "Modify redraw area", "editPrompt": "Edit Prompt", "promptPlaceholder": "Describe the edits you want to make to the image...", "imageSettings": "Image Settings", "imageQuality": "Image Quality", "imageSize": "Image Size", "generateCount": "Generate Count", "startEdit": "Start Editing", "editing": "Editing...", "imageWillShowHere": "Your image will appear here", "uploadAndEdit": "Upload an image and enter a prompt, then click 'Start Editing' to begin", "editTips": "Image Editing Tips", "editResult": "Edit Result", "viewOriginal": "View Original", "download": "Download", "imageCount": "Image {current} / {total}", "prompt": "Prompt", "usageTips": "Usage Tips", "brushSize": "Brush Size", "clear": "Clear", "finish": "Finish", "maskTip": "Painted areas will be redrawn, unpainted areas will remain unchanged", "quality": {"low": "Low Quality", "medium": "Medium Quality", "high": "High Quality"}, "size": {"square": "Square (1:1)", "portrait": "Portrait (2:3)", "landscape": "Landscape (3:2)"}, "tips": {"0": "Clearly describe the specific changes you want, like 'change background to beach scene'", "1": "High quality option generates more detailed images but takes longer to process", "2": "More generated images consume more quota", "3": "Redraw only applies to the first image", "4": "Processing time is about 1-3 minutes, please be patient"}, "usageTipsList": {"0": "Try different style combinations for better results", "1": "Edit multiple times to optimize image details", "2": "Use more detailed descriptions for more precise results"}, "errors": {"uploadImageAndPrompt": "Please upload an image and enter an edit prompt", "invalidImageFile": "Invalid image file", "editTimeout": "Image editing timeout, please try again", "downloadFailed": "Image download failed, please try again later"}, "status": {"preparing": "Preparing to edit...", "processing": "Starting image processing...", "waitingResult": "Image processing in progress, please wait 1-5 minutes, or check in drawing history...", "checkingStatus": "Checking processing status...", "successGenerated": "Successfully generated {count} images!", "completedInvalid": "Image editing completed but result is invalid, please modify and try again!"}}, "generator": {"title": "AI Image Generation", "prompt": "Description Prompt", "promptPlaceholder": "Describe the image you want to generate...", "negativePrompt": "Negative Prompt", "negativePromptPlaceholder": "Describe elements you don't want...", "style": "Art Style", "model": "Generation Model", "imageSize": "Image Size", "generateCount": "Generate Count", "generateButton": "Start Generation", "generating": "Generating...", "generationTips": "Generation Tips", "tips": {"0": "Detailed descriptions yield more precise results", "1": "Use art style keywords like 'oil painting', 'watercolor', etc.", "2": "Negative prompts can exclude unwanted elements", "3": "High-resolution images require more generation time"}, "styles": {"realistic": "Realistic Style", "anime": "Anime Style", "cartoon": "Cartoon Style", "oil": "Oil Painting Style", "watercolor": "Watercolor Style", "sketch": "Sketch Style", "digital": "Digital Art"}, "models": {"stable": "Stable Diffusion", "midjourney": "Midjourney", "dalle": "DALL-E"}, "suggestions": {"cat": "A cute kitten sitting on a windowsill, sunlight shining, blue sky background", "cyberpunk": "Futuristic city skyline, neon lights, cyberpunk style", "forest": "Miyazaki-style fairy tale forest with little spirits dancing", "landscape": "Watercolor style landscape with misty mountains", "portrait": "Retro pop art style portrait with vibrant colors"}, "errors": {"promptRequired": "Please enter a prompt", "generateFailed": "Image generation failed", "downloadFailed": "Image download failed, please try again later", "noTaskId": "Failed to get task ID, please try again"}, "status": {"connecting": "Connecting to server...", "generating": "Starting image generation...", "processing": "Generating images...", "checking": "Checking generation status...", "success": "Images generated successfully!", "failed": "Image generation failed, please modify and try again!", "timeout": "Image generation timeout, please try again", "waiting": "Image processing in progress, please wait 1-5 minutes, or check in drawing history..."}}, "history": {"title": "Drawing History", "description": "View all your previously generated and edited images (status syncs every 5 minutes)", "noHistory": "No history records", "startCreating": "When you generate or edit images, they will appear here. You can review and reuse your previous works anytime.", "loadMore": "Load More", "loading": "Loading...", "createdAt": "Created At", "prompt": "Prompt", "model": "Model", "size": "Size", "status": "Status", "download": "Download", "regenerate": "Regenerate", "delete": "Delete", "filters": {"all": "All Images", "generated": "Generated Images", "edited": "Edited Images"}, "copyPrompt": "Copy Prompt", "copySuccess": "Prompt copied successfully", "copyFailed": "Failed to copy prompt", "types": {"generated": "Generated", "edited": "Edited", "generatedImage": "Generated Image", "editedImage": "Edited Image"}, "statuses": {"pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Processing Failed"}, "errors": {"fetchFailed": "Failed to fetch history records, please try again later"}, "deleteImage": "Delete Image", "deleting": "Deleting...", "confirmDelete": "Are you sure you want to delete this image? This action cannot be undone.", "deleteFailed": "Delete failed, please try again later", "deleteSuccess": "Delete successful"}, "record": {"title": "Usage Record", "noRecord": "No usage record", "pleaseLogin": "Please login first", "index": "Index", "amount": "Amount", "type": "Change Type", "createdAt": "Usage Time", "remark": "Remark", "noRemark": "No remark", "date": "Date", "cost": "Cost", "description": "Description", "types": {"purchase": "Package Purchase", "consumption": "Drawing Consumption", "refund": "Failure Refund", "adminAdjust": "Admin Adjustment", "other": "Other", "generate": "Image Generation", "edit": "Image Editing", "upscale": "Image Upscaling"}, "errors": {"fetchFailed": "Failed to fetch usage records"}}}}
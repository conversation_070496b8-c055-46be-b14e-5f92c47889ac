/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80039
 Source Host           : localhost:3306
 Source Schema         : cool

 Target Server Type    : MySQL
 Target Server Version : 80039
 File Encoding         : 65001

 Date: 05/05/2025 22:54:52
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for base_eps_admin
-- ----------------------------
DROP TABLE IF EXISTS `base_eps_admin`;
CREATE TABLE `base_eps_admin`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `module` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `method` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `path` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `prefix` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `summary` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `tag` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `dts` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for base_eps_app
-- ----------------------------
DROP TABLE IF EXISTS `base_eps_app`;
CREATE TABLE `base_eps_app`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `module` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `method` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `path` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `prefix` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `summary` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `tag` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `dts` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for base_sys_conf
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_conf`;
CREATE TABLE `base_sys_conf`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `cKey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `cValue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_base_sys_conf_c_key`(`cKey`) USING BTREE,
  INDEX `idx_base_sys_conf_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for base_sys_department
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_department`;
CREATE TABLE `base_sys_department`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `parentId` bigint(0) DEFAULT NULL,
  `orderNum` int(0) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_base_sys_department_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_sys_department
-- ----------------------------
INSERT INTO `base_sys_department` VALUES (1, '2024-01-01 13:14:58.313', '2024-01-01 13:14:58.313', NULL, 'COOL', NULL, 0);
INSERT INTO `base_sys_department` VALUES (11, '2024-01-01 13:14:58.313', '2024-01-01 13:14:58.313', NULL, '开发', 1, 0);
INSERT INTO `base_sys_department` VALUES (12, '2024-01-01 13:14:58.313', '2024-01-01 13:14:58.313', NULL, '测试', 1, 0);
INSERT INTO `base_sys_department` VALUES (13, '2024-01-01 13:14:58.313', '2024-01-01 13:14:58.313', NULL, '游客', 1, 0);

-- ----------------------------
-- Table structure for base_sys_init
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_init`;
CREATE TABLE `base_sys_init`  (
  `id` bigint unsigned NOT NULL,
  `table` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `group` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_base_sys_init_table`(`table`) USING BTREE,
  INDEX `idx_base_sys_init_group`(`group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_sys_init
-- ----------------------------
INSERT INTO `base_sys_init` VALUES (1, 'base_sys_menu', 'default');
INSERT INTO `base_sys_init` VALUES (2, 'base_sys_user', 'default');
INSERT INTO `base_sys_init` VALUES (3, 'base_sys_user_role', 'default');
INSERT INTO `base_sys_init` VALUES (4, 'base_sys_role', 'default');
INSERT INTO `base_sys_init` VALUES (5, 'base_sys_role_menu', 'default');
INSERT INTO `base_sys_init` VALUES (6, 'base_sys_department', 'default');
INSERT INTO `base_sys_init` VALUES (7, 'base_sys_role_department', 'default');
INSERT INTO `base_sys_init` VALUES (8, 'base_sys_param', 'default');
INSERT INTO `base_sys_init` VALUES (9, 'dict_info', 'default');
INSERT INTO `base_sys_init` VALUES (10, 'dict_type', 'default');
INSERT INTO `base_sys_init` VALUES (11, 'task_info', 'default');

-- ----------------------------
-- Table structure for base_sys_log
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_log`;
CREATE TABLE `base_sys_log`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `userId` bigint unsigned,
  `action` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ipAddr` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `params` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_base_sys_log_deleted_at`(`deleted_at`) USING BTREE,
  INDEX `IDX_51a2caeb5713efdfcb343a8772`(`userId`) USING BTREE,
  INDEX `IDX_938f886fb40e163db174b7f6c3`(`action`) USING BTREE,
  INDEX `IDX_24e18767659f8c7142580893f2`(`ip`) USING BTREE,
  INDEX `IDX_a03a27f75cf8d502b3060823e1`(`ipAddr`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 287 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_sys_log
-- ----------------------------
INSERT INTO `base_sys_log` VALUES (216, '2024-09-02 11:56:21.736', '2024-09-02 11:56:21.736', '2024-09-03 03:02:01.961', 0, 'GET:/admin/base/open/captcha', '**************', '**************', '');
INSERT INTO `base_sys_log` VALUES (217, '2024-09-02 11:56:31.438', '2024-09-02 11:56:31.438', '2024-09-03 03:02:01.961', 0, 'POST:/admin/base/open/login', '**************', '**************', '{\"username\":\"admin\",\"password\":\"123456\",\"captchaId\":\"u4k0u501000d3vhv4fyklhe200ca7h77\",\"verifyCode\":\"1279\"}');
INSERT INTO `base_sys_log` VALUES (218, '2024-09-02 11:56:31.749', '2024-09-02 11:56:31.749', '2024-09-03 03:02:01.961', 1, 'GET:/admin/base/comm/person', '**************', '**************', '');
INSERT INTO `base_sys_log` VALUES (219, '2024-09-02 11:56:31.752', '2024-09-02 11:56:31.752', '2024-09-03 03:02:01.961', 1, 'GET:/admin/base/comm/permmenu', '**************', '**************', '');
INSERT INTO `base_sys_log` VALUES (220, '2024-09-02 11:57:19.011', '2024-09-02 11:57:19.011', '2024-09-03 03:02:01.961', 1, 'POST:/admin/base/comm/personUpdate', '**************', '**************', '{\"headImg\":\"https://cool-admin-pro.oss-cn-shanghai.aliyuncs.com/app/c8128c24-d0e9-4e07-9c0d-6f65446e105b.png\",\"nickName\":\"管理员\",\"password\":\"lyl@2202\"}');
INSERT INTO `base_sys_log` VALUES (221, '2024-09-02 11:57:19.303', '2024-09-02 11:57:19.303', '2024-09-03 03:02:01.961', 1, 'GET:/admin/base/comm/person', '**************', '**************', '');
INSERT INTO `base_sys_log` VALUES (222, '2024-09-03 15:00:36.563', '2024-09-03 15:00:36.563', '2024-09-04 03:02:01.239', 0, 'GET:/admin/base/open/refreshToken', '112.97.203.209', '112.97.203.209', '');
INSERT INTO `base_sys_log` VALUES (223, '2024-09-03 15:00:42.077', '2024-09-03 15:00:42.077', '2024-09-04 03:02:01.239', 1, 'GET:/admin/base/comm/permmenu', '112.97.203.209', '112.97.203.209', '');
INSERT INTO `base_sys_log` VALUES (224, '2024-09-03 15:00:42.080', '2024-09-03 15:00:42.080', '2024-09-04 03:02:01.239', 1, 'GET:/admin/base/comm/person', '112.97.203.209', '112.97.203.209', '');
INSERT INTO `base_sys_log` VALUES (225, '2024-09-03 15:00:42.348', '2024-09-03 15:00:42.348', '2024-09-04 03:02:01.239', 1, 'POST:/admin/dict/info/data', '112.97.203.209', '112.97.203.209', '{}');
INSERT INTO `base_sys_log` VALUES (226, '2024-09-03 15:00:45.411', '2024-09-03 15:00:45.411', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/conversations/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (227, '2024-09-03 15:00:53.108', '2024-09-03 15:00:53.108', '2024-09-04 03:02:01.239', 1, 'GET:/admin/chatgpt/conversations/info', '112.97.203.209', '112.97.203.209', '');
INSERT INTO `base_sys_log` VALUES (228, '2024-09-03 15:00:58.285', '2024-09-03 15:00:58.285', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/conversations/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (229, '2024-09-03 15:01:09.465', '2024-09-03 15:01:09.465', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/conversations/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (230, '2024-09-03 15:01:23.447', '2024-09-03 15:01:23.447', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/conversations/page', '112.97.203.209', '112.97.203.209', '{\"page\":173,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (231, '2024-09-03 15:06:00.370', '2024-09-03 15:06:00.370', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/user/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (232, '2024-09-03 15:06:02.769', '2024-09-03 15:06:02.769', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/session/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (233, '2024-09-03 15:32:34.594', '2024-09-03 15:32:34.594', '2024-09-04 03:02:01.239', 1, 'POST:/admin/base/sys/param/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (234, '2024-09-03 15:35:10.476', '2024-09-03 15:35:10.476', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/conversations/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (235, '2024-09-03 15:35:24.238', '2024-09-03 15:35:24.238', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/session/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (236, '2024-09-03 15:39:17.693', '2024-09-03 15:39:17.693', '2024-09-04 03:02:01.239', 1, 'GET:/admin/base/comm/person', '112.97.203.209', '112.97.203.209', '');
INSERT INTO `base_sys_log` VALUES (237, '2024-09-03 15:39:17.696', '2024-09-03 15:39:17.696', '2024-09-04 03:02:01.239', 1, 'GET:/admin/base/comm/permmenu', '112.97.203.209', '112.97.203.209', '');
INSERT INTO `base_sys_log` VALUES (238, '2024-09-03 15:39:17.970', '2024-09-03 15:39:17.970', '2024-09-04 03:02:01.239', 1, 'POST:/admin/dict/info/data', '112.97.203.209', '112.97.203.209', '{}');
INSERT INTO `base_sys_log` VALUES (239, '2024-09-03 15:39:23.420', '2024-09-03 15:39:23.420', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/conversations/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (240, '2024-09-03 15:40:12.272', '2024-09-03 15:40:12.272', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/conversations/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (241, '2024-09-03 15:40:21.595', '2024-09-03 15:40:21.595', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/conversations/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":100}');
INSERT INTO `base_sys_log` VALUES (242, '2024-09-03 15:42:49.023', '2024-09-03 15:42:49.023', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/conversations/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":100}');
INSERT INTO `base_sys_log` VALUES (243, '2024-09-03 16:44:55.997', '2024-09-03 16:44:55.997', '2024-09-04 03:02:01.239', 1, 'GET:/admin/base/comm/permmenu', '112.97.203.209', '112.97.203.209', '');
INSERT INTO `base_sys_log` VALUES (244, '2024-09-03 16:44:55.997', '2024-09-03 16:44:55.997', '2024-09-04 03:02:01.239', 1, 'GET:/admin/base/comm/person', '112.97.203.209', '112.97.203.209', '');
INSERT INTO `base_sys_log` VALUES (245, '2024-09-03 16:44:56.306', '2024-09-03 16:44:56.306', '2024-09-04 03:02:01.239', 1, 'POST:/admin/dict/info/data', '112.97.203.209', '112.97.203.209', '{}');
INSERT INTO `base_sys_log` VALUES (246, '2024-09-03 16:45:00.066', '2024-09-03 16:45:00.066', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/conversations/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (247, '2024-09-03 16:56:08.466', '2024-09-03 16:56:08.466', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/user/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (248, '2024-09-03 16:56:12.395', '2024-09-03 16:56:12.395', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/conversations/page', '112.97.203.209', '112.97.203.209', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (249, '2024-09-03 17:33:59.332', '2024-09-03 17:33:59.332', '2024-09-04 03:02:01.239', 0, 'GET:/admin/base/open/refreshToken', '172.93.32.37', '172.93.32.37', '');
INSERT INTO `base_sys_log` VALUES (250, '2024-09-03 17:33:59.817', '2024-09-03 17:33:59.817', '2024-09-04 03:02:01.239', 1, 'GET:/admin/base/comm/person', '172.93.32.37', '172.93.32.37', '');
INSERT INTO `base_sys_log` VALUES (251, '2024-09-03 17:33:59.820', '2024-09-03 17:33:59.820', '2024-09-04 03:02:01.239', 1, 'GET:/admin/base/comm/permmenu', '172.93.32.37', '172.93.32.37', '');
INSERT INTO `base_sys_log` VALUES (252, '2024-09-03 17:34:00.135', '2024-09-03 17:34:00.135', '2024-09-04 03:02:01.239', 1, 'POST:/admin/dict/info/data', '172.93.32.37', '172.93.32.37', '{}');
INSERT INTO `base_sys_log` VALUES (253, '2024-09-03 17:34:00.708', '2024-09-03 17:34:00.708', '2024-09-04 03:02:01.239', 1, 'POST:/admin/chatgpt/conversations/page', '172.93.32.37', '172.93.32.37', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (254, '2024-09-08 20:42:45.724', '2024-09-08 20:42:45.724', '2024-09-09 03:02:01.877', 0, 'GET:/admin/base/open/refreshToken', '172.93.32.37', '172.93.32.37', '');
INSERT INTO `base_sys_log` VALUES (255, '2024-09-08 20:42:51.912', '2024-09-08 20:42:51.912', '2024-09-09 03:02:01.877', 0, 'GET:/admin/base/open/captcha', '172.93.32.37', '172.93.32.37', '');
INSERT INTO `base_sys_log` VALUES (256, '2024-09-08 20:42:56.700', '2024-09-08 20:42:56.700', '2024-09-09 03:02:01.877', 0, 'POST:/admin/base/open/login', '172.93.32.37', '172.93.32.37', '{\"username\":\"admin\",\"password\":\"lyl@2202\",\"captchaId\":\"u4k0u301000d40wtifu5vwjm00rh3as3\",\"verifyCode\":\"1938\"}');
INSERT INTO `base_sys_log` VALUES (257, '2024-09-08 20:42:57.001', '2024-09-08 20:42:57.001', '2024-09-09 03:02:01.877', 1, 'GET:/admin/base/comm/permmenu', '172.93.32.37', '172.93.32.37', '');
INSERT INTO `base_sys_log` VALUES (258, '2024-09-08 20:42:57.002', '2024-09-08 20:42:57.002', '2024-09-09 03:02:01.877', 1, 'POST:/admin/dict/info/data', '172.93.32.37', '172.93.32.37', '{}');
INSERT INTO `base_sys_log` VALUES (259, '2024-09-08 20:42:57.003', '2024-09-08 20:42:57.003', '2024-09-09 03:02:01.877', 1, 'GET:/admin/base/comm/person', '172.93.32.37', '172.93.32.37', '');
INSERT INTO `base_sys_log` VALUES (260, '2024-09-08 20:43:01.130', '2024-09-08 20:43:01.130', '2024-09-09 03:02:01.877', 1, 'POST:/admin/chatgpt/conversations/page', '172.93.32.37', '172.93.32.37', '{\"page\":1,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (261, '2024-09-08 20:43:09.242', '2024-09-08 20:43:09.242', '2024-09-09 03:02:01.877', 1, 'POST:/admin/chatgpt/conversations/page', '172.93.32.37', '172.93.32.37', '{\"page\":5,\"size\":20}');
INSERT INTO `base_sys_log` VALUES (262, '2024-09-08 20:43:24.688', '2024-09-08 20:43:24.688', '2024-09-09 03:02:01.877', 1, 'POST:/admin/chatgpt/conversations/page', '172.93.32.37', '172.93.32.37', '{\"page\":1,\"size\":100}');
INSERT INTO `base_sys_log` VALUES (263, '2024-09-08 20:43:32.367', '2024-09-08 20:43:32.367', '2024-09-09 03:02:01.877', 1, 'POST:/admin/chatgpt/conversations/page', '172.93.32.37', '172.93.32.37', '{\"page\":2,\"size\":100}');
INSERT INTO `base_sys_log` VALUES (264, '2024-09-08 20:43:41.896', '2024-09-08 20:43:41.896', '2024-09-09 03:02:01.877', 1, 'POST:/admin/chatgpt/conversations/page', '172.93.32.37', '172.93.32.37', '{\"page\":41,\"size\":100}');
INSERT INTO `base_sys_log` VALUES (265, '2024-09-08 21:35:44.442', '2024-09-08 21:35:44.442', '2024-09-09 03:02:01.877', 1, 'POST:/admin/chatgpt/conversations/page', '172.93.32.37', '172.93.32.37', '{\"page\":40,\"size\":100}');
INSERT INTO `base_sys_log` VALUES (266, '2024-09-08 21:46:34.399', '2024-09-08 21:46:34.399', '2024-09-09 03:02:01.877', 1, 'POST:/admin/chatgpt/conversations/page', '172.93.32.37', '172.93.32.37', '{\"page\":1,\"size\":100}');
INSERT INTO `base_sys_log` VALUES (267, '2024-09-09 17:19:07.375', '2024-09-09 17:19:07.375', '2024-09-10 03:02:01.944', 0, 'GET:/admin/base/open/refreshToken', '112.96.231.171', '112.96.231.171', '');
INSERT INTO `base_sys_log` VALUES (268, '2024-09-09 17:19:07.639', '2024-09-09 17:19:07.639', '2024-09-10 03:02:01.944', 0, 'GET:/admin/base/open/refreshToken', '112.96.231.171', '112.96.231.171', '');
INSERT INTO `base_sys_log` VALUES (269, '2024-09-09 17:44:57.176', '2024-09-09 17:44:57.176', '2024-09-10 03:02:01.944', 0, 'GET:/admin/base/open/captcha', '112.96.231.171', '112.96.231.171', '');
INSERT INTO `base_sys_log` VALUES (270, '2024-09-09 17:45:01.573', '2024-09-09 17:45:01.573', '2024-09-10 03:02:01.944', 0, 'POST:/admin/base/open/login', '112.96.231.171', '112.96.231.171', '{\"username\":\"admin\",\"password\":\"lyl@2202\",\"captchaId\":\"u4k0u301000d41nnu5a62hv800hitvkg\",\"verifyCode\":\"9215\"}');
INSERT INTO `base_sys_log` VALUES (271, '2024-09-09 17:45:01.903', '2024-09-09 17:45:01.903', '2024-09-10 03:02:01.944', 1, 'GET:/admin/base/comm/permmenu', '112.96.231.171', '112.96.231.171', '');
INSERT INTO `base_sys_log` VALUES (272, '2024-09-09 17:45:01.904', '2024-09-09 17:45:01.904', '2024-09-10 03:02:01.944', 1, 'GET:/admin/base/comm/person', '112.96.231.171', '112.96.231.171', '');
INSERT INTO `base_sys_log` VALUES (273, '2024-09-09 17:45:01.907', '2024-09-09 17:45:01.907', '2024-09-10 03:02:01.944', 1, 'POST:/admin/dict/info/data', '112.96.231.171', '112.96.231.171', '{}');
INSERT INTO `base_sys_log` VALUES (274, '2024-09-11 10:00:50.852', '2024-09-11 10:00:50.852', '2024-09-11 10:03:40.354', 0, 'GET:/admin/base/open/refreshToken', '112.96.229.185', '112.96.229.185', '');
INSERT INTO `base_sys_log` VALUES (275, '2024-09-11 10:00:56.403', '2024-09-11 10:00:56.403', '2024-09-11 10:03:40.354', 0, 'GET:/admin/base/open/captcha', '112.96.229.185', '112.96.229.185', '');
INSERT INTO `base_sys_log` VALUES (276, '2024-09-11 10:01:00.951', '2024-09-11 10:01:00.951', '2024-09-11 10:03:40.354', 0, 'POST:/admin/base/open/login', '112.96.229.185', '112.96.229.185', '{\"username\":\"admin\",\"password\":\"lyl@2202\",\"captchaId\":\"u4k0u501000d4331nhobmzde00vrl7wv\",\"verifyCode\":\"5218\"}');
INSERT INTO `base_sys_log` VALUES (277, '2024-09-11 10:01:01.305', '2024-09-11 10:01:01.305', '2024-09-11 10:03:40.354', 1, 'POST:/admin/dict/info/data', '112.96.229.185', '112.96.229.185', '{}');
INSERT INTO `base_sys_log` VALUES (278, '2024-09-11 10:01:01.313', '2024-09-11 10:01:01.313', '2024-09-11 10:03:40.354', 1, 'GET:/admin/base/comm/permmenu', '112.96.229.185', '112.96.229.185', '');
INSERT INTO `base_sys_log` VALUES (279, '2024-09-11 10:01:01.313', '2024-09-11 10:01:01.313', '2024-09-11 10:03:40.354', 1, 'GET:/admin/base/comm/person', '112.96.229.185', '112.96.229.185', '');
INSERT INTO `base_sys_log` VALUES (280, '2024-09-11 10:01:10.633', '2024-09-11 10:01:10.633', '2024-09-11 10:03:40.354', 1, 'GET:/admin/base/sys/log/getKeep', '112.96.229.185', '112.96.229.185', '');
INSERT INTO `base_sys_log` VALUES (281, '2024-09-11 10:01:10.649', '2024-09-11 10:01:10.649', '2024-09-11 10:03:40.354', 1, 'POST:/admin/base/sys/log/page', '112.96.229.185', '112.96.229.185', '{\"page\":1,\"size\":20,\"sort\":\"desc\",\"order\":\"createTime\"}');
INSERT INTO `base_sys_log` VALUES (282, '2024-09-11 10:03:40.345', '2024-09-11 10:03:40.345', '2024-09-11 10:03:40.354', 1, 'POST:/admin/base/sys/log/clear', '112.96.229.185', '112.96.229.185', '');
INSERT INTO `base_sys_log` VALUES (283, '2024-09-11 10:03:40.592', '2024-09-11 10:03:40.592', '2024-09-12 03:02:01.539', 1, 'POST:/admin/base/sys/log/page', '112.96.229.185', '112.96.229.185', '{\"page\":1,\"size\":20,\"sort\":\"desc\",\"order\":\"createTime\"}');
INSERT INTO `base_sys_log` VALUES (284, '2024-09-11 10:03:41.993', '2024-09-11 10:03:41.993', '2024-09-12 03:02:01.539', 1, 'POST:/admin/task/info/page', '112.96.229.185', '112.96.229.185', '{\"type\":0,\"status\":1,\"size\":10,\"page\":1,\"total\":0}');
INSERT INTO `base_sys_log` VALUES (285, '2024-09-11 10:03:42.014', '2024-09-11 10:03:42.014', '2024-09-12 03:02:01.539', 1, 'POST:/admin/task/info/page', '112.96.229.185', '112.96.229.185', '{\"type\":1,\"status\":1,\"size\":10,\"page\":1,\"total\":0}');
INSERT INTO `base_sys_log` VALUES (286, '2024-09-11 10:03:42.017', '2024-09-11 10:03:42.017', '2024-09-12 03:02:01.539', 1, 'POST:/admin/task/info/page', '112.96.229.185', '112.96.229.185', '{\"type\":null,\"status\":0,\"size\":10,\"page\":1,\"total\":0}');
INSERT INTO `base_sys_log` VALUES (287, '2024-09-11 10:03:42.017', '2024-09-11 10:03:42.017', '2024-09-12 03:02:01.539', 1, 'GET:/admin/task/info/log', '112.96.229.185', '112.96.229.185', '');

-- ----------------------------
-- Table structure for base_sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_menu`;
CREATE TABLE `base_sys_menu`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `parentId` bigint(0) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `router` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `perms` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` int(0) NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `orderNum` int(0) NOT NULL DEFAULT 0,
  `viewPath` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `keepAlive` int(0) NOT NULL DEFAULT 1,
  `isShow` int(0) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_base_sys_menu_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 227 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_sys_menu
-- ----------------------------
INSERT INTO `base_sys_menu` VALUES (1, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, NULL, '工作台', '/', NULL, 0, 'icon-workbench', 1, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (2, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, NULL, '系统管理', '/sys', NULL, 0, 'icon-system', 2, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (8, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 27, '菜单列表', '/sys/menu', NULL, 1, 'icon-menu', 2, 'cool/modules/base/views/menu.vue', 1, 1);
INSERT INTO `base_sys_menu` VALUES (10, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 8, '新增', NULL, 'base:sys:menu:add', 2, NULL, 1, NULL, 0, 1);
INSERT INTO `base_sys_menu` VALUES (11, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 8, '删除', NULL, 'base:sys:menu:delete', 2, NULL, 2, NULL, 0, 1);
INSERT INTO `base_sys_menu` VALUES (12, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 8, '修改', NULL, 'base:sys:menu:update', 2, NULL, 3, NULL, 0, 1);
INSERT INTO `base_sys_menu` VALUES (13, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 8, '查询', NULL, 'base:sys:menu:page,base:sys:menu:list,base:sys:menu:info', 2, NULL, 4, NULL, 0, 1);
INSERT INTO `base_sys_menu` VALUES (22, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 27, '角色列表', '/sys/role', NULL, 1, 'icon-common', 3, 'cool/modules/base/views/role.vue', 1, 1);
INSERT INTO `base_sys_menu` VALUES (23, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 22, '新增', NULL, 'base:sys:role:add', 2, NULL, 1, NULL, 0, 1);
INSERT INTO `base_sys_menu` VALUES (24, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 22, '删除', NULL, 'base:sys:role:delete', 2, NULL, 2, NULL, 0, 1);
INSERT INTO `base_sys_menu` VALUES (25, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 22, '修改', NULL, 'base:sys:role:update', 2, NULL, 3, NULL, 0, 1);
INSERT INTO `base_sys_menu` VALUES (26, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 22, '查询', NULL, 'base:sys:role:page,base:sys:role:list,base:sys:role:info', 2, NULL, 4, NULL, 0, 1);
INSERT INTO `base_sys_menu` VALUES (27, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 2, '权限管理', NULL, NULL, 0, 'icon-auth', 1, NULL, 0, 1);
INSERT INTO `base_sys_menu` VALUES (29, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 105, '请求日志', '/sys/log', NULL, 1, 'icon-log', 1, 'cool/modules/base/views/log.vue', 1, 1);
INSERT INTO `base_sys_menu` VALUES (30, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 29, '权限', NULL, 'base:sys:log:page,base:sys:log:clear,base:sys:log:getKeep,base:sys:log:setKeep', 2, NULL, 1, NULL, 0, 1);
INSERT INTO `base_sys_menu` VALUES (59, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 97, '部门列表', NULL, 'base:sys:department:list', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (60, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 97, '新增部门', NULL, 'base:sys:department:add', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (61, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 97, '更新部门', NULL, 'base:sys:department:update', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (62, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 97, '删除部门', NULL, 'base:sys:department:delete', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (63, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 97, '部门排序', NULL, 'base:sys:department:order', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (65, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 97, '用户转移', NULL, 'base:sys:user:move', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (78, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 2, '参数配置', NULL, NULL, 0, 'icon-common', 4, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (79, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 78, '参数列表', '/sys/param', NULL, 1, 'icon-menu', 0, 'cool/modules/base/views/param.vue', 1, 1);
INSERT INTO `base_sys_menu` VALUES (80, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 79, '新增', NULL, 'base:sys:param:add', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (81, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 79, '修改', NULL, 'base:sys:param:info,base:sys:param:update', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (82, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 79, '删除', NULL, 'base:sys:param:delete', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (83, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 79, '查看', NULL, 'base:sys:param:page,base:sys:param:list,base:sys:param:info', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (84, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, NULL, '通用', NULL, NULL, 0, 'icon-radioboxfill', 99, NULL, 1, 0);
INSERT INTO `base_sys_menu` VALUES (85, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 84, '图片上传', NULL, 'space:info:page,space:info:list,space:info:info,space:info:add,space:info:delete,space:info:update,space:type:page,space:type:list,space:type:info,space:type:add,space:type:delete,space:type:update', 2, NULL, 1, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (90, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 84, '客服聊天', NULL, 'base:app:im:message:read,base:app:im:message:page,base:app:im:session:page,base:app:im:session:list,base:app:im:session:unreadCount,base:app:im:session:delete', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (97, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 27, '用户列表', '/sys/user', NULL, 1, 'icon-user', 0, 'cool/modules/base/views/user.vue', 1, 1);
INSERT INTO `base_sys_menu` VALUES (98, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 97, '新增', NULL, 'base:sys:user:add', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (99, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 97, '删除', NULL, 'base:sys:user:delete', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (100, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 97, '修改', NULL, 'base:sys:user:delete,base:sys:user:update', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (101, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 97, '查询', NULL, 'base:sys:user:page,base:sys:user:list,base:sys:user:info', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (105, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 2, '监控管理', NULL, NULL, 0, 'icon-rank', 6, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (117, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, NULL, '任务管理', NULL, NULL, 0, 'icon-activity', 5, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (118, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 117, '任务列表', '/task', NULL, 1, 'icon-menu', 0, 'cool/modules/task/views/task.vue', 1, 1);
INSERT INTO `base_sys_menu` VALUES (119, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 118, '权限', NULL, 'task:info:page,task:info:list,task:info:info,task:info:add,task:info:delete,task:info:update,task:info:stop,task:info:start,task:info:once,task:info:log', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (197, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, NULL, '字典管理', NULL, NULL, 0, 'icon-log', 3, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (198, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 197, '字典列表', '/dict/list', NULL, 1, 'icon-menu', 1, 'modules/dict/views/list.vue', 1, 1);
INSERT INTO `base_sys_menu` VALUES (199, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 198, '删除', NULL, 'dict:info:delete', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (200, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 198, '修改', NULL, 'dict:info:update,dict:info:info', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (201, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 198, '获得字典数据', NULL, 'dict:info:data', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (202, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 198, '单个信息', NULL, 'dict:info:info', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (203, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 198, '列表查询', NULL, 'dict:info:list', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (204, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 198, '分页查询', NULL, 'dict:info:page', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (205, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 198, '新增', NULL, 'dict:info:add', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (206, '2024-01-01 13:14:56.605', '2024-01-01 13:14:56.605', NULL, 198, '组权限', NULL, 'dict:type:list,dict:type:update,dict:type:delete,dict:type:add', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (207, '2024-01-01 19:37:47.375', '2024-01-01 19:37:47.375', NULL, 1, '账号管理', '/chatgpt/session', NULL, 1, 'icon-command', 1, 'modules/chatgpt/views/session.vue', 1, 1);
INSERT INTO `base_sys_menu` VALUES (208, '2024-01-01 19:37:48.749', '2024-01-01 19:37:48.749', NULL, 207, 'add', NULL, 'chatgpt:session:add', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (209, '2024-01-01 19:37:48.749', '2024-01-01 19:37:48.749', NULL, 207, 'delete', NULL, 'chatgpt:session:delete', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (210, '2024-01-01 19:37:48.749', '2024-01-01 19:37:48.749', NULL, 207, 'info', NULL, 'chatgpt:session:info', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (211, '2024-01-01 19:37:48.749', '2024-01-01 19:37:48.749', NULL, 207, 'list', NULL, 'chatgpt:session:list', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (212, '2024-01-01 19:37:48.749', '2024-01-01 19:37:48.749', NULL, 207, 'page', NULL, 'chatgpt:session:page', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (213, '2024-01-01 19:37:48.749', '2024-01-01 19:37:48.749', NULL, 207, 'update', NULL, 'chatgpt:session:update,chatgpt:session:info', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (214, '2024-01-01 19:53:18.214', '2024-01-01 19:53:18.214', NULL, 1, '用户管理', '/chatgpt/user', NULL, 1, 'icon-user', 2, 'modules/chatgpt/views/user.vue', 1, 1);
INSERT INTO `base_sys_menu` VALUES (215, '2024-01-01 19:53:19.581', '2024-01-01 19:53:19.581', NULL, 214, 'add', NULL, 'chatgpt:user:add', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (216, '2024-01-01 19:53:19.581', '2024-01-01 19:53:19.581', NULL, 214, 'delete', NULL, 'chatgpt:user:delete', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (217, '2024-01-01 19:53:19.581', '2024-01-01 19:53:19.581', NULL, 214, 'info', NULL, 'chatgpt:user:info', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (218, '2024-01-01 19:53:19.581', '2024-01-01 19:53:19.581', NULL, 214, 'list', NULL, 'chatgpt:user:list', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (219, '2024-01-01 19:53:19.581', '2024-01-01 19:53:19.581', NULL, 214, 'page', NULL, 'chatgpt:user:page', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (220, '2024-01-01 19:53:19.581', '2024-01-01 19:53:19.581', NULL, 214, 'update', NULL, 'chatgpt:user:update,chatgpt:user:info', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (221, '2024-01-07 12:29:55.677', '2024-01-07 12:29:55.677', NULL, 1, '会话管理', '/chatgpt/conversations', NULL, 1, 'icon-menu', 3, 'modules/chatgpt/views/conversations.vue', 1, 1);
INSERT INTO `base_sys_menu` VALUES (222, '2024-01-07 12:29:57.263', '2024-01-07 12:29:57.263', NULL, 221, 'add', NULL, 'chatgpt:conversations:add', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (223, '2024-01-07 12:29:57.263', '2024-01-07 12:29:57.263', NULL, 221, 'delete', NULL, 'chatgpt:conversations:delete', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (224, '2024-01-07 12:29:57.263', '2024-01-07 12:29:57.263', NULL, 221, 'info', NULL, 'chatgpt:conversations:info', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (225, '2024-01-07 12:29:57.263', '2024-01-07 12:29:57.263', NULL, 221, 'list', NULL, 'chatgpt:conversations:list', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (226, '2024-01-07 12:29:57.263', '2024-01-07 12:29:57.263', NULL, 221, 'page', NULL, 'chatgpt:conversations:page', 2, NULL, 0, NULL, 1, 1);
INSERT INTO `base_sys_menu` VALUES (227, '2024-01-07 12:29:57.263', '2024-01-07 12:29:57.263', NULL, 221, 'update', NULL, 'chatgpt:conversations:update,chatgpt:conversations:info', 2, NULL, 0, NULL, 1, 1);

-- ----------------------------
-- Table structure for base_sys_param
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_param`;
CREATE TABLE `base_sys_param`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `keyName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `dataType` int(0) NOT NULL DEFAULT 0,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_base_sys_param_deleted_at`(`deleted_at`) USING BTREE,
  INDEX `IDX_cf19b5e52d8c71caa9c4534454`(`keyName`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_sys_param
-- ----------------------------
INSERT INTO `base_sys_param` VALUES (1, '2024-01-01 13:14:58.771', '2024-01-01 13:14:58.771', NULL, 'text', '富文本参数', '<p><strong class=\"ql-size-huge\">111xxxxx2222<span class=\"ql-cursor\">﻿﻿</span></strong></p>', 0, NULL);
INSERT INTO `base_sys_param` VALUES (2, '2024-01-01 13:14:58.771', '2024-01-01 13:14:58.771', NULL, 'json', 'JSON参数', '{\n    code: 111\n}', 0, NULL);

-- ----------------------------
-- Table structure for base_sys_role
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_role`;
CREATE TABLE `base_sys_role`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `userId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `label` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `relevance` int(0) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_base_sys_role_deleted_at`(`deleted_at`) USING BTREE,
  INDEX `IDX_469d49a5998170e9550cf113da`(`name`) USING BTREE,
  INDEX `IDX_f3f24fbbccf00192b076e549a7`(`label`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_sys_role
-- ----------------------------
INSERT INTO `base_sys_role` VALUES (1, '2024-01-01 13:14:57.686', '2024-01-01 13:14:57.686', NULL, '1', '超管', 'admin', '最高权限的角色', 1);
INSERT INTO `base_sys_role` VALUES (10, '2024-01-01 13:14:57.686', '2024-01-01 13:14:57.686', NULL, '1', '系统管理员', 'admin-sys', NULL, 1);
INSERT INTO `base_sys_role` VALUES (11, '2024-01-01 13:14:57.686', '2024-01-01 13:14:57.686', NULL, '1', '游客', 'visitor', NULL, 0);
INSERT INTO `base_sys_role` VALUES (12, '2024-01-01 13:14:57.686', '2024-01-01 13:14:57.686', NULL, '1', '开发', 'dev', NULL, 0);
INSERT INTO `base_sys_role` VALUES (13, '2024-01-01 13:14:57.686', '2024-01-01 13:14:57.686', NULL, '1', '测试', 'test', NULL, 0);

-- ----------------------------
-- Table structure for base_sys_role_department
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_role_department`;
CREATE TABLE `base_sys_role_department`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `roleId` bigint(0) NOT NULL,
  `departmentId` bigint(0) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_base_sys_role_department_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_sys_role_department
-- ----------------------------
INSERT INTO `base_sys_role_department` VALUES (1, '2024-01-01 13:14:58.498', '2024-01-01 13:14:58.498', NULL, 8, 4);
INSERT INTO `base_sys_role_department` VALUES (2, '2024-01-01 13:14:58.498', '2024-01-01 13:14:58.498', NULL, 9, 1);
INSERT INTO `base_sys_role_department` VALUES (3, '2024-01-01 13:14:58.498', '2024-01-01 13:14:58.498', NULL, 9, 4);
INSERT INTO `base_sys_role_department` VALUES (4, '2024-01-01 13:14:58.498', '2024-01-01 13:14:58.498', NULL, 9, 5);
INSERT INTO `base_sys_role_department` VALUES (5, '2024-01-01 13:14:58.498', '2024-01-01 13:14:58.498', NULL, 9, 8);
INSERT INTO `base_sys_role_department` VALUES (6, '2024-01-01 13:14:58.498', '2024-01-01 13:14:58.498', NULL, 9, 9);
INSERT INTO `base_sys_role_department` VALUES (23, '2024-01-01 13:14:58.498', '2024-01-01 13:14:58.498', NULL, 12, 11);
INSERT INTO `base_sys_role_department` VALUES (25, '2024-01-01 13:14:58.498', '2024-01-01 13:14:58.498', NULL, 10, 1);
INSERT INTO `base_sys_role_department` VALUES (27, '2024-01-01 13:14:58.498', '2024-01-01 13:14:58.498', NULL, 13, 12);

-- ----------------------------
-- Table structure for base_sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_role_menu`;
CREATE TABLE `base_sys_role_menu`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `roleId` bigint(0) NOT NULL,
  `menuId` bigint(0) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_base_sys_role_menu_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 516 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_sys_role_menu
-- ----------------------------
INSERT INTO `base_sys_role_menu` VALUES (1, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 1);
INSERT INTO `base_sys_role_menu` VALUES (2, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 96);
INSERT INTO `base_sys_role_menu` VALUES (3, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 45);
INSERT INTO `base_sys_role_menu` VALUES (4, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 43);
INSERT INTO `base_sys_role_menu` VALUES (5, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 49);
INSERT INTO `base_sys_role_menu` VALUES (6, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 86);
INSERT INTO `base_sys_role_menu` VALUES (7, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 2);
INSERT INTO `base_sys_role_menu` VALUES (8, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 27);
INSERT INTO `base_sys_role_menu` VALUES (9, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 97);
INSERT INTO `base_sys_role_menu` VALUES (10, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 59);
INSERT INTO `base_sys_role_menu` VALUES (11, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 60);
INSERT INTO `base_sys_role_menu` VALUES (12, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 61);
INSERT INTO `base_sys_role_menu` VALUES (13, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 62);
INSERT INTO `base_sys_role_menu` VALUES (14, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 63);
INSERT INTO `base_sys_role_menu` VALUES (15, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 65);
INSERT INTO `base_sys_role_menu` VALUES (16, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 98);
INSERT INTO `base_sys_role_menu` VALUES (17, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 99);
INSERT INTO `base_sys_role_menu` VALUES (18, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 100);
INSERT INTO `base_sys_role_menu` VALUES (19, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 101);
INSERT INTO `base_sys_role_menu` VALUES (20, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 8);
INSERT INTO `base_sys_role_menu` VALUES (21, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 10);
INSERT INTO `base_sys_role_menu` VALUES (22, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 11);
INSERT INTO `base_sys_role_menu` VALUES (23, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 12);
INSERT INTO `base_sys_role_menu` VALUES (24, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 13);
INSERT INTO `base_sys_role_menu` VALUES (25, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 22);
INSERT INTO `base_sys_role_menu` VALUES (26, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 23);
INSERT INTO `base_sys_role_menu` VALUES (27, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 24);
INSERT INTO `base_sys_role_menu` VALUES (28, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 25);
INSERT INTO `base_sys_role_menu` VALUES (29, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 26);
INSERT INTO `base_sys_role_menu` VALUES (30, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 69);
INSERT INTO `base_sys_role_menu` VALUES (31, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 70);
INSERT INTO `base_sys_role_menu` VALUES (32, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 71);
INSERT INTO `base_sys_role_menu` VALUES (33, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 72);
INSERT INTO `base_sys_role_menu` VALUES (34, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 73);
INSERT INTO `base_sys_role_menu` VALUES (35, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 74);
INSERT INTO `base_sys_role_menu` VALUES (36, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 75);
INSERT INTO `base_sys_role_menu` VALUES (37, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 76);
INSERT INTO `base_sys_role_menu` VALUES (38, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 77);
INSERT INTO `base_sys_role_menu` VALUES (39, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 78);
INSERT INTO `base_sys_role_menu` VALUES (40, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 79);
INSERT INTO `base_sys_role_menu` VALUES (41, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 80);
INSERT INTO `base_sys_role_menu` VALUES (42, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 81);
INSERT INTO `base_sys_role_menu` VALUES (43, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 82);
INSERT INTO `base_sys_role_menu` VALUES (44, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 83);
INSERT INTO `base_sys_role_menu` VALUES (45, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 105);
INSERT INTO `base_sys_role_menu` VALUES (46, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 102);
INSERT INTO `base_sys_role_menu` VALUES (47, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 103);
INSERT INTO `base_sys_role_menu` VALUES (48, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 29);
INSERT INTO `base_sys_role_menu` VALUES (49, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 30);
INSERT INTO `base_sys_role_menu` VALUES (50, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 47);
INSERT INTO `base_sys_role_menu` VALUES (51, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 48);
INSERT INTO `base_sys_role_menu` VALUES (52, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 84);
INSERT INTO `base_sys_role_menu` VALUES (53, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 90);
INSERT INTO `base_sys_role_menu` VALUES (54, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 8, 85);
INSERT INTO `base_sys_role_menu` VALUES (55, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 1);
INSERT INTO `base_sys_role_menu` VALUES (56, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 96);
INSERT INTO `base_sys_role_menu` VALUES (57, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 45);
INSERT INTO `base_sys_role_menu` VALUES (58, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 43);
INSERT INTO `base_sys_role_menu` VALUES (59, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 49);
INSERT INTO `base_sys_role_menu` VALUES (60, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 86);
INSERT INTO `base_sys_role_menu` VALUES (61, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 2);
INSERT INTO `base_sys_role_menu` VALUES (62, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 27);
INSERT INTO `base_sys_role_menu` VALUES (63, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 97);
INSERT INTO `base_sys_role_menu` VALUES (64, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 59);
INSERT INTO `base_sys_role_menu` VALUES (65, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 60);
INSERT INTO `base_sys_role_menu` VALUES (66, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 61);
INSERT INTO `base_sys_role_menu` VALUES (67, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 62);
INSERT INTO `base_sys_role_menu` VALUES (68, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 63);
INSERT INTO `base_sys_role_menu` VALUES (69, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 65);
INSERT INTO `base_sys_role_menu` VALUES (70, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 98);
INSERT INTO `base_sys_role_menu` VALUES (71, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 99);
INSERT INTO `base_sys_role_menu` VALUES (72, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 100);
INSERT INTO `base_sys_role_menu` VALUES (73, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 101);
INSERT INTO `base_sys_role_menu` VALUES (74, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 8);
INSERT INTO `base_sys_role_menu` VALUES (75, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 10);
INSERT INTO `base_sys_role_menu` VALUES (76, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 11);
INSERT INTO `base_sys_role_menu` VALUES (77, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 12);
INSERT INTO `base_sys_role_menu` VALUES (78, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 13);
INSERT INTO `base_sys_role_menu` VALUES (79, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 22);
INSERT INTO `base_sys_role_menu` VALUES (80, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 23);
INSERT INTO `base_sys_role_menu` VALUES (81, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 24);
INSERT INTO `base_sys_role_menu` VALUES (82, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 25);
INSERT INTO `base_sys_role_menu` VALUES (83, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 26);
INSERT INTO `base_sys_role_menu` VALUES (84, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 69);
INSERT INTO `base_sys_role_menu` VALUES (85, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 70);
INSERT INTO `base_sys_role_menu` VALUES (86, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 71);
INSERT INTO `base_sys_role_menu` VALUES (87, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 72);
INSERT INTO `base_sys_role_menu` VALUES (88, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 73);
INSERT INTO `base_sys_role_menu` VALUES (89, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 74);
INSERT INTO `base_sys_role_menu` VALUES (90, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 75);
INSERT INTO `base_sys_role_menu` VALUES (91, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 76);
INSERT INTO `base_sys_role_menu` VALUES (92, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 77);
INSERT INTO `base_sys_role_menu` VALUES (93, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 78);
INSERT INTO `base_sys_role_menu` VALUES (94, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 79);
INSERT INTO `base_sys_role_menu` VALUES (95, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 80);
INSERT INTO `base_sys_role_menu` VALUES (96, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 81);
INSERT INTO `base_sys_role_menu` VALUES (97, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 82);
INSERT INTO `base_sys_role_menu` VALUES (98, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 83);
INSERT INTO `base_sys_role_menu` VALUES (99, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 105);
INSERT INTO `base_sys_role_menu` VALUES (100, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 102);
INSERT INTO `base_sys_role_menu` VALUES (101, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 103);
INSERT INTO `base_sys_role_menu` VALUES (102, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 29);
INSERT INTO `base_sys_role_menu` VALUES (103, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 30);
INSERT INTO `base_sys_role_menu` VALUES (104, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 47);
INSERT INTO `base_sys_role_menu` VALUES (105, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 48);
INSERT INTO `base_sys_role_menu` VALUES (106, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 84);
INSERT INTO `base_sys_role_menu` VALUES (107, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 90);
INSERT INTO `base_sys_role_menu` VALUES (108, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 9, 85);
INSERT INTO `base_sys_role_menu` VALUES (161, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 11, 1);
INSERT INTO `base_sys_role_menu` VALUES (162, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 11, 96);
INSERT INTO `base_sys_role_menu` VALUES (163, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 11, 45);
INSERT INTO `base_sys_role_menu` VALUES (164, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 11, 43);
INSERT INTO `base_sys_role_menu` VALUES (165, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 11, 49);
INSERT INTO `base_sys_role_menu` VALUES (166, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 11, 86);
INSERT INTO `base_sys_role_menu` VALUES (167, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 11, 47);
INSERT INTO `base_sys_role_menu` VALUES (168, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 11, 48);
INSERT INTO `base_sys_role_menu` VALUES (169, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 11, 85);
INSERT INTO `base_sys_role_menu` VALUES (170, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 11, 84);
INSERT INTO `base_sys_role_menu` VALUES (290, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 1);
INSERT INTO `base_sys_role_menu` VALUES (291, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 96);
INSERT INTO `base_sys_role_menu` VALUES (292, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 45);
INSERT INTO `base_sys_role_menu` VALUES (293, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 43);
INSERT INTO `base_sys_role_menu` VALUES (294, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 49);
INSERT INTO `base_sys_role_menu` VALUES (295, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 86);
INSERT INTO `base_sys_role_menu` VALUES (296, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 2);
INSERT INTO `base_sys_role_menu` VALUES (297, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 27);
INSERT INTO `base_sys_role_menu` VALUES (298, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 97);
INSERT INTO `base_sys_role_menu` VALUES (299, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 59);
INSERT INTO `base_sys_role_menu` VALUES (300, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 60);
INSERT INTO `base_sys_role_menu` VALUES (301, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 61);
INSERT INTO `base_sys_role_menu` VALUES (302, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 62);
INSERT INTO `base_sys_role_menu` VALUES (303, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 63);
INSERT INTO `base_sys_role_menu` VALUES (304, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 65);
INSERT INTO `base_sys_role_menu` VALUES (305, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 98);
INSERT INTO `base_sys_role_menu` VALUES (306, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 99);
INSERT INTO `base_sys_role_menu` VALUES (307, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 100);
INSERT INTO `base_sys_role_menu` VALUES (308, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 101);
INSERT INTO `base_sys_role_menu` VALUES (309, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 8);
INSERT INTO `base_sys_role_menu` VALUES (310, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 10);
INSERT INTO `base_sys_role_menu` VALUES (311, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 11);
INSERT INTO `base_sys_role_menu` VALUES (312, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 12);
INSERT INTO `base_sys_role_menu` VALUES (313, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 13);
INSERT INTO `base_sys_role_menu` VALUES (314, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 22);
INSERT INTO `base_sys_role_menu` VALUES (315, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 23);
INSERT INTO `base_sys_role_menu` VALUES (316, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 24);
INSERT INTO `base_sys_role_menu` VALUES (317, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 25);
INSERT INTO `base_sys_role_menu` VALUES (318, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 26);
INSERT INTO `base_sys_role_menu` VALUES (319, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 69);
INSERT INTO `base_sys_role_menu` VALUES (320, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 70);
INSERT INTO `base_sys_role_menu` VALUES (321, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 71);
INSERT INTO `base_sys_role_menu` VALUES (322, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 72);
INSERT INTO `base_sys_role_menu` VALUES (323, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 73);
INSERT INTO `base_sys_role_menu` VALUES (324, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 74);
INSERT INTO `base_sys_role_menu` VALUES (325, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 75);
INSERT INTO `base_sys_role_menu` VALUES (326, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 76);
INSERT INTO `base_sys_role_menu` VALUES (327, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 77);
INSERT INTO `base_sys_role_menu` VALUES (328, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 78);
INSERT INTO `base_sys_role_menu` VALUES (329, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 79);
INSERT INTO `base_sys_role_menu` VALUES (330, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 80);
INSERT INTO `base_sys_role_menu` VALUES (331, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 81);
INSERT INTO `base_sys_role_menu` VALUES (332, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 82);
INSERT INTO `base_sys_role_menu` VALUES (333, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 83);
INSERT INTO `base_sys_role_menu` VALUES (334, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 105);
INSERT INTO `base_sys_role_menu` VALUES (335, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 102);
INSERT INTO `base_sys_role_menu` VALUES (336, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 103);
INSERT INTO `base_sys_role_menu` VALUES (337, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 29);
INSERT INTO `base_sys_role_menu` VALUES (338, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 30);
INSERT INTO `base_sys_role_menu` VALUES (339, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 47);
INSERT INTO `base_sys_role_menu` VALUES (340, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 48);
INSERT INTO `base_sys_role_menu` VALUES (341, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 84);
INSERT INTO `base_sys_role_menu` VALUES (342, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 90);
INSERT INTO `base_sys_role_menu` VALUES (343, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 12, 85);
INSERT INTO `base_sys_role_menu` VALUES (355, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 1);
INSERT INTO `base_sys_role_menu` VALUES (356, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 96);
INSERT INTO `base_sys_role_menu` VALUES (357, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 45);
INSERT INTO `base_sys_role_menu` VALUES (358, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 43);
INSERT INTO `base_sys_role_menu` VALUES (359, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 49);
INSERT INTO `base_sys_role_menu` VALUES (360, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 86);
INSERT INTO `base_sys_role_menu` VALUES (361, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 2);
INSERT INTO `base_sys_role_menu` VALUES (362, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 27);
INSERT INTO `base_sys_role_menu` VALUES (363, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 97);
INSERT INTO `base_sys_role_menu` VALUES (364, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 59);
INSERT INTO `base_sys_role_menu` VALUES (365, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 60);
INSERT INTO `base_sys_role_menu` VALUES (366, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 61);
INSERT INTO `base_sys_role_menu` VALUES (367, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 62);
INSERT INTO `base_sys_role_menu` VALUES (368, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 63);
INSERT INTO `base_sys_role_menu` VALUES (369, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 65);
INSERT INTO `base_sys_role_menu` VALUES (370, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 98);
INSERT INTO `base_sys_role_menu` VALUES (371, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 99);
INSERT INTO `base_sys_role_menu` VALUES (372, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 100);
INSERT INTO `base_sys_role_menu` VALUES (373, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 101);
INSERT INTO `base_sys_role_menu` VALUES (374, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 8);
INSERT INTO `base_sys_role_menu` VALUES (375, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 10);
INSERT INTO `base_sys_role_menu` VALUES (376, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 11);
INSERT INTO `base_sys_role_menu` VALUES (377, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 12);
INSERT INTO `base_sys_role_menu` VALUES (378, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 13);
INSERT INTO `base_sys_role_menu` VALUES (379, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 22);
INSERT INTO `base_sys_role_menu` VALUES (380, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 23);
INSERT INTO `base_sys_role_menu` VALUES (381, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 24);
INSERT INTO `base_sys_role_menu` VALUES (382, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 25);
INSERT INTO `base_sys_role_menu` VALUES (383, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 26);
INSERT INTO `base_sys_role_menu` VALUES (384, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 69);
INSERT INTO `base_sys_role_menu` VALUES (385, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 70);
INSERT INTO `base_sys_role_menu` VALUES (386, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 71);
INSERT INTO `base_sys_role_menu` VALUES (387, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 72);
INSERT INTO `base_sys_role_menu` VALUES (388, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 73);
INSERT INTO `base_sys_role_menu` VALUES (389, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 74);
INSERT INTO `base_sys_role_menu` VALUES (390, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 75);
INSERT INTO `base_sys_role_menu` VALUES (391, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 76);
INSERT INTO `base_sys_role_menu` VALUES (392, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 77);
INSERT INTO `base_sys_role_menu` VALUES (393, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 78);
INSERT INTO `base_sys_role_menu` VALUES (394, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 79);
INSERT INTO `base_sys_role_menu` VALUES (395, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 80);
INSERT INTO `base_sys_role_menu` VALUES (396, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 81);
INSERT INTO `base_sys_role_menu` VALUES (397, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 82);
INSERT INTO `base_sys_role_menu` VALUES (398, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 83);
INSERT INTO `base_sys_role_menu` VALUES (399, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 105);
INSERT INTO `base_sys_role_menu` VALUES (400, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 102);
INSERT INTO `base_sys_role_menu` VALUES (401, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 103);
INSERT INTO `base_sys_role_menu` VALUES (402, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 29);
INSERT INTO `base_sys_role_menu` VALUES (403, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 30);
INSERT INTO `base_sys_role_menu` VALUES (404, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 47);
INSERT INTO `base_sys_role_menu` VALUES (405, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 48);
INSERT INTO `base_sys_role_menu` VALUES (406, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 84);
INSERT INTO `base_sys_role_menu` VALUES (407, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 90);
INSERT INTO `base_sys_role_menu` VALUES (408, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 10, 85);
INSERT INTO `base_sys_role_menu` VALUES (463, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 1);
INSERT INTO `base_sys_role_menu` VALUES (464, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 96);
INSERT INTO `base_sys_role_menu` VALUES (465, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 45);
INSERT INTO `base_sys_role_menu` VALUES (466, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 43);
INSERT INTO `base_sys_role_menu` VALUES (467, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 49);
INSERT INTO `base_sys_role_menu` VALUES (468, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 86);
INSERT INTO `base_sys_role_menu` VALUES (469, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 2);
INSERT INTO `base_sys_role_menu` VALUES (470, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 27);
INSERT INTO `base_sys_role_menu` VALUES (471, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 97);
INSERT INTO `base_sys_role_menu` VALUES (472, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 59);
INSERT INTO `base_sys_role_menu` VALUES (473, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 60);
INSERT INTO `base_sys_role_menu` VALUES (474, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 61);
INSERT INTO `base_sys_role_menu` VALUES (475, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 62);
INSERT INTO `base_sys_role_menu` VALUES (476, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 63);
INSERT INTO `base_sys_role_menu` VALUES (477, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 65);
INSERT INTO `base_sys_role_menu` VALUES (478, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 98);
INSERT INTO `base_sys_role_menu` VALUES (479, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 99);
INSERT INTO `base_sys_role_menu` VALUES (480, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 100);
INSERT INTO `base_sys_role_menu` VALUES (481, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 101);
INSERT INTO `base_sys_role_menu` VALUES (482, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 8);
INSERT INTO `base_sys_role_menu` VALUES (483, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 10);
INSERT INTO `base_sys_role_menu` VALUES (484, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 11);
INSERT INTO `base_sys_role_menu` VALUES (485, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 12);
INSERT INTO `base_sys_role_menu` VALUES (486, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 13);
INSERT INTO `base_sys_role_menu` VALUES (487, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 22);
INSERT INTO `base_sys_role_menu` VALUES (488, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 23);
INSERT INTO `base_sys_role_menu` VALUES (489, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 24);
INSERT INTO `base_sys_role_menu` VALUES (490, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 25);
INSERT INTO `base_sys_role_menu` VALUES (491, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 26);
INSERT INTO `base_sys_role_menu` VALUES (492, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 69);
INSERT INTO `base_sys_role_menu` VALUES (493, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 70);
INSERT INTO `base_sys_role_menu` VALUES (494, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 71);
INSERT INTO `base_sys_role_menu` VALUES (495, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 72);
INSERT INTO `base_sys_role_menu` VALUES (496, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 73);
INSERT INTO `base_sys_role_menu` VALUES (497, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 74);
INSERT INTO `base_sys_role_menu` VALUES (498, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 75);
INSERT INTO `base_sys_role_menu` VALUES (499, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 76);
INSERT INTO `base_sys_role_menu` VALUES (500, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 77);
INSERT INTO `base_sys_role_menu` VALUES (501, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 78);
INSERT INTO `base_sys_role_menu` VALUES (502, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 79);
INSERT INTO `base_sys_role_menu` VALUES (503, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 80);
INSERT INTO `base_sys_role_menu` VALUES (504, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 81);
INSERT INTO `base_sys_role_menu` VALUES (505, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 82);
INSERT INTO `base_sys_role_menu` VALUES (506, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 83);
INSERT INTO `base_sys_role_menu` VALUES (507, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 105);
INSERT INTO `base_sys_role_menu` VALUES (508, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 102);
INSERT INTO `base_sys_role_menu` VALUES (509, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 103);
INSERT INTO `base_sys_role_menu` VALUES (510, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 29);
INSERT INTO `base_sys_role_menu` VALUES (511, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 30);
INSERT INTO `base_sys_role_menu` VALUES (512, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 47);
INSERT INTO `base_sys_role_menu` VALUES (513, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 48);
INSERT INTO `base_sys_role_menu` VALUES (514, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 84);
INSERT INTO `base_sys_role_menu` VALUES (515, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 90);
INSERT INTO `base_sys_role_menu` VALUES (516, '2024-01-01 13:14:58.028', '2024-01-01 13:14:58.028', NULL, 13, 85);

-- ----------------------------
-- Table structure for base_sys_user
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_user`;
CREATE TABLE `base_sys_user`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `departmentId` bigint(0) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `passwordV` int(0) NOT NULL DEFAULT 1,
  `nickName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `headImg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` int(0) NOT NULL DEFAULT 1,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `socketId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_base_sys_user_deleted_at`(`deleted_at`) USING BTREE,
  INDEX `idx_base_sys_user_department_id`(`departmentId`) USING BTREE,
  INDEX `idx_base_sys_user_username`(`username`) USING BTREE,
  INDEX `idx_base_sys_user_phone`(`phone`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_sys_user
-- ----------------------------
INSERT INTO `base_sys_user` VALUES (1, '2024-01-01 13:14:56.871', '2024-09-02 11:57:19.034', NULL, 1, '超级管理员', 'admin', 'e10adc3949ba59abbe56e057f20f883e', 4, '管理员', 'https://cool-admin-pro.oss-cn-shanghai.aliyuncs.com/app/c8128c24-d0e9-4e07-9c0d-6f65446e105b.png', '18000000000', '<EMAIL>', 1, '拥有最高权限的用户', NULL);

-- ----------------------------
-- Table structure for base_sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `base_sys_user_role`;
CREATE TABLE `base_sys_user_role`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `userId` bigint(0) NOT NULL,
  `roleId` bigint(0) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_base_sys_user_role_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of base_sys_user_role
-- ----------------------------
INSERT INTO `base_sys_user_role` VALUES (1, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 1, 1);
INSERT INTO `base_sys_user_role` VALUES (2, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 2, 1);
INSERT INTO `base_sys_user_role` VALUES (3, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 3, 1);
INSERT INTO `base_sys_user_role` VALUES (4, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 4, 1);
INSERT INTO `base_sys_user_role` VALUES (5, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 5, 1);
INSERT INTO `base_sys_user_role` VALUES (6, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 6, 1);
INSERT INTO `base_sys_user_role` VALUES (7, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 7, 1);
INSERT INTO `base_sys_user_role` VALUES (8, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 8, 1);
INSERT INTO `base_sys_user_role` VALUES (9, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 9, 1);
INSERT INTO `base_sys_user_role` VALUES (10, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 10, 1);
INSERT INTO `base_sys_user_role` VALUES (11, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 11, 1);
INSERT INTO `base_sys_user_role` VALUES (12, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 12, 1);
INSERT INTO `base_sys_user_role` VALUES (13, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 13, 1);
INSERT INTO `base_sys_user_role` VALUES (14, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 14, 1);
INSERT INTO `base_sys_user_role` VALUES (16, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 16, 1);
INSERT INTO `base_sys_user_role` VALUES (17, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 15, 1);
INSERT INTO `base_sys_user_role` VALUES (19, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 18, 1);
INSERT INTO `base_sys_user_role` VALUES (21, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 17, 1);
INSERT INTO `base_sys_user_role` VALUES (22, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 20, 1);
INSERT INTO `base_sys_user_role` VALUES (24, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 22, 1);
INSERT INTO `base_sys_user_role` VALUES (27, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 19, 1);
INSERT INTO `base_sys_user_role` VALUES (28, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 21, 8);
INSERT INTO `base_sys_user_role` VALUES (29, '2024-01-01 13:14:57.348', '2024-01-01 13:14:57.348', NULL, 23, 8);

-- ----------------------------
-- Table structure for chatgpt_aff_record
-- ----------------------------
DROP TABLE IF EXISTS `chatgpt_aff_record`;
CREATE TABLE `chatgpt_aff_record`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `inviterId` bigint(0) NOT NULL COMMENT '邀请人id',
  `inviteeId` bigint(0) NOT NULL COMMENT '被邀请人id',
  `affMoney` double NOT NULL COMMENT '推广金额',
  `orderType` tinyint(1) DEFAULT NULL COMMENT '订单类型1-充值 2-兑换',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（0：未处理，1：已处理）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatgpt_aff_record
-- ----------------------------
INSERT INTO `chatgpt_aff_record` VALUES (1, '2024-12-30 22:44:11.055', 999999999, 1000000025, 0.1, 2, 1);
INSERT INTO `chatgpt_aff_record` VALUES (2, '2024-12-30 23:02:29.314', 999999999, 1000000032, 0.1, 2, 1);
INSERT INTO `chatgpt_aff_record` VALUES (3, '2025-01-10 21:29:37.593', 999999999, 1000000022, 100, 2, 0);

-- ----------------------------
-- Table structure for chatgpt_config
-- ----------------------------
DROP TABLE IF EXISTS `chatgpt_config`;
CREATE TABLE `chatgpt_config`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `key` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'key',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '值',
  `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 275 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatgpt_config
-- ----------------------------
INSERT INTO `chatgpt_config` VALUES (149, '2025-01-07 19:52:35.081', '2025-01-07 19:52:35.081', NULL, 'delivery', 'false', NULL);
INSERT INTO `chatgpt_config` VALUES (150, '2025-01-07 19:52:35.083', '2025-01-07 19:52:35.083', NULL, 'enableClaudeNode', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (151, '2025-01-07 19:52:35.085', '2025-01-07 19:52:35.085', NULL, 'openWechat', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (152, '2025-01-07 19:52:35.087', '2025-01-07 19:52:35.087', NULL, 'enableRegister', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (153, '2025-01-07 19:52:35.088', '2025-01-07 19:52:35.089', NULL, 'enablePlus', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (154, '2025-01-07 19:52:35.090', '2025-01-07 19:52:35.090', NULL, 'enableSiteShop', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (155, '2025-01-07 19:52:35.092', '2025-01-07 19:52:35.092', NULL, 'enableSSO', 'false', NULL);
INSERT INTO `chatgpt_config` VALUES (157, '2025-01-07 19:52:35.095', '2025-01-07 19:52:35.095', NULL, 'enablePromotion', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (158, '2025-01-07 19:52:35.097', '2025-01-07 19:52:35.097', NULL, 'emailNotification', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (159, '2025-01-07 19:52:35.098', '2025-01-07 19:52:35.098', NULL, 'affRate', '0.1', NULL);
INSERT INTO `chatgpt_config` VALUES (160, '2025-01-07 19:52:35.100', '2025-01-07 19:52:35.100', NULL, 'enableCalcCarCodes', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (161, '2025-01-07 19:52:35.102', '2025-01-07 19:52:35.102', NULL, 'showVersion', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (162, '2025-01-07 19:52:35.104', '2025-01-07 19:52:35.104', NULL, 'enableBackNode', 'false', NULL);
INSERT INTO `chatgpt_config` VALUES (163, '2025-01-07 19:52:35.105', '2025-01-07 19:52:35.105', NULL, 'enableNoLogin', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (164, '2025-01-07 19:52:35.107', '2025-01-07 19:52:35.107', NULL, 'visitorLimit', '1', NULL);
INSERT INTO `chatgpt_config` VALUES (165, '2025-01-07 19:52:35.109', '2025-01-07 19:52:35.109', NULL, 'enableExpirationReminder', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (166, '2025-01-07 19:52:35.110', '2025-01-07 19:52:35.110', NULL, 'visitorUsePlus', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (167, '2025-01-07 19:52:35.112', '2025-01-07 19:52:35.112', NULL, 'enablePaySuccessNotice', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (168, '2025-01-07 19:52:35.115', '2025-01-07 19:52:35.115', NULL, 'useBackNode', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (169, '2025-01-07 19:52:35.118', '2025-01-07 19:52:35.118', NULL, 'showPaymentHistory', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (170, '2025-01-07 19:52:35.121', '2025-01-07 19:52:35.121', NULL, 'enableInvite', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (171, '2025-01-07 19:52:35.122', '2025-01-07 19:52:35.122', NULL, 'enableShowRemaining', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (172, '2025-01-07 19:52:35.125', '2025-01-07 19:52:35.125', NULL, 'enableCashbackForPaidUsers', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (173, '2025-01-07 19:52:35.126', '2025-01-07 19:52:35.126', NULL, 'enableNoSelectCar', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (174, '2025-01-07 19:52:35.128', '2025-01-07 19:52:35.128', NULL, 'enableInviteCode', 'false', NULL);
INSERT INTO `chatgpt_config` VALUES (175, '2025-01-07 19:52:35.130', '2025-01-07 19:52:35.130', NULL, 'enableRepeatRegister', 'false', NULL);
INSERT INTO `chatgpt_config` VALUES (176, '2025-01-07 19:52:35.132', '2025-01-07 19:52:35.132', NULL, 'maxDevices', '', NULL);
INSERT INTO `chatgpt_config` VALUES (177, '2025-01-07 19:52:35.134', '2025-01-07 19:52:35.134', NULL, 'signInTime', '100', NULL);
INSERT INTO `chatgpt_config` VALUES (178, '2025-01-07 19:52:35.136', '2025-01-07 19:52:35.136', NULL, 'enableSignIn', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (179, '2025-01-07 19:52:35.137', '2025-01-07 19:52:35.137', NULL, 'enablePlusSignIn', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (180, '2025-01-07 19:52:35.139', '2025-01-07 19:52:35.139', NULL, 'enableFreeSignIn', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (181, '2025-01-07 19:58:17.624', '2025-01-07 19:58:17.624', NULL, 'siteName', '云上未来', NULL);
INSERT INTO `chatgpt_config` VALUES (182, '2025-01-07 19:58:17.627', '2025-01-07 19:58:17.627', NULL, 'themeName', 'basic', NULL);
INSERT INTO `chatgpt_config` VALUES (183, '2025-01-07 19:58:17.628', '2025-01-07 19:58:17.629', NULL, 'saveLogDays', '1', NULL);
INSERT INTO `chatgpt_config` VALUES (184, '2025-01-07 19:58:17.631', '2025-01-07 19:58:17.631', NULL, 'visitorUsagePeriod', '1s', NULL);
INSERT INTO `chatgpt_config` VALUES (185, '2025-01-07 19:58:17.634', '2025-01-07 19:58:17.634', NULL, 'siteShopName', '123', NULL);
INSERT INTO `chatgpt_config` VALUES (186, '2025-01-07 19:58:17.637', '2025-01-07 19:58:17.637', NULL, 'claudeUrl', 'https://yunshang.940309.xyz', NULL);
INSERT INTO `chatgpt_config` VALUES (187, '2025-01-07 19:58:17.639', '2025-01-07 19:58:17.639', NULL, 'userGuideUrl', 'https://docs.qq.com/doc/DQlpkSmF0Zkhmakdy', NULL);
INSERT INTO `chatgpt_config` VALUES (188, '2025-01-07 19:58:17.641', '2025-01-07 19:58:17.641', NULL, 'customerSidebarUrl', 'https://chat.chatshare.xyz/codetoken?logintoken=beibei111', NULL);
INSERT INTO `chatgpt_config` VALUES (189, '2025-01-07 19:58:17.643', '2025-01-07 19:58:17.643', NULL, 'fkAddress', '', NULL);
INSERT INTO `chatgpt_config` VALUES (190, '2025-01-07 19:58:17.645', '2025-01-07 19:58:17.645', NULL, 'expireTime', '10000', NULL);
INSERT INTO `chatgpt_config` VALUES (191, '2025-01-07 19:58:17.648', '2025-01-07 19:58:17.648', NULL, 'freeTime', '', NULL);
INSERT INTO `chatgpt_config` VALUES (192, '2025-01-07 19:58:17.651', '2025-01-07 19:58:17.651', NULL, 'affTime', '', NULL);
INSERT INTO `chatgpt_config` VALUES (193, '2025-01-07 19:58:17.653', '2025-01-07 19:58:17.653', NULL, 'usagePeriod', '3h', NULL);
INSERT INTO `chatgpt_config` VALUES (194, '2025-01-07 19:58:17.655', '2025-01-07 19:58:17.655', NULL, 'usageCount', '', NULL);
INSERT INTO `chatgpt_config` VALUES (195, '2025-01-07 19:58:17.657', '2025-01-07 19:58:17.657', NULL, 'loginAnnouncement', '1', NULL);
INSERT INTO `chatgpt_config` VALUES (196, '2025-01-07 19:58:17.659', '2025-01-07 19:58:17.659', NULL, 'siteAnnouncement', '2', NULL);
INSERT INTO `chatgpt_config` VALUES (197, '2025-01-07 19:58:17.661', '2025-01-07 19:58:17.661', NULL, 'freeNodeName', '', NULL);
INSERT INTO `chatgpt_config` VALUES (198, '2025-01-07 19:58:17.662', '2025-01-07 19:58:17.662', NULL, 'normalNodeName', '', NULL);
INSERT INTO `chatgpt_config` VALUES (199, '2025-01-07 19:58:17.665', '2025-01-07 19:58:17.665', NULL, 'plusNodeName', '', NULL);
INSERT INTO `chatgpt_config` VALUES (200, '2025-01-07 19:58:17.668', '2025-01-07 19:58:17.668', NULL, 'claudeNodeName', 'Claude', NULL);
INSERT INTO `chatgpt_config` VALUES (201, '2025-01-07 19:58:17.670', '2025-01-07 19:58:17.670', NULL, 'backupNode', '', NULL);
INSERT INTO `chatgpt_config` VALUES (202, '2025-01-07 19:58:17.672', '2025-01-07 19:58:17.672', NULL, 'backupUrl', 'https://chat.chatshare.xyz/codetoken?logintoken=beibei111', NULL);
INSERT INTO `chatgpt_config` VALUES (203, '2025-01-07 19:58:25.504', '2025-01-07 19:58:25.504', NULL, 'threshold', '100', NULL);
INSERT INTO `chatgpt_config` VALUES (204, '2025-01-07 19:58:39.124', '2025-01-07 19:58:39.124', NULL, 'signInType', '2', NULL);
INSERT INTO `chatgpt_config` VALUES (205, '2025-01-07 19:58:39.126', '2025-01-07 19:58:39.126', NULL, 'signInAnnouncement', '<p>欢迎签到喔~</p>', NULL);
INSERT INTO `chatgpt_config` VALUES (206, '2025-01-07 20:56:36.042', '2025-01-07 20:56:36.042', NULL, 'virtualNo', '1', NULL);
INSERT INTO `chatgpt_config` VALUES (207, '2025-01-07 20:56:36.045', '2025-01-07 20:56:36.045', NULL, 'virtualTeamNo', '1', NULL);
INSERT INTO `chatgpt_config` VALUES (208, '2025-01-07 20:56:36.048', '2025-01-07 20:56:36.048', NULL, 'virtualProNo', '11', NULL);
INSERT INTO `chatgpt_config` VALUES (209, '2025-01-07 20:56:36.051', '2025-01-07 20:56:36.051', NULL, 'virtualClaudeNo', '0', NULL);
INSERT INTO `chatgpt_config` VALUES (210, '2025-01-07 20:56:36.053', '2025-01-07 20:56:36.053', NULL, 'freeNodes', '1', NULL);
INSERT INTO `chatgpt_config` VALUES (211, '2025-01-08 19:38:43.907', '2025-01-08 19:38:43.911', NULL, 'collapseSidebar', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (212, '2025-01-13 17:49:10.945', '2025-01-13 17:49:10.949', NULL, 'closeCardExchange', 'false', NULL);
INSERT INTO `chatgpt_config` VALUES (213, '2025-01-13 22:57:22.648', '2025-01-13 22:57:22.648', NULL, 'showModelRate', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (226, '2025-01-16 23:36:37.939', '2025-01-16 23:36:37.939', NULL, 'dialogModelMultiplier', '{\"gpt-4o-mini\":0}', NULL);
INSERT INTO `chatgpt_config` VALUES (227, '2025-01-26 22:07:44.456', '2025-01-26 22:07:44.456', NULL, 'closeSubTypeShow', 'false', NULL);
INSERT INTO `chatgpt_config` VALUES (228, '2025-02-02 14:44:18.055', '2025-02-02 14:44:18.055', NULL, 'enableEmailCheck', 'false', NULL);
INSERT INTO `chatgpt_config` VALUES (229, '2025-02-02 14:48:02.534', '2025-02-02 14:48:02.538', NULL, 'fkAddressOpenType', '_self', NULL);
INSERT INTO `chatgpt_config` VALUES (230, '2025-02-02 14:48:02.540', '2025-02-02 14:48:02.541', NULL, 'userGuideUrlOpenType', '_self', NULL);
INSERT INTO `chatgpt_config` VALUES (231, '2025-02-02 14:48:02.541', '2025-02-02 14:48:02.542', NULL, 'sidebarOpenType', '_self', NULL);
INSERT INTO `chatgpt_config` VALUES (244, '2025-02-07 16:39:40.392', '2025-02-07 16:39:40.393', NULL, 'modelLimit', '{\"gpt-4o\": {\"limit\": 10, \"per\": \"1h\"}, \"o1\": {\"limit\": 20, \"per\": \"1h\"}}', NULL);
INSERT INTO `chatgpt_config` VALUES (245, '2025-02-19 11:29:06.901', '2025-02-19 11:29:06.901', NULL, 'sxClaudeUrl', '', NULL);
INSERT INTO `chatgpt_config` VALUES (246, '2025-02-19 22:46:11.316', '2025-02-19 22:46:11.323', NULL, 'enableSxClaude', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (247, '2025-02-20 21:58:13.212', '2025-02-20 21:58:13.212', NULL, 'backupNode2', '备用2', NULL);
INSERT INTO `chatgpt_config` VALUES (248, '2025-02-20 21:58:13.214', '2025-02-20 21:58:13.215', NULL, 'backupNode3', '备用3', NULL);
INSERT INTO `chatgpt_config` VALUES (249, '2025-02-20 21:58:13.216', '2025-02-20 21:58:13.216', NULL, 'backup2Url', 'https://docs.qq.com/doc/DQlpkSmF0Zkhmakdy', NULL);
INSERT INTO `chatgpt_config` VALUES (250, '2025-02-20 21:58:13.217', '2025-02-20 21:58:13.217', NULL, 'backup3Url', 'https://docs.qq.com/doc/DQlpkSmF0Zkhmakdy', NULL);
INSERT INTO `chatgpt_config` VALUES (251, '2025-02-23 16:11:16.583', '2025-02-23 16:11:16.586', NULL, 'backupSites', '[]', NULL);
INSERT INTO `chatgpt_config` VALUES (252, '2025-02-23 16:25:41.252', '2025-02-23 16:25:41.252', NULL, 'emailPort', '0', NULL);
INSERT INTO `chatgpt_config` VALUES (253, '2025-02-24 11:21:10.569', '2025-02-24 11:21:10.570', NULL, 'customerSidebarName', '111', NULL);
INSERT INTO `chatgpt_config` VALUES (254, '2025-02-24 11:21:10.575', '2025-02-24 11:21:10.575', NULL, 'soruxGptName', '123', NULL);
INSERT INTO `chatgpt_config` VALUES (255, '2025-02-24 11:21:10.586', '2025-02-24 11:21:10.586', NULL, 'soruxGptUrl', '123123', NULL);
INSERT INTO `chatgpt_config` VALUES (256, '2025-02-24 11:21:10.597', '2025-02-24 11:21:10.597', NULL, 'soruxGptOpenType', '_self', NULL);
INSERT INTO `chatgpt_config` VALUES (257, '2025-02-24 11:37:08.483', '2025-02-24 11:37:08.495', NULL, 'soruxGptSideBarName', 'ChatGPT', NULL);
INSERT INTO `chatgpt_config` VALUES (258, '2025-02-24 11:37:08.504', '2025-02-24 11:37:08.504', NULL, 'soruxGptSideBarUrl', 'https://open-webui.ainx.cc/auth/?email=<EMAIL>&username=test', NULL);
INSERT INTO `chatgpt_config` VALUES (259, '2025-02-24 11:37:08.509', '2025-02-24 11:37:08.510', NULL, 'soruxGptSideBarOpenType', '_self', NULL);
INSERT INTO `chatgpt_config` VALUES (260, '2025-03-01 00:11:59.688', '2025-03-01 00:11:59.690', NULL, 'grokNodeName', 'Grok', NULL);
INSERT INTO `chatgpt_config` VALUES (261, '2025-03-01 00:11:59.692', '2025-03-01 00:11:59.692', NULL, 'grokUrl', 'https://grok.fllai.cn', NULL);
INSERT INTO `chatgpt_config` VALUES (262, '2025-03-01 00:11:59.693', '2025-03-01 00:11:59.693', NULL, 'grokOpenType', '_self', NULL);
INSERT INTO `chatgpt_config` VALUES (263, '2025-03-02 21:00:51.814', '2025-03-02 21:00:51.817', NULL, 'grokNum', '80', NULL);
INSERT INTO `chatgpt_config` VALUES (264, '2025-03-02 21:53:06.136', '2025-03-02 21:53:06.136', NULL, 'grokName', '1,2,3,4,5,6,7,8', NULL);
INSERT INTO `chatgpt_config` VALUES (268, '2025-03-10 20:25:53.469', '2025-03-10 20:25:53.486', NULL, 'modelLimits', '{\"claude-3.5\":{\"limit\":20,\"per\":\"1h\"},\"gpt-4o\":{\"limit\":20,\"per\":\"1h\"},\"o1-pro\":{\"limit\":10,\"per\":\"1d\"},\"gpt-4-5\":{\"limit\":50,\"per\":\"1w\"},\"grok-3\":{\"limit\":50,\"per\":\"1d\"},\"research\":{\"limit\":50,\"per\":\"1d\"},\"third-models\":{\"limit\":50,\"per\":\"1h\"},\"o3\":{\"limit\":5,\"per\":\"1w\"},\"o4-mini\":{\"limit\":10,\"per\":\"1d\"},\"o4-mini-high\":{\"limit\":5,\"per\":\"1w\"}}', NULL);
INSERT INTO `chatgpt_config` VALUES (269, '2025-04-25 10:28:49.562', '2025-04-25 10:28:49.562', NULL, 'apiUrl', 'https://api.midsummer.work', NULL);
INSERT INTO `chatgpt_config` VALUES (270, '2025-04-25 10:28:49.563', '2025-04-25 10:28:49.564', NULL, 'apiKey', 'sk-mVVqd69NAgtK9etCL8vBJLrR2zr4g1u8uvK2pGdZl0UDaBpA', NULL);
INSERT INTO `chatgpt_config` VALUES (271, '2025-04-25 14:40:37.165', '2025-04-25 14:40:37.165', NULL, 'nodeOrder', '[{\"type\":\"draw\"},{\"type\":\"free\"},{\"type\":\"4o\"},{\"type\":\"plus\"},{\"type\":\"claude\"},{\"type\":\"grok\"},{\"type\":\"embedded\"}]', NULL);
INSERT INTO `chatgpt_config` VALUES (272, '2025-04-25 14:40:37.166', '2025-04-25 14:40:37.166', NULL, 'drawNodeName', '在线绘图', NULL);
INSERT INTO `chatgpt_config` VALUES (273, '2025-04-25 14:40:57.867', '2025-04-25 14:40:57.867', NULL, 'enableDraw', 'true', NULL);
INSERT INTO `chatgpt_config` VALUES (274, '2025-04-25 22:24:56.274', '2025-04-25 22:24:56.274', NULL, 'drawAnnouncement', '<p>一键生图，一张一毛钱。</p>', NULL);

-- ----------------------------
-- Table structure for chatgpt_conversations
-- ----------------------------
DROP TABLE IF EXISTS `chatgpt_conversations`;
CREATE TABLE `chatgpt_conversations`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `usertoken` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户token',
  `convid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话id',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话标题',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '官网账号邮箱',
  `chatgptaccountid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'ChatGPT-Account-ID',
  `gizmoid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工具ID',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '会话内容',
  `is_archived` tinyint(1) DEFAULT 0 COMMENT '是否归档',
  `need_update` tinyint(1) DEFAULT 0 COMMENT '是否需要更新',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_chatgpt_conversations_conv_id`(`convid`) USING BTREE,
  INDEX `idx_chatgpt_conversations_email`(`email`) USING BTREE,
  INDEX `idx_chatgpt_conversations_deleted_at`(`deleted_at`) USING BTREE,
  INDEX `idx_chatgpt_conversations_user_token`(`usertoken`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15725 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatgpt_conversations
-- ----------------------------
INSERT INTO `chatgpt_conversations` VALUES (1, '2025-04-08 09:08:18.000', '2025-04-08 09:08:18.000', NULL, 'admin', 'asdasdlkasdlkasdl', 'asdjaslkdjasldkj', 'sadljsadlasjl', 'jalskdjasldjl', 'asjdlasjdl', 'asdjlkasjdlksjaldjaslasdjlkasjdlksjaldjaslasdjlkasjdlksjaldjaslasdjlkasjdlksjaldjaslasdjlkasjdlksjaldjaslasdjlkasjdlksjaldjaslasdjlkasjdlksjaldjaslasdjlkasjdlksjaldjaslasdjlkasjdlksjaldjaslasdjlkasjdlksjaldjaslasdjlkasjdlksjaldjaslasdjlkasjdlksjaldjaslasdjlkasjdlksjaldjasl', 0, 0);

-- ----------------------------
-- Table structure for chatgpt_coupon
-- ----------------------------
DROP TABLE IF EXISTS `chatgpt_coupon`;
CREATE TABLE `chatgpt_coupon`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `coupon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '优惠卷',
  `discount` double(5, 2) DEFAULT NULL COMMENT '折扣',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态(0：启用，1：废弃)',
  `expireTime` datetime(0) DEFAULT NULL COMMENT '过期时间',
  `createTime` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `updateTime` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `discountAmount` double DEFAULT NULL COMMENT '优惠金额',
  `subTypeIds` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '商品ids',
  `discountCount` int(0) DEFAULT NULL COMMENT '优惠次数',
  `usageDiscountCount` int(0) DEFAULT NULL COMMENT '已优惠次数',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_coupon`(`coupon`) USING BTREE COMMENT '优惠码唯一索引'
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatgpt_coupon
-- ----------------------------
INSERT INTO `chatgpt_coupon` VALUES (6, 'E94BAANI8GSRDPHQ', 0.90, 0, '2027-11-26 00:00:00', '2024-09-30 10:43:00', '2024-09-30 10:43:00', '', NULL, '31,32,33,34,35,13', 200, 5);
INSERT INTO `chatgpt_coupon` VALUES (7, 'S2Z9EPUP0T9IBZH4', 0.85, 1, '2024-10-30 00:00:00', '2024-10-29 09:48:39', '2024-10-29 09:48:39', '', NULL, '1,2,4', NULL, NULL);

-- ----------------------------
-- Table structure for chatgpt_epaylogs
-- ----------------------------
DROP TABLE IF EXISTS `chatgpt_epaylogs`;
CREATE TABLE `chatgpt_epaylogs`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `userToken` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'userToken',
  `money` double NOT NULL COMMENT '支付金额',
  `tradeNo` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `subTypeId` bigint(0) NOT NULL COMMENT '订阅套餐',
  `days` bigint(0) NOT NULL COMMENT '有效天数',
  `status` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单状态',
  `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_chatgpt_epaylogs_deleted_at`(`deleted_at`) USING BTREE,
  INDEX `idx_chatgpt_epaylogs_trade_no`(`tradeNo`) USING BTREE,
  INDEX `idx_chatgpt_epaylogs_user_token`(`userToken`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1245 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatgpt_epaylogs
-- ----------------------------
INSERT INTO `chatgpt_epaylogs` VALUES (1135, '2024-12-12 20:15:24.606', '2024-12-12 20:15:24.606', NULL, 'admin', 0.1, '202412122015219422', 12, 123, 'pending', '【admin】购买了12312套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1136, '2024-12-12 20:20:45.027', '2024-12-12 20:20:45.027', NULL, 'admin', 0.1, '202412122020420325', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1137, '2024-12-12 20:24:49.624', '2024-12-12 20:24:49.624', NULL, 'admin', 0.1, '202412122024479857', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1138, '2024-12-12 20:26:35.868', '2024-12-12 20:26:35.868', NULL, 'admin', 0.1, '202412122026336477', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1139, '2024-12-12 20:26:56.864', '2024-12-12 20:26:56.864', NULL, 'admin', 0.1, '202412122026553746', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1140, '2024-12-12 20:28:16.819', '2024-12-12 20:28:16.819', NULL, 'admin', 0.1, '202412122028148734', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1141, '2024-12-12 20:28:34.700', '2024-12-12 20:28:34.700', NULL, 'admin', 0.1, '202412122028333941', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1142, '2024-12-12 20:31:19.266', '2024-12-12 20:31:19.266', NULL, 'admin', 0.1, '202412122031179084', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1143, '2024-12-12 20:41:50.683', '2024-12-12 20:41:50.683', NULL, 'admin', 90, '202412122041472571', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1144, '2024-12-12 20:44:19.352', '2024-12-12 20:44:19.352', NULL, 'admin', 90, '202412122044175339', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1145, '2024-12-12 20:48:28.137', '2024-12-12 20:48:28.137', NULL, 'admin', 90, '202412122048263760', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1146, '2024-12-12 20:51:24.453', '2024-12-12 20:51:24.453', NULL, 'admin', 0.1, '202412122051226720', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1147, '2024-12-12 20:57:00.907', '2024-12-12 20:57:00.907', NULL, 'admin', 100, '202412122056589907', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1148, '2024-12-12 20:58:26.022', '2024-12-12 20:58:26.022', NULL, 'admin', 100, '202412122058243595', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1149, '2024-12-12 21:00:06.788', '2024-12-12 21:00:06.788', NULL, 'admin', 100, '202412122100047157', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1150, '2024-12-12 21:01:32.300', '2024-12-12 21:01:32.300', NULL, 'admin', 100, '202412122101303667', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1151, '2024-12-12 21:02:47.145', '2024-12-13 21:02:47.145', NULL, 'admin', 100, '202412122102453830', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1152, '2024-12-12 21:03:24.663', '2024-12-14 21:03:24.663', NULL, 'admin', 100, '202412122103236241', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1153, '2024-12-12 21:06:51.442', '2024-12-12 21:06:51.442', NULL, 'admin', 100, '202412122106493250', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1154, '2024-12-12 21:08:07.069', '2024-12-12 21:08:07.069', NULL, 'admin', 100, '202412122108043779', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1155, '2024-12-12 21:09:34.927', '2024-12-12 21:09:34.927', NULL, 'admin', 100, '202412122109338873', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1156, '2024-12-12 21:12:41.689', '2024-12-12 21:12:41.689', NULL, 'admin', 100, '202412122112390227', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1157, '2024-12-12 21:13:54.720', '2024-11-14 21:13:54.000', NULL, 'admin', 100, '202412122113522940', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1158, '2024-12-12 21:14:14.024', '2024-12-12 21:14:14.025', NULL, 'admin', 90, '202412122114128968', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1159, '2024-12-12 21:14:26.019', '2024-12-15 21:14:26.000', NULL, 'admin', 100, '202412122114241388', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1160, '2024-12-12 21:14:52.266', '2024-12-12 21:14:52.266', NULL, 'admin', 100, '202412122114505550', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1161, '2024-12-12 21:15:09.193', '2024-12-12 21:15:09.193', NULL, 'admin', 0.1, '202412122115076390', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1162, '2024-12-12 21:16:07.144', '2024-12-12 21:16:07.144', NULL, 'admin', 0.1, '202412122116051665', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1163, '2024-12-12 21:16:39.256', '2024-12-12 21:16:39.256', NULL, 'admin', 0.1, '202412122116374129', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1164, '2024-12-12 21:20:54.205', '2024-12-12 21:20:54.205', NULL, 'admin', 0.1, '202412122120539597', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1165, '2024-12-12 21:21:24.940', '2024-12-09 21:21:24.000', NULL, 'admin', 0.1, '202412122121232315', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1166, '2024-12-12 21:22:53.907', '2024-12-08 21:22:53.000', NULL, 'admin', 100, '202412122122518150', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1167, '2024-12-12 23:03:28.645', '2024-12-12 23:03:28.645', NULL, 'admin', 0.1, '202412122303267402', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1168, '2024-12-12 23:05:10.666', '2024-12-07 23:05:10.000', NULL, 'admin', 100, '202412122305095283', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1169, '2024-12-12 23:08:48.665', '2024-12-06 23:08:48.000', NULL, 'admin', 100, '202412122308461226', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1170, '2024-12-12 23:08:59.434', '2024-12-12 23:08:59.434', NULL, 'admin', 0.1, '202412122308583977', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1171, '2024-12-12 23:10:26.292', '2024-12-05 23:10:26.000', NULL, 'admin', 100, '202412122310242855', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1172, '2024-12-12 23:10:52.317', '2024-12-04 23:10:52.000', NULL, 'admin', 0.1, '202412122310502132', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1173, '2024-12-12 23:12:24.069', '2024-12-03 23:12:24.000', NULL, 'admin', 100, '202412122312228484', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1174, '2024-12-12 23:13:02.170', '2024-12-02 23:13:02.000', NULL, 'admin', 100, '202412122313003130', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1175, '2024-12-12 23:13:20.030', '2024-12-01 23:13:20.000', NULL, 'admin', 0.1, '202412122313180084', 12, 123, 'success', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1176, '2024-12-12 23:13:32.386', '2024-11-13 23:13:32.000', NULL, 'admin', 100, '202412122313319211', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1177, '2024-12-12 23:14:11.356', '2024-12-12 23:14:11.356', NULL, 'admin', 100, '202412122314108302', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1178, '2024-12-12 23:16:48.924', '2024-12-12 23:16:48.924', NULL, 'admin', 100, '202412122316476868', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1179, '2024-12-12 23:18:57.196', '2024-12-12 23:18:57.196', NULL, 'admin', 100, '202412122318551882', 13, 12312, 'success', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1180, '2024-12-18 22:04:41.190', '2024-12-18 22:04:41.190', NULL, 'admin', 0.1, '202412182204200775', 12, 123, 'pending', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1181, '2024-12-18 22:08:50.103', '2024-12-18 22:08:50.103', NULL, 'admin', 0.1, '202412182208495835', 12, 123, 'pending', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1182, '2024-12-18 22:24:11.486', '2024-12-18 22:24:11.489', NULL, 'admin', 0.1, '202412182224090917', 12, 123, 'pending', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1183, '2024-12-18 22:24:38.633', '2024-12-18 22:24:38.633', NULL, 'admin', 0.1, '202412182224384577', 12, 123, 'pending', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1184, '2024-12-20 20:50:30.339', '2024-12-20 20:50:30.339', NULL, 'admin', 0.1, '202412202050287107', 12, 123, 'pending', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1185, '2024-12-23 08:53:43.209', '2024-12-23 08:53:43.212', NULL, 'admin', 0.1, '202412230852124885', 12, 123, 'pending', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1186, '2024-12-23 08:54:25.389', '2024-12-23 08:54:25.389', NULL, 'admin', 0.1, '202412230854222503', 12, 123, 'pending', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1187, '2024-12-23 08:58:07.841', '2024-12-23 08:58:07.843', NULL, 'admin', 0.1, '202412230856495623', 12, 123, 'pending', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1188, '2024-12-23 08:58:15.626', '2024-12-23 08:58:15.626', NULL, 'admin', 0.1, '202412230858158490', 12, 123, 'pending', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1189, '2025-01-03 16:12:56.351', '2025-01-03 16:12:56.351', NULL, 'admin', 0.1, '202501031612551575', 12, 123, 'pending', '【admin】购买了aaaa套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1190, '2025-01-03 16:22:56.057', '2025-01-03 16:22:56.062', NULL, 'admin', 100, '202501031622547307', 13, 12312, 'pending', '【admin】购买了aaaa套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1191, '2025-01-08 18:58:44.958', '2025-01-08 18:58:44.958', NULL, 'admin', 0.1, '202501081858418907', 12, 123, 'pending', '【admin】购买了123套餐，有效期：123天');
INSERT INTO `chatgpt_epaylogs` VALUES (1192, '2025-01-26 13:14:16.094', '2025-01-26 13:14:16.095', NULL, 'admin', 100, '202501261314150511', 13, 12312, 'pending', '【admin】购买了123套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1193, '2025-01-26 13:15:03.293', '2025-01-26 13:15:03.293', NULL, 'admin', 100, '202501261315020351', 13, 12312, 'pending', '【admin】购买了123套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1194, '2025-02-23 21:46:08.997', '2025-02-23 21:46:08.999', NULL, '6cb8ef5e-ffb4-4686-abcd-593d03207d04', 100, '202502232146080644', 13, 12312, 'pending', '【6cb8ef5e-ffb4-4686-abcd-593d03207d04】购买了123套餐，有效期：12312天');
INSERT INTO `chatgpt_epaylogs` VALUES (1195, '2025-04-02 22:42:45.761', '2025-04-02 22:42:45.766', NULL, 'admin', 1, '202504022242455423', 35, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1196, '2025-04-02 22:50:19.011', '2025-04-02 22:50:19.016', NULL, 'admin', 1, '202504022250186211', 35, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1197, '2025-04-03 17:26:59.231', '2025-04-03 17:26:59.231', NULL, 'admin', 1, '202504031726596933', 35, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1198, '2025-04-03 17:34:00.366', '2025-04-03 17:34:00.370', NULL, 'admin', 1, '202504031734008686', 35, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1199, '2025-04-03 18:14:42.778', '2025-04-03 18:14:42.783', NULL, 'admin', 1, '202504031814423921', 35, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1200, '2025-04-03 19:05:26.886', '2025-04-03 19:05:26.887', NULL, 'admin', 12, '202504031905268208', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1201, '2025-04-03 19:05:28.990', '2025-04-03 19:05:28.990', NULL, 'admin', 12, '202504031905281584', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1202, '2025-04-03 19:05:29.621', '2025-04-03 19:05:29.621', NULL, 'admin', 12, '202504031905296764', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1203, '2025-04-03 19:05:33.443', '2025-04-03 19:05:33.443', NULL, 'admin', 12, '202504031905336627', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1204, '2025-04-03 19:06:58.757', '2025-04-03 19:06:58.757', NULL, 'admin', 12, '202504031906589667', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1205, '2025-04-03 19:08:04.711', '2025-04-03 19:08:04.711', NULL, 'admin', 12, '202504031908040535', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1206, '2025-04-03 19:11:15.598', '2025-04-03 19:11:15.598', NULL, 'admin', 12, '202504031911153238', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1207, '2025-04-03 19:11:16.860', '2025-04-03 19:11:16.860', NULL, 'admin', 12, '202504031911163175', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1208, '2025-04-03 19:11:17.693', '2025-04-03 19:11:17.693', NULL, 'admin', 12, '202504031911172605', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1209, '2025-04-03 19:11:17.972', '2025-04-03 19:11:17.972', NULL, 'admin', 12, '202504031911175811', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1210, '2025-04-03 19:11:18.169', '2025-04-03 19:11:18.169', NULL, 'admin', 12, '202504031911182220', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1211, '2025-04-03 19:11:18.325', '2025-04-03 19:11:18.325', NULL, 'admin', 12, '202504031911182528', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1212, '2025-04-03 19:11:18.503', '2025-04-03 19:11:18.503', NULL, 'admin', 12, '202504031911181884', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1213, '2025-04-03 19:11:18.687', '2025-04-03 19:11:18.687', NULL, 'admin', 12, '202504031911180683', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1214, '2025-04-03 19:11:18.850', '2025-04-03 19:11:18.851', NULL, 'admin', 12, '202504031911181049', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1215, '2025-04-03 19:11:19.034', '2025-04-03 19:11:19.034', NULL, 'admin', 12, '202504031911199507', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1216, '2025-04-03 19:11:19.210', '2025-04-03 19:11:19.210', NULL, 'admin', 12, '202504031911195478', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1217, '2025-04-03 19:11:19.393', '2025-04-03 19:11:19.393', NULL, 'admin', 12, '202504031911199221', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1218, '2025-04-03 19:11:19.549', '2025-04-03 19:11:19.549', NULL, 'admin', 12, '202504031911192838', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1219, '2025-04-03 19:11:19.731', '2025-04-03 19:11:19.731', NULL, 'admin', 12, '202504031911192244', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1220, '2025-04-03 19:11:19.926', '2025-04-03 19:11:19.926', NULL, 'admin', 12, '202504031911196936', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1221, '2025-04-03 19:13:02.450', '2025-04-03 19:13:02.450', NULL, 'admin', 12, '202504031913022005', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1222, '2025-04-03 19:14:26.717', '2025-04-03 19:14:26.717', NULL, 'admin', 12, '202504031914261929', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1223, '2025-04-03 19:14:51.336', '2025-04-03 19:14:51.337', NULL, 'admin', 12, '202504031914514221', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1224, '2025-04-03 19:16:10.993', '2025-04-03 19:16:10.993', NULL, 'admin', 12, '202504031916102769', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1225, '2025-04-03 19:16:50.459', '2025-04-03 19:16:50.459', NULL, 'admin', 12, '202504031916501624', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1226, '2025-04-03 19:18:03.217', '2025-04-03 19:18:03.218', NULL, 'admin', 12, '202504031918034616', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1227, '2025-04-03 19:18:05.587', '2025-04-03 19:18:05.587', NULL, 'admin', 12, '202504031918058220', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1228, '2025-04-03 19:18:06.479', '2025-04-03 19:18:06.479', NULL, 'admin', 12, '202504031918061120', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1229, '2025-04-03 19:18:06.665', '2025-04-03 19:18:06.665', NULL, 'admin', 12, '202504031918064864', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1230, '2025-04-03 19:18:06.840', '2025-04-03 19:18:06.840', NULL, 'admin', 12, '202504031918061855', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1231, '2025-04-03 19:18:07.015', '2025-04-03 19:18:07.015', NULL, 'admin', 12, '202504031918061503', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1232, '2025-04-03 19:18:07.174', '2025-04-03 19:18:07.174', NULL, 'admin', 12, '202504031918073491', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1233, '2025-04-03 19:18:07.323', '2025-04-03 19:18:07.323', NULL, 'admin', 12, '202504031918074038', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');
INSERT INTO `chatgpt_epaylogs` VALUES (1234, '2025-04-03 19:18:07.495', '2025-04-03 19:18:07.495', NULL, 'admin', 12, '202504031918075886', 31, 1, 'pending', '【admin】购买了123套餐，有效期：1天');

-- ----------------------------
-- Table structure for chatgpt_pay_config
-- ----------------------------
DROP TABLE IF EXISTS `chatgpt_pay_config`;
CREATE TABLE `chatgpt_pay_config`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT 'Primary Key',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Title of the configuration',
  `appId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'App ID',
  `appKey` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `extraData` json COMMENT 'Extra data stored in JSON format',
  `paymentsType` int(0) DEFAULT NULL COMMENT 'Type of payment',
  `status` int(0) DEFAULT NULL COMMENT 'Status of the configuration',
  `sort` int(0) DEFAULT NULL COMMENT 'Sort order',
  `createTime` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation time',
  `updateTime` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Record last update time',
  `iconUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Table to store ChatGPT payment configuration' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatgpt_pay_config
-- ----------------------------
INSERT INTO `chatgpt_pay_config` VALUES (6, '支付宝', '2024120101421782', 'WkjFHv5dFMZPtgxMEJjg6qK6Hxoh4NAd', '{\"enableH5\": false, \"notifyUrl\": \"http://localhost:8888/api/callBack/ePayNotify\", \"merchantId\": null, \"serialNumber\": null, \"payGatewayUrl\": \"https://z-pay.cn\", \"privateKeyPem\": \"\"}', 1, 0, 1, '2024-09-03 22:35:48', '2024-09-03 22:35:48', '');
INSERT INTO `chatgpt_pay_config` VALUES (7, '微信支付', '2024120101421782', 'WkjFHv5dFMZPtgxMEJjg6qK6Hxoh4NAd', '{\"enableH5\": true, \"notifyUrl\": \"http://localhost:8888/api/callBack/ePayNotify\", \"merchantId\": null, \"serialNumber\": null, \"payGatewayUrl\": \"https://z-pay.cn\", \"privateKeyPem\": \"\"}', 2, 1, 2, '2024-09-03 22:36:35', '2024-09-03 23:52:27', '');
INSERT INTO `chatgpt_pay_config` VALUES (8, '当面付', '2021003194670894', 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgRH5CbsRB2eBACHNAelGd8XmOggltoG883ADClZxN8f3RtasXE5Hj+mSejwKpS6IjWRjvE0t8lkmtWuCawVEQIlXbTME5+gh2/2kQ/xzi8D5Q8Z4raAvsrpf0e/HhztzY6FofLtpXkYfa46XklK923wU2zy+hGnb/G35gp21RuuXcWe/twM0cZ0ix3lqPwbo8d4muPSoPrcuTb9BX6NucqRKGOL30SDYZkF3TjQj6K+pUVXzMaeK7oahORsosAlXxwpBDq/JyQvQs4z2ZzVa7McEuEIOYoOIQesDHYLp46D+avwG31JkxDuddkWUysen+2EwL/ldUE6pS0QUft3IBQIDAQAB', '{\"enableH5\": false, \"notifyUrl\": \"http://localhost:8888/api/callBack/aliPayNotify\", \"merchantId\": null, \"serialNumber\": null, \"payGatewayUrl\": \"https://openapi.alipay.com/gateway.do\", \"privateKeyPem\": \"MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQClq0pEDg+p0Il/NBP/74pQoC/yaskAjTL018pVUkCWwzN6SUGKONzhIC6ga99I1dwLJhO03p1bM+vByKnEEZ+FZMC+vQ3ii1MDuRCz0JMxUdTQ1bkWTiQQRC92OVLspP17UTdSODp/OUDlNvKNuB8/E85+68t1uS6XzOQ1Cx1RepnClrmzpAQq+dS45lVPohCO3ZIKWpHvHiC7UQsU8roRE7YT4He4/H088SlcsHMz0rqkjwY6z//IjL0u4xOkz+RsBMtBguxS4dKxkAnVjJ+8PefmpbBAySQlLvZ1fNy6rg5CGFVK/VeY1gm0ruvZVrAUauqdihWP1wUDIKUG3ZmhAgMBAAECggEAGC5+cpyS0LituTGC0dLHH6miFgJcQIZbr71nvTh6UQdBFlJnDKPsKbgm1ieoudrteXsfWUE9OrjDas+9Z+8il7fzImQ6CJ1JiruSooyJT6OgKrNnhsf1H6K0Qs9vOTeEAGIEby3h6/wRauOwV9tOREjB1WjZk99p26J+lPeAWgoL3qxs9q4Xrh13eMcmb60ybRQ2j+El0FJywkySO4kFuV7QFmA+BI9arPSJX0vcUXDMD9KONcj0hr/kzf7RImR2+pCFwXnq2v2HG+7LTnQiTsrV/8iySVuat5ANWmInjvE7z9UKVi/uuf9zkPm/kTCxmltyowqvNOcEFvCNxC2ZWQKBgQD7on+xJtAtvX3m1py6dNIgCv5rZoOTV/sn+95053M6J0Tqcj6D97K61SdFgXX4GwgM9NfMTa7qKcoO+BC+l/W35pj3h9ItbwnnYehFxrcrBiAtv0XaNIEfS/26J6B3nNphKmkvuHTUNH9FwFBvfpx+M68R274LJHcxaN5N5UALAwKBgQCoiwVTuF3qwsTWpgEMg+ahZqGOQgH7SUD4FeeuNOd2AEXl/IF0Byb9btI7GmfunjHG4Qwc2q+pPoPbSIw9kybjmG9njj0S5YvA4caCI0tmZV98UXpvY72bUtRLmZvmxevLdwoGLNUiMjyauwLJWUZ1BV5sMxHT8bX4zVs1qPA1iwKBgEsjBX/vOABAccSo1pmyYj/hKIEHVkh73+nbglxj8/ssWJ6syE/9eWfiV9ygQIp5/9hmj71YgN845TW9CrrugxeLiqd27eJVIp7tlD6pjG4TYS5ny8LRJLEkrrmUNY0hz+9ti4UNTg1DM7GvMBVqt+3THvZ1J/UJyXF+f8rApnPjAoGBAKIKBnUXLDQoTVaRAzElHvbN1UUP8RfGtc/OP5AdDOtGF+2ldlA3ECVYBocMPCANt42dkCetmHT+xYZtmWcZsqneRD0rY0xnpJUse81iYXtwuvyq2St+mSxb1f9ZD6ovXyOeruSA9eiKo/mfEm7Chhq7rqwX7Wv3Vxx9UtOkOE1BAoGBAKzTchn4tM3A7DqANjXjqdZNis89hVMbmAsI5x0bOc0ATtXRarkaf/gJw0xYc6dk36g8uzBI5tVWGVCVyB6YmEw8AGB2QTofZeOwqbetfan7leQJvRJVr0OyFb1IR6fCNZH8eeXMV9WqF5oLwA01lBypdYgqYjBGf9YjJIjDfIva\"}', 3, 0, 3, '2024-09-04 01:27:56', '2024-09-04 01:27:56', '');
INSERT INTO `chatgpt_pay_config` VALUES (9, '虎皮椒微信', '201906169739', 'b847360e95994b283a75cae3691fe20f', '{\"enableH5\": true, \"notifyUrl\": \"http://localhost:8888/api/callBack/xunHuPayNotify\", \"merchantId\": null, \"serialNumber\": null, \"payGatewayUrl\": \"https://api.xunhunet.com/payment/do.html\", \"privateKeyPem\": \"\"}', 4, 0, 1, '2024-09-08 12:28:18', '2024-09-08 12:28:18', '');
INSERT INTO `chatgpt_pay_config` VALUES (10, '蓝兔支付', '1679253304', 'a4e6589fac1e327d774c5419a9daae63', '{\"enableH5\": true, \"notifyUrl\": \"http://localhost:8888/api/callBack/lanTuPayNotify\", \"merchantId\": null, \"serialNumber\": null, \"payGatewayUrl\": \"https://api.ltzf.cn/api/wxpay/native\", \"privateKeyPem\": \"\"}', 5, 0, 1, '2024-09-21 17:44:10', '2024-09-21 17:44:10', NULL);
INSERT INTO `chatgpt_pay_config` VALUES (13, '网站支付', '2021004170684701', 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAl3qMUGrdwVD/+2c+bk1d/DA7aheee0tTV0/1HcObZOIbH9g/ugBET7/LcEOJbSFwGdGdjx5hSvbPkquHinGue7b4o1rL/jT8TKSkoVbdieMkmL8v/TsrYx7c8SuwcI9d3jfArnRzYtUZqkGfXJYcZcmwbg9z5tW27xtT9moj8urD/tYWy9Jaq/gG8G/aVPtFYcoxQ6HbBh8/caeiFcAf7b6KKosBXtQkmfTEw0F7z1dVgKKD7XLtEyxK/JY8QS7mdFHW62XbCZwq96WMVSpKl89eW8x1t3uAEwTh1fxIRz/IUyakJbxpeaMd4RWF7B9nDXhFH1sFTCpNGQobrx7RQwIDAQAB', '{\"enableH5\": true, \"notifyUrl\": \"https://你的域名/api/callBack/aliPayNotify\", \"merchantId\": null, \"serialNumber\": null, \"payGatewayUrl\": \"https://openapi.alipay.com/gateway.do\", \"privateKeyPem\": \"MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCEpKNfQBMmUEmZuriVjqsIXSFQsWi6VxYmS0IDeZmi4gZbp4S7xlr4ir6M3fu5MhQP0uJuRriAE+LauxoEQZQcQi8Vtp14vMHA6XBUDiWn+G0gkG/nEucj+G9PUtL4QJQU9Ir21bQNmcBoT+aC9aSQUdPgBScbMw/0b+1m2cHFl2nbSodTJptKNYr0KhtycXBWn2fxnio7XexhkIwIwIqjavy/b7J7LycFTl8wnjKrXb01cCTY0X7rsRg8TezUoK0Ed/vHJYJj1vrOuOqFHpxZYI9uLlJaPbKh5WSUgFB+q4jm4uhHthglNz3Xbl//roYI3GGWSo6kAp9tsI+KTDC5AgMBAAECggEAH2twV4ux/Y1fIc8dEjAtS1nkHl0n1ZvzXzbMeE5Bmle38dYIlwIxYlZbNLsQeZnnLqhhgoW2BoCvZ+iSDRgLoXX7+US8oHD15GBnf0GqodMy5lfQaggMQHUEyOFxn+SMLzqOAFI2yFH3Rb5HeBX/2T/I1HPkA4lg6iHpNke2orsXOw8q5BXYI+HNAX9JO++aI+YKyk7nQM2ag/vX5Z5y/PjYhe2vMnRB8ivinLFaG6+9z+O4AUn8kdAVVlTwFacs99HqN1WNCHWuRp7gQthqjo+UlXKdLu2/ZIijlwsQv2qZ3OgB+dE5BtQb6a/wM6jTtpSoMaE9n5DdJn/JVvAcAQKBgQDOGYH9zB4Ap4zf4N0KciIQziCu7sQZakS6cOwwTdOxi6di7zGvkcytghLCYxY97PRsYTs7U2IlaeDIdWIlEFF24KfqB+4ypIk2OiuZeBLE9RI0dUIS5KazB/UvacBYwmsVpJXemEyYmU0k3a5EYyZNuQk7X4fCDf0UMPdo3o1pGQKBgQCkwiO7JLzPPrGf35TrnQXbWlF+fym49bGDcX5dr1fUOJ22uaWNwpuXa2PnESSi7u3gGvWGKk7dkSwUThs7wH7szmjvwXZuc90w4rLNOtAfTsRgTGDkVwA8cm0jbahsLxpebEnCFf8vmWsCQUGPlHe9XiHttl/eL0b3ffHQvjDYoQKBgQCPwekTL5pJ2JUB4CYuUBgE4tKXUoJEPAS+i1+j/dVIhWH3N2LP1QIQkKcnIdh0z2BabKPVKL9P239oM8CGm9BG/EMDkg6LEouG4KJixQdKjKrrHlM9PHIudMGIFdqNzubEzO2gu1jKJDxUF7CjkQk5s3SGDodySzGZMOKSKpI2mQKBgQCDN4dDjc+98r1+iY/OUqGOgJE1AMvW3f7Bo8szi+mUQH8DPzml/F70qnfHWLTn+wiCmpX+g4WKGwuTJF22KeosaXvTry3GbAq222Xx46xLsuzdkRb95Hhwq+VV+QjRGHaUywO3StORMkuyYQx48JKow+abrssNZnPLuoezazX14QKBgGOPAsOabTjOa5oL464GpN21gqMI5wYml8p7vkAIp0pkdEvQtQGhtKvmK3Jb5dxzDPGkiTpd3/pjlHRwtZ3D/dZ2HMT9YEHe/H5bX0dhcIKlf9ggCaKfkHjJkHQ8gw44z8FRWhz67G58ebSVxq2kS9L6jl+hSKca0s05UYb/lYw2\"}', 9, 1, 1, '2025-04-02 22:32:46', '2025-04-03 19:51:26', NULL);

-- ----------------------------
-- Table structure for chatgpt_redemption
-- ----------------------------
DROP TABLE IF EXISTS `chatgpt_redemption`;
CREATE TABLE `chatgpt_redemption`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `userId` bigint(0) NOT NULL COMMENT '创建用户id',
  `key` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'key',
  `status` bigint(0) NOT NULL COMMENT '状态',
  `subTypeId` bigint(0) NOT NULL COMMENT '订阅类型',
  `redeemedTime` datetime(3) DEFAULT NULL COMMENT '兑换时间',
  `usedUserId` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '使用者',
  `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `limit` bigint(0) NOT NULL DEFAULT 20 COMMENT '额度',
  `per` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1h' COMMENT '限制',
  `isPlus` tinyint(1) DEFAULT 1 COMMENT 'PLUS',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_chatgpt_redemption_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 454 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatgpt_redemption
-- ----------------------------
INSERT INTO `chatgpt_redemption` VALUES (299, '2024-12-11 18:45:51.876', '2024-12-11 18:45:51.876', NULL, 1, '944f8b730dec47e788fbb11226e091cc', 1, 12, '2024-12-18 20:28:46.460', '999999999', '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (300, '2024-12-18 20:49:40.397', '2024-12-18 20:49:40.400', NULL, 1, '647af98994ba408ea9e2cfa69f2598cf', 1, 12, '2024-12-18 20:53:56.484', '999999999', '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (301, '2024-12-18 20:51:09.289', '2024-12-18 20:51:09.289', NULL, 1, 'cdd2ca03edd440e481dbf96aeb5d2699', 1, 12, '2024-12-18 20:52:46.636', '999999999', '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (302, '2024-12-18 20:54:25.786', '2024-12-18 20:54:25.786', NULL, 1, '112f78a201104113aa8df3a49c25479f', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (303, '2024-12-18 20:54:25.787', '2024-12-18 20:54:25.787', NULL, 1, '97ab266a0a4f44269733e2da317f83e1', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (304, '2024-12-18 20:54:25.788', '2024-12-18 20:54:25.788', NULL, 1, '494dcc8e937747559db968a112b79282', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (305, '2024-12-18 20:54:25.788', '2024-12-18 20:54:25.788', NULL, 1, 'fb6d3cbcaf1f43b29d379fb7ce7e8b7c', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (306, '2024-12-18 20:54:25.789', '2024-12-18 20:54:25.789', NULL, 1, '1438f8bc9f76493b9d913b93e124e198', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (307, '2024-12-18 20:54:25.789', '2024-12-18 20:54:25.789', NULL, 1, '8606762e4b554843be5224a4d2e52107', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (308, '2024-12-18 20:54:25.789', '2024-12-18 20:54:25.789', NULL, 1, '29075c53c7014a1db3d400c6fb7b9cd7', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (309, '2024-12-18 20:54:25.790', '2024-12-18 20:54:25.790', NULL, 1, 'cfcecf08be2e4bf6b374895d07beff86', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (310, '2024-12-18 20:54:25.790', '2024-12-18 20:54:25.790', NULL, 1, '056ddf49983e447c9a13f788ef37c5ab', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (311, '2024-12-18 20:54:25.790', '2024-12-18 20:54:25.790', NULL, 1, '7be1d909a55049debc01db67001bfef1', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (312, '2024-12-18 20:54:25.790', '2024-12-18 20:54:25.790', NULL, 1, 'bc2edb60ba5a4fc9a89a45f89976d3d9', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (313, '2024-12-18 20:54:25.791', '2024-12-18 20:54:25.791', NULL, 1, 'bf9ab13f1eed4298b991b0f434efe951', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (314, '2024-12-18 20:54:25.791', '2024-12-18 20:54:25.791', NULL, 1, '3612ba3832104e80be84c313be99e59c', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (315, '2024-12-18 20:54:25.791', '2024-12-18 20:54:25.791', NULL, 1, 'f835b61c391e4f9a9b90995b234e1128', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (316, '2024-12-18 20:54:25.792', '2024-12-18 20:54:25.792', NULL, 1, '25133c50b5204948a593878e01c0abe7', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (317, '2024-12-18 20:54:25.793', '2024-12-18 20:54:25.793', NULL, 1, 'd0a6b8c27f20479fa73312c5a91ba142', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (318, '2024-12-18 20:54:25.794', '2024-12-18 20:54:25.794', NULL, 1, '0bd0ee7cca54436db1a060f5e0a8922f', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (319, '2024-12-18 20:54:25.794', '2024-12-18 20:54:25.794', NULL, 1, '27baa7c4a8434b34a5d84047b8d33672', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (320, '2024-12-18 20:54:25.794', '2024-12-18 20:54:25.794', NULL, 1, '027d161437d9497d80caec8908e25620', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (321, '2024-12-18 20:54:25.795', '2024-12-18 20:54:25.795', NULL, 1, '33fc9d41cc4542e390df58922571b678', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (322, '2024-12-18 20:54:25.796', '2024-12-18 20:54:25.796', NULL, 1, '431bdb6f63b54c2c9ac1699a28a767b9', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (323, '2024-12-18 20:54:25.797', '2024-12-18 20:54:25.797', NULL, 1, '10800f0301874d3fac87866e8b12f83f', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (324, '2024-12-18 20:54:25.798', '2024-12-18 20:54:25.798', NULL, 1, 'cea82f911fcc43b6bff71ba79deed1ed', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (325, '2024-12-18 20:54:25.799', '2024-12-18 20:54:25.799', NULL, 1, '11d60cd30f424d1e90ecaedfbf0dfd80', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (326, '2024-12-18 20:54:25.800', '2024-12-18 20:54:25.800', NULL, 1, '355f15d1ad8a497f8614b80b1f3861d1', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (327, '2024-12-18 20:54:25.800', '2024-12-18 20:54:25.800', NULL, 1, '898f7095217448f0895b5dbc09c69ba4', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (328, '2024-12-18 20:54:25.801', '2024-12-18 20:54:25.801', NULL, 1, 'ec89cdd24ccd4f159fdae6e19b9cc1a7', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (329, '2024-12-18 20:54:25.801', '2024-12-18 20:54:25.801', NULL, 1, 'bfdfff80e1674942b3d9f2c8c177cf5b', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (330, '2024-12-18 20:54:25.801', '2024-12-18 20:54:25.801', NULL, 1, '5c56896da3cb4177a36d67f42f676b1c', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (331, '2024-12-18 20:54:25.801', '2024-12-18 20:54:25.801', NULL, 1, '7e2ac0c9ecfa4457a32f93b2c9245f5f', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (332, '2024-12-18 20:54:25.802', '2024-12-18 20:54:25.802', NULL, 1, 'f028d71cd0cd4611a6e1320cf18af91a', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (333, '2024-12-18 20:54:25.802', '2024-12-18 20:54:25.802', NULL, 1, 'b033ccf733e7465c854b49814852ffe8', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (334, '2024-12-18 20:54:25.802', '2024-12-18 20:54:25.802', NULL, 1, '50c313b8318649e4b705d666e819fb7a', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (335, '2024-12-18 20:54:25.802', '2024-12-18 20:54:25.802', NULL, 1, 'f7dd0156758e4da88b5a6eeac39d8840', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (336, '2024-12-18 20:54:25.803', '2024-12-18 20:54:25.803', NULL, 1, '4737f94651644408a09f4f7e9690bea1', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (337, '2024-12-18 20:54:25.803', '2024-12-18 20:54:25.803', NULL, 1, 'f17f5211f0264a23a3cfb9ec324b0897', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (338, '2024-12-18 20:54:25.804', '2024-12-18 20:54:25.804', NULL, 1, '4a0fbb249bf74430855215bfe828399f', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (339, '2024-12-18 20:54:25.804', '2024-12-18 20:54:25.804', NULL, 1, 'd737fb8ad22d4c1da67b4a6ad4e32506', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (340, '2024-12-18 20:54:25.805', '2024-12-18 20:54:25.805', NULL, 1, '5e124ba538aa438cb739a647d63b17f1', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (341, '2024-12-18 20:54:25.805', '2024-12-18 20:54:25.805', NULL, 1, '6527ae69a5b04e788404f783901c1a31', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (342, '2024-12-18 20:54:25.805', '2024-12-18 20:54:25.805', NULL, 1, '34dc4d708ff544179ea8e8df38fd201f', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (343, '2024-12-18 20:54:25.805', '2024-12-18 20:54:25.805', NULL, 1, '7c4a18297c2d41a8b1c8318ecded5e2c', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (344, '2024-12-18 20:54:25.805', '2024-12-18 20:54:25.805', NULL, 1, '902c82667953446fa9e6f8fb8faaa97f', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (345, '2024-12-18 20:54:25.806', '2024-12-18 20:54:25.806', NULL, 1, 'a615af3c0e57408eba6c8a3c0b7bd2d1', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (346, '2024-12-18 20:54:25.806', '2024-12-18 20:54:25.806', NULL, 1, 'd58cae1c695b4a50b832dc7eac18c4d8', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (347, '2024-12-18 20:54:25.806', '2024-12-18 20:54:25.806', NULL, 1, 'e5f7b84367b54754b0f771c43bf935fc', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (348, '2024-12-18 20:54:25.806', '2024-12-18 20:54:25.806', NULL, 1, '6954ef01f9474fb8aaebefe3b5c06f3e', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (349, '2024-12-18 20:54:25.807', '2024-12-18 20:54:25.807', NULL, 1, 'a648d012c7b24c819c11407c52f502ea', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (350, '2024-12-18 20:54:25.807', '2024-12-18 20:54:25.807', NULL, 1, 'c917c7eb939f47f689fe205d03b84caa', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (351, '2024-12-18 20:54:25.807', '2024-12-18 20:54:25.807', NULL, 1, 'fbf547acd81f4646ab2604b67a6339e2', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (352, '2024-12-18 20:54:25.807', '2024-12-18 20:54:25.807', NULL, 1, 'de389dbb59b24395889853c5414caf13', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (353, '2024-12-18 20:54:25.808', '2024-12-18 20:54:25.808', NULL, 1, '3c43ca3483b44e9ca9e7716b49b93303', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (354, '2024-12-18 20:54:25.808', '2024-12-18 20:54:25.808', NULL, 1, '6a925b776ad04d2fbde7055abaa2fb10', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (355, '2024-12-18 20:54:25.808', '2024-12-18 20:54:25.808', NULL, 1, 'ea8f2eb9580a4d46b90fe722a5c408af', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (356, '2024-12-18 20:54:25.809', '2024-12-18 20:54:25.809', NULL, 1, 'd31c03a5bb0b49a787b851149f222330', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (357, '2024-12-18 20:54:25.809', '2024-12-18 20:54:25.809', NULL, 1, 'b57009ecbc894a89a18cd8e4fc89c977', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (358, '2024-12-18 20:54:25.809', '2024-12-18 20:54:25.809', NULL, 1, '4ca087fb1f9a44b1996a69db2cd7626f', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (359, '2024-12-18 20:54:25.809', '2024-12-18 20:54:25.809', NULL, 1, 'cd5505cf3d004edb8aab36d04baecd87', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (360, '2024-12-18 20:54:25.809', '2024-12-18 20:54:25.809', NULL, 1, 'dba8efb611b44f24bc8e37bd76043340', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (361, '2024-12-18 20:54:25.810', '2024-12-18 20:54:25.810', NULL, 1, '76eda663847a4d0abdafc65fa539ed96', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (362, '2024-12-18 20:54:25.810', '2024-12-18 20:54:25.810', NULL, 1, 'af4ea96c1cab42b3a14881dcdc2dc3cc', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (363, '2024-12-18 20:54:25.810', '2024-12-18 20:54:25.810', NULL, 1, '611ce6c465584e8796ebf5a1a1a75042', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (364, '2024-12-18 20:54:25.810', '2024-12-18 20:54:25.810', NULL, 1, '5fc99caf868b415d9ee109aa790a65ef', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (365, '2024-12-18 20:54:25.810', '2024-12-18 20:54:25.810', NULL, 1, 'c062c8afbf7d453fb24a579d356c74d1', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (366, '2024-12-18 20:54:25.810', '2024-12-18 20:54:25.810', NULL, 1, '46c5c307b7ac427098f46998f03e5436', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (367, '2024-12-18 20:54:25.811', '2024-12-18 20:54:25.811', NULL, 1, '78c4b56b049744c8b2746ced79bf34e1', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (368, '2024-12-18 20:54:25.812', '2024-12-18 20:54:25.812', NULL, 1, 'f2397b6245214c4aadac64572b2ce2f2', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (369, '2024-12-18 20:54:25.813', '2024-12-18 20:54:25.813', NULL, 1, '95774607b700497faf357a649db79956', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (370, '2024-12-18 20:54:25.814', '2024-12-18 20:54:25.814', NULL, 1, 'f567c3d1c2b64f7188460f7e5a1d2643', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (371, '2024-12-18 20:54:25.814', '2024-12-18 20:54:25.814', NULL, 1, '4b5ea0105dc6404285c240d9f5448d62', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (372, '2024-12-18 20:54:25.815', '2024-12-18 20:54:25.815', NULL, 1, '81c12d9d27f34a84a1493e5171ebe287', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (373, '2024-12-18 20:54:25.815', '2024-12-18 20:54:25.815', NULL, 1, '9e386cfaeca54048a8e99e3120caf51d', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (374, '2024-12-18 20:54:25.816', '2024-12-18 20:54:25.816', NULL, 1, 'd12408e5911644bdb3e32ff641612861', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (375, '2024-12-18 20:54:25.816', '2024-12-18 20:54:25.816', NULL, 1, '95500924cc8f4997aca8b56c3badfd7b', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (376, '2024-12-18 20:54:25.817', '2024-12-18 20:54:25.817', NULL, 1, '5135b338d86c4e41b25ab20e8bcb5b53', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (377, '2024-12-18 20:54:25.817', '2024-12-18 20:54:25.817', NULL, 1, '86c2ac52698441258adae06d2df34458', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (378, '2024-12-18 20:54:25.817', '2024-12-18 20:54:25.817', NULL, 1, '42d1330543c64516b2b25946989078f5', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (379, '2024-12-18 20:54:25.818', '2024-12-18 20:54:25.818', NULL, 1, 'a8125ff3ecab418e8a8a556c8162ef77', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (380, '2024-12-18 20:54:25.818', '2024-12-18 20:54:25.818', NULL, 1, '066b605891684223a73f32e645a95ee2', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (381, '2024-12-18 20:54:25.819', '2024-12-18 20:54:25.819', NULL, 1, '0cf03682fa704bd79a20f66a4fb9abf0', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (382, '2024-12-18 20:54:25.819', '2024-12-18 20:54:25.819', NULL, 1, 'f53283f1dafd4bc9b2b181acc5dd58fe', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (383, '2024-12-18 20:54:25.819', '2024-12-18 20:54:25.819', NULL, 1, 'd62aa1ee5e544f9186dcc4f69bcf9028', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (384, '2024-12-18 20:54:25.820', '2024-12-18 20:54:25.820', NULL, 1, '4eb1720faaa845218d1633896b28bf82', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (385, '2024-12-18 20:54:25.820', '2024-12-18 20:54:25.820', NULL, 1, 'de662ac8fe384bd79b9aa7ca82221fbf', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (386, '2024-12-18 20:54:25.820', '2024-12-18 20:54:25.821', NULL, 1, 'e9302a3e39a746308dcd49427b0a2294', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (387, '2024-12-18 20:54:25.821', '2024-12-18 20:54:25.821', NULL, 1, '0dce3db4813d45d4a35c32d62b319da2', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (388, '2024-12-18 20:54:25.821', '2024-12-18 20:54:25.821', NULL, 1, 'ed6b6641006143eaa7333021008f81d8', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (389, '2024-12-18 20:54:25.821', '2024-12-18 20:54:25.821', NULL, 1, '4e5798f59c47496e83d2641353bcb346', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (390, '2024-12-18 20:54:25.821', '2024-12-18 20:54:25.821', NULL, 1, '257cdf1c5403475b82fba852970acbd0', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (391, '2024-12-18 20:54:25.822', '2024-12-18 20:54:25.822', NULL, 1, '442b0a8b39924a9f9d1b9a93ed37e87f', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (392, '2024-12-18 20:54:25.822', '2024-12-18 20:54:25.822', NULL, 1, '7072e6b62a254de0adb43b813ea1caab', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (393, '2024-12-18 20:54:25.822', '2024-12-18 20:54:25.822', NULL, 1, 'cfef60c6825b41fc867b89d8eab5f600', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (394, '2024-12-18 20:54:25.822', '2024-12-18 20:54:25.822', NULL, 1, 'fd92381822164de78b40c17a61a64a05', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (395, '2024-12-18 20:54:25.822', '2024-12-18 20:54:25.822', NULL, 1, '9d77d6b14f594c6f90b804f0237bf608', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (396, '2024-12-18 20:54:25.822', '2024-12-18 20:54:25.822', NULL, 1, '0bdb3052c9454553b7a00b3be9325add', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (397, '2024-12-18 20:54:25.823', '2024-12-18 20:54:25.823', NULL, 1, '04de73fbe7564249b32a6a9adca498a0', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (398, '2024-12-18 20:54:25.823', '2024-12-18 20:54:25.823', NULL, 1, '4da5499e98ea491ebcd34dce1c3c9645', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (399, '2024-12-18 20:54:25.823', '2024-12-18 20:54:25.823', NULL, 1, '54d24649c20541d1b74b8d0a635d29ff', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (400, '2024-12-18 20:54:25.823', '2024-12-18 20:54:25.823', NULL, 1, '2ed544c87ab8434b8d24b79e528e4cae', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (401, '2024-12-18 20:54:25.823', '2024-12-18 20:54:25.823', NULL, 1, '2e11d3e7fa1446aba13df05002af24dd', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (402, '2024-12-18 20:54:25.823', '2024-12-18 20:54:25.823', NULL, 1, '7756a880c31541bfb3dbcc477f1b70b0', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (403, '2024-12-18 20:54:25.824', '2024-12-18 20:54:25.824', NULL, 1, 'efc31308eceb456b8f2649f57d98b243', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (404, '2024-12-18 20:54:25.824', '2024-12-18 20:54:25.824', NULL, 1, 'a22b1c2987364b149f2747df77aaa728', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (405, '2024-12-18 20:54:25.824', '2024-12-18 20:54:25.824', NULL, 1, '0460f72e01a64431ac23ac2a7cd53922', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (406, '2024-12-18 20:54:25.824', '2024-12-18 20:54:25.824', NULL, 1, '384cc7fb78114c58818ee651a941e348', 0, 12, NULL, NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (407, '2024-12-18 20:54:25.825', '2024-12-18 20:54:25.825', NULL, 1, '827d6e9628c643a59fd30291278f3b08', 1, 12, '2024-12-18 21:07:18.975', '1000000025', '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (408, '2024-12-18 20:54:25.825', '2024-12-18 20:54:25.825', NULL, 1, 'aea070cd3bb94f55a63434da02562491', 1, 12, '2024-12-18 21:09:33.084', '1000000025', '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (409, '2024-12-18 20:54:25.825', '2024-12-18 20:54:25.825', NULL, 1, '368e627a107640fea07f14cb4dccc7b9', 1, 12, '2024-12-27 22:26:32.038', '999999999', '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (410, '2024-12-18 20:54:25.825', '2024-12-18 20:54:25.825', NULL, 1, 'e1e312754a13482cb8bbe3e9227ad261', 1, 12, '2024-12-30 22:44:00.062', '1000000025', '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (411, '2024-12-18 20:54:25.825', '2024-12-18 20:54:25.825', NULL, 1, 'f2b34a4aacef40028fcf9e8354762a2b', 1, 12, '2024-12-30 23:02:19.697', '1000000032', '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (412, '2024-12-18 20:54:25.826', '2024-12-18 20:54:25.826', NULL, 1, '8dba4d998d6a45998989ad5e9e777d80', 1, 12, '2024-12-18 20:55:39.313', '999999999', '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (413, '2024-12-29 12:36:48.000', '2024-12-29 12:36:48.000', NULL, 1, 'e6618986b8144befbcf9a985165f7702', 1, 22, '2024-12-29 17:32:44.392', '999999999', '1天', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (414, '2024-12-29 19:05:16.000', '2024-12-29 19:05:16.000', NULL, 1, 'b750adf6462f4d999ba583fa71b0a928', 0, 12, '2024-12-29 19:06:11.734', NULL, '12312', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (415, '2025-01-10 21:28:50.619', '2025-01-10 21:28:50.619', NULL, 1, '4234a4a82a534c5aad830222f84864d4', 1, 13, '2025-01-10 21:29:33.388', '1000000022', '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (416, '2025-01-12 20:33:08.230', '2025-01-12 20:33:08.232', NULL, 1, 'ed1ce7bb2ad8423d89fcae74f946287d', 1, 13, '2025-01-12 20:36:38.040', '1000000044', '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (418, '2025-01-26 12:36:51.000', '2025-01-26 12:36:51.000', NULL, 1, '09d68f4b74a447acb847d995d7b64099', 1, 13, '2025-01-26 12:44:19.411', '1000000044', '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (419, '2025-02-07 16:27:30.000', '2025-02-07 16:27:30.000', NULL, 1, 'f783eb2f17804a33b3ddd937d384997a', 1, 23, '2025-02-07 16:28:33.372', '1000000058', '123', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (430, '2025-04-09 22:31:47.932', '2025-04-09 22:31:47.932', NULL, 1, '773fbc9be61b4f88b9197213b4eeeb45', 0, 13, NULL, NULL, '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (431, '2025-04-09 22:31:47.935', '2025-04-09 22:31:47.935', NULL, 1, 'a39322e7846342679fadced385e049f9', 0, 13, NULL, NULL, '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (432, '2025-04-09 22:31:47.936', '2025-04-09 22:31:47.936', NULL, 1, '69c74b81f02d488784af21711736be8c', 0, 13, NULL, NULL, '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (433, '2025-04-09 22:31:47.936', '2025-04-09 22:31:47.936', NULL, 1, '70d7a807525a4996ac747daa93520ca4', 0, 13, NULL, NULL, '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (434, '2025-04-09 22:31:47.937', '2025-04-09 22:31:47.937', NULL, 1, '4fb43fadf4c0426884b30006eabd2c5e', 0, 13, NULL, NULL, '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (435, '2025-04-09 22:31:47.938', '2025-04-09 22:31:47.938', NULL, 1, '0bd7443219ad4de79171d8f1ff57587e', 0, 13, NULL, NULL, '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (436, '2025-04-09 22:31:47.938', '2025-04-09 22:31:47.939', NULL, 1, '8a6ac857dfe947b3837f6746698de72c', 0, 13, NULL, NULL, '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (437, '2025-04-09 22:31:47.940', '2025-04-09 22:31:47.940', NULL, 1, 'd883524ced8943ab9f89efe8bdd045b6', 0, 13, NULL, NULL, '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (438, '2025-04-09 22:31:47.940', '2025-04-09 22:31:47.940', NULL, 1, '746b724f3ba3415f8a357a407849819f', 0, 13, NULL, NULL, '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (439, '2025-04-09 22:31:47.942', '2025-04-09 22:31:47.942', NULL, 1, '39fca7643a7e4bd0b7c15fa5743a8696', 0, 13, NULL, NULL, '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 20, '1h', 1);
INSERT INTO `chatgpt_redemption` VALUES (440, '2025-04-09 22:31:57.288', '2025-04-09 22:31:57.288', NULL, 1, '7c0aaebda36f4ab28ea8a83de9504c55', 0, 31, NULL, NULL, '1天gpt', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (441, '2025-04-18 17:24:46.908', '2025-04-18 17:24:46.908', NULL, 1, 'fffb9e654ab540f98f99175728d0d61a', 0, 31, NULL, NULL, '1天gpt', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (442, '2025-04-18 17:24:46.909', '2025-04-18 17:24:46.909', NULL, 1, '9b893a7a461b4eb79f9dd16cc7a47c14', 0, 31, NULL, NULL, '1天gpt', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (443, '2025-04-18 17:24:46.914', '2025-04-18 17:24:46.914', NULL, 1, '014f1c1bd8164014bc6e21e644b2df05', 0, 31, NULL, NULL, '1天gpt', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (444, '2025-04-18 17:24:46.916', '2025-04-18 17:24:46.916', NULL, 1, '881089fa17c54c4ab90c7cdc4c957f71', 0, 31, NULL, NULL, '1天gpt', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (445, '2025-04-18 17:24:46.917', '2025-04-18 17:24:46.917', NULL, 1, 'cf84444a0db1487cba918657fdc9777c', 0, 31, NULL, NULL, '1天gpt', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (446, '2025-04-18 17:24:46.918', '2025-04-18 17:24:46.919', NULL, 1, '94d09115b4a44b939bbe2ea7a8fb0f83', 0, 31, NULL, NULL, '1天gpt', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (447, '2025-04-18 17:24:46.920', '2025-04-18 17:24:46.920', NULL, 1, '123208418b46411eaae9bfbfc053e34e', 0, 31, NULL, NULL, '1天gpt', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (448, '2025-04-18 17:24:46.921', '2025-04-18 17:24:46.921', NULL, 1, '274df0dd81124937b342e2f102380147', 0, 31, NULL, NULL, '1天gpt', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (449, '2025-04-18 17:24:46.921', '2025-04-18 17:24:46.921', NULL, 1, '710b22c975904d84b3c3ed3bd2614a5d', 0, 31, NULL, NULL, '1天gpt', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (450, '2025-04-18 17:24:46.922', '2025-04-18 17:24:46.922', NULL, 1, '68435f3d0ede4f35b55be111115a3bad', 0, 31, NULL, NULL, '1天gpt', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (451, '2025-04-26 00:01:37.000', '2025-04-26 00:01:37.000', NULL, 1, 'd639f14216fc4b6abdcfcd619b778094', 1, 39, '2025-04-26 01:56:09.912', '999999999', '绘画', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (452, '2025-04-26 01:48:12.000', '2025-04-26 01:48:12.000', NULL, 1, '720e4970891c4334b06feeee4a81ade3', 1, 39, '2025-04-26 02:00:21.672', '999999999', '绘画', 20, '1h', 0);
INSERT INTO `chatgpt_redemption` VALUES (453, '2025-05-03 21:56:00.679', '2025-05-03 21:56:00.679', NULL, 1, '0834c8bcede048c088904af01cca2fac', 0, 31, NULL, NULL, '1天gpt', 20, '1h', 0);

-- ----------------------------
-- Table structure for chatgpt_sensitive_word
-- ----------------------------
DROP TABLE IF EXISTS `chatgpt_sensitive_word`;
CREATE TABLE `chatgpt_sensitive_word`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `word` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '敏感词',
  `status` bigint(0) NOT NULL DEFAULT 1 COMMENT '状态',
  `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatgpt_sensitive_word
-- ----------------------------
INSERT INTO `chatgpt_sensitive_word` VALUES (7, '2024-10-10 11:52:08.178', '2024-10-10 11:52:08.178', '12312', 1, '批量导入');
INSERT INTO `chatgpt_sensitive_word` VALUES (8, '2024-10-10 11:52:08.180', '2024-10-10 11:52:08.180', '1231232', 1, '批量导入');
INSERT INTO `chatgpt_sensitive_word` VALUES (9, '2024-10-10 11:52:42.000', '2024-10-10 11:52:42.000', '你妈', 1, '批量导入');
INSERT INTO `chatgpt_sensitive_word` VALUES (10, '2024-10-10 11:52:42.000', '2024-10-10 11:52:42.000', '草', 1, '批量导入');
INSERT INTO `chatgpt_sensitive_word` VALUES (11, '2025-04-08 15:57:53.709', '2025-04-08 15:57:53.709', '1231231232', 1, '');
INSERT INTO `chatgpt_sensitive_word` VALUES (12, '2025-04-08 15:58:11.360', '2025-04-08 15:58:11.360', '1aaaa', 1, 'aaa');
INSERT INTO `chatgpt_sensitive_word` VALUES (13, '2025-04-08 15:59:51.821', '2025-04-08 15:59:51.821', '123', 1, '');
INSERT INTO `chatgpt_sensitive_word` VALUES (14, '2025-04-08 16:04:03.117', '2025-04-08 16:04:03.117', '1', 1, '批量导入');
INSERT INTO `chatgpt_sensitive_word` VALUES (15, '2025-04-08 16:04:03.121', '2025-04-08 16:04:03.122', '2', 1, '批量导入');
INSERT INTO `chatgpt_sensitive_word` VALUES (16, '2025-04-08 16:04:03.122', '2025-04-08 16:04:03.122', '3', 1, '批量导入');
INSERT INTO `chatgpt_sensitive_word` VALUES (17, '2025-04-08 16:04:03.124', '2025-04-08 16:04:03.124', '4', 1, '批量导入');
INSERT INTO `chatgpt_sensitive_word` VALUES (20, '2025-04-09 22:43:11.498', '2025-04-09 22:43:11.498', '123', 1, '');
INSERT INTO `chatgpt_sensitive_word` VALUES (21, '2025-04-09 22:43:17.950', '2025-04-09 22:43:17.950', '1', 1, '批量导入');
INSERT INTO `chatgpt_sensitive_word` VALUES (22, '2025-04-09 22:43:17.951', '2025-04-09 22:43:17.951', '2', 1, '批量导入');
INSERT INTO `chatgpt_sensitive_word` VALUES (23, '2025-04-09 22:43:17.952', '2025-04-09 22:43:17.952', '34', 1, '批量导入');

-- ----------------------------
-- Table structure for chatgpt_session
-- ----------------------------
DROP TABLE IF EXISTS `chatgpt_session`;
CREATE TABLE `chatgpt_session`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `email` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `password` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态',
  `isPlus` tinyint(1) DEFAULT 0 COMMENT 'PLUS',
  `carID` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '展示ID',
  `officialSession` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '官方session',
  `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `sort` bigint(0) DEFAULT 0 COMMENT '排序',
  `count` bigint(0) DEFAULT 0 COMMENT '统计',
  `user_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户id',
  `exclusive_expire_time` datetime(3) DEFAULT NULL COMMENT '独享到期时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_chatgpt_session_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 55 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatgpt_session
-- ----------------------------
INSERT INTO `chatgpt_session` VALUES (3, '2024-09-01 13:45:59.000', '2024-12-17 13:30:00.792', NULL, '<EMAIL>', 'Mountaineers1*', 1, 3, 'xPLpFYfb', '{\"accessToken\":\"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik1UaEVOVUpHTkVNMVFURTRNMEZCTWpkQ05UZzVNRFUxUlRVd1FVSkRNRU13UmtGRVFrRXpSZyJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ToIPSFDQNNd-Q8mb5bG6rzMP1dINz3suhFiK2PcOv4MgSeBHG7u_KK3CnAz7MPBINEU4wtoRkrKfoWR40yxtW_l33WrZ-CAp4kUMhBI3a1N27ew0dCfv_HFJfbfAbq6HUhepASxITNDIMpd3NdUd-JVZypOxNHaLD_DIQ3l0ydiXzdR_VJXwCf63faWdVI7rG-xNZpvYUVcoKYCw7vS-O7CH6udgvem-q6jBclnYP7uQIQiYu1d-lX1LJrhW9ZS3GqYWwM0BZy2MU2BSJFyJzJ_oEBE1G3YVzompy-dFoKDp1TTgEUu3ii967DMd7DHDehDD72McQy2F9saRU9y2BA\",\"access_token\":\"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik1UaEVOVUpHTkVNMVFURTRNMEZCTWpkQ05UZzVNRFUxUlRVd1FVSkRNRU13UmtGRVFrRXpSZyJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ToIPSFDQNNd-Q8mb5bG6rzMP1dINz3suhFiK2PcOv4MgSeBHG7u_KK3CnAz7MPBINEU4wtoRkrKfoWR40yxtW_l33WrZ-CAp4kUMhBI3a1N27ew0dCfv_HFJfbfAbq6HUhepASxITNDIMpd3NdUd-JVZypOxNHaLD_DIQ3l0ydiXzdR_VJXwCf63faWdVI7rG-xNZpvYUVcoKYCw7vS-O7CH6udgvem-q6jBclnYP7uQIQiYu1d-lX1LJrhW9ZS3GqYWwM0BZy2MU2BSJFyJzJ_oEBE1G3YVzompy-dFoKDp1TTgEUu3ii967DMd7DHDehDD72McQy2F9saRU9y2BA\",\"accountCheckInfo\":{\"expires_at\":\"\",\"is_deactivated\":false,\"plan_type\":\"free\",\"team_ids\":[]},\"accounts_info\":{\"account_ordering\":[\"bd72c455-7a77-4bbe-9ef6-9b4ebc722c44\"],\"accounts\":{\"bd72c455-7a77-4bbe-9ef6-9b4ebc722c44\":{\"account\":{\"account_id\":\"bd72c455-7a77-4bbe-9ef6-9b4ebc722c44\",\"account_residency_region\":\"no_constraint\",\"account_user_id\":\"user-8Ah5AW6HjCicnHfE4goteJuy__bd72c455-7a77-4bbe-9ef6-9b4ebc722c44\",\"account_user_role\":\"account-owner\",\"has_previously_paid_subscription\":false,\"is_deactivated\":false,\"is_most_recent_expired_subscription_gratis\":false,\"name\":null,\"organization_id\":null,\"plan_type\":\"free\",\"processor\":{\"a001\":{\"has_customer_object\":false},\"b001\":{\"has_transaction_history\":false},\"c001\":{\"has_transaction_history\":false}},\"profile_picture_id\":null,\"profile_picture_url\":null,\"promo_data\":{},\"reseller_hosted_account\":false,\"reseller_id\":null,\"structure\":\"personal\"},\"can_access_with_session\":true,\"can_modify_improve_model_setting\":true,\"entitlement\":{\"billing_period\":null,\"expires_at\":null,\"has_active_subscription\":false,\"is_active_subscription_gratis\":false,\"scheduled_plan_change\":null,\"subscription_id\":null,\"subscription_plan\":\"chatgptfreeplan\"},\"features\":[\"bizmo_settings\",\"breeze_available\",\"canvas\",\"canvas_code_execution\",\"canvas_opt_in\",\"chat_preferences_available\",\"chatgpt_ios_attest\",\"d3_controls\",\"d3_editor_gpts\",\"gizmo_canvas_toggle\",\"gizmo_interact_unpaid\",\"gizmo_reviews\",\"gizmo_support_emails\",\"mfa\",\"model_ab_use_v2\",\"new_plugin_oauth_endpoint\",\"no_auth_training_enabled_by_default\",\"paragen_mainline_alternative\",\"privacy_policy_nov_2023\",\"sentinel_enabled_for_subscription\",\"share_multimodal_links\",\"shareable_links\",\"starter_prompts\",\"sunshine_available\",\"user_settings_announcements\",\"voice_advanced_ga\"],\"is_eligible_for_yearly_plus_subscription\":false,\"is_permitted_to_improve_model\":true,\"last_active_subscription\":{\"purchase_origin_platform\":\"chatgpt_not_purchased\",\"subscription_id\":null,\"will_renew\":false},\"rate_limits\":[],\"sso_connection_name\":null},\"default\":{\"account\":{\"account_id\":\"bd72c455-7a77-4bbe-9ef6-9b4ebc722c44\",\"account_residency_region\":\"no_constraint\",\"account_user_id\":\"user-8Ah5AW6HjCicnHfE4goteJuy__bd72c455-7a77-4bbe-9ef6-9b4ebc722c44\",\"account_user_role\":\"account-owner\",\"has_previously_paid_subscription\":false,\"is_deactivated\":false,\"is_most_recent_expired_subscription_gratis\":false,\"name\":null,\"organization_id\":null,\"plan_type\":\"free\",\"processor\":{\"a001\":{\"has_customer_object\":false},\"b001\":{\"has_transaction_history\":false},\"c001\":{\"has_transaction_history\":false}},\"profile_picture_id\":null,\"profile_picture_url\":null,\"promo_data\":{},\"reseller_hosted_account\":false,\"reseller_id\":null,\"structure\":\"personal\"},\"can_access_with_session\":true,\"can_modify_improve_model_setting\":true,\"entitlement\":{\"billing_period\":null,\"expires_at\":null,\"has_active_subscription\":false,\"is_active_subscription_gratis\":false,\"scheduled_plan_change\":null,\"subscription_id\":null,\"subscription_plan\":\"chatgptfreeplan\"},\"features\":[\"bizmo_settings\",\"breeze_available\",\"canvas\",\"canvas_code_execution\",\"canvas_opt_in\",\"chat_preferences_available\",\"chatgpt_ios_attest\",\"d3_controls\",\"d3_editor_gpts\",\"gizmo_canvas_toggle\",\"gizmo_interact_unpaid\",\"gizmo_reviews\",\"gizmo_support_emails\",\"mfa\",\"model_ab_use_v2\",\"new_plugin_oauth_endpoint\",\"no_auth_training_enabled_by_default\",\"paragen_mainline_alternative\",\"privacy_policy_nov_2023\",\"sentinel_enabled_for_subscription\",\"share_multimodal_links\",\"shareable_links\",\"starter_prompts\",\"sunshine_available\",\"user_settings_announcements\",\"voice_advanced_ga\"],\"is_eligible_for_yearly_plus_subscription\":false,\"is_permitted_to_improve_model\":true,\"last_active_subscription\":{\"purchase_origin_platform\":\"chatgpt_not_purchased\",\"subscription_id\":null,\"will_renew\":false},\"rate_limits\":[],\"sso_connection_name\":null}}},\"expires_in\":864000,\"id_token\":\"****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"models\":[{\"capabilities\":{},\"description\":\"Our fastest model, great for most everyday tasks.\",\"max_tokens\":8191,\"product_features\":{},\"slug\":\"text-davinci-002-render-sha\",\"tags\":[\"gpt3.5\"],\"title\":\"Default (GPT-3.5)\"},{\"capabilities\":{},\"description\":\"Our most capable model, great for tasks that require creativity and advanced reasoning.\",\"max_tokens\":98304,\"product_features\":{},\"slug\":\"o1-mini\",\"tags\":[\"o1\"],\"title\":\"o1-mini\"},{\"capabilities\":{},\"description\":\"Browsing, Advanced Data Analysis, and DALL·E are now built into GPT-4\",\"enabled_tools\":[\"tools\",\"tools2\",\"dalle_3\"],\"max_tokens\":8191,\"product_features\":{\"attachments\":{\"accepted_mime_types\":[\"application/x-scala\",\"text/x-r\",\"text/x-objectivec++\",\"text/calendar\",\"text/x-jade\",\"text/x-lisp\",\"application/x-powershell\",\"application/javascript\",\"text/x-kotlin\",\"text/x-astro\",\"text/x-go\",\"text/x-sh\",\"application/x-sql\",\"text/plain\",\"application/vnd.oasis.opendocument.text\",\"text/x-typescript\",\"text/x-objectivec\",\"text/x-ejs\",\"application/x-rust\",\"text/x-elixir\",\"text/jsx\",\"text/x-php\",\"application/vnd.apple.pages\",\"text/x-python\",\"text/x-c\",\"text/x-ruby\",\"text/x-tex\",\"text/x-shellscript\",\"text/javascript\",\"text/x-scala\",\"text/x-c++\",\"text/x-erlang\",\"text/x-script.python\",\"text/x-clojure\",\"text/x-csharp\",\"application/json\",\"message/rfc822\",\"text/x-perl\",\"text/x-erb\",\"application/x-yaml\",\"text/x-java\",\"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\"application/rtf\",\"application/vnd.ms-powerpoint\",\"text/x-vcard\",\"text/tsx\",\"text/x-makefile\",\"text/x-rst\",\"text/x-groovy\",\"text/x-liquid\",\"text/x-twig\",\"text/x-asm\",\"text/markdown\",\"text/x-tmpl\",\"text/x-handlebars\",\"text/x-rust\",\"text/xml\",\"text/rtf\",\"application/pdf\",\"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\"text/vbscript\",\"text/x-haskell\",\"text/x-dart\",\"text/css\",\"text/x-swift\",\"application/toml\",\"text/html\",\"text/x-mustache\",\"application/msword\",\"application/vnd.apple.keynote\",\"text/x-jinja2\",\"text/x-pug\",\"text/x-diff\",\"text/x-julia\",\"text/x-lua\"],\"can_accept_all_mime_types\":true,\"image_mime_types\":[\"image/jpeg\",\"image/png\",\"image/gif\",\"image/webp\"],\"type\":\"retrieval\"}},\"slug\":\"gpt-4o-mini\",\"tags\":[\"gpt4\",\"gpt4o\",\"gpt3.5\"],\"title\":\"GPT-4o mini\"},{\"capabilities\":{},\"description\":\"Newest and most advanced model\",\"enabled_tools\":[\"tools\",\"tools2\",\"dalle_3\",\"canvas\"],\"max_tokens\":8192,\"product_features\":{\"attachments\":{\"accepted_mime_types\":[\"application/x-scala\",\"text/x-r\",\"text/x-objectivec++\",\"text/calendar\",\"text/x-jade\",\"text/x-lisp\",\"application/x-powershell\",\"application/javascript\",\"text/x-kotlin\",\"text/x-astro\",\"text/x-go\",\"text/x-sh\",\"application/x-sql\",\"text/plain\",\"application/vnd.oasis.opendocument.text\",\"text/x-typescript\",\"text/x-objectivec\",\"text/x-ejs\",\"application/x-rust\",\"text/x-elixir\",\"text/jsx\",\"text/x-php\",\"application/vnd.apple.pages\",\"text/x-python\",\"text/x-c\",\"text/x-ruby\",\"text/x-tex\",\"text/x-shellscript\",\"text/javascript\",\"text/x-scala\",\"text/x-c++\",\"text/x-erlang\",\"text/x-script.python\",\"text/x-clojure\",\"text/x-csharp\",\"application/json\",\"message/rfc822\",\"text/x-perl\",\"text/x-erb\",\"application/x-yaml\",\"text/x-java\",\"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\"application/rtf\",\"application/vnd.ms-powerpoint\",\"text/x-vcard\",\"text/tsx\",\"text/x-makefile\",\"text/x-rst\",\"text/x-groovy\",\"text/x-liquid\",\"text/x-twig\",\"text/x-asm\",\"text/markdown\",\"text/x-tmpl\",\"text/x-handlebars\",\"text/x-rust\",\"text/xml\",\"text/rtf\",\"application/pdf\",\"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\"text/vbscript\",\"text/x-haskell\",\"text/x-dart\",\"text/css\",\"text/x-swift\",\"application/toml\",\"text/html\",\"text/x-mustache\",\"application/msword\",\"application/vnd.apple.keynote\",\"text/x-jinja2\",\"text/x-pug\",\"text/x-diff\",\"text/x-julia\",\"text/x-lua\"],\"can_accept_all_mime_types\":true,\"image_mime_types\":[\"image/jpeg\",\"image/png\",\"image/gif\",\"image/webp\"],\"type\":\"retrieval\"}},\"slug\":\"gpt-4o\",\"tags\":[\"gpt4\",\"gpt4o\"],\"title\":\"GPT-4o\"},{\"capabilities\":{},\"description\":\"Use the right model for my request\",\"enabled_tools\":[\"tools\",\"tools2\",\"dalle_3\",\"canvas\"],\"max_tokens\":8192,\"product_features\":{\"attachments\":{\"accepted_mime_types\":[\"application/x-scala\",\"text/x-r\",\"text/x-objectivec++\",\"text/calendar\",\"text/x-jade\",\"text/x-lisp\",\"application/x-powershell\",\"application/javascript\",\"text/x-kotlin\",\"text/x-astro\",\"text/x-go\",\"text/x-sh\",\"application/x-sql\",\"text/plain\",\"application/vnd.oasis.opendocument.text\",\"text/x-typescript\",\"text/x-objectivec\",\"text/x-ejs\",\"application/x-rust\",\"text/x-elixir\",\"text/jsx\",\"text/x-php\",\"application/vnd.apple.pages\",\"text/x-python\",\"text/x-c\",\"text/x-ruby\",\"text/x-tex\",\"text/x-shellscript\",\"text/javascript\",\"text/x-scala\",\"text/x-c++\",\"text/x-erlang\",\"text/x-script.python\",\"text/x-clojure\",\"text/x-csharp\",\"application/json\",\"message/rfc822\",\"text/x-perl\",\"text/x-erb\",\"application/x-yaml\",\"text/x-java\",\"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\"application/rtf\",\"application/vnd.ms-powerpoint\",\"text/x-vcard\",\"text/tsx\",\"text/x-makefile\",\"text/x-rst\",\"text/x-groovy\",\"text/x-liquid\",\"text/x-twig\",\"text/x-asm\",\"text/markdown\",\"text/x-tmpl\",\"text/x-handlebars\",\"text/x-rust\",\"text/xml\",\"text/rtf\",\"application/pdf\",\"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\"text/vbscript\",\"text/x-haskell\",\"text/x-dart\",\"text/css\",\"text/x-swift\",\"application/toml\",\"text/html\",\"text/x-mustache\",\"application/msword\",\"application/vnd.apple.keynote\",\"text/x-jinja2\",\"text/x-pug\",\"text/x-diff\",\"text/x-julia\",\"text/x-lua\"],\"can_accept_all_mime_types\":true,\"image_mime_types\":[\"image/jpeg\",\"image/png\",\"image/gif\",\"image/webp\"],\"type\":\"retrieval\"}},\"slug\":\"auto\",\"tags\":[\"gpt4\",\"gpt4o\"],\"title\":\"Auto\"}],\"refresh_token\":\"-O4SP32l-46VpcBHkSWmufMTg-RCny-eqQccaRsG8GaUO\",\"scope\":\"openid profile email model.read model.request organization.read organization.write offline_access\",\"token_type\":\"Bearer\"}', '翻车|不可用', NULL, 0, NULL, NULL);
INSERT INTO `chatgpt_session` VALUES (9, '2024-09-11 23:55:56.249', '2024-12-17 13:30:00.798', NULL, '<EMAIL>', '2zr$8@Kv8$@$', 1, 2, 'M7UJ5zyD', 'Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379', '翻车|不可用', NULL, 0, NULL, NULL);
INSERT INTO `chatgpt_session` VALUES (10, '2024-09-11 23:55:56.262', '2024-12-17 13:30:00.802', NULL, '<EMAIL>', '$H3U3kFDsK#S', 1, 2, 'wD7s1OTH', '401 Unauthorized: \"{\"detail\":\"接入点ys.xyhelper-agent.com目前设置白名单为:[\\u0022173.***.***.54\\u0022,\\u0022194.***.***.51\\u0022,\\u0022173.***.***.54\\u0022,\\u0022199.***.***.134\\u0022],当前IP为:*************,2604:9cc0:0:47b3::1, *************】,不在白名单内\"}\"', '翻车|不可用', NULL, 0, NULL, NULL);
INSERT INTO `chatgpt_session` VALUES (11, '2024-09-11 23:55:56.263', '2024-12-17 13:30:00.806', NULL, '<EMAIL>', '5@g@Exv#7TuJ', 1, 2, 'M77LLv3F', '401 Unauthorized: \"{\"detail\":\"接入点ys.xyhelper-agent.com目前设置白名单为:[\\u0022173.***.***.54\\u0022,\\u0022194.***.***.51\\u0022,\\u0022173.***.***.54\\u0022,\\u0022199.***.***.134\\u0022],当前IP为:*************,2604:9cc0:0:47b3::1, *************】,不在白名单内\"}\"', '翻车|不可用', NULL, 0, NULL, NULL);
INSERT INTO `chatgpt_session` VALUES (12, '2024-09-11 23:55:56.263', '2024-12-17 13:30:00.811', NULL, '<EMAIL>', 'uED#J9a##t@4', 1, 2, 'rI3strFp', 'Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379', '翻车|不可用', NULL, 0, NULL, NULL);
INSERT INTO `chatgpt_session` VALUES (28, '2024-09-11 23:55:56.271', '2024-12-17 13:30:00.816', NULL, '<EMAIL>', '2$W2e$r@R@82', 1, 1, 'ELrE5dUW', 'Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379', '翻车|不可用', NULL, 0, NULL, NULL);
INSERT INTO `chatgpt_session` VALUES (29, '2024-09-11 23:55:56.271', '2024-12-17 13:30:00.819', NULL, '<EMAIL>', '$$@yE#nT4fE2', 1, 1, 'wXm5vD3o', 'Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379', '翻车|不可用', NULL, 0, NULL, NULL);
INSERT INTO `chatgpt_session` VALUES (30, '2024-09-11 23:55:56.271', '2024-12-17 13:30:00.825', NULL, '<EMAIL>', 'mk##M#8e$VrV', 1, 1, 'glv389lp', '401 Unauthorized: \"{\"detail\":\"接入点ys.xyhelper-agent.com目前设置白名单为:[\\u0022173.***.***.54\\u0022,\\u0022194.***.***.51\\u0022,\\u0022173.***.***.54\\u0022,\\u0022199.***.***.134\\u0022],当前IP为:*************,2604:9cc0:0:47b3::1, *************】,不在白名单内\"}\"', '翻车|不可用', NULL, 0, NULL, NULL);
INSERT INTO `chatgpt_session` VALUES (31, '2024-09-11 23:55:56.000', '2024-12-17 13:30:00.829', NULL, '<EMAIL>', 'j42HV@p3w74Q', 1, 1, 'mO8zX5K5', '401 Unauthorized: \"{\"detail\":\"接入点ys.xyhelper-agent.com目前设置白名单为:[\\u0022173.***.***.54\\u0022,\\u0022194.***.***.51\\u0022,\\u0022173.***.***.54\\u0022,\\u0022199.***.***.134\\u0022],当前IP为:*************,2604:9cc0:0:47b3::1, **************】,不在白名单内\"}\"', '翻车|不可用', NULL, 0, NULL, NULL);
INSERT INTO `chatgpt_session` VALUES (32, '2024-09-11 23:55:56.273', '2024-12-17 13:30:00.832', NULL, '<EMAIL>', '7r5#e@N$6$eU', 1, 0, 'tjJt9LMm', 'Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379', '翻车|不可用', NULL, 0, NULL, NULL);
INSERT INTO `chatgpt_session` VALUES (33, '2024-09-11 23:55:56.274', '2024-12-17 13:30:00.836', NULL, '<EMAIL>', 'U5n#dAu@63@6', 1, 0, 'yNhTBlbv', '401 Unauthorized: \"{\"detail\":\"接入点ys.xyhelper-agent.com目前设置白名单为:[\\u0022173.***.***.54\\u0022,\\u0022194.***.***.51\\u0022,\\u0022173.***.***.54\\u0022,\\u0022199.***.***.134\\u0022],当前IP为:*************,2604:9cc0:0:47b3::1, *************】,不在白名单内\"}\"', '翻车|不可用', NULL, 0, NULL, NULL);
INSERT INTO `chatgpt_session` VALUES (36, '2024-09-25 14:28:50.376', '2024-12-17 13:30:00.840', NULL, '<EMAIL>', '52ejN63YjkNg', 1, 0, '6q7XQHlu', '401 Unauthorized: \"{\"detail\":\"接入点666.xyhelper-gateway.com目前设置白名单为:[\\u0022194.***.***.51\\u0022,\\u0022199.***.***.134\\u0022,\\u0022199.***.**.210\\u0022],当前IP为:112.**.***.236】,不在白名单内\"}\"', '翻车|不可用', NULL, 0, NULL, NULL);
INSERT INTO `chatgpt_session` VALUES (37, '2024-09-25 14:28:50.391', '2024-12-17 13:30:00.844', NULL, '<EMAIL>', '2pLe2IEjvE8H1', 1, 3, 'Lj5JHwNX', '401 Unauthorized: \"{\"detail\":\"接入点666.xyhelper-gateway.com目前设置白名单为:[\\u0022194.***.***.51\\u0022,\\u0022199.***.***.134\\u0022,\\u0022199.***.**.210\\u0022],当前IP为:112.**.***.236】,不在白名单内\"}\"', '翻车|不可用', NULL, 0, NULL, NULL);

-- ----------------------------
-- Table structure for chatgpt_subtype
-- ----------------------------
DROP TABLE IF EXISTS `chatgpt_subtype`;
CREATE TABLE `chatgpt_subtype`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `name` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `validDays` bigint(0) NOT NULL COMMENT '有效天数',
  `money` double NOT NULL COMMENT '订阅金额',
  `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `limit` bigint(0) NOT NULL DEFAULT 20 COMMENT '额度',
  `per` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1h' COMMENT '限制',
  `isPlus` tinyint(1) DEFAULT 1 COMMENT 'PLUS',
  `isNotValued` tinyint(1) DEFAULT 0 COMMENT '不计价值',
  `subType` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'chatgpt' COMMENT '订阅类型，chatgpt或者claude',
  `sort` bigint(0) DEFAULT 0 COMMENT '排序',
  `claudeLimit` bigint(0) DEFAULT NULL,
  `isPro` tinyint(1) DEFAULT NULL COMMENT 'pro',
  `claudePer` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `modelLimits` json COMMENT '模型限制',
  `model_limits` json COMMENT '模型限制',
  `exclusive` tinyint(1) DEFAULT 0 COMMENT '是否独显：0-否，1-是',
  `exclusiveType` tinyint(1) DEFAULT 0 COMMENT '是否独显：0-free，1-plus,2-team,3-pro',
  `isHotSale` tinyint(1) DEFAULT 0 COMMENT '显示热卖标志：0-否，1-是',
  `isSuper` tinyint(1) DEFAULT NULL COMMENT 'grok super标志',
  `draw_quota` bigint(0) NOT NULL DEFAULT 0 COMMENT '绘画额度',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_chatgpt_subtype_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatgpt_subtype
-- ----------------------------
INSERT INTO `chatgpt_subtype` VALUES (13, '2024-10-29 11:31:52.000', '2025-05-03 17:46:23.640', NULL, '这是一个很长很长很长的套餐名这是一个很长很长很长的套餐名', 30, 0.1, '官方同款功能,支持免梯直登,支持实时语音,支持对话隔离,支持会话漫游,支持插件功能,专属客服解答,支持最新模型', 0, '3h', 1, 0, '7', 1, 50, 0, '1h', NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 1}, \"gpt-4o\": {\"per\": \"3h\", \"limit\": 1}, \"grok-3\": {\"per\": \"1s\", \"limit\": 1}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 1}, \"gpt-4-5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 1}, \"research\": {\"per\": \"1s\", \"limit\": 1}, \"claude-3.5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 1}, \"third-models\": {\"per\": \"\", \"limit\": 0}}', 0, 0, 0, 0, 0);
INSERT INTO `chatgpt_subtype` VALUES (31, '2025-02-28 16:13:07.000', '2025-05-03 17:46:24.805', NULL, '1天gpt', 1, 12, '官方同款功能,支持免梯直登,支持对话隔离,支持实时语音,支持会话漫游,支持插件功能,专属客服解答,支持最新模型', 20, '1h', 0, 0, '1', 1, NULL, NULL, NULL, NULL, '{\"o3\": {\"per\": \"1s\", \"limit\": 1}, \"gpt-4o\": {\"per\": \"1s\", \"limit\": 1}, \"o1-pro\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini\": {\"per\": \"1s\", \"limit\": 1}, \"r1-mini\": {\"per\": \"1s\", \"limit\": 1}, \"claude-3.5\": {\"per\": \"\", \"limit\": 0}, \"o4-mini-high\": {\"per\": \"1s\", \"limit\": 1}}', 1, 0, 1, NULL, 0);
INSERT INTO `chatgpt_subtype` VALUES (32, '2025-02-28 16:13:33.316', '2025-02-28 16:13:33.316', NULL, '1天claude', 1, 12, '官方同款功能,支持免梯直登,支持对话隔离,支持实时语音,支持会话漫游,支持插件功能,专属客服解答,支持最新模型', 20, '1h', 0, 0, '2', 1, NULL, 0, NULL, NULL, '{\"o3\": {\"per\": \"\", \"limit\": 0}, \"gpt-4o\": {\"per\": \"\", \"limit\": 0}, \"o1-pro\": {\"per\": \"\", \"limit\": 0}, \"o4-mini\": {\"per\": \"\", \"limit\": 0}, \"r1-mini\": {\"per\": \"\", \"limit\": 0}, \"claude-3.5\": {\"per\": \"1s\", \"limit\": 22}, \"o4-mini-high\": {\"per\": \"\", \"limit\": 0}}', 0, 0, 1, NULL, 0);
INSERT INTO `chatgpt_subtype` VALUES (33, '2025-02-28 16:16:20.802', '2025-02-28 16:16:20.805', NULL, '1天gpt+claude', 1, 1, '官方同款功能,支持免梯直登,支持对话隔离,支持实时语音,支持会话漫游,支持插件功能,专属客服解答,支持最新模型', 20, '1h', 1, 0, '3', 1, NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1s\", \"limit\": 2}, \"gpt-4o\": {\"per\": \"1s\", \"limit\": 2}, \"o1-pro\": {\"per\": \"1s\", \"limit\": 2}, \"o4-mini\": {\"per\": \"1s\", \"limit\": 2}, \"r1-mini\": {\"per\": \"1s\", \"limit\": 2}, \"claude-3.5\": {\"per\": \"1s\", \"limit\": 2}, \"o4-mini-high\": {\"per\": \"1s\", \"limit\": 2}}', 0, 0, 1, NULL, 0);
INSERT INTO `chatgpt_subtype` VALUES (36, '2025-04-20 12:22:41.629', '2025-04-20 12:22:41.629', NULL, 'a', 0, 1, '官方同款功能和UI，一比一还原体验。,降低使用门槛：支持免梯直登，无需任何魔法,支持对话隔离，保护用户隐私。,支持插件功能：语音、联网、绘画、识图、文档解析、深度思考。,支持GPT全模型：官网有的模型，我们都有。,内置本地Deepseek R1满血版，免受官网【服务器繁忙】的困扰。,支持Grok全模型，响应最快，主打丝滑。,支持Claude3.7，超长上下文，理工科必备生产力。,专属客服解答：提供售前咨询，售后保障。', 20, '1h', 0, 0, '1', 1, NULL, 0, NULL, NULL, '{\"o3\": {\"per\": \"\", \"limit\": 0}, \"gpt-4o\": {\"per\": \"\", \"limit\": 0}, \"o1-pro\": {\"per\": \"\", \"limit\": 0}, \"gpt-4-5\": {\"per\": \"\", \"limit\": 0}, \"o4-mini\": {\"per\": \"\", \"limit\": 0}, \"research\": {\"per\": \"\", \"limit\": 0}, \"o4-mini-high\": {\"per\": \"\", \"limit\": 0}, \"third-models\": {\"per\": \"\", \"limit\": 0}}', 0, 0, 1, 0, 0);
INSERT INTO `chatgpt_subtype` VALUES (38, '2025-04-25 23:30:53.128', '2025-04-25 23:30:53.128', NULL, '123', 1, 123, NULL, 20, '1h', 1, 0, '1', 1, NULL, NULL, NULL, NULL, '{\"o3\": {\"per\": \"\", \"limit\": 0}, \"gpt-4o\": {\"per\": \"\", \"limit\": 0}, \"o1-pro\": {\"per\": \"\", \"limit\": 0}, \"gpt-4-5\": {\"per\": \"\", \"limit\": 0}, \"o4-mini\": {\"per\": \"\", \"limit\": 0}, \"research\": {\"per\": \"\", \"limit\": 0}, \"o4-mini-high\": {\"per\": \"\", \"limit\": 0}, \"third-models\": {\"per\": \"\", \"limit\": 0}}', 1, 0, 1, NULL, 0);
INSERT INTO `chatgpt_subtype` VALUES (39, '2025-04-25 23:43:41.709', '2025-04-25 23:43:41.709', NULL, '绘画', 100, 100, '官方同款功能和UI，一比一还原体验。,降低使用门槛：支持免梯直登，无需任何魔法,支持对话隔离，保护用户隐私。,支持插件功能：语音、联网、绘画、识图、文档解析、深度思考。,支持GPT全模型：官网有的模型，我们都有。,内置本地Deepseek R1满血版，免受官网【服务器繁忙】的困扰。,支持Grok全模型，响应最快，主打丝滑。,支持Claude3.7，超长上下文，理工科必备生产力。,专属客服解答：提供售前咨询，售后保障。,智能AI绘图：文生图功能，将文字描述转化为精美图像。,智能图像编辑：上传图片进行风格化改造和智能编辑。,多种绘图风格选择：写实、动漫、油画、素描等多种风格可选。,高清图像生成：支持生成高分辨率精美图像。,图像放大与优化：将模糊或低分辨率图像转换为高清晰度版本。,专业提示词指导：内置提示词模板，让AI绘图更加精准。', 20, '1h', 0, 0, '8', 1, NULL, NULL, NULL, NULL, '{}', 0, 0, 1, NULL, 1000);

-- ----------------------------
-- Table structure for chatgpt_sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `chatgpt_sys_notice`;
CREATE TABLE `chatgpt_sys_notice`  (
  `id` bigint unsigned NOT NULL COMMENT '主键',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '公告内容',
  `publishTime` datetime(0) DEFAULT NULL COMMENT '发布时间',
  `createTime` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `updateTime` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatgpt_sys_notice
-- ----------------------------
INSERT INTO `chatgpt_sys_notice` VALUES (1, '<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"> <!-- Ensure proper scaling on mobile devices -->\n    <style>   \n    h5 {\n        margin: 0;\n        font-size: 1.25em; /* Ensure headline is readable */\n    }\n    .customer p {\n        font-size: 1em;\n        color: red;\n        margin-top: 0.1em;\n    }\n    .img {\n        margin-right: 2em;\n    }\n\n    .container {\n        text-align: center;\n    }\n    .', '2024-09-28 09:29:26', '2024-09-28 09:29:26', '2024-09-28 09:29:26', NULL);
INSERT INTO `chatgpt_sys_notice` VALUES (2, '<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"> <!-- Ensure proper scaling on mobile devices -->\n    <style>   \n    h5 {\n        margin: 0;\n        font-size: 1.25em; /* Ensure headline is readable */\n    }\n    .customer p {\n        font-size: 1em;\n        color: red;\n        margin-top: 0.1em;\n    }\n    .img {\n        margin-right: 2em;\n    }\n\n    .container {\n        text-align: center;\n    }\n    .timer {\n        display: flex;\n		justify-content: center; /* 保证内容水平居中 */\n		align-items: center; /* 垂直居中对齐各元素 */\n		flex-wrap: nowrap; /* 确保不换行，所有元素都在一行显示 */\n		width: 100%; /* 确保占满整个容器宽度 */\n		font-size: 2em; /* 根据需要调整字体大小 */\n    }\n    .flip-unit {\n        position: relative;\n        width: 1.5em;\n        height: 1.5em;\n        margin: 0 0.5em;\n        perspective: 100em;\n    }\n    .flip-unit .card {\n        position: absolute;\n        width: 100%;\n        height: 100%;\n        background: #333;\n        color: #fff;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        backface-visibility: hidden;\n        border-radius: 5px;\n    }\n    .unit-label {\n		font-size:0.5em;\n        line-height: 1em;\n    }\n    .flip-unit .card.front {\n        z-index: 2;\n        transform: rotateX(0deg);\n    }\n    .flip-unit .card.back {\n        transform: rotateX(180deg);\n    }\n    .flip-unit.flipping .front {\n        animation: flipFront 0.5s ease-in-out forwards;\n    }\n    .flip-unit.flipping .back {\n        animation: flipBack 0.5s ease-in-out forwards;\n    }\n    @keyframes flipFront {\n        0% {\n            transform: rotateX(0deg);\n        }\n        100% {\n            transform: rotateX(-180deg);\n        }\n    }\n    @keyframes flipBack {\n        0% {\n            transform: rotateX(180deg);\n        }\n        100% {\n            transform: rotateX(0deg);\n        }\n    }\n\n    /* Responsive styles for mobile devices */\n    @media (max-width: 600px) {\n        body, .container {\n            flex-direction: column; /* Stack elements vertically */\n        }\n        .flip-unit {\n            width: 16%; /* Relative width to adjust based on screen size */\n            margin: 1% 1%; /* Smaller margins */\n        }\n        .img {\n            width: 30%; /* Full width images */\n            height: auto; /* Maintain aspect ratio */\n            margin-right: 0.5em; /* Remove right margin */\n        }\n        h5, .customer p {\n            font-size: 4vw; /* Responsive font size */\n        }\n    }\n    </style>\n</head>\n <div class=\"customer\">\n	<div class=\"container\">\n        <h3>网站运行时间</h3>\n        <div class=\"timer\">\n            <div class=\"flip-unit\" id=\"daysUnit\">\n                <div class=\"card front\" id=\"daysFront\">0</div>\n                <div class=\"card back\" id=\"daysBack\">0</div>\n            </div>\n			<span class=\"unit-label\">天</span>\n            <div class=\"flip-unit\" id=\"hoursUnit\">\n                <div class=\"card front\" id=\"hoursFront\">0</div>\n                <div class=\"card back\" id=\"hoursBack\">0</div>\n            </div>\n			<span class=\"unit-label\">时</span>\n            <div class=\"flip-unit\" id=\"minutesUnit\">\n                <div class=\"card front\" id=\"minutesFront\">0</div>\n                <div class=\"card back\" id=\"minutesBack\">0</div>\n            </div>\n			<span class=\"unit-label\">分</span>\n            <div class=\"flip-unit\" id=\"secondsUnit\">\n                <div class=\"card front\" id=\"secondsFront\">0</div>\n                <div class=\"card back\" id=\"secondsBack\">0</div>\n            </div>\n			<span class=\"unit-label\">秒</span>\n        </div>\n    </div>\n	<h5>功能优势💪</h5>\n	<p>\n		<img src=\"https://img.shields.io/badge/官方同款-免梯直登-blue\" alt=\"4o\">\n		<img src=\"https://img.shields.io/badge/无次数限制-随便用-blue\" alt=\"4o\">\n		<img src=\"https://img.shields.io/badge/支持-ChatGPT3.5-blue\" alt=\"3.5\">\n		<img src=\"https://img.shields.io/badge/支持-ChatGPT4.0-blue\" alt=\"4o\">\n		<img src=\"https://img.shields.io/badge/支持-ChatGPT4o-blue\" alt=\"4o\">\n		<img src=\"https://img.shields.io/badge/支持-o1系列最新模型-blue\" alt=\"4o\">\n		<img src=\"https://img.shields.io/badge/支持-DALL·E绘画-blue\" alt=\"4o\">\n		<img src=\"https://img.shields.io/badge/支持-Vision识图-blue\" alt=\"4o\">\n		<img src=\"https://img.shields.io/badge/支持-文档解析-blue\" alt=\"4o\">\n		<img src=\"https://img.shields.io/badge/支持-实时语音-blue\" alt=\"4o\">\n	</p>\n\n	<h5>节点说明</h5>\n	<img src=\"https://img.shields.io/badge/免费节点-无需付费-blue\" alt=\"4o\">\n	<img src=\"https://img.shields.io/badge/4o节点-普通会员可用-blue\" alt=\"4o\">\n	<img src=\"https://img.shields.io/badge/Plus节点-购买高级会员-blue\" alt=\"4o\">\n	<img src=\"https://img.shields.io/badge/Claude节点-高级会员专属-blue\" alt=\"4o\">\n		\n		<p>如有需要更多次数，可以在【商店】中直接购买。</p>\n			<p>邀请好友将获得更多时长的奖励。</p>\n			<p>中秋佳节即将来临，祝您和您的家人节日快乐。奉上9折优惠卷码：save10，有效期：2024-09-22，复制卷码，在商店购买的时候填写！</p>\n			<p style=\"color:red\">合作共赢：有任何商务合作直接联系，包括购买站内会员、搭建同款、代理我们站点等等</p>\n	<h5>为防失联，请添加我们任一方式</h5>\n	<img class=\"img\" src=\"https://chat.freeuse.top/image/wechat01.png\" width=\"150\" height=\"150\" alt=\"wx\">\n	<img class=\"img\" src=\"https://chat.freeuse.top/image/qq01.png\" width=\"150\" height=\"150\" alt=\"qq\">\n	<img class=\"img\" src=\"https://chat.freeuse.top/image/group01.png\" width=\"150\" height=\"150\" alt=\"qq群\">\n	\n</div>\n\n<script>\n        const startTime = new Date(\'2024-05-01T00:00:00\');\n\n        function updateTimer() {\n            const now = new Date();\n            const elapsed = now - startTime;\n\n            const days = Math.floor(elapsed / (1000 * 60 * 60 * 24));\n            const hours = Math.floor((elapsed % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n            const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));\n            const seconds = Math.floor((elapsed % (1000 * 60)) / 1000);\n\n            flip(\'days\', days);\n            flip(\'hours\', hours);\n            flip(\'minutes\', minutes);\n            flip(\'seconds\', seconds);\n        }\n\n        function flip(unit, value) {\n            const unitFront = document.getElementById(unit + \'Front\');\n            const unitBack = document.getElementById(unit + \'Back\');\n            const flipUnit = document.getElementById(unit + \'Unit\');\n\n            if (unitFront.textContent !== String(value)) {\n                unitBack.textContent = value;\n                flipUnit.classList.add(\'flipping\');\n                setTimeout(() => {\n                    unitFront.textContent = value;\n                    flipUnit.classList.remove(\'flipping\');\n                }, 500);\n            }\n        }\n\n        setInterval(updateTimer, 1000);\n        updateTimer();\n    </script>\n</body>\n</html>\n', '2024-09-28 10:35:12', '2024-09-28 10:35:12', '2025-04-20 10:27:36', NULL);
INSERT INTO `chatgpt_sys_notice` VALUES (5, '<p>123</p>', '2025-04-20 11:22:16', '2025-04-20 11:22:16', '2025-04-20 11:22:16', NULL);

-- ----------------------------
-- Table structure for chatgpt_user
-- ----------------------------
DROP TABLE IF EXISTS `chatgpt_user`;
CREATE TABLE `chatgpt_user`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `userToken` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'UserToken',
  `expireTime` datetime(3) NOT NULL COMMENT '过期时间',
  `isPlus` tinyint(1) DEFAULT 0 COMMENT 'PLUS',
  `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `password` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `isAdmin` tinyint(1) DEFAULT 0 COMMENT '是否为管理员',
  `sessionId` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'sessionId',
  `email` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'email',
  `carids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'chatgpt车队',
  `limit` bigint(0) DEFAULT 20 COMMENT '限制',
  `per` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '1h' COMMENT '时间段',
  `subTypeId` bigint(0) DEFAULT 0 COMMENT '用户订阅类型id',
  `affCode` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '推广码',
  `affQuota` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '可提现金额',
  `affHistoryQuota` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '推广已提现金额',
  `affTotalQuota` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '推广总金额',
  `affCount` bigint(0) DEFAULT 0 COMMENT '推广人数',
  `receiptFile` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '收款码',
  `status` bigint(0) DEFAULT 1 COMMENT '状态',
  `inviterId` bigint(0) DEFAULT NULL COMMENT '邀请人的Id',
  `isBackupData` tinyint(1) DEFAULT 1 COMMENT '聊天数据是否备份',
  `lastActiveTime` datetime(3) DEFAULT NULL COMMENT '最后登陆时间',
  `affRate` double DEFAULT NULL COMMENT '返佣比例',
  `userType` bigint(0) DEFAULT 1 COMMENT '账号类型',
  `gptsIds` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'gptsIds',
  `claudeExpireTime` datetime(3) DEFAULT NULL COMMENT 'claude过期时间',
  `isPro` tinyint(1) DEFAULT 0 COMMENT 'PRO',
  `claudeCarids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'claude车队',
  `claudeLimit` bigint(0) DEFAULT 20 COMMENT '限制',
  `claudePer` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '1h' COMMENT '时间段',
  `claudeSubTypeId` bigint(0) DEFAULT 0 COMMENT 'claude订阅类型id',
  `plusExpireTime` datetime(3) DEFAULT NULL COMMENT 'plus过期时间',
  `dailyConversationCount` int(0) DEFAULT 0 COMMENT '对话次数',
  `claudeProExpireTime` datetime(3) DEFAULT NULL COMMENT 'claude pro过期时间',
  `affApplied` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已触发aff',
  `clientIp` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户端ip',
  `mygizmosIds` json COMMENT '个人gpts',
  `dailyClaudeConversationCount` int(0) DEFAULT 0 COMMENT 'claude对话次数',
  `deviceId` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '设备id',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户名',
  `loginType` tinyint(1) DEFAULT 1 COMMENT '登录方式',
  `grokExpireTime` datetime(3) DEFAULT NULL COMMENT 'grok过期时间',
  `grokSuperExpireTime` datetime(3) DEFAULT NULL COMMENT 'grok super过期时间',
  `model_limits` json COMMENT '模型限制',
  `loginToken` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登陆凭证',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_chatgpt_user_deleted_at`(`deleted_at`) USING BTREE,
  INDEX `index_userToken`(`userToken`(100)) USING BTREE,
  INDEX `idx_login_token`(`loginToken`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1895390234955325450 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatgpt_user
-- ----------------------------
INSERT INTO `chatgpt_user` VALUES (999999999, '2024-09-25 09:26:59.000', '2025-04-26 02:00:21.644', NULL, 'admin', '9999-08-20 05:08:08.000', 1, '123', 'e10adc3949ba59abbe56e057f20f883e', 1, NULL, '<EMAIL>', NULL, 10, '1h', 39, '88888888', '10.00', '20.05', '29.96', 8, '136ee6dd-e9a3-43e0-a3f7-9c0421d591ee.jpg', 1, NULL, 1, '2025-04-25 09:41:10.671', 0.5, 3, NULL, '9999-08-15 20:08:08.000', 0, NULL, 1, '1m', 0, '9999-08-15 20:08:08.000', 0, '2025-01-13 22:07:11.000', 0, '192.168.0.101', NULL, 0, '0', 'admin', 1, '9999-08-15 20:08:08.000', NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '7f8c51026e7644f1ad013d7231a6f8d9');
INSERT INTO `chatgpt_user` VALUES (1000000001, '2024-09-30 08:24:58.000', '2024-12-19 15:02:44.455', '2025-02-03 18:46:59.000', '123', '2028-02-17 02:00:00.000', 1, '456', '220466675e31b9d20c051d5e57974150', 0, NULL, '', NULL, 20, '1h', 0, '12e4d0', '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, '2024-12-13 17:33:03.000', 0, NULL, 20, '1h', 0, '2024-12-16 00:00:00.000', 0, '2024-12-13 17:33:03.000', 0, NULL, NULL, 0, '0', '123', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '3c11efeb201a478d891fadcec675f8bd');
INSERT INTO `chatgpt_user` VALUES (1000000002, '2024-09-30 08:38:41.000', '2024-12-19 15:02:38.774', '2025-02-03 18:46:59.000', '1231231231', '2028-02-28 02:00:26.000', 0, '', '220466675e31b9d20c051d5e57974150', 0, NULL, '', NULL, 20, '1h', 0, '219c0a', '0', '0', '0', 0, NULL, 1, 999999999, 1, '2024-12-11 09:46:35.000', 0.1, 3, NULL, '2025-12-14 23:24:18.000', 0, NULL, 20, '1h', 0, '2025-12-14 23:24:18.000', 0, '2025-12-14 23:24:18.000', 0, '14.26.195.24', NULL, 0, '0', '1231231231', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '52e12657839c4192b342ee6abdb6906a');
INSERT INTO `chatgpt_user` VALUES (1000000010, '2024-10-26 23:52:11.347', '2024-10-26 23:52:11.401', '2025-02-03 18:46:59.000', 'yb1klgv6', '2024-11-27 01:52:10.885', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 2, '1s', 0, NULL, '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', 'yb1klgv6', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'c3345e111e664d008dfaa480b6addc55');
INSERT INTO `chatgpt_user` VALUES (1000000011, '2024-10-26 23:54:20.318', '2024-10-26 23:54:20.318', '2025-02-03 18:46:59.000', '123_ppx7l1qt', '2024-11-27 01:54:20.316', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 2, '1s', 0, NULL, '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', '123_ppx7l1qt', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'e23e2edef7a74ba2b4f8b177a55174c4');
INSERT INTO `chatgpt_user` VALUES (1000000012, '2024-10-26 23:55:10.436', '2024-10-26 23:55:10.436', '2025-02-03 18:46:59.000', '1_gqp44f3a', '2024-11-27 01:55:10.434', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 2, '1s', 0, NULL, '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', '1_gqp44f3a', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '927c07a068a44ba989fac009debd9df8');
INSERT INTO `chatgpt_user` VALUES (1000000020, '2024-11-07 22:50:04.285', '2024-11-07 22:50:04.289', '2025-02-03 18:46:59.000', 'free', '2025-11-09 00:49:59.000', 0, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '', NULL, 3, '1d', 0, 'QHX4VE', '0', '0', '0', 0, NULL, 1, 999999999, 1, '2024-11-07 23:10:52.644', 0.1, 2, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', 'free', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '8ccf3c9553684574a738eeb5092dd56d');
INSERT INTO `chatgpt_user` VALUES (1000000021, '2024-11-08 22:44:26.000', '2024-12-09 08:41:31.815', '2025-02-03 18:46:54.000', 'aaa', '2024-12-23 22:31:05.000', 1, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 2, '1s', 0, 'N0LVU7', '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, '2024-12-12 08:41:30.000', 0, NULL, 2, '1h', 0, '2024-12-03 03:13:39.000', 0, NULL, 0, NULL, NULL, 0, '0', 'aaa', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'e828bb291ce74f2eb51a6d0a8211991b');
INSERT INTO `chatgpt_user` VALUES (1000000022, '2024-12-15 01:19:13.829', '2025-01-10 21:29:33.396', '2025-02-03 18:46:54.000', 'aaa', '2058-11-30 01:19:11.000', 1, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '', NULL, 0, '', 13, 'HG95OL', '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 3, NULL, '2058-09-26 21:29:33.396', 0, NULL, 0, '', 0, '2058-09-26 21:29:33.396', 0, NULL, 0, '192.168.31.254', NULL, 0, '0', 'aaa', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 1}, \"gpt-4o\": {\"per\": \"3h\", \"limit\": 1}, \"grok-3\": {\"per\": \"1s\", \"limit\": 1}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 1}, \"gpt-4-5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 1}, \"research\": {\"per\": \"1s\", \"limit\": 1}, \"claude-3.5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 1}, \"third-models\": {\"per\": \"\", \"limit\": 0}}', '5a32e41d15ba49fcbb3c760a58731e6f');
INSERT INTO `chatgpt_user` VALUES (1000000023, '2024-12-15 01:20:05.813', '2024-12-15 01:20:27.650', '2025-02-03 18:46:54.000', 'aaa_vsvrw8wm', '2024-12-16 01:20:05.803', 1, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 2, '1s', 12, '5U9JKE', '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, '2025-04-17 01:20:27.650', 0, NULL, NULL, NULL, 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', 'aaa_vsvrw8wm', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'f5cce210db8541d39698ed8f0dd49653');
INSERT INTO `chatgpt_user` VALUES (1000000024, '2024-12-15 01:21:37.000', '2024-12-18 20:24:30.110', '2025-02-03 18:46:54.000', 'abc', '2024-12-16 01:21:37.000', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 2, '1s', 0, 'G6OZUY', '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2024-12-15 01:21:37.000', 0, NULL, 0, NULL, NULL, 0, '0', 'abc', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'ec783a482be74d15994e8f88ddc8d4d6');
INSERT INTO `chatgpt_user` VALUES (1000000025, '2024-12-18 21:06:25.000', '2024-12-30 22:44:00.136', '2025-02-03 18:46:54.000', '123456', '2024-12-19 21:06:25.000', 1, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 2, '1s', 12, '6XATP5', '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 3, NULL, '2025-12-22 21:07:11.000', 0, NULL, NULL, NULL, 0, '2024-12-18 21:06:25.000', 0, NULL, 0, '192.168.31.254', NULL, 0, '0', '123456', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'c52790b0dc3c4821a1c2f7bc317cae90');
INSERT INTO `chatgpt_user` VALUES (1000000026, '2024-12-23 15:16:39.987', '2024-12-23 15:16:39.987', '2025-02-03 18:46:54.000', '123123123123', '2024-12-24 15:16:39.973', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 2, '1s', 0, 'GH1SIL', '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2024-12-23 15:16:39.973', 0, NULL, 0, NULL, NULL, 0, '0', '123123123123', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '103c8a1cf54d4b6c93726c7805c0e13d');
INSERT INTO `chatgpt_user` VALUES (1000000027, '2024-12-23 15:16:58.464', '2024-12-23 15:16:58.464', '2025-02-03 18:46:54.000', '1231231231231', '2024-12-24 15:16:58.462', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 2, '1s', 0, 'U6CGZQ', '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2024-12-23 15:16:58.462', 0, NULL, 0, NULL, NULL, 0, '0', '1231231231231', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '3be2c486f09449518dfe369ad73032cf');
INSERT INTO `chatgpt_user` VALUES (1000000028, '2024-12-23 15:21:51.060', '2024-12-23 15:21:51.065', '2025-02-03 18:46:54.000', '12312312312311', '2024-12-24 15:21:51.028', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 2, '1s', 0, 'NZ5RXW', '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2024-12-23 15:21:51.028', 0, NULL, 0, '0:0:0:0:0:0:0:1', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', '12312312312311', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '8a289f3b164f4a3790d0fcaa1357779b');
INSERT INTO `chatgpt_user` VALUES (1000000029, '2024-12-23 15:22:19.324', '2024-12-23 15:22:19.324', '2025-02-03 18:46:54.000', '123123123123111', '2024-12-24 15:22:19.322', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 2, '1s', 0, 'UXEYLZ', '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2024-12-23 15:22:19.322', 0, NULL, 0, '0:0:0:0:0:0:0:1', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', '123123123123111', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '04f87ea0ea734681970f4e0aa7eb75b8');
INSERT INTO `chatgpt_user` VALUES (1000000030, '2024-12-23 15:27:01.615', '2024-12-23 15:27:01.620', '2025-02-03 18:46:54.000', '1231213', '2024-12-24 15:27:01.598', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 2, '1s', 0, '29WXJS', '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2024-12-23 15:27:01.598', 0, NULL, 0, '0:0:0:0:0:0:0:1', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', '1231213', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '835c05ad74c742dcbb769e6466fd0828');
INSERT INTO `chatgpt_user` VALUES (1000000031, '2024-12-23 15:38:12.883', '2024-12-23 15:38:12.884', '2025-02-03 18:46:49.000', '12312312', '2024-12-24 15:38:12.859', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 2, '1s', 0, '3BY3XO', '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2024-12-23 15:38:12.859', 0, NULL, 0, '0:0:0:0:0:0:0:1', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', '12312312', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'f870ca29ce744cf487b29eaa4ce426ea');
INSERT INTO `chatgpt_user` VALUES (1000000032, '2024-12-23 15:48:23.000', '2024-12-30 23:02:19.709', '2025-02-03 18:46:49.000', '123123121', '2024-12-24 15:48:23.000', 1, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 2, '1s', 12, '0AF4JD', '0', '0', '0', 0, NULL, 1, 999999999, 1, NULL, 0.1, 1, NULL, '2025-05-02 23:02:19.709', 0, NULL, NULL, NULL, 0, '2024-12-23 15:48:23.000', 0, NULL, 0, '192.168.31.254', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', '123123121', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'dbfb174352df4cf2b49cc42fae6fc71b');
INSERT INTO `chatgpt_user` VALUES (1000000033, '2025-01-04 12:03:02.044', '2025-01-04 12:03:02.049', '2025-02-03 18:46:49.000', '12312321312', '2025-01-04 12:03:02.025', 0, NULL, '4297f44b13955235245b2497399d7a93', 0, NULL, '<EMAIL>', NULL, 2, '1s', 0, '4HKPW2', '0', '0', '0', 0, NULL, 1, 0, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2025-01-04 12:03:02.025', 0, NULL, 0, '192.168.31.254', NULL, 0, 'ce082007a1103d7026843fad95e0792a', '12312321312', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'c216495a9eda4bee961644ef7da4c4f6');
INSERT INTO `chatgpt_user` VALUES (1000000034, '2025-01-04 12:03:46.000', '2025-01-04 15:53:37.752', '2025-02-03 18:46:49.000', '123123', '2025-01-04 12:03:46.000', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 2, '1s', 0, 'HKELJW', '0', '0', '0', 0, NULL, 1, 0, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2025-01-04 12:03:46.000', 0, NULL, 0, '192.168.31.254', NULL, 0, 'ce082007a1103d7026843fad95e0792a', '123123', 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '3618d7292b364fbabab85abede0bb022');
INSERT INTO `chatgpt_user` VALUES (1000000035, '2025-01-04 16:01:18.000', '2025-01-04 16:35:20.472', '2025-02-03 18:46:49.000', '12345612312312', '2025-01-04 17:13:08.000', 1, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 2, '1s', 12, 'O4B8UE', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.2, 3, NULL, '2025-05-07 17:01:34.000', 0, NULL, NULL, NULL, 0, NULL, 0, NULL, 0, '192.168.31.254', NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'c6a669af553f4c3981a780ba14e8a19a');
INSERT INTO `chatgpt_user` VALUES (1000000036, '2025-01-04 16:35:48.000', '2025-01-05 19:17:13.891', '2025-02-03 18:46:49.000', 'aaa_4ut5v1cr', '2025-01-04 18:40:56.000', 1, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 2, '1s', 12, 'YOGXZ3', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 3, NULL, '2025-09-08 15:58:36.978', 0, NULL, NULL, NULL, 0, '2025-01-04 17:40:56.000', 0, NULL, 0, '192.168.31.254', NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '9bd3fc8048fe4f55a286e043e81b64aa');
INSERT INTO `chatgpt_user` VALUES (1000000037, '2025-01-09 15:06:36.533', '2025-01-09 15:06:36.534', '2025-02-03 18:46:49.000', '21312312', '2025-01-10 15:06:35.000', 0, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '', NULL, 2, '1s', 0, 'J5I4LV', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '3903bd66037e4672921933b25328de77');
INSERT INTO `chatgpt_user` VALUES (1000000038, '2025-01-09 16:37:46.000', '2025-01-09 16:40:23.356', '2025-02-03 18:46:49.000', '1231231231123123', '2025-01-09 16:37:40.000', 0, '', 'e6db1baa29d3df1eb307ff6a12c778da', 0, NULL, '', NULL, 2, '1s', 0, '1IWG16', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '68b8994d6f144ac0ac386f4746460c32');
INSERT INTO `chatgpt_user` VALUES (1000000039, '2025-01-09 16:44:59.000', '2025-01-09 17:19:09.743', '2025-02-03 18:46:49.000', 'aaa123', '2025-01-09 16:44:58.000', 0, '', '220466675e31b9d20c051d5e57974150', 0, NULL, '', NULL, 2, '1s', 0, 'L6LGJG', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '869d7aa0782440f5912b4e9a96f2faec');
INSERT INTO `chatgpt_user` VALUES (1000000040, '2025-01-09 17:20:40.000', '2025-01-09 18:23:37.679', '2025-02-03 18:46:49.000', 'aaasss', '2025-01-09 17:20:39.000', 0, NULL, 'e6db1baa29d3df1eb307ff6a12c778da', 0, NULL, NULL, NULL, 2, '1s', 0, '5R141M', '0', '0', '0', 0, NULL, 1, NULL, 1, '2025-02-07 16:31:59.299', 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '1192a5aaa95343cc834abc4276cb3b54');
INSERT INTO `chatgpt_user` VALUES (1000000041, '2025-01-10 22:00:14.078', '2025-01-10 22:00:14.078', '2025-01-10 22:12:39.000', 'aaabbb123', '2025-01-10 22:00:12.000', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 0, '5OC4YE', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'fcdde625fb85483296445ab261f5ae22');
INSERT INTO `chatgpt_user` VALUES (1000000042, '2025-01-10 22:01:41.405', '2025-01-10 22:01:41.405', '2025-01-10 22:12:39.000', 'aaadasdas', '2025-01-10 23:01:41.404', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 20, '1h', 0, 'MYKBSB', '0', '0', '0', 0, NULL, 1, 0, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2025-01-10 23:01:41.404', 0, NULL, 0, '192.168.31.254', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '5e273ab885a04972993d4044577680b6');
INSERT INTO `chatgpt_user` VALUES (1000000043, '2025-01-10 22:03:31.251', '2025-01-10 22:03:31.251', '2025-01-10 22:12:39.000', '123aaa', '2025-01-10 22:03:30.000', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 0, 'CYY9Z1', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '650423fb00304eb690fdb607167f7d9b');
INSERT INTO `chatgpt_user` VALUES (1000000044, '2025-01-10 22:13:44.973', '2025-01-26 12:44:19.382', '2025-02-02 19:52:25.000', 'aaa', '2092-06-13 20:36:38.049', 1, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 13, 'L4EZ2W', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 3, NULL, '2092-06-13 20:36:38.049', 0, NULL, 20, '1h', 0, '2092-06-13 20:36:38.049', 0, NULL, 0, '192.168.0.100', NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 1}, \"gpt-4o\": {\"per\": \"3h\", \"limit\": 1}, \"grok-3\": {\"per\": \"1s\", \"limit\": 1}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 1}, \"gpt-4-5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 1}, \"research\": {\"per\": \"1s\", \"limit\": 1}, \"claude-3.5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 1}, \"third-models\": {\"per\": \"\", \"limit\": 0}}', 'cba183a82521478c969dc6a284d34d3d');
INSERT INTO `chatgpt_user` VALUES (1000000045, '2025-01-10 22:22:46.035', '2025-01-10 22:22:46.054', '2025-02-02 19:52:25.000', 'aaabbb', '2025-01-10 23:22:42.295', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 20, '1h', 0, '294TZM', '0', '0', '0', 0, NULL, 1, 0, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2025-01-10 23:22:42.295', 0, NULL, 0, '192.168.31.254', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'aa9169ffc6e54159bf1aa2faa3f2d0e5');
INSERT INTO `chatgpt_user` VALUES (1000000046, '2025-01-10 22:55:22.000', '2025-01-14 22:05:40.212', '2025-02-02 19:52:25.000', 'aaazzz', '2025-01-10 22:55:19.000', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 20, '1h', 0, 'GMROT6', '0', '0', '0', 0, NULL, 1, 0, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2025-01-10 22:55:19.000', 0, NULL, 0, '192.168.31.254', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'e513d2f2bfb14c5eb3882ded636f7e81');
INSERT INTO `chatgpt_user` VALUES (1000000047, '2025-01-14 18:49:33.109', '2025-01-14 18:49:33.109', '2025-02-02 19:52:25.000', 'aaabbbaaa', '2025-01-14 18:47:53.147', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 20, '1h', 0, 'NXP8LW', '0', '0', '0', 0, NULL, 1, 0, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2025-01-14 18:47:53.147', 0, NULL, 0, '192.168.1.9', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '45d99691ca35493482335bbfae956f96');
INSERT INTO `chatgpt_user` VALUES (1000000048, '2025-01-14 19:03:40.000', '2025-01-14 19:04:13.356', '2025-02-02 19:52:25.000', '123_kpi5q99w', '2025-01-14 19:03:28.000', 0, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 20, '1h', 0, 'ZZUWOD', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, '192.168.1.9', NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'd2cd23cf3a864a55b8ea1ab27b1e262e');
INSERT INTO `chatgpt_user` VALUES (1000000049, '2025-01-14 21:51:27.000', '2025-01-19 18:48:58.879', '2025-02-02 19:52:25.000', 'aaa12312312', '2025-01-14 21:51:24.000', 0, NULL, '8d4646eb2d7067126eb08adb0672f7bb', 0, NULL, '<EMAIL>', NULL, 20, '1h', 0, 'KRVG27', '0', '0', '0', 0, NULL, 1, 0, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2025-01-14 21:51:24.000', 0, NULL, 0, '192.168.1.9', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '15fcd5d2b246422992db43afe5170ca9');
INSERT INTO `chatgpt_user` VALUES (1000000050, '2025-01-20 16:04:45.000', '2025-01-26 20:57:08.304', '2025-02-02 19:52:25.000', '123', '2025-01-20 16:04:45.000', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 20, '1h', 0, '2D0P42', '0', '0', '0', 0, NULL, 1, 0, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2025-01-20 16:04:45.000', 0, NULL, 0, '192.168.0.100', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '62d09cea5d3f4b79966477bcd6f7b40a');
INSERT INTO `chatgpt_user` VALUES (1000000051, '2025-02-02 13:34:38.739', '2025-02-02 13:43:29.489', '2025-02-02 14:20:08.000', '123456_ukeor3lq', '2159-12-05 13:38:51.956', 1, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 13, 'KGQD9H', '0', '0', '0', 0, NULL, 1, NULL, 1, '2025-02-02 13:44:08.921', 0.1, 3, NULL, '2159-12-05 13:38:51.956', 0, NULL, 20, '1h', 0, '2159-12-05 13:38:51.956', 0, NULL, 0, '*************', NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 1}, \"gpt-4o\": {\"per\": \"3h\", \"limit\": 1}, \"grok-3\": {\"per\": \"1s\", \"limit\": 1}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 1}, \"gpt-4-5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 1}, \"research\": {\"per\": \"1s\", \"limit\": 1}, \"claude-3.5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 1}, \"third-models\": {\"per\": \"\", \"limit\": 0}}', '9f6e999a88474819ba612efd4da84784');
INSERT INTO `chatgpt_user` VALUES (1000000052, '2025-02-02 13:46:25.198', '2025-02-02 13:46:25.198', '2025-02-02 14:15:12.000', 'asdasdas', '2025-02-02 13:46:25.173', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 20, '1h', 0, 'IFN43R', '0', '0', '0', 0, NULL, 1, 0, 1, '2025-02-02 13:46:32.573', 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2025-02-02 13:46:25.173', 0, NULL, 0, '*************', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '5c756e341c4b4e709a64fc3066edcc49');
INSERT INTO `chatgpt_user` VALUES (1000000053, '2025-02-02 14:14:24.826', '2025-02-02 14:14:24.829', '2025-02-02 19:52:25.000', 'aaabbbccc', '2025-02-02 14:14:24.811', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 20, '1h', 0, 'QSQNVH', '0', '0', '0', 0, NULL, 1, 0, 1, '2025-02-02 14:14:26.182', 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2025-02-02 14:14:24.811', 0, NULL, 0, '*************', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '510d47cede2f4ba29c4ee6bbd2aebc8a');
INSERT INTO `chatgpt_user` VALUES (1000000054, '2025-02-02 14:20:20.858', '2025-02-02 14:20:42.580', '2025-02-02 19:52:25.000', 'aaa_877mipnn', '2058-10-19 14:20:42.580', 1, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 13, '9DJ6BP', '0', '0', '0', 0, NULL, 1, NULL, 1, '2025-02-02 14:20:42.592', 0.1, 3, NULL, '2058-10-19 14:20:42.581', 0, NULL, 20, '1h', 0, '2058-10-19 14:20:42.580', 0, NULL, 0, '*************', NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 1}, \"gpt-4o\": {\"per\": \"3h\", \"limit\": 1}, \"grok-3\": {\"per\": \"1s\", \"limit\": 1}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 1}, \"gpt-4-5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 1}, \"research\": {\"per\": \"1s\", \"limit\": 1}, \"claude-3.5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 1}, \"third-models\": {\"per\": \"\", \"limit\": 0}}', 'd315466356294ceb9b2f3bd57335d313');
INSERT INTO `chatgpt_user` VALUES (1000000055, '2025-02-02 14:26:02.000', '2025-02-02 19:51:04.543', '2025-02-02 19:52:25.000', 'zzz_npq4p51r', '2058-10-19 14:26:12.000', 1, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 13, 'MVTZCH', '0', '0', '0', 0, NULL, 1, NULL, 1, '2025-02-02 14:26:12.000', 0.1, 3, NULL, '2058-10-19 14:26:12.000', 0, NULL, 20, '1h', 0, '2058-10-19 14:26:12.000', 0, NULL, 0, '*************', NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 1}, \"gpt-4o\": {\"per\": \"3h\", \"limit\": 1}, \"grok-3\": {\"per\": \"1s\", \"limit\": 1}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 1}, \"gpt-4-5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 1}, \"research\": {\"per\": \"1s\", \"limit\": 1}, \"claude-3.5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 1}, \"third-models\": {\"per\": \"\", \"limit\": 0}}', '91d6bd5bda0f4ed09fba8a120a0c61f6');
INSERT INTO `chatgpt_user` VALUES (1000000056, '2025-02-06 13:35:04.000', '2025-02-06 13:35:21.754', NULL, 'aaa', '2025-02-06 13:35:03.000', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 0, 'V1C2D3', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '9b6667a5eb7d49e1b17591ca4fe853c2');
INSERT INTO `chatgpt_user` VALUES (1000000057, '2025-02-06 13:35:38.396', '2025-02-06 13:36:14.946', NULL, 'batch_jihc9tqs', '2025-02-07 13:36:14.946', 1, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 23, 'RKYEMS', '0', '0', '0', 0, NULL, 1, NULL, 1, '2025-02-06 13:36:14.974', 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, '2025-02-07 13:36:14.946', 0, NULL, 0, '192.168.0.104', NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '4e28ee47ac4f4f8898c4ab0fe8a0bbab');
INSERT INTO `chatgpt_user` VALUES (1000000058, '2025-02-06 13:37:54.000', '2025-02-23 19:37:20.106', NULL, 'aaasss', '2025-02-08 16:28:33.000', 1, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, '<EMAIL>', NULL, 20, '1h', 23, 'H6UQAK', '0', '0', '0', 0, NULL, 1, 0, 1, '2025-02-07 16:31:59.000', 0.1, 1, NULL, '2025-02-06 16:14:08.000', 0, NULL, 20, '1h', 0, '2025-02-08 16:28:33.000', 0, '2025-02-06 16:14:08.000', 0, '192.168.1.9', NULL, 0, '8f31bd4f15e4ef8dc26c37299471485c', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'f5917f0feec2414aa3a3d0ef25e4fde6');
INSERT INTO `chatgpt_user` VALUES (1000000059, '2025-02-19 23:13:49.000', '2025-03-03 22:36:02.327', NULL, 'expander', '2025-03-03 22:35:56.000', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 0, '9WA03X', '0', '0', '0', 0, NULL, 1, NULL, 1, '2025-02-20 00:10:49.000', NULL, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, '192.168.31.254', NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '51b6c72ca2df48b2bb5bb9598497bfa2');
INSERT INTO `chatgpt_user` VALUES (1000000060, '2025-02-23 21:15:48.191', '2025-02-23 21:15:48.191', NULL, 'v2mw05ak', '2025-02-23 21:15:48.180', 0, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 0, 'ONLXCZ', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'f882a46508b24e17a43f8888c9e27ab7');
INSERT INTO `chatgpt_user` VALUES (1000000061, '2025-02-23 21:27:43.764', '2025-02-23 21:27:43.768', NULL, '6cb8ef5e-ffb4-4686-abcd-593d03207d04', '2025-02-23 21:27:43.746', 0, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 0, 'TY554A', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 2, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'd547203ace6c46e3b2f401ae785c4d3f');
INSERT INTO `chatgpt_user` VALUES (1000000062, '2025-02-25 17:17:54.252', '2025-02-25 17:17:54.252', NULL, '615357bd-0ff4-4177-8c1b-2f4d04cc9555', '2025-02-25 17:17:54.232', 0, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 30, 'S78DRD', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 2, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '17ce0f0eacf944e1815667d2358c798b');
INSERT INTO `chatgpt_user` VALUES (1000000063, '2025-02-25 17:22:59.000', '2025-02-28 16:10:24.287', NULL, 'addc0b93-3ee0-44e8-bfae-312e97fadebc', '2025-03-07 17:23:14.000', 1, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 30, '8U0C77', '0', '0', '0', 0, NULL, 1, NULL, 1, '2025-02-26 21:21:53.000', 0.1, 1, NULL, '2025-03-07 17:23:14.000', 0, NULL, 20, '1h', 0, '2025-03-07 17:23:14.000', 0, NULL, 0, '192.168.31.254', NULL, 0, '0', NULL, 2, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'b1886b3aeb394df58adba9a07d3d8270');
INSERT INTO `chatgpt_user` VALUES (1000000064, '2025-02-28 16:10:33.000', '2025-02-28 16:10:36.902', NULL, '13ffbd06-3a88-4b26-b4f7-57262f0b9989', '2025-02-28 16:10:33.000', 0, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 0, 'RKM3UD', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 2, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'b73905441559492abbf56b570256ecf6');
INSERT INTO `chatgpt_user` VALUES (1895387634700754945, '2025-02-28 16:16:41.378', '2025-02-28 16:16:41.378', NULL, 'test', '2025-02-28 16:16:40.000', 0, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 0, 'OVES78', '0', '0', '0', 0, NULL, 1, NULL, 1, '2025-02-28 16:17:07.299', NULL, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, '192.168.0.101', NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', 'faf422992ecd422299549f0b4f330fe5');
INSERT INTO `chatgpt_user` VALUES (1895388644714323969, '2025-02-28 16:21:05.000', '2025-02-28 17:04:12.941', NULL, '123', '2025-03-02 16:52:12.640', 1, NULL, 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 31, 'PSWD9P', '0', '0', '0', 0, NULL, 1, NULL, 1, '2025-02-28 16:41:19.000', NULL, 1, NULL, '2025-03-01 16:52:28.800', 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, '192.168.0.101', NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '909a61006aef4792853d7969f5292eec');
INSERT INTO `chatgpt_user` VALUES (1895390234955325442, '2025-02-28 16:27:08.000', '2025-03-10 11:16:06.548', NULL, '12312312', '2025-02-28 16:26:55.000', 0, NULL, '74c43b7ec689955c9c1517294e92500f', 0, NULL, NULL, NULL, 20, '1h', 13, '6FO3K0', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, NULL, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '93283d1c2edc40909bf427d53d337d9a');
INSERT INTO `chatgpt_user` VALUES (1895390234955325443, '2025-03-01 11:47:36.000', '2025-03-10 11:15:34.503', NULL, '97cb0b51-066d-4f00-8954-ee2f13044aaa', '2025-03-01 11:47:36.000', 0, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 13, 'FU04PC', '0', '0', '0', 0, NULL, 1, NULL, 1, '2025-03-01 12:12:16.000', 0.1, 1, NULL, '2026-03-03 18:59:45.000', 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, '192.168.31.254', NULL, 0, '0', NULL, 2, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 1}, \"gpt-4o\": {\"per\": \"3h\", \"limit\": 1}, \"grok-3\": {\"per\": \"1s\", \"limit\": 1}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 1}, \"gpt-4-5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 1}, \"research\": {\"per\": \"1s\", \"limit\": 1}, \"claude-3.5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 1}, \"third-models\": {\"per\": \"\", \"limit\": 0}}', '9399d7835bab44c893bb3f2154cfed1b');
INSERT INTO `chatgpt_user` VALUES (1895390234955325444, '2025-03-10 15:11:26.399', '2025-03-10 15:11:26.399', NULL, 'aebe543d-53e2-45b5-bb1e-9d781d4e10c8', '2025-03-10 15:11:26.384', 0, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 13, 'FILVL3', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, 0.1, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 2, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '52cc8417a71a429fb27aeaea64ef4adc');
INSERT INTO `chatgpt_user` VALUES (1895390234955325445, '2025-03-10 21:24:53.855', '2025-03-10 21:25:03.599', NULL, '8ead6988-cc97-40cc-815d-7930567fb3f7', '2025-06-18 21:25:03.600', 1, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 13, '10SKDV', '0', '0', '0', 0, NULL, 1, NULL, 1, '2025-03-10 21:25:03.601', 0.1, 3, NULL, '2025-06-18 21:25:03.600', 0, NULL, 20, '1h', 0, '2025-06-18 21:25:03.600', 0, '2025-06-18 21:25:03.600', 0, '192.168.31.254', NULL, 0, '0', NULL, 2, '2025-06-18 21:25:03.600', '2025-06-18 21:25:03.600', '{\"o3\": {\"per\": \"1w\", \"limit\": 1}, \"gpt-4o\": {\"per\": \"3h\", \"limit\": 1}, \"grok-3\": {\"per\": \"1s\", \"limit\": 1}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 1}, \"gpt-4-5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 1}, \"research\": {\"per\": \"1s\", \"limit\": 1}, \"claude-3.5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 1}, \"third-models\": {\"per\": \"\", \"limit\": 0}}', 'ad14a7056c334f72bccea2c2b3602276');
INSERT INTO `chatgpt_user` VALUES (1895390234955325446, '2025-03-10 21:28:19.203', '2025-03-10 21:41:37.400', NULL, '9794cf2d-d13f-446d-ab2d-d8aec44d2bd2', '2025-05-09 21:28:29.116', 1, '', 'e10adc3949ba59abbe56e057f20f883e', 0, NULL, NULL, NULL, 20, '1h', 13, '0IHCIQ', '0', '0', '0', 0, NULL, 1, NULL, 1, '2025-03-10 21:43:38.503', 0.1, 3, NULL, '2025-05-09 21:28:29.116', 0, NULL, 20, '1h', 0, '2025-05-09 21:28:29.116', 0, '2025-04-09 21:28:29.116', 0, '192.168.31.254', NULL, 0, '0', NULL, 2, '2025-05-09 21:28:29.116', NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 1}, \"gpt-4o\": {\"per\": \"3h\", \"limit\": 1}, \"grok-3\": {\"per\": \"1s\", \"limit\": 1}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 1}, \"gpt-4-5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 1}, \"research\": {\"per\": \"1s\", \"limit\": 1}, \"claude-3.5\": {\"per\": \"1s\", \"limit\": 1}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 1}, \"third-models\": {\"per\": \"\", \"limit\": 0}}', 'bd4927a82fdf423fa518b996b8711858');
INSERT INTO `chatgpt_user` VALUES (1895390234955325447, '2025-04-13 20:05:47.000', '2025-04-13 20:05:47.000', NULL, 'y343256451', '2025-02-21 14:25:44.000', 1, NULL, '760db71eb0cc545497c9f97badfc66cc', 0, NULL, '<EMAIL>', NULL, 20, '1h', 0, 'V4rL2', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, NULL, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '71400ae5c86e405ab097c544a36bb436');
INSERT INTO `chatgpt_user` VALUES (1895390234955325448, '2024-11-28 14:16:36.000', '2025-04-12 00:00:00.475', NULL, 'nonohu', '2025-04-18 16:28:12.504', 0, NULL, 'fbb1f957db66e2fd6edad8fc93826d38', 0, NULL, 'nonohu', NULL, 20, '1h', 0, NULL, '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, NULL, 1, NULL, '2026-02-09 00:02:02.000', 1, NULL, 20, '1h', 0, NULL, 0, '2026-02-09 00:02:02.000', 0, NULL, NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '2117afe2-3380-4d3a-bf6a-10d7044712ad');
INSERT INTO `chatgpt_user` VALUES (1895390234955325449, '2025-04-20 17:00:59.920', '2025-04-20 17:00:59.922', '2025-04-20 18:00:54.000', 'editor', '2026-04-20 17:00:58.000', 0, NULL, '96e79218965eb72c92a549dd5a330112', 0, NULL, NULL, NULL, 20, '1h', 0, 'DYO3L9', '0', '0', '0', 0, NULL, 1, NULL, 1, NULL, NULL, 1, NULL, NULL, 0, NULL, 20, '1h', 0, NULL, 0, NULL, 0, NULL, NULL, 0, '0', NULL, 1, NULL, NULL, '{\"o3\": {\"per\": \"1w\", \"limit\": 5}, \"gpt-4o\": {\"per\": \"1h\", \"limit\": 20}, \"grok-3\": {\"per\": \"1d\", \"limit\": 50}, \"o1-pro\": {\"per\": \"1d\", \"limit\": 10}, \"gpt-4-5\": {\"per\": \"1w\", \"limit\": 50}, \"o4-mini\": {\"per\": \"1d\", \"limit\": 10}, \"research\": {\"per\": \"1d\", \"limit\": 50}, \"claude-3.5\": {\"per\": \"1h\", \"limit\": 20}, \"o4-mini-high\": {\"per\": \"1w\", \"limit\": 5}, \"third-models\": {\"per\": \"1h\", \"limit\": 50}}', '639bbf1175604935af2d79ba7f81ddc9');

-- ----------------------------
-- Table structure for chatgpt_withdrawals
-- ----------------------------
DROP TABLE IF EXISTS `chatgpt_withdrawals`;
CREATE TABLE `chatgpt_withdrawals`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `withdrawalTime` datetime(3) NOT NULL COMMENT '提现时间',
  `username` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提现人',
  `withdrawalMoney` double NOT NULL COMMENT '提现金额',
  `userId` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提现人id',
  `contact` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系方式',
  `withdrawalQrcode` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提现二维码',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '提现状态（0：未处理，1：提现成功）',
  `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatgpt_withdrawals
-- ----------------------------
INSERT INTO `chatgpt_withdrawals` VALUES (7, '2024-10-19 00:29:14.041', '2024-10-19 00:29:14.041', '2024-10-19 00:29:14.036', 'admin', 19.9, '999999999', '123', '9cecffbc-8631-43d8-a4ef-596021d842ca.jpg', 2, '驳回原因：二维码错误');
INSERT INTO `chatgpt_withdrawals` VALUES (8, '2024-10-19 00:31:39.198', '2024-10-19 00:31:39.198', '2024-10-19 00:31:39.198', 'admin', 19.9, '999999999', '123', 'e96e63a0-88ff-4e40-8878-9f7e474e854a.jpg', 1, '打款成功，请注意查收');
INSERT INTO `chatgpt_withdrawals` VALUES (9, '2024-10-19 14:16:56.080', '2024-10-19 14:16:56.080', '2024-10-19 14:16:56.076', 'admin', 0.01, '999999999', '123', '29794c6e-5d0c-4266-b8af-c7fd6ddc60e8.jpg', 1, '打款成功，请注意查收');

-- ----------------------------
-- Table structure for claude_session
-- ----------------------------
DROP TABLE IF EXISTS `claude_session`;
CREATE TABLE `claude_session`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `email` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态',
  `isPro` tinyint(1) DEFAULT 0 COMMENT 'isPro',
  `carID` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '展示ID',
  `officialSession` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '官方session',
  `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `remaining` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `resetsAt` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_chatgpt_session_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of claude_session
-- ----------------------------
INSERT INTO `claude_session` VALUES (27, '2025-02-19 23:26:32.000', '2025-02-19 23:26:32.000', NULL, '123', 0, 1, 'LyuAISzp', '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', 'Unexpected character (\'<\' (code 60)): expected a valid value (JSON String, Number, Array, Object or token \'null\', \'true\' or \'false\')\n at [Source: (StringReader); line: 1, column: 2]', NULL, NULL);
INSERT INTO `claude_session` VALUES (30, '2025-04-20 14:21:30.462', '2025-04-20 14:21:30.462', NULL, '123', 0, 1, 'rMRKQCN9', '123', '', NULL, NULL);

-- ----------------------------
-- Table structure for dict_info
-- ----------------------------
DROP TABLE IF EXISTS `dict_info`;
CREATE TABLE `dict_info`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `typeId` int(0) NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `orderNum` int(0) NOT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parentId` int(0) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dict_info_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dict_info
-- ----------------------------
INSERT INTO `dict_info` VALUES (1, '2024-01-01 13:14:59.413', '2024-01-01 13:14:59.413', NULL, 1, '衣服', 2, NULL, NULL);
INSERT INTO `dict_info` VALUES (2, '2024-01-01 13:14:59.413', '2024-01-01 13:14:59.413', NULL, 1, '裤子', 1, NULL, NULL);
INSERT INTO `dict_info` VALUES (3, '2024-01-01 13:14:59.413', '2024-01-01 13:14:59.413', NULL, 1, '鞋子', 3, NULL, NULL);
INSERT INTO `dict_info` VALUES (4, '2024-01-01 13:14:59.413', '2024-01-01 13:14:59.413', NULL, 2, '闪酷', 2, NULL, NULL);
INSERT INTO `dict_info` VALUES (5, '2024-01-01 13:14:59.413', '2024-01-01 13:14:59.413', NULL, 2, 'COOL', 1, NULL, NULL);

-- ----------------------------
-- Table structure for dict_type
-- ----------------------------
DROP TABLE IF EXISTS `dict_type`;
CREATE TABLE `dict_type`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dict_type_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dict_type
-- ----------------------------
INSERT INTO `dict_type` VALUES (1, '2024-01-01 13:14:59.442', '2024-01-01 13:14:59.442', NULL, '类别', 'type');
INSERT INTO `dict_type` VALUES (2, '2024-01-01 13:14:59.442', '2024-01-01 13:14:59.442', NULL, '品牌', 'brand');

-- ----------------------------
-- Table structure for draw_record
-- ----------------------------
DROP TABLE IF EXISTS `draw_record`;
CREATE TABLE `draw_record`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '对话内容',
  `draw_type` tinyint(1) NOT NULL COMMENT '画图方式：1：生图、2：改图',
  `task_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务id',
  `task_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务状态（PENDING，COMPLETED，FAILED）',
  `image_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `model` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模型',
  `image_size` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片大小',
  `num` tinyint(0) DEFAULT NULL COMMENT '图片数量',
  `response` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '响应结果',
  `response_format` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片响应格式：url或者b64_json',
  `created_at` datetime(3) NOT NULL COMMENT '创建时间',
  `updated_at` datetime(3) NOT NULL COMMENT '更新时间',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '绘图详情',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `draw_record_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of draw_record
-- ----------------------------
INSERT INTO `draw_record` VALUES (4, 999999999, '吉卜力风格', 2, '1745667900004', 'COMPLETED', 'https://filesystem.site/cdn/20250426/5UgRiRHH4piX3wkyMk72L6RZgLO9ke.png,https://filesystem.site/cdn/20250426/fRuOz6kdtyzB86Pv67n0Jv1aMoFI4T.png', 'gpt-image-1', '1024x1024', 1, '{\"created\":1745667954,\"data\":[{\"url\":\"https://filesystem.site/cdn/20250426/5UgRiRHH4piX3wkyMk72L6RZgLO9ke.png\",\"b64Json\":null}]}', 'url', '2025-04-26 19:45:00.019', '2025-04-26 19:49:54.552', '绘图完成');
INSERT INTO `draw_record` VALUES (5, 999999999, '图一举着图二', 2, '1745669458380', 'COMPLETED', 'https://filesystem.site/cdn/20250426/fRuOz6kdtyzB86Pv67n0Jv1aMoFI4T.png', 'gpt-image-1', '1024x1024', 1, '{\"created\":1745669528,\"data\":[{\"url\":\"https://filesystem.site/cdn/20250426/fRuOz6kdtyzB86Pv67n0Jv1aMoFI4T.png\",\"b64Json\":null}]}', 'url', '2025-04-26 20:10:58.383', '2025-04-26 20:14:54.540', '绘图完成');
INSERT INTO `draw_record` VALUES (6, 999999999, '把这个开工大吉和这个人放在一起，放在一张图片里不要改变他们原有风格', 2, '1745669626759', 'COMPLETED', 'https://filesystem.site/cdn/20250426/9E7mfsuw3gUeH9smHGTyIJC8mt89Qz.png', 'gpt-image-1', '1024x1024', 1, '{\"created\":1745669694,\"data\":[{\"url\":\"https://filesystem.site/cdn/20250426/9E7mfsuw3gUeH9smHGTyIJC8mt89Qz.png\",\"b64Json\":null}]}', 'url', '2025-04-26 20:13:46.762', '2025-04-26 20:19:54.533', '绘图完成');
INSERT INTO `draw_record` VALUES (7, 999999999, '生成2张不一样风格的', 2, '1745670119730', 'COMPLETED', 'https://filesystem.site/cdn/20250426/eJ4RfGhgrgxKUB5mCVcnkMdB7YQOR1.png', 'gpt-image-1', '1024x1024', 2, '{\"created\":1745670182,\"data\":[{\"url\":\"https://filesystem.site/cdn/20250426/eJ4RfGhgrgxKUB5mCVcnkMdB7YQOR1.png\",\"b64Json\":null}]}', 'url', '2025-04-26 20:21:59.732', '2025-04-26 20:24:54.533', '绘图完成');
INSERT INTO `draw_record` VALUES (8, 999999999, '3D渲染风格', 2, '1745670544543', 'COMPLETED', 'https://filesystem.site/cdn/20250426/rW6TYFJwahHhoScRkVQDtniq8Hy7XM.png', 'gpt-image-1', '1024x1024', 2, '{\"created\":1745670613,\"data\":[{\"url\":\"https://filesystem.site/cdn/20250426/rW6TYFJwahHhoScRkVQDtniq8Hy7XM.png\",\"b64Json\":null}]}', 'url', '2025-04-26 20:29:04.545', '2025-04-26 20:34:54.548', '绘图完成');
INSERT INTO `draw_record` VALUES (9, 999999999, '赛博朋克风格', 2, '1745672358384', 'COMPLETED', 'https://filesystem.site/cdn/20250426/NkWt19JVN10g5aQWXNjymo54Q41h5d.png', 'gpt-image-1', '1024x1024', 2, '{\"created\":1745672433,\"data\":[{\"url\":\"https://filesystem.site/cdn/20250426/NkWt19JVN10g5aQWXNjymo54Q41h5d.png\",\"b64Json\":null}]}', 'url', '2025-04-26 20:59:18.387', '2025-04-26 21:04:54.533', '绘图完成');
INSERT INTO `draw_record` VALUES (10, 999999999, '一只可爱的小猫咪坐在窗台上，阳光照射，背景是蓝天', 1, '1745824198647', 'COMPLETED', NULL, 'gpt-image-1', '1024x1024', 1, '{\"created\":1745824231,\"data\":[{\"url\":\"\",\"b64Json\":null}]}', NULL, '2025-04-28 15:09:58.655', '2025-04-28 15:17:14.275', '绘图失败');
INSERT INTO `draw_record` VALUES (11, 999999999, '一只可爱的小猫咪坐在窗台上，阳光照射，背景是蓝天', 1, '1745824258169', 'COMPLETED', NULL, 'gpt-image-1', '1024x1024', 1, '{\"created\":1745824295,\"data\":[{\"url\":\"\",\"b64Json\":null}]}', NULL, '2025-04-28 15:10:58.170', '2025-04-28 15:17:14.300', '绘图失败');
INSERT INTO `draw_record` VALUES (12, 999999999, '一只可爱的小猫咪坐在窗台上，阳光照射，背景是蓝天', 1, '1745824431916', 'COMPLETED', NULL, 'gpt-image-1', '1024x1024', 1, '{\"created\":1745824463,\"data\":[{\"url\":\"\",\"b64Json\":null}]}', NULL, '2025-04-28 15:13:51.917', '2025-04-28 15:17:14.291', '绘图失败');
INSERT INTO `draw_record` VALUES (13, 999999999, '宫崎骏风格的童话森林，小精灵在飞舞', 1, '1745824862452', 'FAILED', NULL, 'gpt-image-1', '1024x1024', 1, NULL, NULL, '2025-04-28 15:21:02.458', '2025-04-28 15:22:33.509', '绘图失败');
INSERT INTO `draw_record` VALUES (14, 999999999, '一只可爱的小猫咪坐在窗台上，阳光照射，背景是蓝天', 1, '1745824932324', 'FAILED', NULL, 'gpt-image-1', '1024x1024', 1, NULL, NULL, '2025-04-28 15:22:12.325', '2025-05-02 21:44:06.000', '绘图失败');
INSERT INTO `draw_record` VALUES (15, 999999999, '宫崎骏风格的童话森林，小精灵在飞舞', 1, '1745825171493', 'FAILED', NULL, 'gpt-image-1', '1024x1024', 1, NULL, NULL, '2025-04-28 15:26:11.499', '2025-04-28 15:30:50.061', '绘图失败');
INSERT INTO `draw_record` VALUES (16, 999999999, '一只可爱的小猫咪坐在窗台上，阳光照射，背景是蓝天', 1, '1745834925742', 'COMPLETED', 'b64_c6def4ae33a244aab38491e4339d6130,D:\\backup\\project\\share\\share-api\\data\\images\\20250428\\1745834925742_b64_1745835029402.png', 'gpt-image-1', '1024x1024', 1, NULL, NULL, '2025-04-28 18:08:45.749', '2025-04-28 18:10:38.196', NULL);
INSERT INTO `draw_record` VALUES (17, 999999999, '一只可爱的小猫咪坐在窗台上，阳光照射，背景是蓝天', 1, '1745847121363', 'FAILED', NULL, 'gpt-image-1', '1024x1024', 2, NULL, NULL, '2025-04-28 21:32:01.369', '2025-05-02 21:44:06.000', '绘图失败');
INSERT INTO `draw_record` VALUES (18, 999999999, '一只可爱的小猫咪坐在窗台上，阳光照射，背景是蓝天', 1, '1745847125730', 'COMPLETED', 'http://localhost:8888/api/images/20250428/1745847125730_b64_1745847165125.png,', 'gpt-image-1', '1024x1024', 1, '{\"created\":1745847160,\"data\":[{\"url\":\"http://localhost:8888/api/images/20250428/1745847125730_b64_1745847165125.png\",\"b64_json\":null}]}', NULL, '2025-04-28 21:32:05.731', '2025-04-28 21:32:45.149', '绘图完成');
INSERT INTO `draw_record` VALUES (19, 999999999, '一只可爱的小猫咪坐在窗台上，阳光照射，背景是蓝天', 1, '1745847190529', 'FAILED', NULL, 'gpt-image-1', '1024x1024', 1, NULL, NULL, '2025-04-28 21:33:10.530', '2025-05-02 21:44:06.000', '绘图失败');

-- ----------------------------
-- Table structure for grok_conversations
-- ----------------------------
DROP TABLE IF EXISTS `grok_conversations`;
CREATE TABLE `grok_conversations`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `usertoken` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户token',
  `convid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话id',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '会话标题',
  `sso` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'grok sso',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '会话内容',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_grok_conversations_deleted_at`(`deleted_at`) USING BTREE,
  INDEX `idx_grok_conversations_user_token`(`usertoken`) USING BTREE,
  INDEX `idx_grok_conversations_conv_id`(`convid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for grok_session
-- ----------------------------
DROP TABLE IF EXISTS `grok_session`;
CREATE TABLE `grok_session`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `email` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `password` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态',
  `isPro` tinyint(1) DEFAULT 0 COMMENT 'Pro',
  `officialSession` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '官方session',
  `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `sort` bigint(0) DEFAULT 0 COMMENT '排序',
  `count` bigint(0) DEFAULT 0 COMMENT '统计',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_grok_session_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for model_limits
-- ----------------------------
DROP TABLE IF EXISTS `model_limits`;
CREATE TABLE `model_limits`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `model_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型类型',
  `rate_limit` int(0) DEFAULT NULL COMMENT '速率限制',
  `rate_period` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '速率周期',
  `created_at` timestamp(0) DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp(0) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `index_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4625 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of model_limits
-- ----------------------------
INSERT INTO `model_limits` VALUES (3880, 1000000001, 'gpt-4o', 20, '1h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3881, 1000000002, 'gpt-4o', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3882, 1000000010, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3883, 1000000011, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3884, 1000000012, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3885, 1000000020, 'gpt-4o', 3, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3886, 1000000021, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3887, 1000000022, 'gpt-4o', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3888, 1000000023, 'gpt-4o', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3889, 1000000024, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3890, 1000000025, 'gpt-4o', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3891, 1000000026, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3892, 1000000027, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3893, 1000000028, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3894, 1000000029, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3895, 1000000030, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3896, 1000000031, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3897, 1000000032, 'gpt-4o', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3898, 1000000033, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3899, 1000000034, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3900, 1000000035, 'gpt-4o', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3901, 1000000036, 'gpt-4o', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3902, 1000000037, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3903, 1000000038, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3904, 1000000039, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3905, 1000000040, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3906, 1000000041, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3907, 1000000042, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3908, 1000000043, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3909, 1000000044, 'gpt-4o', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3910, 1000000045, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3911, 1000000046, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3912, 1000000047, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3913, 1000000048, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3914, 1000000049, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3915, 1000000050, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3916, 1000000051, 'gpt-4o', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3917, 1000000052, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3918, 1000000053, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3919, 1000000054, 'gpt-4o', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3920, 1000000055, 'gpt-4o', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3921, 1000000056, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3922, 1000000057, 'gpt-4o', 10, '3h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3925, 1000000001, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3926, 1000000002, 'claude-3.5', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3927, 1000000010, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3928, 1000000011, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3929, 1000000012, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3930, 1000000020, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3931, 1000000021, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3932, 1000000022, 'claude-3.5', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3933, 1000000023, 'claude-3.5', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3934, 1000000024, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3935, 1000000025, 'claude-3.5', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3936, 1000000026, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3937, 1000000027, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3938, 1000000028, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3939, 1000000029, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3940, 1000000030, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3941, 1000000031, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3942, 1000000032, 'claude-3.5', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3943, 1000000033, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3944, 1000000034, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3945, 1000000035, 'claude-3.5', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3946, 1000000036, 'claude-3.5', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3947, 1000000037, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3948, 1000000038, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3949, 1000000039, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3950, 1000000040, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3951, 1000000041, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3952, 1000000042, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3953, 1000000043, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3954, 1000000044, 'claude-3.5', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3955, 1000000045, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3956, 1000000046, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3957, 1000000047, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3958, 1000000048, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3959, 1000000049, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3960, 1000000050, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3961, 1000000051, 'claude-3.5', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3962, 1000000052, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3963, 1000000053, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3964, 1000000054, 'claude-3.5', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3965, 1000000055, 'claude-3.5', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3966, 1000000056, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3967, 1000000057, 'claude-3.5', 20, '1h', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3970, 1000000056, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3971, 1000000057, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3973, 1000000041, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3974, 1000000042, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3975, 1000000043, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3976, 1000000052, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3977, 1000000051, 'o1', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3978, 1000000044, 'o1', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3979, 1000000045, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3980, 1000000046, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3981, 1000000047, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3982, 1000000048, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3983, 1000000049, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3984, 1000000050, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3985, 1000000053, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3986, 1000000054, 'o1', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3987, 1000000055, 'o1', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3988, 1000000031, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3989, 1000000032, 'o1', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3990, 1000000033, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3991, 1000000034, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3992, 1000000035, 'o1', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3993, 1000000036, 'o1', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (3994, 1000000037, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3995, 1000000038, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3996, 1000000039, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3997, 1000000040, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3998, 1000000021, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (3999, 1000000022, 'o1', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4000, 1000000023, 'o1', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4001, 1000000024, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4002, 1000000025, 'o1', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4003, 1000000026, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4004, 1000000027, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4005, 1000000028, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4006, 1000000029, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4007, 1000000030, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4008, 1000000001, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4009, 1000000002, 'o1', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4010, 1000000010, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4011, 1000000011, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4012, 1000000012, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4013, 1000000020, 'o1', 5, '1w', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4015, 1000000056, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4016, 1000000057, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4018, 1000000041, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4019, 1000000042, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4020, 1000000043, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4021, 1000000052, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4022, 1000000051, 'o1-mini', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4023, 1000000044, 'o1-mini', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4024, 1000000045, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4025, 1000000046, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4026, 1000000047, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4027, 1000000048, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4028, 1000000049, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4029, 1000000050, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4030, 1000000053, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4031, 1000000054, 'o1-mini', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4032, 1000000055, 'o1-mini', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4033, 1000000031, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4034, 1000000032, 'o1-mini', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4035, 1000000033, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4036, 1000000034, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4037, 1000000035, 'o1-mini', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4038, 1000000036, 'o1-mini', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4039, 1000000037, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4040, 1000000038, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4041, 1000000039, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4042, 1000000040, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4043, 1000000021, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4044, 1000000022, 'o1-mini', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4045, 1000000023, 'o1-mini', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4046, 1000000024, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4047, 1000000025, 'o1-mini', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4048, 1000000026, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4049, 1000000027, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4050, 1000000028, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4051, 1000000029, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4052, 1000000030, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4053, 1000000001, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4054, 1000000002, 'o1-mini', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4055, 1000000010, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4056, 1000000011, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4057, 1000000012, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4058, 1000000020, 'o1-mini', 10, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4060, 1000000056, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4061, 1000000057, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4063, 1000000041, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4064, 1000000042, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4065, 1000000043, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4066, 1000000052, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4067, 1000000051, 'o1-pro', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4068, 1000000044, 'o1-pro', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4069, 1000000045, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4070, 1000000046, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4071, 1000000047, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4072, 1000000048, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4073, 1000000049, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4074, 1000000050, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4075, 1000000053, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4076, 1000000054, 'o1-pro', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4077, 1000000055, 'o1-pro', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4078, 1000000031, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4079, 1000000032, 'o1-pro', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4080, 1000000033, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4081, 1000000034, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4082, 1000000035, 'o1-pro', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4083, 1000000036, 'o1-pro', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4084, 1000000037, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4085, 1000000038, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4086, 1000000039, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4087, 1000000040, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4088, 1000000021, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4089, 1000000022, 'o1-pro', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4090, 1000000023, 'o1-pro', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4091, 1000000024, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4092, 1000000025, 'o1-pro', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4093, 1000000026, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4094, 1000000027, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4095, 1000000028, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4096, 1000000029, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4097, 1000000030, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4098, 1000000001, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4099, 1000000002, 'o1-pro', 200, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4100, 1000000010, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4101, 1000000011, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4102, 1000000012, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4103, 1000000020, 'o1-pro', 10, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4135, 1000000056, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4136, 1000000057, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4138, 1000000041, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4139, 1000000042, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4140, 1000000043, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4141, 1000000052, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4142, 1000000051, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4143, 1000000044, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4144, 1000000045, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4145, 1000000046, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4146, 1000000047, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4147, 1000000048, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4148, 1000000049, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4149, 1000000050, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4150, 1000000053, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4151, 1000000054, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4152, 1000000055, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4153, 1000000031, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4154, 1000000032, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4155, 1000000033, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4156, 1000000034, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4157, 1000000035, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4158, 1000000036, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4159, 1000000037, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4160, 1000000038, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4161, 1000000039, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4162, 1000000040, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4163, 1000000021, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4164, 1000000022, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4165, 1000000023, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4166, 1000000024, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4167, 1000000025, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4168, 1000000026, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4169, 1000000027, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4170, 1000000028, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4171, 1000000029, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4172, 1000000030, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4173, 1000000001, 'o3-mini', 10, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4174, 1000000002, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4175, 1000000010, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4176, 1000000011, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4177, 1000000012, 'o3-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4178, 1000000020, 'o3-mini', 10, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4180, 1000000056, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4181, 1000000057, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4183, 1000000041, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4184, 1000000042, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4185, 1000000043, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4186, 1000000052, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4187, 1000000051, 'o3-mini-high', 30, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4188, 1000000044, 'o3-mini-high', 30, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4189, 1000000045, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4190, 1000000046, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4191, 1000000047, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4192, 1000000048, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4193, 1000000049, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4194, 1000000050, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4195, 1000000053, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4196, 1000000054, 'o3-mini-high', 30, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4197, 1000000055, 'o3-mini-high', 30, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4198, 1000000031, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4199, 1000000032, 'o3-mini-high', 30, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4200, 1000000033, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4201, 1000000034, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4202, 1000000035, 'o3-mini-high', 30, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4203, 1000000036, 'o3-mini-high', 30, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4204, 1000000037, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4205, 1000000038, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4206, 1000000039, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4207, 1000000040, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4208, 1000000021, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4209, 1000000022, 'o3-mini-high', 30, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4210, 1000000023, 'o3-mini-high', 30, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4211, 1000000024, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4212, 1000000025, 'o3-mini-high', 30, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4213, 1000000026, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4214, 1000000027, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4215, 1000000028, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4216, 1000000029, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4217, 1000000030, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4218, 1000000001, 'o3-mini-high', 5, '1w', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4219, 1000000002, 'o3-mini-high', 30, '1d', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4220, 1000000010, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4221, 1000000011, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4222, 1000000012, 'o3-mini-high', 50, '1w', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4223, 1000000020, 'o3-mini-high', 5, '1w', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4225, 1000000056, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4226, 1000000057, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4228, 1000000041, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4229, 1000000042, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4230, 1000000043, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4231, 1000000052, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4232, 1000000051, 'r1-mini', 60, '3h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4233, 1000000044, 'r1-mini', 60, '3h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4234, 1000000045, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4235, 1000000046, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4236, 1000000047, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4237, 1000000048, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4238, 1000000049, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4239, 1000000050, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4240, 1000000053, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4241, 1000000054, 'r1-mini', 60, '3h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4242, 1000000055, 'r1-mini', 60, '3h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4243, 1000000031, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4244, 1000000032, 'r1-mini', 60, '3h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4245, 1000000033, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4246, 1000000034, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4247, 1000000035, 'r1-mini', 60, '3h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4248, 1000000036, 'r1-mini', 60, '3h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4249, 1000000037, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4250, 1000000038, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4251, 1000000039, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4252, 1000000040, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4253, 1000000021, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4254, 1000000022, 'r1-mini', 60, '3h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4255, 1000000023, 'r1-mini', 60, '3h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4256, 1000000024, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4257, 1000000025, 'r1-mini', 60, '3h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4258, 1000000026, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4259, 1000000027, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4260, 1000000028, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4261, 1000000029, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4262, 1000000030, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4263, 1000000001, 'r1-mini', 5, '1w', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4264, 1000000002, 'r1-mini', 60, '3h', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4265, 1000000010, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4266, 1000000011, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4267, 1000000012, 'r1-mini', 50, '1d', '2025-02-10 00:39:52', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4268, 1000000020, 'r1-mini', 5, '1w', '2025-02-10 00:39:52', '2025-02-10 00:39:52');
INSERT INTO `model_limits` VALUES (4317, 1000000058, 'gpt-4o', 10, '3h', '2025-02-23 19:37:20', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4318, 1000000058, 'o1', 5, '1w', '2025-02-23 19:37:20', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4319, 1000000058, 'o1-mini', 10, '1d', '2025-02-23 19:37:20', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4320, 1000000058, 'o1-pro', 10, '1d', '2025-02-23 19:37:20', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4321, 1000000058, 'o3-mini', 50, '1d', '2025-02-23 19:37:20', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4322, 1000000058, 'o3-mini-high', 50, '1w', '2025-02-23 19:37:20', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4323, 1000000058, 'r1-mini', 50, '1d', '2025-02-23 19:37:20', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4324, 1000000058, 'claude-3.5', 20, '1h', '2025-02-23 19:37:20', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4325, 1000000060, 'o1-pro', 10, '1d', '2025-02-23 21:15:48', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4326, 1000000060, 'claude-3.5', 20, '1h', '2025-02-23 21:15:48', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4327, 1000000060, 'o1', 5, '1w', '2025-02-23 21:15:48', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4328, 1000000060, 'r1-mini', 50, '1d', '2025-02-23 21:15:48', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4329, 1000000060, 'o3-mini', 50, '1d', '2025-02-23 21:15:48', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4330, 1000000060, 'o1-mini', 10, '1d', '2025-02-23 21:15:48', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4331, 1000000060, 'o3-mini-high', 50, '1w', '2025-02-23 21:15:48', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4332, 1000000060, 'gpt-4o', 10, '3h', '2025-02-23 21:15:48', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4333, 1000000061, 'o1-pro', 10, '1d', '2025-02-23 21:27:44', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4334, 1000000061, 'claude-3.5', 20, '1h', '2025-02-23 21:27:44', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4335, 1000000061, 'o1', 5, '1w', '2025-02-23 21:27:44', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4336, 1000000061, 'r1-mini', 50, '1d', '2025-02-23 21:27:44', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4337, 1000000061, 'o3-mini', 50, '1d', '2025-02-23 21:27:44', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4338, 1000000061, 'o1-mini', 10, '1d', '2025-02-23 21:27:44', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4339, 1000000061, 'o3-mini-high', 50, '1w', '2025-02-23 21:27:44', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4340, 1000000061, 'gpt-4o', 10, '3h', '2025-02-23 21:27:44', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4365, 999999999, 'o1', 200, '1d', '2025-02-24 19:35:21', '2025-02-24 19:35:21');
INSERT INTO `model_limits` VALUES (4366, 999999999, 'gpt-4o', 200, '1d', '2025-02-24 19:35:21', '2025-02-24 19:35:21');
INSERT INTO `model_limits` VALUES (4367, 999999999, 'o1-pro', 200, '1d', '2025-02-24 19:35:21', '2025-02-24 19:35:21');
INSERT INTO `model_limits` VALUES (4368, 999999999, 'o1-mini', 200, '1d', '2025-02-24 19:35:21', '2025-02-24 19:35:21');
INSERT INTO `model_limits` VALUES (4369, 999999999, 'o3-mini', 50, '1d', '2025-02-24 19:35:21', '2025-02-24 19:35:21');
INSERT INTO `model_limits` VALUES (4370, 999999999, 'r1-mini', 60, '3h', '2025-02-24 19:35:21', '2025-02-24 19:35:21');
INSERT INTO `model_limits` VALUES (4371, 999999999, 'claude-3.5', 200, '1d', '2025-02-24 19:35:21', '2025-02-24 19:35:21');
INSERT INTO `model_limits` VALUES (4372, 999999999, 'o3-mini-high', 30, '1d', '2025-02-24 19:35:21', '2025-02-24 19:35:21');
INSERT INTO `model_limits` VALUES (4373, 1000000062, 'o1-pro', 10, '1d', '2025-02-25 17:17:54', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4374, 1000000062, 'claude-3.5', 20, '1h', '2025-02-25 17:17:54', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4375, 1000000062, 'o1', 5, '1w', '2025-02-25 17:17:54', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4376, 1000000062, 'r1-mini', 50, '1d', '2025-02-25 17:17:54', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4377, 1000000062, 'o3-mini', 50, '1d', '2025-02-25 17:17:54', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4378, 1000000062, 'o1-mini', 10, '1d', '2025-02-25 17:17:54', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4379, 1000000062, 'o3-mini-high', 50, '1w', '2025-02-25 17:17:54', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4380, 1000000062, 'gpt-4o', 10, '3h', '2025-02-25 17:17:54', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4485, 1000000063, 'gpt-4o', 10, '3h', '2025-02-28 16:10:24', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4486, 1000000063, 'o1', 5, '1w', '2025-02-28 16:10:24', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4487, 1000000063, 'o1-mini', 10, '1d', '2025-02-28 16:10:24', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4488, 1000000063, 'o1-pro', 10, '1d', '2025-02-28 16:10:24', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4489, 1000000063, 'o3-mini', 50, '1d', '2025-02-28 16:10:24', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4490, 1000000063, 'o3-mini-high', 50, '1w', '2025-02-28 16:10:24', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4491, 1000000063, 'r1-mini', 50, '1d', '2025-02-28 16:10:24', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4492, 1000000063, 'claude-3.5', 20, '1h', '2025-02-28 16:10:24', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4501, 1000000064, 'gpt-4o', 10, '3h', '2025-02-28 16:10:37', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4502, 1000000064, 'o1', 5, '1w', '2025-02-28 16:10:37', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4503, 1000000064, 'o1-mini', 10, '1d', '2025-02-28 16:10:37', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4504, 1000000064, 'o1-pro', 10, '1d', '2025-02-28 16:10:37', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4505, 1000000064, 'o3-mini', 50, '1d', '2025-02-28 16:10:37', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4506, 1000000064, 'o3-mini-high', 50, '1w', '2025-02-28 16:10:37', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4507, 1000000064, 'r1-mini', 50, '1d', '2025-02-28 16:10:37', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4508, 1000000064, 'claude-3.5', 20, '1h', '2025-02-28 16:10:37', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4517, 1895387634700754945, 'gpt-4o', 10, '3h', '2025-02-28 16:16:41', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4518, 1895387634700754945, 'o1', 5, '1w', '2025-02-28 16:16:41', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4519, 1895387634700754945, 'o1-mini', 10, '1d', '2025-02-28 16:16:41', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4520, 1895387634700754945, 'o1-pro', 10, '1d', '2025-02-28 16:16:41', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4521, 1895387634700754945, 'o3-mini', 50, '1d', '2025-02-28 16:16:41', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4522, 1895387634700754945, 'o3-mini-high', 50, '1w', '2025-02-28 16:16:41', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4523, 1895387634700754945, 'r1-mini', 50, '1d', '2025-02-28 16:16:41', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4524, 1895387634700754945, 'claude-3.5', 20, '1h', '2025-02-28 16:16:41', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4533, 1895390234955325442, 'gpt-4o', 10, '3h', '2025-02-28 16:27:26', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4534, 1895390234955325442, 'o1', 5, '1w', '2025-02-28 16:27:26', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4535, 1895390234955325442, 'o1-mini', 10, '1d', '2025-02-28 16:27:26', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4536, 1895390234955325442, 'o1-pro', 10, '1d', '2025-02-28 16:27:26', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4537, 1895390234955325442, 'o3-mini', 50, '1d', '2025-02-28 16:27:26', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4538, 1895390234955325442, 'o3-mini-high', 50, '1w', '2025-02-28 16:27:26', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4539, 1895390234955325442, 'r1-mini', 50, '1d', '2025-02-28 16:27:26', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4540, 1895390234955325442, 'claude-3.5', 20, '1h', '2025-02-28 16:27:26', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4589, 1895388644714323969, 'o1', 5, '1w', '2025-02-28 17:05:04', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4590, 1895388644714323969, 'gpt-4o', 10, '3h', '2025-02-28 17:05:04', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4591, 1895388644714323969, 'o1-pro', 10, '1d', '2025-02-28 17:05:04', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4592, 1895388644714323969, 'o1-mini', 10, '1d', '2025-02-28 17:05:04', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4593, 1895388644714323969, 'o3-mini', 50, '1d', '2025-02-28 17:05:04', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4594, 1895388644714323969, 'r1-mini', 50, '1d', '2025-02-28 17:05:04', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4595, 1895388644714323969, 'claude-3.5', 20, '1h', '2025-02-28 17:05:04', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4596, 1895388644714323969, 'o3-mini-high', 50, '1w', '2025-02-28 17:05:04', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4605, 1895390234955325443, 'gpt-4o', 10, '3h', '2025-03-03 18:59:50', '2025-03-03 18:59:50');
INSERT INTO `model_limits` VALUES (4606, 1895390234955325443, 'o1', 5, '1w', '2025-03-03 18:59:50', '2025-03-03 18:59:50');
INSERT INTO `model_limits` VALUES (4607, 1895390234955325443, 'o1-mini', 10, '1d', '2025-03-03 18:59:50', '2025-03-03 18:59:50');
INSERT INTO `model_limits` VALUES (4608, 1895390234955325443, 'o1-pro', 10, '1d', '2025-03-03 18:59:50', '2025-03-03 18:59:50');
INSERT INTO `model_limits` VALUES (4609, 1895390234955325443, 'o3-mini', 50, '1d', '2025-03-03 18:59:50', '2025-03-03 18:59:50');
INSERT INTO `model_limits` VALUES (4610, 1895390234955325443, 'o3-mini-high', 50, '1w', '2025-03-03 18:59:50', '2025-03-03 18:59:50');
INSERT INTO `model_limits` VALUES (4611, 1895390234955325443, 'r1-mini', 50, '1d', '2025-03-03 18:59:50', '2025-03-03 18:59:50');
INSERT INTO `model_limits` VALUES (4612, 1895390234955325443, 'claude-3.5', 20, '1h', '2025-03-03 18:59:50', '2025-03-03 18:59:50');
INSERT INTO `model_limits` VALUES (4613, 1000000059, 'gpt-4o', 10, '3h', '2025-03-03 22:36:02', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4614, 1000000059, 'o1', 5, '1w', '2025-03-03 22:36:02', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4615, 1000000059, 'o1-mini', 10, '1d', '2025-03-03 22:36:02', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4616, 1000000059, 'o1-pro', 10, '1d', '2025-03-03 22:36:02', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4617, 1000000059, 'o3-mini', 50, '1d', '2025-03-03 22:36:02', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4618, 1000000059, 'o3-mini-high', 50, '1w', '2025-03-03 22:36:02', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4619, 1000000059, 'r1-mini', 50, '1d', '2025-03-03 22:36:02', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4620, 1000000059, 'claude-3.5', 20, '1h', '2025-03-03 22:36:02', '2025-03-08 00:00:00');
INSERT INTO `model_limits` VALUES (4621, 1895390234955325444, 'claude-3.5', 20, '1h', '2025-03-10 15:12:00', '2025-03-10 15:12:00');
INSERT INTO `model_limits` VALUES (4622, 1895390234955325444, 'o1-mini', 10, '1d', '2025-03-10 15:12:00', '2025-03-10 15:12:00');
INSERT INTO `model_limits` VALUES (4623, 1895390234955325444, 'o1-pro', 10, '1d', '2025-03-10 15:12:00', '2025-03-10 15:12:00');
INSERT INTO `model_limits` VALUES (4624, 1895390234955325444, 'o1', 5, '1w', '2025-03-10 15:12:00', '2025-03-10 15:12:00');
INSERT INTO `model_limits` VALUES (4625, 1895390234955325444, 'gpt-4o', 20, '1h', '2025-03-10 15:12:00', '2025-03-10 15:12:00');

-- ----------------------------
-- Table structure for quota_change_record
-- ----------------------------
DROP TABLE IF EXISTS `quota_change_record`;
CREATE TABLE `quota_change_record`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `change_type` tinyint(0) NOT NULL COMMENT '变更类型：1-套餐购买，2-绘图消耗，3-管理员调整，4-套餐重置',
  `change_amount` bigint(0) NOT NULL COMMENT '变更数量，正数为增加，负数为减少',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `created_at` datetime(3) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_create_time`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '配额变更记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of quota_change_record
-- ----------------------------
INSERT INTO `quota_change_record` VALUES (1, 999999999, 2, -1, NULL, '2025-04-26 01:27:44.829');
INSERT INTO `quota_change_record` VALUES (2, 999999999, 1, 1000, '变更说明：购买1000次绘画额度', '2025-04-26 01:48:20.522');
INSERT INTO `quota_change_record` VALUES (3, 999999999, 1, 1000, '变更说明：购买1000次绘画额度', '2025-04-26 01:56:09.892');
INSERT INTO `quota_change_record` VALUES (4, 999999999, 2, -1, '变更说明：绘图额度消耗', '2025-04-26 01:59:43.034');
INSERT INTO `quota_change_record` VALUES (5, 999999999, 1, 1000, '变更说明：购买1000次绘画额度', '2025-04-26 02:00:21.651');
INSERT INTO `quota_change_record` VALUES (7, 999999999, 2, -1, '变更说明：绘图额度消耗', '2025-04-26 11:45:52.298');
INSERT INTO `quota_change_record` VALUES (8, 999999999, 2, -1, '变更说明：绘图额度消耗', '2025-04-26 19:36:51.335');
INSERT INTO `quota_change_record` VALUES (9, 999999999, 2, -1, '变更说明：绘图额度消耗', '2025-04-26 19:45:00.046');
INSERT INTO `quota_change_record` VALUES (10, 999999999, 2, -1, '变更说明：绘图额度消耗', '2025-04-26 20:10:58.397');
INSERT INTO `quota_change_record` VALUES (11, 999999999, 2, -1, '变更说明：绘图额度消耗', '2025-04-26 20:13:46.776');
INSERT INTO `quota_change_record` VALUES (12, 999999999, 2, -1, '变更说明：绘图额度消耗', '2025-04-26 20:21:59.744');
INSERT INTO `quota_change_record` VALUES (15, 999999999, 2, -1, '变更说明：绘图额度消耗', '2025-04-28 15:09:58.696');
INSERT INTO `quota_change_record` VALUES (16, 999999999, 2, -1, '变更说明：绘图额度消耗', '2025-04-28 15:10:58.190');
INSERT INTO `quota_change_record` VALUES (17, 999999999, 2, -1, '变更说明：绘图额度消耗', '2025-04-28 15:13:51.937');
INSERT INTO `quota_change_record` VALUES (18, 999999999, 2, -1, '变更说明：绘图额度消耗', '2025-04-28 15:21:02.484');
INSERT INTO `quota_change_record` VALUES (19, 999999999, 2, -1, '变更说明：绘图额度消耗', '2025-04-28 15:22:12.339');
INSERT INTO `quota_change_record` VALUES (20, 999999999, 2, -1, '变更说明：绘图额度消耗', '2025-04-28 15:26:11.524');

-- ----------------------------
-- Table structure for risk_control_record
-- ----------------------------
DROP TABLE IF EXISTS `risk_control_record`;
CREATE TABLE `risk_control_record`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '对话内容',
  `keyword` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关键词',
  `content_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '内容类型',
  `created_at` datetime(3) NOT NULL COMMENT '创建时间',
  `updated_at` datetime(3) NOT NULL COMMENT '更新时间',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of risk_control_record
-- ----------------------------
INSERT INTO `risk_control_record` VALUES (1, '1', '1', '啊实打实大师大都是啊实打实大师大都是啊实打实大师大都是啊实打实大师大都是啊实打实大师大都是啊实打实大师大都是啊实打实大师大都是啊实打实大师大都是啊实打实大师大都是啊实打实大师大都是啊实打实大师大都是啊实打实大师大都是啊实打实大师大都是啊实打实大师大都是啊实打实大师大都是', '1', '2025-04-08 09:37:04.000', '2025-04-08 09:37:04.000', NULL);

-- ----------------------------
-- Table structure for sign_in_records
-- ----------------------------
DROP TABLE IF EXISTS `sign_in_records`;
CREATE TABLE `sign_in_records`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `userId` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '签到人id',
  `signedDate` date NOT NULL COMMENT '签到日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sign_in_records
-- ----------------------------
INSERT INTO `sign_in_records` VALUES (17, '2025-01-04 16:41:17.770', '1000000036', '2025-01-04');
INSERT INTO `sign_in_records` VALUES (18, '2025-01-07 19:59:59.838', '999999999', '2025-01-07');
INSERT INTO `sign_in_records` VALUES (19, '2025-01-09 19:08:45.089', '999999999', '2025-01-09');
INSERT INTO `sign_in_records` VALUES (20, '2025-01-13 20:27:11.213', '999999999', '2025-01-13');
INSERT INTO `sign_in_records` VALUES (21, '2025-02-06 14:34:08.314', '1000000058', '2025-02-06');

-- ----------------------------
-- Table structure for space_info
-- ----------------------------
DROP TABLE IF EXISTS `space_info`;
CREATE TABLE `space_info`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地址',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型',
  `classifyId` bigint(0) DEFAULT NULL COMMENT '分类ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_space_info_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for space_type
-- ----------------------------
DROP TABLE IF EXISTS `space_type`;
CREATE TABLE `space_type`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类别名称 ',
  `parentId` int(0) DEFAULT NULL COMMENT '父分类ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_space_type_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_sys_config
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_config`;
CREATE TABLE `t_sys_config`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `param_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '系统内置: Y N ',
  `param_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT 'key',
  `param_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT 'value',
  `param_name` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT 'name',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT '0' COMMENT '状态   0：禁用   1：启用',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `valid` int(0) DEFAULT 1 COMMENT '有效状态：0->无效；1->有效',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `param_key_idx`(`param_key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '系统配置信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_sys_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_operation_log`;
CREATE TABLE `t_sys_operation_log`  (
  `id` bigint unsigned NOT NULL,
  `business_type` tinyint(0) NOT NULL COMMENT '类型（0=其它,1=新增,2=修改,3=删除,4=授权,5=导出,6=导入,7=强退,8=生成代码,9=清空数据）',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '请求方式',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '描述',
  `req_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '请求IP',
  `req_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '请求信息',
  `resp` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '响应信息',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '错误消息',
  `create_id` bigint(0) DEFAULT NULL COMMENT '创建者ID',
  `create_by` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '创建者名称',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_id` bigint(0) DEFAULT NULL COMMENT '修改者ID',
  `update_by` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '修改者名称',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(0) DEFAULT NULL COMMENT '是否已删除：0->未删除；1->已删除',
  `delete_time` datetime(0) DEFAULT NULL COMMENT '删除时间',
  `oper_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '操作人',
  `oper_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '操作地点',
  `status` tinyint(0) DEFAULT NULL COMMENT '状态 1-可用，2-禁用',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `t_sys_operation_log_id_idx`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 68 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '系统操作日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_sys_operation_log
-- ----------------------------
INSERT INTO `t_sys_operation_log` VALUES (1, 10, 'org.seven.share.controller.admin.ChatGptSessionController.list()', 'GET', '查询gpt账号列表', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":[{\"label\":\"车号：tjJt9LMm[Free账号]\",\"id\":32},{\"label\":\"车号：yNhTBlbv[Free账号]\",\"id\":33},{\"label\":\"车号：6q7XQHlu[Free账号]\",\"id\":36},{\"label\":\"车号：ELrE5dUW[Plus账号]\",\"id\":28},{\"label\":\"车号：wXm5vD3o[Plus账号]\",\"id\":29},{\"label\":\"车号：glv389lp[Plus账号]\",\"id\":30},{\"label\":\"车号：mO8zX5K5[Plus账号]\",\"id\":31},{\"label\":\"车号：M7UJ5zyD[Team账号]\",\"id\":9},{\"label\":\"车号：wD7s1OTH[Team账号]\",\"id\":10},{\"label\":\"车号：M77LLv3F[Team账号]\",\"id\":11},{\"label\":\"车号：rI3strFp[Team账号]\",\"id\":12},{\"label\":\"车号：xPLpFYfb[Pro账号]\",\"id\":3},{\"label\":\"车号：Lj5JHwNX[Pro账号]\",\"id\":37}]}', '', NULL, NULL, '2025-05-05 17:42:01', NULL, NULL, '2025-05-05 17:42:01', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (2, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"Expander\",\"userPhone\":\"\",\"updateTime\":1746428828000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746438054000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746438081000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 17:52:14', NULL, NULL, '2025-05-05 17:52:14', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (3, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"Expander\",\"userPhone\":\"\",\"updateTime\":1746428828000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746438054000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746438081000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 17:54:14', NULL, NULL, '2025-05-05 17:54:14', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (4, 0, 'org.seven.share.controller.admin.SysRoleController.list()', 'GET', '查询角色信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"type\":2,\"createBy\":\"admin\",\"roleDesc\":\"权限超级大，拥有所有权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"createId\":1,\"roleCode\":\"SUPBER_ADMIN\",\"roleName\":\"超级管理员\",\"id\":1,\"status\":\"1\"},{\"updateTime\":1709950885000,\"type\":2,\"updateId\":1,\"createBy\":\"admin\",\"roleDesc\":\"只拥有部分管理权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"updateBy\":\"admin\",\"createId\":1,\"roleCode\":\"ADMIN\",\"roleName\":\"普通管理员\",\"id\":2,\"status\":\"1\"},{\"type\":1,\"updateId\":18,\"createBy\":\"Expander\",\"roleDesc\":\"测试角色\",\"isDeleted\":0,\"createTime\":1746424713000,\"updateBy\":\"Expander\",\"createId\":18,\"roleCode\":\"test\",\"roleName\":\"test\",\"id\":12,\"status\":\"1\"}],\"total\":3,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 18:04:49', NULL, NULL, '2025-05-05 18:04:49', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (5, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"Expander\",\"userPhone\":\"\",\"updateTime\":1746428828000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746439382000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746438081000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 18:04:53', NULL, NULL, '2025-05-05 18:04:53', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (6, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"Expander\",\"userPhone\":\"1\",\"updateTime\":1746439504000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746439382000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"1\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746438081000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 18:05:04', NULL, NULL, '2025-05-05 18:05:04', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (7, 0, 'org.seven.share.controller.admin.SysRoleController.list()', 'GET', '查询角色信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"type\":2,\"createBy\":\"admin\",\"roleDesc\":\"权限超级大，拥有所有权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"createId\":1,\"roleCode\":\"SUPBER_ADMIN\",\"roleName\":\"超级管理员\",\"id\":1,\"status\":\"1\"},{\"updateTime\":1709950885000,\"type\":2,\"updateId\":1,\"createBy\":\"admin\",\"roleDesc\":\"只拥有部分管理权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"updateBy\":\"admin\",\"createId\":1,\"roleCode\":\"ADMIN\",\"roleName\":\"普通管理员\",\"id\":2,\"status\":\"1\"},{\"type\":1,\"updateId\":18,\"createBy\":\"Expander\",\"roleDesc\":\"测试角色\",\"isDeleted\":0,\"createTime\":1746424713000,\"updateBy\":\"Expander\",\"createId\":18,\"roleCode\":\"test\",\"roleName\":\"test\",\"id\":12,\"status\":\"1\"}],\"total\":3,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 18:05:27', NULL, NULL, '2025-05-05 18:05:27', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (8, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"Expander\",\"userPhone\":\"1\",\"updateTime\":1746439504000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746439382000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"1\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746438081000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 18:05:29', NULL, NULL, '2025-05-05 18:05:29', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (9, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"Expander\",\"userPhone\":\"1\",\"updateTime\":1746439504000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746439617000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"1\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746438081000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 18:07:02', NULL, NULL, '2025-05-05 18:07:02', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (10, 0, 'org.seven.share.controller.admin.SysRoleController.list()', 'GET', '查询角色信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"type\":2,\"createBy\":\"admin\",\"roleDesc\":\"权限超级大，拥有所有权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"createId\":1,\"roleCode\":\"SUPBER_ADMIN\",\"roleName\":\"超级管理员\",\"id\":1,\"status\":\"1\"},{\"updateTime\":1709950885000,\"type\":2,\"updateId\":1,\"createBy\":\"admin\",\"roleDesc\":\"只拥有部分管理权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"updateBy\":\"admin\",\"createId\":1,\"roleCode\":\"ADMIN\",\"roleName\":\"普通管理员\",\"id\":2,\"status\":\"1\"},{\"type\":1,\"updateId\":18,\"createBy\":\"Expander\",\"roleDesc\":\"测试角色\",\"isDeleted\":0,\"createTime\":1746424713000,\"updateBy\":\"Expander\",\"createId\":18,\"roleCode\":\"test\",\"roleName\":\"test\",\"id\":12,\"status\":\"1\"}],\"total\":3,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 18:10:55', NULL, NULL, '2025-05-05 18:10:55', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (11, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"Expander\",\"userPhone\":\"1\",\"updateTime\":1746439504000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746439617000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"1\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746438081000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 18:10:58', NULL, NULL, '2025-05-05 18:10:58', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (12, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"Expander\",\"userPhone\":\"1\",\"updateTime\":1746439867000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746439617000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"<EMAIL>\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746438081000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 18:11:08', NULL, NULL, '2025-05-05 18:11:08', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (13, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"1\",\"userPhone\":\"2\",\"updateTime\":1746439953000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746439617000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"<EMAIL>\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746438081000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 18:12:35', NULL, NULL, '2025-05-05 18:12:35', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (14, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"Expander\",\"userPhone\":\"1\",\"updateTime\":1746439991000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746439617000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"<EMAIL>\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746438081000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 18:16:57', NULL, NULL, '2025-05-05 18:16:57', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (15, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"Expander\",\"userPhone\":\"1\",\"updateTime\":1746439991000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746442190000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"<EMAIL>\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746438081000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 18:59:20', NULL, NULL, '2025-05-05 18:59:20', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (16, 0, 'org.seven.share.controller.admin.SysRoleController.list()', 'GET', '查询角色信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"type\":2,\"createBy\":\"admin\",\"roleDesc\":\"权限超级大，拥有所有权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"createId\":1,\"roleCode\":\"SUPBER_ADMIN\",\"roleName\":\"超级管理员\",\"id\":1,\"status\":\"1\"},{\"updateTime\":1709950885000,\"type\":2,\"updateId\":1,\"createBy\":\"admin\",\"roleDesc\":\"只拥有部分管理权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"updateBy\":\"admin\",\"createId\":1,\"roleCode\":\"ADMIN\",\"roleName\":\"普通管理员\",\"id\":2,\"status\":\"1\"},{\"type\":1,\"updateId\":18,\"createBy\":\"Expander\",\"roleDesc\":\"测试角色\",\"isDeleted\":0,\"createTime\":1746424713000,\"updateBy\":\"Expander\",\"createId\":18,\"roleCode\":\"test\",\"roleName\":\"test\",\"id\":12,\"status\":\"1\"}],\"total\":3,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 18:59:38', NULL, NULL, '2025-05-05 18:59:38', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (17, 0, 'org.seven.share.controller.admin.SysRoleController.list()', 'GET', '查询角色信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"type\":2,\"createBy\":\"admin\",\"roleDesc\":\"权限超级大，拥有所有权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"createId\":1,\"roleCode\":\"SUPBER_ADMIN\",\"roleName\":\"超级管理员\",\"id\":1,\"status\":\"1\"},{\"updateTime\":1709950885000,\"type\":2,\"updateId\":1,\"createBy\":\"admin\",\"roleDesc\":\"只拥有部分管理权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"updateBy\":\"admin\",\"createId\":1,\"roleCode\":\"ADMIN\",\"roleName\":\"普通管理员\",\"id\":2,\"status\":\"1\"},{\"type\":1,\"updateId\":18,\"createBy\":\"Expander\",\"roleDesc\":\"测试角色\",\"isDeleted\":0,\"createTime\":1746424713000,\"updateBy\":\"Expander\",\"createId\":18,\"roleCode\":\"test\",\"roleName\":\"test\",\"id\":12,\"status\":\"1\"}],\"total\":3,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 19:01:32', NULL, NULL, '2025-05-05 19:01:32', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (18, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"Expander\",\"userPhone\":\"1\",\"updateTime\":1746439991000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746442190000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"<EMAIL>\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746442883000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 19:01:38', NULL, NULL, '2025-05-05 19:01:38', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (19, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"Expander\",\"userPhone\":\"1\",\"updateTime\":1746442902000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746442190000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"<EMAIL>\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746442883000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 19:01:43', NULL, NULL, '2025-05-05 19:01:43', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (20, 0, 'org.seven.share.controller.admin.SysRoleController.list()', 'GET', '查询角色信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"type\":2,\"createBy\":\"admin\",\"roleDesc\":\"权限超级大，拥有所有权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"createId\":1,\"roleCode\":\"SUPBER_ADMIN\",\"roleName\":\"超级管理员\",\"id\":1,\"status\":\"1\"},{\"updateTime\":1709950885000,\"type\":2,\"updateId\":1,\"createBy\":\"admin\",\"roleDesc\":\"只拥有部分管理权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"updateBy\":\"admin\",\"createId\":1,\"roleCode\":\"ADMIN\",\"roleName\":\"普通管理员\",\"id\":2,\"status\":\"1\"},{\"type\":1,\"updateId\":18,\"createBy\":\"Expander\",\"roleDesc\":\"测试角色\",\"isDeleted\":0,\"createTime\":1746424713000,\"updateBy\":\"Expander\",\"createId\":18,\"roleCode\":\"test\",\"roleName\":\"test\",\"id\":12,\"status\":\"1\"}],\"total\":3,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 19:05:14', NULL, NULL, '2025-05-05 19:05:14', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (21, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"Expander\",\"userPhone\":\"1\",\"updateTime\":1746442902000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746442190000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746411224000,\"userGender\":\"1\",\"userEmail\":\"<EMAIL>\",\"id\":18,\"status\":\"1\"},{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746443003000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 19:05:16', NULL, NULL, '2025-05-05 19:05:16', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (22, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746443003000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"}],\"total\":1,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 19:05:21', NULL, NULL, '2025-05-05 19:05:21', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (23, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746443003000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"},{\"nickName\":\"Expander\",\"userPhone\":\"\",\"updateTime\":1746443129000,\"userName\":\"Expander\",\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746443129000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":22,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 19:05:29', NULL, NULL, '2025-05-05 19:05:29', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (24, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746443003000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"},{\"nickName\":\"Expander\",\"userPhone\":\"\",\"updateTime\":1746443129000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746443133000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746443129000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":22,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 19:05:34', NULL, NULL, '2025-05-05 19:05:34', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (25, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746443003000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"},{\"nickName\":\"Expander\",\"userPhone\":\"\",\"updateTime\":1746443129000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746443133000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746443129000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":22,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 19:08:35', NULL, NULL, '2025-05-05 19:08:35', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (26, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746443003000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"},{\"nickName\":\"Expander\",\"userPhone\":\"\",\"updateTime\":1746443129000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746443133000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746443129000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":22,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 19:13:18', NULL, NULL, '2025-05-05 19:13:18', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (27, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746443003000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"},{\"nickName\":\"Expander\",\"userPhone\":\"\",\"updateTime\":1746443129000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746443133000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746443129000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":22,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 21:34:17', NULL, NULL, '2025-05-05 21:34:17', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (28, 0, 'org.seven.share.controller.admin.SysUserController.list()', 'GET', '查询用户信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"nickName\":\"\",\"userPhone\":\"\",\"updateTime\":1746430036000,\"userName\":\"test\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746443003000,\"userRoles\":[\"test\"],\"isDeleted\":0,\"createTime\":1746419196000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":21,\"status\":\"1\"},{\"nickName\":\"Expander\",\"userPhone\":\"\",\"updateTime\":1746443129000,\"userName\":\"Expander\",\"lastLoginIp\":\"***********\",\"lastLoginTime\":1746452831000,\"userRoles\":[\"SUPBER_ADMIN\"],\"isDeleted\":0,\"createTime\":1746443129000,\"userGender\":\"1\",\"userEmail\":\"\",\"id\":22,\"status\":\"1\"}],\"total\":2,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 21:47:12', NULL, NULL, '2025-05-05 21:47:12', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (29, 0, 'org.seven.share.controller.admin.SysRoleController.list()', 'GET', '查询角色信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"type\":2,\"createBy\":\"admin\",\"roleDesc\":\"权限超级大，拥有所有权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"createId\":1,\"roleCode\":\"SUPBER_ADMIN\",\"roleName\":\"超级管理员\",\"id\":1,\"status\":\"1\"},{\"updateTime\":1709950885000,\"type\":2,\"updateId\":1,\"createBy\":\"admin\",\"roleDesc\":\"只拥有部分管理权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"updateBy\":\"admin\",\"createId\":1,\"roleCode\":\"ADMIN\",\"roleName\":\"普通管理员\",\"id\":2,\"status\":\"1\"},{\"type\":1,\"updateId\":18,\"createBy\":\"Expander\",\"roleDesc\":\"测试角色\",\"isDeleted\":0,\"createTime\":1746424713000,\"updateBy\":\"Expander\",\"createId\":18,\"roleCode\":\"test\",\"roleName\":\"test\",\"id\":12,\"status\":\"1\"}],\"total\":3,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 21:48:24', NULL, NULL, '2025-05-05 21:48:24', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (30, 10, 'org.seven.share.controller.admin.GrokConversationController.page()', 'GET', '分页查询对话记录', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 21:51:09', NULL, NULL, '2025-05-05 21:51:09', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (31, 10, 'org.seven.share.controller.admin.GrokConversationController.page()', 'GET', '分页查询对话记录', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 21:53:06', NULL, NULL, '2025-05-05 21:53:06', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (32, 10, 'org.seven.share.controller.admin.GrokConversationController.page()', 'GET', '分页查询对话记录', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 21:53:24', NULL, NULL, '2025-05-05 21:53:24', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (33, 10, 'org.seven.share.controller.admin.GrokConversationController.page()', 'GET', '分页查询对话记录', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 21:55:03', NULL, NULL, '2025-05-05 21:55:03', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (34, 10, 'org.seven.share.controller.admin.GrokConversationController.page()', 'GET', '分页查询对话记录', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 21:55:21', NULL, NULL, '2025-05-05 21:55:21', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (35, 10, 'org.seven.share.controller.admin.GrokConversationController.page()', 'GET', '分页查询对话记录', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 21:55:44', NULL, NULL, '2025-05-05 21:55:44', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (36, 10, 'org.seven.share.controller.admin.GrokConversationController.page()', 'GET', '分页查询对话记录', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 21:56:48', NULL, NULL, '2025-05-05 21:56:48', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (37, 0, 'org.seven.share.controller.admin.SysRoleController.list()', 'GET', '查询角色信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"type\":2,\"createBy\":\"admin\",\"roleDesc\":\"权限超级大，拥有所有权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"createId\":1,\"roleCode\":\"SUPBER_ADMIN\",\"roleName\":\"超级管理员\",\"id\":1,\"status\":\"1\"},{\"updateTime\":1709950885000,\"type\":2,\"updateId\":1,\"createBy\":\"admin\",\"roleDesc\":\"只拥有部分管理权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"updateBy\":\"admin\",\"createId\":1,\"roleCode\":\"ADMIN\",\"roleName\":\"普通管理员\",\"id\":2,\"status\":\"1\"},{\"type\":1,\"updateId\":18,\"createBy\":\"Expander\",\"roleDesc\":\"测试角色\",\"isDeleted\":0,\"createTime\":1746424713000,\"updateBy\":\"Expander\",\"createId\":18,\"roleCode\":\"test\",\"roleName\":\"test\",\"id\":12,\"status\":\"1\"}],\"total\":3,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 21:58:16', NULL, NULL, '2025-05-05 21:58:16', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (38, 0, 'org.seven.share.controller.admin.SysRoleController.list()', 'GET', '查询角色信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"type\":2,\"createBy\":\"admin\",\"roleDesc\":\"权限超级大，拥有所有权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"createId\":1,\"roleCode\":\"SUPBER_ADMIN\",\"roleName\":\"超级管理员\",\"id\":1,\"status\":\"1\"},{\"updateTime\":1709950885000,\"type\":2,\"updateId\":1,\"createBy\":\"admin\",\"roleDesc\":\"只拥有部分管理权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"updateBy\":\"admin\",\"createId\":1,\"roleCode\":\"ADMIN\",\"roleName\":\"普通管理员\",\"id\":2,\"status\":\"1\"},{\"type\":1,\"updateId\":18,\"createBy\":\"Expander\",\"roleDesc\":\"测试角色\",\"isDeleted\":0,\"createTime\":1746424713000,\"updateBy\":\"Expander\",\"createId\":18,\"roleCode\":\"test\",\"roleName\":\"test\",\"id\":12,\"status\":\"1\"}],\"total\":3,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 21:58:45', NULL, NULL, '2025-05-05 21:58:45', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (39, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:00:21', NULL, NULL, '2025-05-05 22:00:21', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (40, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:00:26', NULL, NULL, '2025-05-05 22:00:26', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (41, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:00:29', NULL, NULL, '2025-05-05 22:00:29', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (42, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:00:40', NULL, NULL, '2025-05-05 22:00:40', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (43, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:00:41', NULL, NULL, '2025-05-05 22:00:41', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (44, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:00:51', NULL, NULL, '2025-05-05 22:00:51', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (45, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:01:07', NULL, NULL, '2025-05-05 22:01:07', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (46, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:01:27', NULL, NULL, '2025-05-05 22:01:27', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (47, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:01:54', NULL, NULL, '2025-05-05 22:01:54', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (48, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:02:13', NULL, NULL, '2025-05-05 22:02:13', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (49, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:02:20', NULL, NULL, '2025-05-05 22:02:20', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (50, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:02:25', NULL, NULL, '2025-05-05 22:02:25', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (51, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:02:29', NULL, NULL, '2025-05-05 22:02:29', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (52, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:02:44', NULL, NULL, '2025-05-05 22:02:44', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (53, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:02:50', NULL, NULL, '2025-05-05 22:02:50', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (54, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:03:21', NULL, NULL, '2025-05-05 22:03:21', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (55, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:03:21', NULL, NULL, '2025-05-05 22:03:21', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (56, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:03:25', NULL, NULL, '2025-05-05 22:03:25', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (57, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:03:36', NULL, NULL, '2025-05-05 22:03:36', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (58, 0, 'org.seven.share.controller.admin.SysRoleController.list()', 'GET', '查询角色信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"操作成功\",\"code\":\"200\",\"data\":{\"records\":[{\"type\":2,\"createBy\":\"admin\",\"roleDesc\":\"权限超级大，拥有所有权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"createId\":1,\"roleCode\":\"SUPBER_ADMIN\",\"roleName\":\"超级管理员\",\"id\":1,\"status\":\"1\"},{\"updateTime\":1709950885000,\"type\":2,\"updateId\":1,\"createBy\":\"admin\",\"roleDesc\":\"只拥有部分管理权限\",\"isDeleted\":0,\"createTime\":1709950883000,\"updateBy\":\"admin\",\"createId\":1,\"roleCode\":\"ADMIN\",\"roleName\":\"普通管理员\",\"id\":2,\"status\":\"1\"},{\"type\":1,\"updateId\":18,\"createBy\":\"Expander\",\"roleDesc\":\"测试角色\",\"isDeleted\":0,\"createTime\":1746424713000,\"updateBy\":\"Expander\",\"createId\":18,\"roleCode\":\"test\",\"roleName\":\"test\",\"id\":12,\"status\":\"1\"}],\"total\":3,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:06:05', NULL, NULL, '2025-05-05 22:06:05', 0, NULL, 'Expander', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (59, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:06:49', NULL, NULL, '2025-05-05 22:06:49', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (60, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:07:29', NULL, NULL, '2025-05-05 22:07:29', 0, NULL, 'test', NULL, 1);
INSERT INTO `t_sys_operation_log` VALUES (61, 10, 'org.seven.share.controller.admin.GrokSessionController.page()', 'GET', '分页查询gpt账号信息', '0:0:0:0:0:0:0:1', '', '{\"msg\":\"请求成功\",\"code\":200,\"data\":{\"records\":[],\"total\":0,\"current\":1,\"size\":10}}', '', NULL, NULL, '2025-05-05 22:54:07', NULL, NULL, '2025-05-05 22:54:07', 0, NULL, 'test', NULL, 1);

-- ----------------------------
-- Table structure for t_sys_resource
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_resource`;
CREATE TABLE `t_sys_resource`  (
  `id` bigint unsigned NOT NULL,
  `parent_id` int(0) NOT NULL COMMENT '父节点id',
  `ui_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '唯一标识路径',
  `menu_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '1：菜单路由；2：资源（按钮）3: 基础资源',
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '状态；1:可用，2:禁用',
  `menu_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
  `route_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '路由名称',
  `route_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '菜单路由为path，其他为唯一标识',
  `component` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `meta` varchar(455) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '元数据',
  `weight` int(0) DEFAULT NULL COMMENT '权重顺序',
  `create_id` bigint(0) NOT NULL COMMENT '创建者ID',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者名称',
  `update_id` bigint(0) DEFAULT NULL COMMENT '修改者ID',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改者名称',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `is_deleted` tinyint(0) NOT NULL DEFAULT 0 COMMENT '是否已删除：0->未删除；1->已删除',
  `delete_time` datetime(0) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `t_sys_resource_ui_path_IDX`(`ui_path`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 153 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '资源表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_sys_resource
-- ----------------------------
INSERT INTO `t_sys_resource` VALUES (1, 0, 'home/', '2', '1', '首页', 'home', '/home', 'layout.base$view.home', '{\"iconType\": \"1\",\"icon\": \"mdi:monitor-dashboard\", \"order\": 0, \"title\": \"home\", \"i18nKey\": \"route.home\"}', 1, 1, 'admin', 1, 'admin', '2024-03-09 08:49:27', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
INSERT INTO `t_sys_resource` VALUES (17, 0, '/user-center', '2', '1', '个人中心', 'user-center', '/user-center', 'layout.base$view.user-center', '{\"iconType\": \"1\",\"icon\": \"mdi:monitor-dashboard\", \"title\": \"个人中心\", \"i18nKey\": \"route.user-center\", \"hideInMenu\": true}', 1, 1, 'admin', 1, 'admin', '2024-03-09 08:49:27', '2024-04-03 05:38:50', 0, '2024-03-09 08:49:34');
INSERT INTO `t_sys_resource` VALUES (18, 0, '/manage', '1', '1', '系统管理', 'manage', '/manage', 'layout.base', '{\"iconType\": \"1\",\"icon\": \"carbon:cloud-service-management\", \"order\": 9, \"title\": \"系统管理\", \"i18nKey\": \"route.manage\",}', 9, 1, 'admin', 1, 'admin', '2024-03-09 08:49:27', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
INSERT INTO `t_sys_resource` VALUES (19, 18, '/manage/user', '2', '1', '用户管理', 'manage_user', '/manage/user', 'view.manage_user', '{\"iconType\": \"1\",\"icon\": \"ic:round-manage-accounts\", \"order\": 1, \"title\": \"用户管理\", \"i18nKey\": \"route.manage_user\"}', 1, 1, 'admin', 1, 'admin', '2024-03-09 08:49:27', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
INSERT INTO `t_sys_resource` VALUES (20, 18, '/manage/role', '2', '1', '角色管理', 'manage_role', '/manage/role', 'view.manage_role', '{\"iconType\": \"1\",\"icon\": \"carbon:user-role\", \"order\": 2, \"title\": \"角色管理\", \"i18nKey\": \"route.manage_role\"}', 2, 1, 'admin', 1, 'admin', '2024-03-09 08:49:27', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
INSERT INTO `t_sys_resource` VALUES (21, 18, '/manage/menu', '2', '1', '菜单管理', 'manage_menu', '/manage/menu', 'view.manage_menu', '{\"iconType\": \"1\",\"icon\": \"material-symbols:route\", \"order\": 3, \"title\": \"菜单管理\", \"i18nKey\": \"route.manage_menu\"}', 3, 1, 'admin', 1, 'admin', '2024-03-09 08:49:27', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
INSERT INTO `t_sys_resource` VALUES (22, 18, '/manage/user-detail/:id', '3', '1', '用户详情', 'manage_user-detail', '/manage/user-detail/:id', 'view.manage_user-detail', '{\"title\": \"manage_user-detail\", \"i18nKey\": \"route.manage_user-detail\", \"hideInMenu\": true}', 4, 1, 'admin', 1, 'admin', '2024-03-09 08:49:27', '2024-03-09 08:49:30', 1, '2024-03-09 08:49:34');
INSERT INTO `t_sys_resource` VALUES (23, 0, '403', '4', '1', '403', '403', '/403', 'layout.blank$view.403', '{\"constant\": true, \"hideInMenu\": true, \"title\": \"403\", \"i18nKey\": \"route.403\"}', 1, 1, 'admin', 1, 'admin', '2024-03-26 08:49:27', '2024-03-26 08:49:30', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (24, 0, '404', '4', '1', '404', '404', '/404', 'layout.blank$view.404', '{\"constant\": true, \"hideInMenu\": true, \"title\": \"404\", \"i18nKey\": \"route.404\"}', 1, 1, 'admin', 1, 'admin', '2024-03-26 08:49:27', '2024-03-26 08:49:30', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (25, 0, '500', '4', '1', '500', '500', '/500', 'layout.blank$view.500', '{\"constant\": true, \"hideInMenu\": true, \"title\": \"500\", \"i18nKey\": \"route.500\"}', 1, 1, 'admin', 1, 'admin', '2024-03-26 08:49:27', '2024-03-26 08:49:30', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (26, 0, 'login', '4', '1', '登录', 'login', '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?', 'layout.blank$view.login', '{\"constant\": true, \"hideInMenu\": true, \"title\": \"login\", \"i18nKey\": \"route.login\"}', 1, 1, 'admin', 1, 'admin', '2024-03-26 08:49:27', '2024-03-26 08:49:30', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (27, 19, '/manage/user:add', '3', '1', '用户添加权限', 'manage:user:add', '', '', '', 1, 1, 'admin', 1, 'admin', '2024-04-01 08:49:27', '2024-04-04 03:29:53', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (156, 18, '/manage/log', '2', '1', '操作日志', 'manage_log', '/manage/log', 'view.manage_log', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"fluent:tag-24-regular\",\"title\":\"操作日志\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.manage_log\",\"iconType\":\"1\",\"href\":\"\",\"order\":4}', NULL, 21, 'test', 21, 'test', '2025-05-05 17:11:48', '2025-05-05 17:11:48', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (157, 0, '/account', '1', '1', '账号管理', 'account', '/account', 'layout.base', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"mdi:monitor-dashboard\",\"title\":\"账号管理\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.account\",\"iconType\":\"1\",\"href\":\"\",\"order\":2}', NULL, 21, 'test', 21, 'test', '2025-05-05 17:09:19', '2025-05-05 17:09:19', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (158, 157, '/account/claude', '2', '1', 'Claude', 'account_claude', '/account/claude', 'view.account_claude', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"fluent:tag-24-regular\",\"title\":\"Claude\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.account_claude\",\"iconType\":\"1\",\"href\":\"\",\"order\":2}', NULL, 21, 'test', 21, 'test', '2025-05-05 17:11:41', '2025-05-05 17:11:41', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (159, 157, '/account/openai', '2', '1', 'ChatGPT', 'account_openai', '/account/openai', 'view.account_openai', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"fluent:tag-24-regular\",\"title\":\"ChatGPT\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.account_openai\",\"iconType\":\"1\",\"href\":\"\",\"order\":1}', NULL, 21, 'test', 21, 'test', '2025-05-05 17:11:37', '2025-05-05 17:11:37', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (161, 0, '/about', '1', '1', '关于', 'about', '/about', 'layout.base$view.about', '{\"hideInMenu\":false,\"keepAlive\":true,\"constant\":false,\"multiTab\":false,\"i18nKey\":\"route.about\",\"iconType\":\"1\",\"query\":[],\"icon\":\"fluent:book-information-24-regular\",\"title\":\"关于\",\"order\":10}', NULL, 18, 'Expander', 18, 'Expander', '2025-05-05 16:14:09', '2025-05-05 16:14:09', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (162, 0, '/subtype', '1', '1', '订阅管理', 'subtype', '/subtype', 'layout.base$view.subtype', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"mdi:monitor-dashboard\",\"title\":\"订阅管理\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.subtype\",\"iconType\":\"1\",\"href\":\"\",\"order\":3}', NULL, 21, 'test', 21, 'test', '2025-05-05 17:09:24', '2025-05-05 17:09:24', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (163, 0, '/user', '1', '1', '会员管理', 'user', '/user', 'layout.base$view.user', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"fluent:person-24-regular\",\"title\":\"会员管理\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.user\",\"iconType\":\"1\",\"href\":\"\",\"order\":1}', NULL, 18, 'Expander', 18, 'Expander', '2025-05-05 17:02:52', '2025-05-05 17:02:52', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (164, 0, '/tenant', '1', '1', '租户管理', 'tenant', '/tenant', 'layout.base$view.tenant', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"mdi:account-group\",\"title\":\"租户管理\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.tenant\",\"iconType\":\"1\",\"href\":\"\",\"order\":6}', NULL, 18, 'Expander', 18, 'Expander', '2025-05-05 17:05:42', '2025-05-05 17:05:42', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (165, 0, '/platform', '1', '1', '平台管理', 'platform', '/platform', 'layout.base', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"mdi:monitor-dashboard\",\"title\":\"平台管理\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.platform\",\"iconType\":\"1\",\"href\":\"\",\"order\":7}', NULL, 21, 'test', 21, 'test', '2025-05-05 17:09:29', '2025-05-05 17:09:29', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (166, 0, '/order', '1', '1', '订单管理', 'order', '/order', 'layout.base', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"fluent:tag-24-regular\",\"title\":\"订单管理\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.order\",\"iconType\":\"1\",\"href\":\"\",\"order\":5}', NULL, 21, 'test', 21, 'test', '2025-05-05 17:10:49', '2025-05-05 17:10:49', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (167, 166, '/order/info', '2', '1', '订单信息', 'order_info', '/order/info', 'view.order_info', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"fluent:tag-24-regular\",\"title\":\"订单信息\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.order_info\",\"iconType\":\"1\",\"href\":\"\",\"order\":0}', NULL, 21, 'test', 21, 'test', '2025-05-05 17:12:11', '2025-05-05 17:12:11', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (168, 166, '/order/payconfig', '2', '1', '支付配置', 'order_payconfig', '/order/payconfig', 'view.order_payconfig', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"fluent:tag-24-regular\",\"title\":\"支付配置\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.order_payconfig\",\"iconType\":\"1\",\"href\":\"\",\"order\":0}', NULL, 21, 'test', 21, 'test', '2025-05-05 17:12:15', '2025-05-05 17:12:15', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (169, 165, '/platform/riskcontrol', '2', '1', '风控记录', 'platform_riskcontrol', '/platform/riskcontrol', 'view.platform_riskcontrol', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"fluent:tag-24-regular\",\"title\":\"风控记录\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.platform_riskcontrol\",\"iconType\":\"1\",\"href\":\"\",\"order\":0}', NULL, 21, 'test', 21, 'test', '2025-05-05 17:11:57', '2025-05-05 17:11:57', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (170, 165, '/platform/topic', '2', '1', '对话管理', 'platform_topic', '/platform/topic', 'view.platform_topic', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"fluent:tag-24-regular\",\"title\":\"对话管理\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.platform_topic\",\"iconType\":\"1\",\"href\":\"\",\"order\":0}', NULL, 21, 'test', 21, 'test', '2025-05-05 17:12:00', '2025-05-05 17:12:00', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (171, 165, '/platform/word', '2', '1', '敏感词', 'platform_word', '/platform/word', 'view.platform_word', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"fluent:tag-24-regular\",\"title\":\"敏感词\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.platform_word\",\"iconType\":\"1\",\"href\":\"\",\"order\":0}', NULL, 21, 'test', 21, 'test', '2025-05-05 17:12:04', '2025-05-05 17:12:04', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (172, 0, '/marketing', '1', '1', '营销管理', 'marketing', '/marketing', 'layout.base', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"fluent:tag-24-regular\",\"title\":\"营销管理\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.marketing\",\"iconType\":\"1\",\"href\":\"\",\"order\":4}', NULL, 21, 'test', 21, 'test', '2025-05-05 17:10:55', '2025-05-05 17:10:55', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (173, 172, '/marketing/cdkey', '2', '1', '激活码', 'marketing_cdkey', '/marketing/cdkey', 'view.marketing_cdkey', '{\"hideInMenu\":false,\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"i18nKey\":\"route.marketing_cdkey\",\"iconType\":\"1\",\"query\":[],\"icon\":\"\",\"title\":\"激活码\",\"order\":1}', NULL, 18, 'Expander', 18, 'Expander', '2025-05-05 16:52:09', '2025-05-05 16:52:09', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (174, 172, '/marketing/coupon', '2', '1', '优惠券', 'marketing_coupon', '/marketing/coupon', 'view.marketing_coupon', '{\"hideInMenu\":false,\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"i18nKey\":\"route.marketing_coupon\",\"iconType\":\"1\",\"query\":[],\"icon\":\"\",\"title\":\"优惠券\",\"order\":2}', NULL, 18, 'Expander', 18, 'Expander', '2025-05-05 16:52:28', '2025-05-05 16:52:28', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (175, 172, '/marketing/notice', '2', '1', '通知公告', 'marketing_notice', '/marketing/notice', 'view.marketing_notice', '{\"hideInMenu\":false,\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"i18nKey\":\"route.marketing_notice\",\"iconType\":\"1\",\"query\":[],\"icon\":\"\",\"title\":\"通知公告\",\"order\":3}', NULL, 18, 'Expander', 18, 'Expander', '2025-05-05 16:52:45', '2025-05-05 16:52:45', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (176, 172, '/marketing/withdrawals', '2', '1', '体现管理', 'marketing_withdrawals', '/marketing/withdrawals', 'view.marketing_withdrawals', '{\"hideInMenu\":false,\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"i18nKey\":\"route.marketing_withdrawals\",\"iconType\":\"1\",\"query\":[],\"icon\":\"\",\"title\":\"体现管理\",\"order\":4}', NULL, 18, 'Expander', 18, 'Expander', '2025-05-05 16:53:03', '2025-05-05 16:53:03', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (177, 165, '/platform/grok-topic', '2', '1', 'Grok会话', 'platform_grok-topic', '/platform/grok-topic', 'view.platform_grok-topic', '{\"localIcon\":\"\",\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"query\":[],\"icon\":\"carbon:cloud-service-management\",\"title\":\"Grok会话\",\"hideInMenu\":false,\"activeMenu\":\"\",\"i18nKey\":\"route.platform_grok-topic\",\"iconType\":\"1\",\"href\":\"\",\"order\":5}', NULL, 22, 'Expander', 22, 'Expander', '2025-05-05 21:51:00', '2025-05-05 21:51:00', 0, NULL);
INSERT INTO `t_sys_resource` VALUES (178, 157, '/account/grok', '2', '1', 'Grok', 'account_grok', '/account/grok', 'view.account_grok', '{\"hideInMenu\":false,\"keepAlive\":false,\"constant\":false,\"multiTab\":false,\"i18nKey\":\"route.account_grok\",\"iconType\":\"1\",\"query\":[],\"icon\":\"carbon:cloud-service-management\",\"title\":\"Grok\",\"order\":3}', NULL, 21, 'test', 21, 'test', '2025-05-05 21:58:13', '2025-05-05 21:58:13', 0, NULL);

-- ----------------------------
-- Table structure for t_sys_role
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_role`;
CREATE TABLE `t_sys_role`  (
  `id` bigint unsigned NOT NULL,
  `role_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `role_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色code',
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '状态；1:可用，2:禁用',
  `role_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `type` tinyint(0) NOT NULL COMMENT '类型：1:公共角色；2:特殊角色',
  `create_id` bigint(0) NOT NULL COMMENT '创建者ID',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者名称',
  `update_id` bigint(0) DEFAULT NULL COMMENT '修改者ID',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改者名称',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `is_deleted` tinyint(0) DEFAULT 0 COMMENT '是否已删除：0->未删除；1->已删除',
  `delete_time` datetime(0) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_sys_role
-- ----------------------------
INSERT INTO `t_sys_role` VALUES (1, '超级管理员', 'SUPBER_ADMIN', '1', '权限超级大，拥有所有权限', 2, 1, 'admin', NULL, NULL, '2024-03-09 10:21:23', NULL, 0, NULL);
INSERT INTO `t_sys_role` VALUES (2, '普通管理员', 'ADMIN', '1', '只拥有部分管理权限', 2, 1, 'admin', 1, 'admin', '2024-03-09 10:21:23', '2024-03-09 10:21:25', 0, NULL);
INSERT INTO `t_sys_role` VALUES (12, 'test', 'test', '1', '测试角色', 1, 18, 'Expander', 18, 'Expander', '2025-05-05 13:58:33', NULL, 0, NULL);

-- ----------------------------
-- Table structure for t_sys_role_resource
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_role_resource`;
CREATE TABLE `t_sys_role_resource`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `role_id` bigint(0) NOT NULL,
  `resource_id` bigint(0) NOT NULL,
  `create_id` bigint unsigned,
  `create_by` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `update_id` bigint(0) DEFAULT NULL,
  `update_by` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime(0) DEFAULT NULL,
  `update_time` datetime(0) DEFAULT NULL,
  `is_deleted` tinyint(0) NOT NULL DEFAULT 0,
  `delete_time` datetime(0) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 718 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '角色资源关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_sys_role_resource
-- ----------------------------
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(1, 0, 'home/', '2', '1', '首页', 'home', '/home', 'layout.base$view.home', '{"iconType": "1","icon": "mdi:monitor-dashboard", "order": 0, "title": "home", "i18nKey": "route.home"}', 1, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(2, 0, '/user-center', '2', '1', '个人中心', 'user-center', '/user-center', 'layout.base$view.user-center', '{"iconType": "1","icon": "mdi:monitor-dashboard", "title": "个人中心", "i18nKey": "route.user-center", "hideInMenu": true}', 1, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-04-03 05:38:50', 0, '2024-03-09 08:49:34');
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(3, 0, '/manage', '1', '1', '系统管理', 'manage', '/manage', 'layout.base', '{"iconType": "1","icon": "carbon:cloud-service-management", "order": 9, "title": "系统管理", "i18nKey": "route.manage",}', 9, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(4, 3, '/manage/user', '2', '1', '用户管理', 'manage_user', '/manage/user', 'view.manage_user', '{"iconType": "1","icon": "ic:round-manage-accounts", "order": 1, "title": "用户管理", "i18nKey": "route.manage_user"}', 1, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(5, 3, '/manage/role', '2', '1', '角色管理', 'manage_role', '/manage/role', 'view.manage_role', '{"iconType": "1","icon": "carbon:user-role", "order": 2, "title": "角色管理", "i18nKey": "route.manage_role"}', 2, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(6, 3, '/manage/menu', '2', '1', '菜单管理', 'manage_menu', '/manage/menu', 'view.manage_menu', '{"iconType": "1","icon": "material-symbols:route", "order": 3, "title": "菜单管理", "i18nKey": "route.manage_menu"}', 3, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(7, 3, '/manage/user-detail/:id', '3', '1', '用户详情', 'manage_user-detail', '/manage/user-detail/:id', 'view.manage_user-detail', '{"title": "manage_user-detail", "i18nKey": "route.manage_user-detail", "hideInMenu": true}', 4, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-03-09 08:49:30', 1, '2024-03-09 08:49:34');
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(8, 3, '/manage/log', '2', '1', '操作日志', 'manage_log', '/manage/log', 'view.manage_log', '{"localIcon":"","keepAlive":false,"constant":false,"multiTab":false,"query":[],"icon":"mdi:foot-print","title":"操作日志","hideInMenu":false,"activeMenu":"","i18nKey":"route.manage_log","iconType":"1","href":"","order":4}', NULL, 1, 'admin', '2025-05-10 22:43:22', 1, 'admin', '2025-05-10 22:43:22', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(9, 0, '403', '4', '1', '403', '403', '/403', 'layout.blank$view.403', '{"constant": true, "hideInMenu": true, "title": "403", "i18nKey": "route.403"}', 1, 1, 'admin', '2024-03-26 08:49:27', 1, 'admin', '2024-03-26 08:49:30', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(10, 0, '404', '4', '1', '404', '404', '/404', 'layout.blank$view.404', '{"constant": true, "hideInMenu": true, "title": "404", "i18nKey": "route.404"}', 1, 1, 'admin', '2024-03-26 08:49:27', 1, 'admin', '2024-03-26 08:49:30', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(11, 0, '500', '4', '1', '500', '500', '/500', 'layout.blank$view.500', '{"constant": true, "hideInMenu": true, "title": "500", "i18nKey": "route.500"}', 1, 1, 'admin', '2024-03-26 08:49:27', 1, 'admin', '2024-03-26 08:49:30', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(12, 0, 'login', '4', '1', '登录', 'login', '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?', 'layout.blank$view.login', '{"constant": true, "hideInMenu": true, "title": "login", "i18nKey": "route.login"}', 1, 1, 'admin', '2024-03-26 08:49:27', 1, 'admin', '2024-03-26 08:49:30', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(13, 0, '/user', '1', '1', '用户管理', 'user', '/user', 'layout.base$view.user', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.user","iconType":"1","query":[],"icon":"ic:round-manage-accounts","title":"用户管理","order":1}', NULL, 1, 'admin', '2025-05-10 22:45:18', 1, 'admin', '2025-05-10 22:46:04', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(14, 0, '/account', '1', '1', '账号管理', 'account', '/account', 'layout.base', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.account","iconType":"1","query":[],"icon":"material-symbols:assignment-add","title":"账号管理","order":2}', NULL, 1, 'admin', '2025-05-10 22:48:52', 1, 'admin', '2025-05-10 22:49:07', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(15, 14, '/account/openai', '2', '1', 'openai', 'account_openai', '/account/openai', 'view.account_openai', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.account_openai","iconType":"1","query":[],"icon":"material-symbols:assignment-add","title":"openai","order":0}', NULL, 1, 'admin', '2025-05-10 22:49:51', 1, 'admin', '2025-05-10 22:50:36', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(16, 14, '/account/claude', '2', '1', 'claude', 'account_claude', '/account/claude', 'view.account_claude', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.account_claude","iconType":"1","query":[],"icon":"material-symbols:assignment-add","title":"claude","order":2}', NULL, 1, 'admin', '2025-05-10 22:51:28', 1, 'admin', '2025-05-10 22:52:26', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(17, 14, '/account/grok', '2', '1', 'grok', 'account_grok', '/account/grok', 'view.account_grok', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.account_grok","iconType":"1","query":[],"icon":"material-symbols:assignment-add","title":"grok","order":2}', NULL, 1, 'admin', '2025-05-10 22:51:53', 1, 'admin', '2025-05-10 22:52:26', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(18, 0, '/subtype', '1', '1', '套餐管理', 'subtype', '/subtype', 'layout.base$view.subtype', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype","iconType":"1","query":[],"icon":"material-symbols-light:shopping-cart-off","title":"套餐管理","order":3}', NULL, 1, 'admin', '2025-05-10 22:53:48', 1, 'admin', '2025-05-10 22:56:10', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(19, 0, '/marketing', '1', '1', '营销管理', 'marketing', '/marketing', 'layout.base', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.marketing","iconType":"1","query":[],"icon":"hugeicons:marketing","title":"营销管理","order":4}', NULL, 1, 'admin', '2025-05-10 22:55:55', 1, 'admin', '2025-05-10 22:56:10', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(20, 19, '/marketing/cdkey', '2', '1', '激活码', 'marketing_cdkey', '/marketing/cdkey', 'view.marketing_cdkey', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.marketing_cdkey","iconType":"1","query":[],"icon":"ic:baseline-key","title":"激活码","order":0}', NULL, 1, 'admin', '2025-05-10 22:58:31', 1, 'admin', '2025-05-10 23:02:11', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(21, 19, '/marketing/coupon', '2', '1', '优惠券', 'marketing_coupon', '/marketing/coupon', 'view.marketing_coupon', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.marketing_coupon","iconType":"1","query":[],"icon":"ri:coupon-2-fill","title":"优惠券","order":1}', NULL, 1, 'admin', '2025-05-10 22:59:20', 1, 'admin', '2025-05-10 23:02:11', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(22, 19, '/marketing/notice', '2', '1', '通知公告', 'marketing_notice', '/marketing/notice', 'view.marketing_notice', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.marketing_notice","iconType":"1","query":[],"icon":"fe:notice-off","title":"通知公告","order":2}', NULL, 1, 'admin', '2025-05-10 23:00:08', 1, 'admin', '2025-05-10 23:02:11', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(23, 19, '/marketing/withdrawals', '2', '1', '推广返现', 'marketing_withdrawals', '/marketing/withdrawals', 'view.marketing_withdrawals', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.marketing_withdrawals","iconType":"1","query":[],"icon":"ph:money-bold","title":"推广返现","order":3}', NULL, 1, 'admin', '2025-05-10 23:01:53', 1, 'admin', '2025-05-10 23:02:11', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(24, 0, '/draw', '1', '1', '绘图管理', 'draw', '/draw', 'layout.base', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.draw","iconType":"1","query":[],"icon":"material-symbols:photo-library-rounded","title":"绘图管理","order":5}', NULL, 1, 'admin', '2025-05-10 23:03:36', 1, 'admin', '2025-05-10 23:08:54', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(25, 24, '/draw/record', '2', '1', '绘图记录', 'draw_record', '/draw/record', 'view.draw_record', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.draw_record","iconType":"1","query":[],"icon":"material-symbols:draw-outline-sharp","title":"绘图记录","order":0}', NULL, 1, 'admin', '2025-05-10 23:05:13', 1, 'admin', '2025-05-10 23:08:54', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(26, 24, '/draw/quota-manage', '2', '1', '额度管理', 'draw_quota-manage', '/draw/quota-manage', 'view.draw_quota-manage', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.draw_quota-manage","iconType":"1","query":[],"icon":"material-symbols-light:table-rows-narrow-outline","title":"额度管理","order":1}', NULL, 1, 'admin', '2025-05-10 23:06:38', 1, 'admin', '2025-05-10 23:08:54', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(27, 24, '/draw/quota-change', '2', '1', '额度变更', 'draw_quota-change', '/draw/quota-change', 'view.draw_quota-change', '{"localIcon":"","keepAlive":false,"constant":false,"multiTab":false,"query":[],"icon":"pajamas:quota","title":"额度变更","hideInMenu":false,"activeMenu":"","i18nKey":"route.draw_quota-change","iconType":"1","href":"","order":2}', NULL, 1, 'admin', '2025-05-10 23:36:06', 1, 'admin', '2025-05-10 23:36:06', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(28, 0, '/order', '1', '1', '订单管理', 'order', '/order', 'layout.base', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.order","iconType":"1","query":[],"icon":"material-symbols:order-approve-sharp","title":"订单管理","order":6}', NULL, 1, 'admin', '2025-05-10 23:10:16', 1, 'admin', '2025-05-10 23:13:21', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(29, 28, '/order/info', '2', '1', '订单信息', 'order_info', '/order/info', 'view.order_info', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.order_info","iconType":"1","query":[],"icon":"material-symbols-light:data-info-alert","title":"订单信息","order":0}', NULL, 1, 'admin', '2025-05-10 23:11:32', 1, 'admin', '2025-05-10 23:13:21', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(30, 28, '/order/payconfig', '2', '1', '支付配置', 'order_payconfig', '/order/payconfig', 'view.order_payconfig', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.order_payconfig","iconType":"1","query":[],"icon":"lineicons:amazon-pay","title":"支付配置","order":1}', NULL, 1, 'admin', '2025-05-10 23:12:53', 1, 'admin', '2025-05-10 23:13:21', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(31, 0, '/tenant', '1', '1', '租户管理', 'tenant', '/tenant', 'layout.base$view.tenant', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.tenant","iconType":"1","query":[],"icon":"akar-icons:people-group","title":"租户管理","order":7}', NULL, 1, 'admin', '2025-05-10 23:16:23', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(32, 0, '/platform', '1', '1', '平台管理', 'platform', '/platform', 'layout.base', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.platform","iconType":"1","query":[],"icon":"mdi:monitor-dashboard","title":"平台管理","order":8}', NULL, 1, 'admin', '2025-05-10 23:17:21', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(33, 32, '/platform/sysconfig', '2', '1', '系统设置', 'platform_sysconfig', '/platform/sysconfig', 'view.platform_sysconfig', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.platform_sysconfig","iconType":"1","query":[],"icon":"icon-park-outline:database-config","title":"系统设置","order":0}', NULL, 1, 'admin', '2025-05-10 23:18:26', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(34, 32, '/platform/word', '2', '1', '敏感词', 'platform_word', '/platform/word', 'view.platform_word', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.platform_word","iconType":"1","query":[],"icon":"ri:file-word-2-fill","title":"敏感词","order":1}', NULL, 1, 'admin', '2025-05-10 23:19:22', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(35, 32, '/platform/riskcontrol', '2', '1', '风控记录', 'platform_riskcontrol', '/platform/riskcontrol', 'view.platform_riskcontrol', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.platform_riskcontrol","iconType":"1","query":[],"icon":"iconoir:warning-triangle-solid","title":"风控记录","order":2}', NULL, 1, 'admin', '2025-05-10 23:20:19', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(36, 32, '/platform/topic', '2', '1', 'openai对话', 'platform_topic', '/platform/topic', 'view.platform_topic', '{"localIcon":"","keepAlive":false,"constant":false,"multiTab":false,"query":[],"icon":"icon-park-solid:topic-discussion","title":"openai对话","hideInMenu":false,"activeMenu":"","i18nKey":"route.platform_topic","iconType":"1","href":"","order":3}', NULL, 1, 'admin', '2025-05-10 23:36:52', 1, 'admin', '2025-05-10 23:36:52', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(37, 32, '/platform/claude-topic', '2', '1', 'claude对话', 'platform_claude-topic', '/platform/claude-topic', 'view.platform_claude-topic', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.platform_claude-topic","iconType":"1","query":[],"icon":"icon-park-solid:topic-discussion","title":"claude对话","order":4}', NULL, 1, 'admin', '2025-05-10 23:22:21', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(38, 32, '/platform/grok-topic', '2', '1', 'grok对话', 'platform_grok-topic', '/platform/grok-topic', 'view.platform_grok-topic', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.platform_grok-topic","iconType":"1","query":[],"icon":"icon-park-solid:topic-discussion","title":"grok对话","order":5}', NULL, 1, 'admin', '2025-05-10 23:23:13', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(39, 13, '/user/page', '3', '1', '用户查询', 'user/page', '/user/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.user/page","iconType":"1","query":[],"icon":"","title":"用户查询","order":0}', NULL, 1, 'admin', '2025-05-16 11:47:08', 1, 'admin', '2025-05-16 11:47:08', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(40, 13, '/user/create', '3', '1', '用户新增', 'user/create', '/user/create', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.user/create","iconType":"1","query":[],"icon":"","title":"用户新增","order":0}', NULL, 1, 'admin', '2025-05-16 11:47:48', 1, 'admin', '2025-05-16 11:47:48', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(41, 13, '/user/update', '3', '1', '用户编辑', 'user/update', '/user/update', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.user/update","iconType":"1","query":[],"icon":"","title":"用户编辑","order":0}', NULL, 1, 'admin', '2025-05-16 11:48:04', 1, 'admin', '2025-05-16 11:48:04', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(42, 13, '/user/batchCreate', '3', '1', '用户批量创建', 'user/batchCreate', '/user/batchCreate', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.user/batchCreate","iconType":"1","query":[],"icon":"","title":"用户批量创建","order":0}', NULL, 1, 'admin', '2025-05-16 11:48:23', 1, 'admin', '2025-05-16 11:48:23', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(43, 13, '/user/delete', '3', '1', '用户删除', 'user/delete', '/user/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.user/delete","iconType":"1","query":[],"icon":"","title":"用户删除","order":0}', NULL, 1, 'admin', '2025-05-16 11:48:45', 1, 'admin', '2025-05-16 11:48:45', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(44, 13, '/subtype/list', '3', '1', '用户套餐列表', 'subtype/list', '/subtype/list', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype/list","iconType":"1","query":[],"icon":"","title":"用户套餐列表","order":0}', NULL, 1, 'admin', '2025-05-16 11:49:07', 1, 'admin', '2025-05-16 11:49:07', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(45, 31, '/tenant/add', '3', '1', '租户增加', 'tenant/add', '/tenant/add', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.tenant/add","iconType":"1","query":[],"icon":"","title":"租户增加","order":0}', NULL, 1, 'admin', '2025-05-16 11:51:37', 1, 'admin', '2025-05-16 11:51:37', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(46, 31, '/tenant/update', '3', '1', '租户编辑', 'tenant/update', '/tenant/update', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.tenant/update","iconType":"1","query":[],"icon":"","title":"租户编辑","order":0}', NULL, 1, 'admin', '2025-05-16 11:51:55', 1, 'admin', '2025-05-16 11:51:55', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(47, 31, '/tenant/getTenantPage', '3', '1', '租户查询', 'tenant/getTenantPage', '/tenant/getTenantPage', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.tenant/getTenantPage","iconType":"1","query":[],"icon":"","title":"租户查询","order":0}', NULL, 1, 'admin', '2025-05-16 11:52:16', 1, 'admin', '2025-05-16 11:52:16', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(48, 31, '/tenant/delete', '3', '1', '租户删除', 'tenant/delete', '/tenant/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.tenant/delete","iconType":"1","query":[],"icon":"","title":"租户删除","order":0}', NULL, 1, 'admin', '2025-05-16 11:52:32', 1, 'admin', '2025-05-16 11:52:32', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(49, 31, '/tenant/deleteByIds', '3', '1', '租户批量删除', 'tenant/deleteByIds', '/tenant/deleteByIds', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.tenant/deleteByIds","iconType":"1","query":[],"icon":"","title":"租户批量删除","order":0}', NULL, 1, 'admin', '2025-05-16 11:52:54', 1, 'admin', '2025-05-16 11:52:54', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(50, 18, '/subtype/page', '3', '1', '套餐查询', 'subtype/page', '/subtype/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype/page","iconType":"1","query":[],"icon":"","title":"套餐查询","order":0}', NULL, 1, 'admin', '2025-05-16 11:54:36', 1, 'admin', '2025-05-16 11:54:36', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(51, 18, '/subtype/list', '3', '1', '套餐明细列表查询', 'subtype/list', '/subtype/list', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype/list","iconType":"1","query":[],"icon":"","title":"套餐明细列表查询","order":0}', NULL, 1, 'admin', '2025-05-16 11:54:57', 1, 'admin', '2025-05-16 11:54:57', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(52, 18, '/subtype/update', '3', '1', '套餐更新', 'subtype/update', '/subtype/update', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype/update","iconType":"1","query":[],"icon":"","title":"套餐更新","order":0}', NULL, 1, 'admin', '2025-05-16 11:55:14', 1, 'admin', '2025-05-16 11:55:14', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(53, 18, '/subtype/add', '3', '1', '套餐新增', 'subtype/add', '/subtype/add', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype/add","iconType":"1","query":[],"icon":"","title":"套餐新增","order":0}', NULL, 1, 'admin', '2025-05-16 11:55:27', 1, 'admin', '2025-05-16 11:55:27', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(54, 18, '/subtype/delete', '2', '1', '套餐删除', 'subtype/delete', '/subtype/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype/delete","iconType":"1","query":[],"icon":"","title":"套餐删除","order":0}', NULL, 1, 'admin', '2025-05-16 11:55:45', 1, 'admin', '2025-05-16 11:55:45', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(55, 20, '/codes/page', '3', '1', '激活码查询', 'codes/page', '/codes/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.codes/page","iconType":"1","query":[],"icon":"","title":"激活码查询","order":0}', NULL, 1, 'admin', '2025-05-16 11:56:47', 1, 'admin', '2025-05-16 11:56:47', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(57, 20, '/codes/generate', '3', '1', '激活码批量新增', 'codes/generate', '/codes/generate', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.codes/generate","iconType":"1","query":[],"icon":"","title":"激活码批量新增","order":0}', NULL, 1, 'admin', '2025-05-16 11:57:30', 1, 'admin', '2025-05-16 11:57:30', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(58, 20, '/codes/delete', '3', '1', '激活码删除', 'codes/delete', '/codes/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.codes/delete","iconType":"1","query":[],"icon":"","title":"激活码删除","order":0}', NULL, 1, 'admin', '2025-05-16 11:57:42', 1, 'admin', '2025-05-16 11:57:42', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(59, 20, '/codes/export-key', '3', '1', '激活码导出', 'codes/export-key', '/codes/export-key', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.codes/export-key","iconType":"1","query":[],"icon":"","title":"激活码导出","order":0}', NULL, 1, 'admin', '2025-05-16 11:57:55', 1, 'admin', '2025-05-16 11:57:55', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(60, 20, '/codes/recycle', '3', '1', '激活码回收', 'codes/recycle', '/codes/recycle', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.codes/recycle","iconType":"1","query":[],"icon":"","title":"激活码回收","order":0}', NULL, 1, 'admin', '2025-05-16 11:58:09', 1, 'admin', '2025-05-16 11:58:09', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(61, 21, '/coupon/page', '3', '1', '优惠卷查询', 'coupon/page', '/coupon/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.coupon/page","iconType":"1","query":[],"icon":"","title":"优惠卷查询","order":0}', NULL, 1, 'admin', '2025-05-16 11:59:25', 1, 'admin', '2025-05-16 11:59:25', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(62, 21, '/coupon/create', '3', '1', '优惠卷新增', 'coupon/create', '/coupon/create', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.coupon/create","iconType":"1","query":[],"icon":"","title":"优惠卷新增","order":0}', NULL, 1, 'admin', '2025-05-16 11:59:42', 1, 'admin', '2025-05-16 11:59:42', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(63, 21, '/coupon/update', '3', '1', '优惠卷更新', 'coupon/update', '/coupon/update', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.coupon/update","iconType":"1","query":[],"icon":"","title":"优惠卷更新","order":0}', NULL, 1, 'admin', '2025-05-16 11:59:53', 1, 'admin', '2025-05-16 11:59:53', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(64, 21, '/coupon/delete', '3', '1', '优惠卷删除', 'coupon/delete', '/coupon/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.coupon/delete","iconType":"1","query":[],"icon":"","title":"优惠卷删除","order":0}', NULL, 1, 'admin', '2025-05-16 12:00:06', 1, 'admin', '2025-05-16 12:00:06', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(65, 22, '/notice/page', '3', '1', '通知公告查询', 'notice/page', '/notice/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.notice/page","iconType":"1","query":[],"icon":"","title":"通知公告查询","order":0}', NULL, 1, 'admin', '2025-05-16 14:06:42', 1, 'admin', '2025-05-16 14:06:42', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(66, 22, '/notice/create', '3', '1', '通知公告新增', 'notice/create', '/notice/create', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.notice/create","iconType":"1","query":[],"icon":"","title":"通知公告新增","order":0}', NULL, 1, 'admin', '2025-05-16 14:06:57', 1, 'admin', '2025-05-16 14:06:57', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(67, 22, '/notice/update', '3', '1', '通告公告编辑', 'notice/update', '/notice/update', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.notice/update","iconType":"1","query":[],"icon":"","title":"通告公告编辑","order":0}', NULL, 1, 'admin', '2025-05-16 14:07:15', 1, 'admin', '2025-05-16 14:07:15', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(69, 22, '/notice/delete', '3', '1', '通知公告删除', 'notice/delete', '/notice/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.notice/delete","iconType":"1","query":[],"icon":"","title":"通知公告删除","order":0}', NULL, 1, 'admin', '2025-05-16 14:08:12', 1, 'admin', '2025-05-16 14:08:12', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(70, 23, '/withdrawals/getWithdrawPage', '3', '1', '推广返现查询', 'withdrawals/getWithdrawPage', '/withdrawals/getWithdrawPage', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.withdrawals/getWithdrawPage","iconType":"1","query":[],"icon":"","title":"推广返现查询","order":0}', NULL, 1, 'admin', '2025-05-16 14:08:30', 1, 'admin', '2025-05-16 14:08:30', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(71, 23, '/withdrawals/delete', '3', '1', '推广提现删除', 'withdrawals/delete', '/withdrawals/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.withdrawals/delete","iconType":"1","query":[],"icon":"","title":"推广提现删除","order":0}', NULL, 1, 'admin', '2025-05-16 14:09:17', 1, 'admin', '2025-05-16 14:09:17', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(72, 23, '/withdrawals/getInviteDetails', '3', '1', '推广返现详情', 'withdrawals/getInviteDetails', '/withdrawals/getInviteDetails', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.withdrawals/getInviteDetails","iconType":"1","query":[],"icon":"","title":"推广返现详情","order":0}', NULL, 1, 'admin', '2025-05-16 14:09:42', 1, 'admin', '2025-05-16 14:09:42', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(73, 29, '/payLogs/page', '3', '1', '订单信息查询', 'payLogs/page', '/payLogs/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.payLogs/page","iconType":"1","query":[],"icon":"","title":"订单信息查询","order":0}', NULL, 1, 'admin', '2025-05-16 14:11:00', 1, 'admin', '2025-05-16 14:11:00', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(74, 29, '/payLogs/delete', '3', '1', '订单信息删除', 'payLogs/delete', '/payLogs/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.payLogs/delete","iconType":"1","query":[],"icon":"","title":"订单信息删除","order":0}', NULL, 1, 'admin', '2025-05-16 14:11:18', 1, 'admin', '2025-05-16 14:11:18', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(75, 29, '/payLogs/change-status', '2', '1', '订单信息手工处理', 'payLogs/change-status', '/payLogs/change-status', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.payLogs/change-status","iconType":"1","query":[],"icon":"","title":"订单信息手工处理","order":0}', NULL, 1, 'admin', '2025-05-16 14:11:35', 1, 'admin', '2025-05-16 14:11:35', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(78, 30, '/pay/page', '3', '1', '支付配置查询', 'pay/page', '/pay/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.pay/page","iconType":"1","query":[],"icon":"","title":"支付配置查询","order":0}', NULL, 1, 'admin', '2025-05-16 14:12:39', 1, 'admin', '2025-05-16 14:12:39', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(79, 30, '/pay/add', '3', '1', '支付配置新增', 'pay/add', '/pay/add', '', '{"localIcon":"","keepAlive":false,"constant":false,"multiTab":false,"query":[],"icon":"","title":"支付配置新增","hideInMenu":false,"activeMenu":"","i18nKey":"route.pay/add","iconType":"1","href":"","order":0}', NULL, 1, 'admin', '2025-05-16 14:13:33', 1, 'admin', '2025-05-16 14:13:33', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(80, 30, '/pay/update', '3', '1', '支付配置编辑', 'pay/update', '/pay/update', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.pay/update","iconType":"1","query":[],"icon":"","title":"支付配置编辑","order":0}', NULL, 1, 'admin', '2025-05-16 14:13:13', 1, 'admin', '2025-05-16 14:13:13', 0, NULL);
INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(81, 30, '/pay/delete', '3', '1', '支付配置删除', 'pay/delete', '/pay/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.pay/delete","iconType":"1","query":[],"icon":"","title":"支付配置删除","order":0}', NULL, 1, 'admin', '2025-05-16 14:13:55', 1, 'admin', '2025-05-16 14:13:55', 0, NULL);
-- ----------------------------
-- Table structure for t_sys_tenant
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_tenant`;
CREATE TABLE `t_sys_tenant`  (
 `id` int(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
 `tenant_id` int(0) NOT NULL COMMENT '租户编号',
 `contact_user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '联系人',
 `contact_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '联系电话',
 `tenant_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '租户名称',
 `domain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '域名',
 `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
 `package_id` int(0) DEFAULT NULL COMMENT '租户套餐编号',
 `expire_time` datetime(0) DEFAULT NULL COMMENT '过期时间',
 `status` int(0) DEFAULT 0 COMMENT '租户状态（0正常 1停用）',
 `is_deleted` int(0) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
 `create_id` int(20)    DEFAULT NULL COMMENT '创建者ID',
 `create_by` varchar(30) DEFAULT NULL COMMENT '创建者',
 `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `update_id`  int(20)    DEFAULT NULL COMMENT '创建者ID',
 `update_by` varchar(30) DEFAULT NULL COMMENT '更新者',
 `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
 `delete_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '删除时间',
 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '租户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_sys_user
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_user`;
CREATE TABLE `t_sys_user`  (
  `id` bigint unsigned NOT NULL,
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '账户昵称',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '用户名',
  `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '密码',
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '状态；1:可用，2:禁用',
  `otp_secret` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `user_gender` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '性别',
  `user_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '电话',
  `user_email` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '电子邮箱',
  `last_login_time` datetime(0) DEFAULT NULL,
  `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `create_id` bigint(0) DEFAULT NULL,
  `create_by` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `create_time` datetime(0) DEFAULT NULL,
  `update_id` bigint(0) DEFAULT NULL,
  `update_by` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `update_time` datetime(0) DEFAULT NULL,
  `is_deleted` tinyint(0) DEFAULT NULL,
  `delete_time` datetime(0) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_sys_user
-- ----------------------------
INSERT INTO `t_sys_user` VALUES (1, 'admin', 'admin', '$2a$10$x2PFc5dCRorVzAiDuoR02eujjcetDb9Dyimpt5m8x3EU.XIphyZW2', '1', NULL, '1', '', '', '2025-05-05 21:47:11', '***********', NULL, NULL, '2025-05-05 19:05:29', NULL, NULL, '2025-05-05 19:05:29', 0, NULL);

-- ----------------------------
-- Table structure for t_sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `t_sys_user_role`;
CREATE TABLE `t_sys_user_role`  (
  `id` bigint unsigned NOT NULL COMMENT 'Id',
  `user_id` bigint(0) NOT NULL COMMENT '用户id',
  `role_id` bigint(0) NOT NULL COMMENT '角色id',
  `create_id` bigint unsigned,
  `create_by` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `update_id` bigint unsigned,
  `update_by` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime(0) NOT NULL,
  `update_time` datetime(0) DEFAULT NULL,
  `is_deleted` tinyint(0) NOT NULL DEFAULT 0,
  `delete_time` datetime(0) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 71 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of t_sys_user_role
-- ----------------------------
INSERT INTO `t_sys_user_role` VALUES (1, 1, 1, 1, 'admin', 1, 'admin', '2024-03-09 10:37:52', '2024-03-09 10:38:04', 0, NULL);
INSERT INTO `t_sys_user_role` VALUES (2, 2, 1, 1, 'admin', 1, 'admin', '2024-03-10 13:10:39', '2024-03-10 13:10:41', 0, NULL);
INSERT INTO `t_sys_user_role` VALUES (62, 11, 1, NULL, NULL, NULL, NULL, '2025-05-03 22:03:45', NULL, 0, NULL);
INSERT INTO `t_sys_user_role` VALUES (63, 10, 1, NULL, NULL, NULL, NULL, '2025-05-03 22:03:59', NULL, 0, NULL);
INSERT INTO `t_sys_user_role` VALUES (64, 12, 1, NULL, NULL, NULL, NULL, '2025-05-03 22:08:04', NULL, 0, NULL);
INSERT INTO `t_sys_user_role` VALUES (67, 13, 1, NULL, NULL, NULL, NULL, '2025-05-03 22:17:04', NULL, 0, NULL);
INSERT INTO `t_sys_user_role` VALUES (68, 17, 1, NULL, NULL, NULL, NULL, '2025-05-05 10:00:01', NULL, 0, NULL);
INSERT INTO `t_sys_user_role` VALUES (69, 16, 1, NULL, NULL, NULL, NULL, '2025-05-05 10:02:58', NULL, 0, NULL);
INSERT INTO `t_sys_user_role` VALUES (75, 21, 12, NULL, NULL, NULL, NULL, '2025-05-05 14:08:53', NULL, 0, NULL);
INSERT INTO `t_sys_user_role` VALUES (81, 22, 1, NULL, NULL, NULL, NULL, '2025-05-05 19:05:29', '2025-05-05 19:05:29', 0, NULL);

-- ----------------------------
-- Table structure for task_info
-- ----------------------------
DROP TABLE IF EXISTS `task_info`;
CREATE TABLE `task_info`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `jobId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务ID',
  `repeatConf` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '重复配置',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务名称',
  `cron` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'cron表达式',
  `limit` bigint(0) DEFAULT NULL COMMENT '限制次数 不传为不限制',
  `every` bigint(0) DEFAULT NULL COMMENT '间隔时间 单位秒',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `status` bigint(0) DEFAULT NULL COMMENT '状态 0:关闭 1:开启',
  `startDate` datetime(3) DEFAULT NULL COMMENT '开始时间',
  `endDate` datetime(3) DEFAULT NULL COMMENT '结束时间',
  `data` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据',
  `service` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '执行的服务',
  `type` bigint(0) DEFAULT NULL COMMENT '类型 0:系统 1:用户',
  `nextRunTime` datetime(3) DEFAULT NULL COMMENT '下次执行时间',
  `taskType` bigint(0) DEFAULT NULL COMMENT '任务类型 0:cron 1:时间间隔',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_task_info_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of task_info
-- ----------------------------
INSERT INTO `task_info` VALUES (1, '2024-01-01 13:15:00.083', '2024-12-17 11:22:50.133', NULL, NULL, NULL, '清理日志', '1 2 3 * * *', NULL, NULL, '每天03:02:01执行清理缓存任务', 1, NULL, NULL, NULL, 'BaseFuncClearLog(false)', 0, '2024-12-18 03:02:01.000', 0);

-- ----------------------------
-- Table structure for task_log
-- ----------------------------
DROP TABLE IF EXISTS `task_log`;
CREATE TABLE `task_log`  (
  `id` bigint unsigned NOT NULL,
  `createTime` datetime(3) NOT NULL COMMENT '创建时间',
  `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL,
  `taskId` bigint unsigned COMMENT '任务ID',
  `status` tinyint unsigned NOT NULL COMMENT '状态 0:失败 1:成功',
  `detail` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '详情',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_task_log_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of task_log
-- ----------------------------
INSERT INTO `task_log` VALUES (1, '2024-01-07 03:02:02.293', '2024-01-07 03:02:02.293', NULL, 1, 1, '任务执行成功');

-- ----------------------------
-- Table structure for user_drawing_quota
-- ----------------------------
DROP TABLE IF EXISTS `user_drawing_quota`;
CREATE TABLE `user_drawing_quota`  (
  `id` bigint(0) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(0) NOT NULL COMMENT '用户ID',
  `total_quota` bigint(0) NOT NULL DEFAULT 0 COMMENT '总配额',
  `used_quota` bigint(0) NOT NULL DEFAULT 0 COMMENT '已使用配额',
  `remaining_quota` bigint(0) NOT NULL DEFAULT 0 COMMENT '剩余配额',
  `reset_at` datetime(3) DEFAULT NULL COMMENT '到期时间',
  `created_at` datetime(3) NOT NULL COMMENT '创建时间',
  `updated_at` datetime(3) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户绘图配额表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_drawing_quota
-- ----------------------------
INSERT INTO `user_drawing_quota` VALUES (1, 999999999, 3, 3, 0, '2026-02-20 00:21:28.016', '2025-04-26 00:21:28.019', '2025-04-28 21:32:05.000');

SET FOREIGN_KEY_CHECKS = 1;



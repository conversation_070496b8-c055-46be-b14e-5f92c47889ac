import request from "../utils/request";


export function getPayList () { 
    return request({
        url: '/pay/list',
        method: 'get',
    })
}


// 查询当面付支付状态
export function queryF2FOrderStatus (params) {
    return request({
        url: '/f2f/status',
        params,
        method: 'get',
    });
}

// 查询易支付支付状态
export function queryEpayOrderStatus (params) {
    return request({
        url: '/ePay/status',
        params,
        method: 'get',
    });
}

// 查询虎皮椒支付状态
export function queryXhOrderStatus (params) {
    return request({
        url: '/xunHu/status',
        params,
        method: 'get',
    });
}
// 查询蓝兔支付状态
export function queryLanTuPayStatus(params) {
    return request({
        url: '/lantu/status',
        params,
        method: 'get',
    });
}
// 创建支付链接
export function createQrCode (params) {
    return request({
        url: '/create/qrcode',
        params,
        method: 'get',
    });
}

// 查询微信native支付状态
export function queryWxNativeStatus (params) {
    return request({
        url: '/native/status',
        params,
        method: 'get',
    });
}

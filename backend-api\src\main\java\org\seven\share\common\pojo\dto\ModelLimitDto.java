package org.seven.share.common.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: ModelLimitDto
 * @Description:
 * @Author: Seven
 * @Date: 2025/1/9
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModelLimitDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Integer limit;

    private String per;

}

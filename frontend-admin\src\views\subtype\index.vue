<script setup lang="tsx">
import { ref } from 'vue';
import { <PERSON>Button, ElMessage, ElPopconfirm, ElSwitch, ElTag, ElTooltip } from 'element-plus';
import type { TableColumnCtx } from 'element-plus';
import { useBoolean } from '@sa/hooks';
import { fetchSubTypePage, removeSubTypeBatchById, updateSubType } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import { usePermission } from '@/hooks/business/auth';
import SubtypeTableHeaderOperation from './modules/subtype-table-header-operation.vue';
import SubtypeOperateModel, { type OperateType } from './modules/subtype-operate-modal.vue';
import SubtypeSearch from './modules/subtype-search.vue';
const { hasPermission } = usePermission();

// Define the proper type for your table data
interface Subtype {
  id: string;
  name: string;
  money: number;
  validDays: string;
  isPlus: string | number;
  sort: number;
  isPro: string | number;
  isNotValued: number;
  features: Array<any>;
  modelLimits: Record<string, any>;
  createTime: string;
  updateTime: string;
  subType: string;
  exclusiveType: number;
  drawQuota: number;
  [key: string]: any; // Add index signature for additional properties
}

type TableFormatter = (row: any, column: TableColumnCtx<any>, cellValue: any, index: number) => any;

const wrapperRef = ref<HTMLElement | null>(null);
const operateType = ref<OperateType>('create');
const { bool: visible, setTrue: openModal } = useBoolean();
const currentData = ref<any>(null);
function getModelLimitsTooltip(modelLimits: Record<string, any>) {
  if (!modelLimits) return '暂无限制';

  // 时间周期转换
  const periodMap: Record<string, string> = {
    '1w': '每周',
    '3h': '每3小时',
    '5h': '每5小时',
    '1s': '每秒',
    '1m': '每分钟',
    '1h': '每小时',
    '1M': '每月',
    '1d': '每天',
    '1y': '每年',
    '': '无限制'
  };

  // 处理每个模型的限制
  const lines = Object.entries(modelLimits)
    .map(([model, config]) => {
      if (typeof config === 'object' && config !== null) {
        const limit = config.limit;
        const period = config.per;
        const displayPeriod = periodMap[period] || period;

        if (limit === '0' || !limit) {
          return `${model}: 无限制`;
        }

        return `${model}: ${displayPeriod}${limit}次`;
      }
      return `${model}: ${JSON.stringify(config)}`;
    })
    .join('<br>');

  return lines || '暂无限制';
}

async function changeSwitch(id: string, value: number) {
  try {
    await updateSubType({ id, isNotValued: value });
    ElMessage.success('更新成功');
    getData();
  } catch (error) {
    ElMessage.error('更新失败');
  }
}

function handleEdit(row: Subtype) {
  operateType.value = 'edit';
  currentData.value = data.value.find(item => item.id === row.id);
  openModal();
}

function handleCopy(row: Subtype) {
  operateType.value = 'copy';
  currentData.value = data.value.find(item => item.id === row.id);
  openModal();
}

async function handleDelClick(id: string) {
  await removeSubTypeBatchById([id]);
  ElMessage({
    message: '删除成功',
    type: 'success',
    plain: true
  });
  getData();
}

const { columns, columnChecks, data, loading, pagination, getData, searchParams, resetSearchParams, getDataByPage } =
  useTable<UI.TableApiFn<Subtype>>({
    apiFn: fetchSubTypePage,
    columns: () => [
      { type: 'selection', width: 48 },
      { type: 'index', label: '序号', align: 'center', width: 60 },
      { prop: 'name', label: '订阅名称', align: 'center', minWidth: 300, showOverflowTooltip: true },
      {
        prop: 'subType',
        label: '订阅类型',
        align: 'center',
        sortable: true,
        width: 160,
        formatter: ((row: any) => {
          const typeMap = {
            '1': { type: 'primary' as const, label: 'ChatGPT' },
            '2': { type: 'success' as const, label: 'Claude' },
            '3': { type: 'danger' as const, label: 'ChatGPT&Claude' },
            '4': { type: 'warning' as const, label: 'Grok' },
            '5': { type: 'info' as const, label: 'ChatGPT&Grok' },
            '6': { type: 'danger' as const, label: 'Claude&Grok' },
            '7': { type: 'primary' as const, label: 'ChatGPT&Claude&Grok' },
            '8': { type: 'success' as const, label: 'Draw' }
          };
          const config = typeMap[row.subType as keyof typeof typeMap] || { type: 'info' as const, label: '未知' };
          return <ElTag type={config.type}>{config.label}</ElTag>;
        }) as TableFormatter
      },
      { prop: 'money', label: '金额', align: 'center', width: 100 },
      { prop: 'validDays', label: '有效天数', align: 'center', width: 100 },
      { prop: 'drawQuota', label: '绘画配额', align: 'center', width: 100 },
      { prop: 'tenantName', label: '所属分站', align: 'center', width: 100, showOverflowTooltip: true },
      {
        prop: 'isPlus',
        label: '可用Plus',
        align: 'center',
        sortable: true,
        width: 120,
        formatter: ((row: any) =>
          row.isPlus === '1' || row.isPlus === 1 ? (
            <ElTag type="danger">是</ElTag>
          ) : (
            <ElTag type="primary">否</ElTag>
          )) as TableFormatter
      },
      {
        prop: 'isPro',
        label: '可用Pro',
        align: 'center',
        sortable: true,
        width: 120,
        formatter: ((row: any) =>
          row.isPro === '1' || row.isPro === 1 ? (
            <ElTag type="danger">是</ElTag>
          ) : (
            <ElTag type="primary">否</ElTag>
          )) as TableFormatter
      },
      {
        prop: 'isNotValued',
        label: '前台展示',
        align: 'center',
        sortable: true,
        width: 120,
        formatter: ((row: any) => (
          <ElSwitch
            modelValue={row.isNotValued}
            activeValue={0}
            inactiveValue={1}
            onChange={() => changeSwitch(row.id, row.isNotValued === 0 ? 1 : 0)}
          />
        )) as TableFormatter
      },
      {
        prop: 'modelLimits',
        label: '模型限制',
        align: 'center',
        sortable: true,
        width: 120,
        formatter: ((row: any) => (
          <ElTooltip effect="dark" content={getModelLimitsTooltip(row.modelLimits)} placement="top" raw-content>
            <span class="cursor-pointer text-xs">查看详情</span>
          </ElTooltip>
        )) as TableFormatter
      },
      { prop: 'createTime', label: '创建时间', align: 'center', sortable: true, width: 180 },
      { prop: 'updateTime', label: '更新时间', align: 'center', sortable: true, width: 180 },
      {
        prop: 'operate',
        label: '操作',
        align: 'center',
        fixed: 'right',
        width: 200,
        formatter: (row: any) => (
          <div class="flex-center gap-8px">
            {hasPermission('subtype/update') && (
              <ElButton type="primary" plain size="small" onClick={() => handleEdit(row)}>
                {$t('common.edit')}
              </ElButton>
            )}
            {hasPermission('subtype/add') && (
              <ElButton type="success" plain size="small" onClick={() => handleCopy(row)}>
                复制
              </ElButton>
            )}
            <ElPopconfirm title={$t('common.confirmDelete')} onConfirm={() => handleDelClick(row.id)}>
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small" v-show={hasPermission('subtype/delete')}>
                    {$t('common.delete')}
                  </ElButton>
                )
              }}
            </ElPopconfirm>
          </div>
        )
      }
    ]
  });

const { checkedRowKeys, onBatchDeleted } = useTableOperate(data as any, getData);

async function handleBatchDelete() {
  await removeSubTypeBatchById(checkedRowKeys.value);
  onBatchDeleted();
}

function handleAdd() {
  operateType.value = 'create';
  currentData.value = null;
  openModal();
}
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <SubtypeSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <div class="flex items-center">
            <SubtypeTableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              :show-add="hasPermission('subtype/add')"
              :show-delete="hasPermission('subtype/delete')"
              :show-sync="hasPermission('subtype/model/sync')"
              @add="handleAdd"
              @delete="handleBatchDelete"
              @refresh="getData"
            />
          </div>
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="(selection: Subtype[]) => checkedRowKeys = selection.map(item => item.id)"
        >
          <ElTableColumn
            v-for="col in columns"
            :key="col.prop"
            v-bind="col"
            :show-overflow-tooltip="col.showOverflowTooltip"
          />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
      <SubtypeOperateModel
        v-model:visible="visible"
        :operate-type="operateType"
        :data="currentData"
        @submitted="getData"
      />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>

<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { ElButton, ElMessage, ElPopconfirm, ElSwitch, ElTag } from 'element-plus';
import { fetchPayConfigPage, removePayConfigBatchByIds, updatePayConfig } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import { usePermission } from '@/hooks/business/auth';
import PayOperateModal from './modules/pay-operate-modal.vue';
const { hasPermission } = usePermission();
interface PayConfig {
  id: number;
  title: string;
  appId: string;
  appKey: string;
  paymentsType: number;
  status: number;
  sort: number;
  createTime: string;
  updateTime: string;
  extraData: {
    payGatewayUrl: string;
    notifyUrl: string;
    privateKeyPem: string;
    merchantId?: string;
    serialNumber?: string;
    enableH5?: boolean;
  };
  iconUrl?: string;
}

const wrapperRef = ref<HTMLElement | null>(null);

const {
  columns,
  columnChecks,
  data,
  loading: tableLoading,
  pagination,
  getData
} = useTable<PayConfig>({
  apiFn: fetchPayConfigPage,
  columns: () => [
    { type: 'selection', width: 48 },
    { prop: 'index', label: $t('common.index'), width: 64 },
    { prop: 'title', label: '名称', width: 100, showOverflowTooltip: true },
    { prop: 'appId', label: 'AppId', minWidth: 200 },
    { prop: 'appKey', label: 'AppKey', width: 300, showOverflowTooltip: true },
    {
      prop: 'paymentsType',
      label: '支付方式',
      width: 160,
      align: 'center',
      sortable: true,
      formatter: (row: PayConfig) => {
        const typeMap: Record<number, { type: 'success' | 'primary' | 'danger' | 'warning' | 'info'; label: string }> =
          {
            1: { type: 'success', label: '易支付（支付宝）' },
            2: { type: 'primary', label: '易支付（微信）' },
            3: { type: 'danger', label: '支付宝（当面付）' },
            4: { type: 'warning', label: '虎皮椒（微信）' },
            5: { type: 'info', label: '蓝兔微信支付' },
            6: { type: 'success', label: '易支付（usdt）' },
            7: { type: 'primary', label: '微信支付（native）' },
            8: { type: 'info', label: '虎皮椒（支付宝）' },
            9: { type: 'danger', label: '支付宝（网站）' }
          };
        const config = typeMap[row.paymentsType] || { type: 'info', label: '未知' };
        return <ElTag type={config.type}>{config.label}</ElTag>;
      }
    },
    {
      prop: 'status',
      label: '状态',
      minWidth: 100,
      align: 'center',
      sortable: true,
      formatter: (row: PayConfig) => (
        <ElSwitch v-model={row.status} active-value={1} inactive-value={0} onChange={() => changeStatus(row)} />
      )
    },
    { prop: 'sort', label: '排序', width: 100, align: 'center', sortable: true },
    { prop: 'createTime', label: '创建时间', width: 180 },
    { prop: 'updateTime', label: '更新时间', width: 180 },
    {
      prop: 'operate',
      label: $t('common.operate'),
      align: 'center',
      fixed: 'right',
      width: 140,
      formatter: (row: PayConfig) => (
        <div class="flex-center">
          {hasPermission('pay/update') && (
            <ElButton type="primary" plain size="small" onClick={() => handleEdit(row)}>
              {$t('common.edit')}
            </ElButton>
          )}
          <ElPopconfirm title={$t('common.confirmDelete')} onConfirm={() => handleDelete(row.id)}>
            {{
              reference: () => (
                <ElButton type="danger" plain size="small" v-show={hasPermission('pay/delete')}>
                  {$t('common.delete')}
                </ElButton>
              )
            }}
          </ElPopconfirm>
        </div>
      )
    }
  ]
});

const { checkedRowKeys, onBatchDeleted } = useTableOperate(data, getData);

const handleDelete = async (id: number) => {
  await removePayConfigBatchByIds([id]);
  ElMessage({
    message: '删除成功',
    type: 'success',
    plain: true
  });
  getData();
};

const handleEdit = (row: PayConfig) => {
  operateModalType.value = 'update';
  operateModalData.value = { ...row };
  operateModalVisible.value = true;
};

const handleAddClick = () => {
  operateModalType.value = 'add';
  operateModalData.value = null;
  operateModalVisible.value = true;
};

const handleBatchDelete = () => {
  removePayConfigBatchByIds(checkedRowKeys.value);
  onBatchDeleted();
};

const changeStatus = async (data: PayConfig) => {
  await updatePayConfig(data);
  ElMessage({
    message: '修改成功',
    type: 'success',
    plain: true
  });
  getData();
};

const operateModalVisible = ref(false);
const operateModalType = ref<'add' | 'update'>('add');
const operateModalData = ref<PayConfig | null>(null);

const handleOperateModalSubmitted = () => {
  getData();
};

onMounted(() => {
  getData();
});
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="tableLoading"
            :show-add="hasPermission('pay/add')"
            @add="handleAddClick"
            @delete="handleBatchDelete"
            @refresh="getData"
          />
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="tableLoading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="(selection: PayConfig[]) => checkedRowKeys = selection.map(item => item.id.toString())"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
    </ElCard>

    <PayOperateModal
      v-model:visible="operateModalVisible"
      :operate-type="operateModalType"
      :row-data="operateModalData"
      @submitted="handleOperateModalSubmitted"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>

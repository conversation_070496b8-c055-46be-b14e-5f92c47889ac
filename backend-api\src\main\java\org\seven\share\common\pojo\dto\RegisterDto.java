package org.seven.share.common.pojo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * @ClassName: UserInfoDto
 * @Description: 注册实体
 * @Author: Seven
 * @Date: 2024/7/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RegisterDto extends LoginDto{

    @NotBlank(message = "邮箱不能为空")
    @Email
    private String email;

    private String inviter;

}

package org.seven.share.service.impl;

import cn.hutool.captcha.AbstractCaptcha;
import cn.hutool.captcha.generator.CodeGenerator;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.api.Result;
import org.seven.share.common.api.ResultCode;
import org.seven.share.common.enums.CacheConstants;
import org.seven.share.common.enums.DelStatusEnums;
import org.seven.share.common.enums.StatusEnums;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.exception.ServiceException;
import org.seven.share.common.pojo.entity.SysRole;
import org.seven.share.common.pojo.entity.SysUser;
import org.seven.share.common.pojo.vo.CaptchaVo;
import org.seven.share.common.pojo.vo.LoginResult;
import org.seven.share.common.pojo.vo.SysUserVO;
import org.seven.share.common.util.IpUtil;
import org.seven.share.common.util.JwtTokenUtil;
import org.seven.share.common.util.SecurityUtil;
import org.seven.share.common.util.SysUserDetail;
import org.seven.share.mapper.CreateTableMapper;
import org.seven.share.mapper.SysRoleMapper;
import org.seven.share.mapper.SysUserMapper;
import org.seven.share.param.CreateUserParam;
import org.seven.share.service.SysRoleService;
import org.seven.share.service.SysUserRoleService;
import org.seven.share.service.SysUserService;
import org.seven.share.vo.UserInfoVO;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.seven.share.common.util.ConstantUtil.SYS_TENANT_ID;
import static org.seven.share.constant.CacheConstant.CAPTCHA_CODE_KEY;

@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser>
        implements SysUserService {

    final AuthenticationManager authenticationManager;

    final JwtTokenUtil jwtTokenUtil;

    final UserDetailsService userDetailsService;

    final SysRoleService sysRoleService;

    final SysUserRoleService sysUserRoleService;

    final PasswordEncoder passwordEncoder;

    final CreateTableMapper createTableMapper;

    private final CacheManager cacheManager;

    @PostConstruct
    public void init() {
        createTableMapper.createSysUserTable();
        if (count() == 0) {
            createTableMapper.insertSysUser();
        }
    }

    final RedisTemplate<String, String> redisTemplate;

    private static final PasswordEncoder encoder = new BCryptPasswordEncoder();

    @Override
    public IPage<SysUserVO> getList(Map<String, Object> params) {
        int pageSize = Integer.parseInt(String.valueOf(params.get("size")));
        int pageNum = Integer.parseInt(String.valueOf(params.get("current")));
        LambdaQueryWrapper<SysUser> wrapper = createWrapper(params);

        IPage<SysUser> sysUserPage = page(new Page<>(pageNum, pageSize), wrapper);

        // 将查询结果中的SysUser对象转换为SysUserVO对象
        List<SysUserVO> sysUserVOList = sysUserPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());


        if (!sysUserVOList.isEmpty()) {
            sysUserVOList.forEach(userVO -> {
                Long id = userVO.getId();
                List<String> userRole = sysRoleService.getUserRole(id);
                userVO.setUserRoles(userRole);
            });
        }

        // 创建新的IPage对象，其中包含转换后的SysUserVO对象列表
        IPage<SysUserVO> sysUserVOPage = new Page<>();
        sysUserVOPage.setRecords(sysUserVOList);
        sysUserVOPage.setCurrent(sysUserPage.getCurrent());
        sysUserVOPage.setSize(sysUserPage.getSize());
        sysUserVOPage.setTotal(sysUserPage.getTotal());

        return sysUserVOPage;
    }

    public SysUserVO convertToVO(SysUser sysUser) {
        SysUserVO sysUserVO = new SysUserVO();
        BeanUtil.copyProperties(sysUser, sysUserVO);
        return sysUserVO;
    }

    private LambdaQueryWrapper<SysUser> createWrapper(Map<String, Object> params) {
        String username = (String) params.get("userName");
        String status = (String) params.get("status");
        String userGender = (String) params.get("userGender");
        String nickName = (String) params.get("nickName");
        String userPhone = (String) params.get("userPhone");
        String userEmail = (String) params.get("userEmail");

        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotEmpty(username), SysUser::getUserName, username);
        wrapper.eq(StrUtil.isNotEmpty(status), SysUser::getStatus, status);
        wrapper.eq(StrUtil.isNotEmpty(userGender), SysUser::getUserGender, userGender);
        wrapper.eq(StrUtil.isNotEmpty(nickName), SysUser::getNickName, nickName);
        wrapper.eq(StrUtil.isNotEmpty(userPhone), SysUser::getUserPhone, userPhone);
        wrapper.eq(StrUtil.isNotEmpty(userEmail), SysUser::getUserEmail, userEmail);
        wrapper.eq( SysUser::getIsDeleted, DelStatusEnums.DISABLE.getCode());

        return wrapper;
    }

    /**
     * 更新最后一次登录时间/登录IP
     */
    private void updateLastLoginInfo(SysUser sysUser) {
        sysUser.setLastLoginIp(IpUtil.getHostIp());
        sysUser.setLastLoginTime(DateUtil.date().toLocalDateTime());
        updateById(sysUser);
    }

    @Override
    @CacheEvict(value = CacheConstants.USER_DETAILS, key = "#username")
    public LoginResult login(String username, String password) {
        LoginResult res = new LoginResult();
        String token;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            Authentication authenticate = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(username, password));
            SecurityContextHolder.getContext().setAuthentication(authenticate);
            token = jwtTokenUtil.generateToken(authenticate.getName());

            SysUser sysUser = SecurityUtil.getSysUser();

            if (sysUser == null) {
                throw new CustomException("获取用户信息失败");
            }

            // 更新最后一次登录时间
            updateLastLoginInfo(sysUser);

            res.setToken(token);
            res.setRefreshToken(token);
            return res;
        }
        catch (Exception e) {
            log.error("登录异常: {}", e.getMessage());
            throw new CustomException(e.getMessage());
        }
    }

    public SysUser getByNickName(String nickName) {
        return getOne(Wrappers.<SysUser>lambdaQuery().eq(SysUser::getNickName, nickName));
    }

    public SysUser getByUserName(String userName) {
        return getOne(Wrappers.<SysUser>lambdaQuery().eq(SysUser::getUserName, userName));
    }

    private Result<String> checkoutUser(CreateUserParam userParam) {
        SysUser sysUser;

        sysUser = getByNickName(userParam.getNickName());
        if (sysUser != null) {
            if (!Objects.equals(userParam.getId(), sysUser.getId())) {
                return Result.failed(ResultCode.ERROR_USER_NAME_REPEAT);
            }
        }

        sysUser = getByUserName(userParam.getUserName());
        if (sysUser != null) {
            if (!Objects.equals(userParam.getId(), sysUser.getId())) {
                return Result.failed(ResultCode.ERROR_NAME_REPEAT);
            }
        }
        return Result.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean insertUser(CreateUserParam createUserParam) {
        SysUser user = new SysUser();
        // 处理加密密码,初始密码123456
        String enPassword = encoder.encode("123456");

        user.setNickName(createUserParam.getNickName());
        user.setUserName(createUserParam.getUserName());
        user.setUserGender(createUserParam.getUserGender());
        user.setUserPhone(createUserParam.getUserPhone());
        user.setUserEmail(createUserParam.getUserEmail());
        user.setPassword(enPassword);
        user.setStatus(StatusEnums.ENABLE.getCode());
        user.setIsDeleted(DelStatusEnums.DISABLE.getCode());
        user.setTenantId(StrUtil.isBlank(createUserParam.getTenantId())? SYS_TENANT_ID : createUserParam.getTenantId());

        return save(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> createUser(CreateUserParam createUserParam) {
        Result<String> checkoutResult = checkoutUser(createUserParam);
        if (!Objects.equals(checkoutResult.getCode(), ResultCode.SUCCESS.getCode())) {
            return checkoutResult;
        }

        if (!insertUser(createUserParam)) {
            Result.failed(ResultCode.ERROR_CREATE_USER);
        }

        // 处理用户角色
        bindingUserRoles(createUserParam);

        return Result.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<UserInfoVO> getUserInfo(String authorizationHeader) {
        if (authorizationHeader == null || !authorizationHeader.startsWith(jwtTokenUtil.getTokenHead())) {
            throw new ServiceException(401, "登录失败");
        }
        UserInfoVO userInfoVO = new UserInfoVO();
        try {
            String authToken = authorizationHeader.substring(jwtTokenUtil.getTokenHead().length());
            String username = jwtTokenUtil.getUserNameFromToken(authToken);
            if (username != null) {
                // 从数据库中获取用户信息
                SysUserDetail userDetails = (SysUserDetail) this.userDetailsService.loadUserByUsername(username);
                SysUser sysUser = userDetails.getSysUser();
                userInfoVO.setRoles(userDetails.getRoles());
                userInfoVO.setUserName(userDetails.getUsername());
                userInfoVO.setUserId(sysUser.getId());
                userInfoVO.setNickName(sysUser.getNickName());
                userInfoVO.setUserEmail(sysUser.getUserEmail());
                userInfoVO.setUserPhone(sysUser.getUserPhone());
                userInfoVO.setUserGender(sysUser.getUserGender());
                userInfoVO.setPermissions(userDetails.getPermissions());
                userInfoVO.setTenantId(sysUser.getTenantId());
                return Result.success(userInfoVO);
            }
        } catch (Exception e) {
            log.info("获取用户信息失败: {}", e.getMessage());
            throw new CustomException("获取用户信息失败");
        }
        return Result.failed();
    }

    @Override
    @CacheEvict(cacheNames = CacheConstants.USER_DETAILS, key = "#createUserParam.userName")
    public Result<String> updateUser(CreateUserParam createUserParam) {
        log.info("修改用户入参: {}", JSONUtil.parse(createUserParam));

        Result<String> checkoutResult = checkoutUser(createUserParam);
        if (!Objects.equals(checkoutResult.getCode(), ResultCode.SUCCESS.getCode())) {
            return checkoutResult;
        }

        SysUser user = new SysUser();
        user.setId(createUserParam.getId());
        user.setNickName(createUserParam.getNickName());
        user.setUserName(createUserParam.getUserName());
        user.setUserGender(createUserParam.getUserGender());
        user.setUserPhone(createUserParam.getUserPhone());
        user.setUserEmail(createUserParam.getUserEmail());
        user.setStatus(createUserParam.getStatus());
        user.setTenantId(StrUtil.isEmpty(createUserParam.getTenantId()) ? SYS_TENANT_ID :createUserParam.getTenantId());
        updateById(user);

        // 修改用户角色信息
        bindingUserRoles(createUserParam);
        return Result.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = CacheConstants.USER_DETAILS, allEntries = true)
    public void deleteSysUserBatch(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return ;
        }
        // 删除用户与角色关联的信息
        sysUserRoleService.deleteUserRoleRelationBatch(ids);

        // 删除用户表中的信息
        removeBatchByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateSysUserPwd(String oldPassword, String newPassword) {
        if (oldPassword == null || newPassword == null) {
            throw new CustomException("密码不能为空");
        }
        try{
            SysUser sysUser = SecurityUtil.getSysUser();
            if (sysUser == null) {
                throw new CustomException("用户信息获取失败");
            }
            String username = sysUser.getUserName();
            authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(username, oldPassword));
            // 加密新密码
            String encodedNewPassword = passwordEncoder.encode(newPassword);

            // 更新密码
            sysUser.setPassword(encodedNewPassword);
            boolean updated = updateById(sysUser);
            log.info("用户密码更新结果: {}", updated);
            if (!updated) {
                throw new CustomException("密码更新失败");
            }
            // 清空缓存信息
            Cache cache = cacheManager.getCache(CacheConstants.USER_DETAILS);
            if (cache != null) {
                cache.evict(username);  // 用username作为key清除特定用户缓存
            }
            return true;
        } catch (AuthenticationException e) {
            throw new CustomException("原密码错误");
        } catch (Exception e) {
            throw new CustomException("密码更新失败，请稍后重试");
        }
    }


    private void bindingUserRoles(CreateUserParam userParam) {
        List<String> userRoles = userParam.getUserRoles();
        if (!userRoles.isEmpty()) {
            SysUser sysUser = getByUserName(userParam.getUserName());
            Result<Boolean> result = sysUserRoleService.bindingUserRoles(userRoles, sysUser.getId());
            if (!Objects.equals(result.getCode(), ResultCode.SUCCESS.getCode())) {
                throw new CustomException("用户绑定角色失败");
            }
        }
    }


}

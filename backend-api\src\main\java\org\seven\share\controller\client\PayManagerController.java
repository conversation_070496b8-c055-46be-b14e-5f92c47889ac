package org.seven.share.controller.client;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.annotation.JwtToken;
import org.seven.share.common.api.R;
import org.seven.share.common.enums.PaymentMethod;
import org.seven.share.common.pojo.vo.ChatGptPayConfigVo;
import org.seven.share.common.pojo.vo.UserPayLogsVo;
import org.seven.share.service.ChatGptPayConfigService;
import org.seven.share.service.ChatGptUserService;
import org.seven.share.service.PayService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: PayManagerController
 * @Description: 支付回调接口
 * @Author: Seven
 * @Date: 2024/6/28
 */
@RestController
@RequestMapping({"/client-api", "/api"}) // 支持多个路径前缀
@Slf4j
public class PayManagerController {

    @Resource
    private PayService payService;

    @Resource
    private ChatGptUserService chatGptUserService;

    /**
     * 易支付异步回调
     * @param request 请求
     */
    @GetMapping("/callBack/ePayNotify")
    public void notify(HttpServletRequest request) {
        log.info("收到易支付异步回调请求");
        payService.ePayNotify(request);
    }

    /**
     * 统一跳转接口
     * @param request 请求
     * @throws IOException 异常
     */
    @GetMapping("/callBack/payReturn")
    public void payReturn(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("收到跳转请求");
        // 回到选车页面
        String url = request.getRequestURL().toString();
        if (StrUtil.isNotBlank(url)) {
            String returnUrl = url.replace("/expander-api/callBack/payReturn", "/list");
            log.info("跳转地址：{}", returnUrl);
            response.sendRedirect("/list/#/home");
        }
    }

    /**
     * 当面付异步回调
     * @param request 请求
     */
    @PostMapping("/callBack/aliPayNotify")
    public String aliPayNotify(HttpServletRequest request) {
        log.info("收到当面付异步回调请求");
        return payService.aliPayNotify(request, PaymentMethod.F2F_PAY.getCode());
    }

    /**
     * 虎皮椒回调
     * @param request 请求
     */
    @PostMapping("/callBack/xunHuPayNotify")
    public String xunHuPayNotify(HttpServletRequest request) {
        log.info("收到虎皮椒微信异步回调请求");
        return payService.xunHuPayNotify(request, PaymentMethod.HU_PI_JIAO_WECHAT.getCode());
    }

    /**
     * 虎皮椒支付宝回调
     * @param request 请求
     */
    @PostMapping("/callBack/xunHuAliPayNotify")
    public String xunHuAliPayNotify(HttpServletRequest request) {
        log.info("收到虎皮椒支付宝异步回调请求");
        return payService.xunHuPayNotify(request, PaymentMethod.HU_PI_JIAO_ALIPAY.getCode());
    }

    /**
     * 易支付订单状态查询
     * @param tradeNo 订单号
     * @return 支付状态
     */
    @GetMapping("/ePay/status")
    @JwtToken
    public String orderStatus(@RequestParam String tradeNo){
        return payService.getOrderStatus(tradeNo);
    }

    /**
     * 生成支付链接
     * @param typeId 套餐类型
     * @param payType 支付类型
     * @param userToken 用户名
     * @param coupon 折扣
     * @return 二维码信息
     */
    @GetMapping ("/create/qrcode")
    @JwtToken
    public R createQrCode(@RequestParam("typeId") String typeId,
                          @RequestParam("payType") Integer payType,
                          @RequestParam("userToken") String userToken,
                          @RequestParam("coupon") String coupon,
                          @RequestParam("isMobile") boolean isMobile) {
        Map<String, Object> map = payService.getQrCode(typeId, payType, userToken, coupon, isMobile);
        return R.ok(map);
    }

    @GetMapping("/f2f/status")
    @JwtToken
    public String f2fOrderQuery(@RequestParam("tradeNo") String tradeNo){
        return payService.queryF2FOrderStatus(tradeNo);
    }

    /**
     * 虎皮椒订单状态查询
     * @param tradeNo 订单号
     * @return 订单支付状态
     */
    @GetMapping("/xunHu/status")
    @JwtToken
    public String queryHpPayStatus(@RequestParam String tradeNo, @RequestParam int payMethod){
        return payService.queryHpPayStatus(tradeNo, payMethod);
    }

    @PostMapping("/callBack/lanTuPayNotify")
    public ResponseEntity<?> lanTuPayNotify(@RequestParam Map<String, String> paramMap) {
        log.info("收到蓝兔异步回调请求");
        return payService.lanTuPayNotify(paramMap);
    }

    /**
     * 蓝兔支付订单状态查询
     * @param tradeNo 订单号
     * @return 订单支付状态
     */
    @GetMapping("/lantu/status")
    @JwtToken
    public String queryLanTuPayStatus(@RequestParam String tradeNo){
        return payService.queryLanTuPayStatus(tradeNo);
    }


    @PostMapping("/callBack/wxNativeNotify")
    public String wxNativeNotify(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("收到微信支付native异步回调请求");
        return payService.wxNativeNotify(request, response);
    }

    /**
     * 虎皮椒订单状态查询
     * @param tradeNo 订单号
     * @return 订单支付状态
     */
    @GetMapping("/native/status")
    @JwtToken
    public String queryNativeStatus(@RequestParam String tradeNo){
        return payService.queryNativeStatus(tradeNo);
    }

    @PostMapping("/callBack/aliPayWapNotify")
    public String aliPayWapNotify(HttpServletRequest request) {
        log.info("收到支付宝网站支付异步回调请求");
        return payService.aliPayNotify(request, PaymentMethod.ALIPAY_WAP_PAY.getCode());
    }

    /**
     * 支付记录
     * @param userToken
     * @return
     */
    @JwtToken
    @GetMapping("/payment/history")
    public R getMacData(@RequestParam String userToken){
        List<UserPayLogsVo> list = chatGptUserService.getPaymentHistory(userToken);
        return R.ok(list);
    }



}

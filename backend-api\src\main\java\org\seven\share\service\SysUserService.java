package org.seven.share.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.api.Result;
import org.seven.share.common.pojo.entity.SysUser;
import org.seven.share.common.pojo.vo.CaptchaVo;
import org.seven.share.common.pojo.vo.LoginResult;
import org.seven.share.common.pojo.vo.SysUserVO;
import org.seven.share.param.CreateUserParam;
import org.seven.share.vo.UserInfoVO;


import java.util.List;
import java.util.Map;

public interface SysUserService extends IService<SysUser> {

    /**
     * 分页获取数据
     *
     * @param params 查询参数
     * @return IPage<SysUserVO>
     */
    IPage<SysUserVO> getList(Map<String, Object> params);

    /**
     * 登录
     *
     * @param username 用户名
     * @param password 密码
     * @return 生成的JWT的token
     */
    LoginResult login(String username, String password);

    /**
     * 创建用户
     *
     * @param createUserParam 创建用户信息
     * @return 创建结果
     */
    Result<String> createUser(CreateUserParam createUserParam);

    /**
     * 获取用户信息
     *
     * @return UserInfoVO
     */
    Result<UserInfoVO> getUserInfo(String authorizationHeader);

    /**
     * 修改用户信息
     *
     * @param createUserParam 修改用户信息
     * @return 修改结果
     */
    Result<String> updateUser(CreateUserParam createUserParam);

    /**
     * 批量删除系统用户信息
     *
     * @param ids 用户ids
     */
    void deleteSysUserBatch(List<Long> ids);

    /**
     * 更新系统用户密码
     *
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 结果
     */
    Boolean updateSysUserPwd(String oldPassword, String newPassword);

}

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import type { FormItemRule } from 'element-plus';
import { useForm, useFormRules } from '@/hooks/common/form';
import { fetchGetUserInfo, fetchUpdatePassword } from '@/service/api/auth';
import { fetchUpdateUser } from '@/service/api/system-manage';
import { REG_PWD } from '@/constants/reg';
const wrapperRef = ref<HTMLElement | null>(null);
const { formRef: profileFormRef, validate: validateProfile } = useForm();
const { formRef: pwdFormRef, validate: validatePwd } = useForm();
const { defaultRequiredRule } = useFormRules();

const activeTab = ref('profile');

// Profile form
const profileForm = ref({
  id: '',
  userName: '',
  nickName: '',
  userPhone: '',
  userEmail: '',
  userGender: '',
  userRoles: [] as string[],
  status: '1' as const
});

// Password form
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const profileRules: Record<string, FormItemRule[]> = {
  userName: [defaultRequiredRule],
  nickName: [defaultRequiredRule],
  userPhone: [defaultRequiredRule],
  userEmail: [
    { required: true, message: '请输入邮箱' },
    { type: 'email', message: '请输入正确的邮箱格式' }
  ]
};

const passwordRules: Record<string, FormItemRule[]> = {
  oldPassword: [defaultRequiredRule],
  newPassword: [
    { required: true, message: '请输入新密码' },
    { pattern: REG_PWD, message: '密码长度6-18位，包含字母、数字和下划线' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码' },
    {
      validator: (_rule: any, value: string) => {
        if (value !== passwordForm.value.newPassword) {
          return Promise.reject(new Error('两次输入的密码不一致'));
        }
        return Promise.resolve();
      }
    }
  ]
};

onMounted(async () => {
  const { data: userInfo } = await fetchGetUserInfo();
  if (userInfo) {
    profileForm.value = {
      id: userInfo.userId,
      userName: userInfo.userName,
      nickName: userInfo.nickName, // Using userName as nickName since it's not in UserInfo type
      userPhone: userInfo.userPhone, // These fields are not in UserInfo type
      userEmail: userInfo.userEmail, // These fields are not in UserInfo type
      userGender: userInfo.userGender,
      userRoles: userInfo.roles,
      status: '1'
    };
  }
});

async function handleUpdateProfile() {
  await validateProfile();
  try {
    await fetchUpdateUser(profileForm.value);
    window.$notification?.success({
      title: '更新成功',
      message: '个人信息已更新'
    });
  } catch {
    window.$notification?.error({
      title: '更新失败',
      message: '请稍后重试'
    });
  }
}

async function handleUpdatePassword() {
  await validatePwd();
  try {
    const res = await fetchUpdatePassword(passwordForm.value.oldPassword, passwordForm.value.newPassword);
    if (res.data === true) { 
      window.$notification?.success({
        title: '更新成功',
        message: '密码已更新'
      });
      passwordForm.value = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
    } else {
      window.$notification?.error({
        title: '更新失败',
        message: '请稍后重试'
      });
    }
  } catch {
    window.$notification?.error({
      title: '更新失败',
      message: '请稍后重试'
    });
  }
}
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <ElTabs v-model="activeTab" class="w-full">
        <ElTabPane label="个人信息" name="profile">
          <ElForm ref="profileFormRef" :model="profileForm" :rules="profileRules" label-width="100px" class="max-w-2xl">
            <ElFormItem label="用户名" prop="userName">
              <ElInput v-model="profileForm.userName" disabled />
            </ElFormItem>
            <ElFormItem label="昵称" prop="nickName">
              <ElInput v-model="profileForm.nickName" />
            </ElFormItem>
            <ElFormItem label="手机号" prop="userPhone">
              <ElInput v-model="profileForm.userPhone" />
            </ElFormItem>
            <ElFormItem label="邮箱" prop="userEmail">
              <ElInput v-model="profileForm.userEmail" />
            </ElFormItem>
            <ElFormItem label="性别" prop="userGender">
              <ElSelect v-model="profileForm.userGender" class="w-full">
                <ElOption label="男" value="1" />
                <ElOption label="女" value="2" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem>
              <ElButton type="primary" @click="handleUpdateProfile">保存修改</ElButton>
            </ElFormItem>
          </ElForm>
        </ElTabPane>

        <ElTabPane label="修改密码" name="password">
          <ElForm ref="pwdFormRef" :model="passwordForm" :rules="passwordRules" label-width="100px" class="max-w-2xl">
            <ElFormItem label="原密码" prop="oldPassword">
              <ElInput v-model="passwordForm.oldPassword" type="password" show-password />
            </ElFormItem>
            <ElFormItem label="新密码" prop="newPassword">
              <ElInput v-model="passwordForm.newPassword" type="password" show-password />
            </ElFormItem>
            <ElFormItem label="确认密码" prop="confirmPassword">
              <ElInput v-model="passwordForm.confirmPassword" type="password" show-password />
            </ElFormItem>
            <ElFormItem>
              <ElButton type="primary" @click="handleUpdatePassword">修改密码</ElButton>
            </ElFormItem>
          </ElForm>
        </ElTabPane>
      </ElTabs>
    </ElCard>
  </div>
</template>

<style scoped>
.user-center {
  padding: 20px;
}
</style>

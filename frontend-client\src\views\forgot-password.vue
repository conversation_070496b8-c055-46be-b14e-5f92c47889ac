<template>
  <div class="min-h-screen flex justify-center items-center ">
    <el-card class="w-full max-w-96">
      <h1 class="text-2xl font-bold text-center mb-6">{{ t('resetPassword.title') }}</h1>
      <el-form ref="resetFormRef" :model="resetForm" :rules="resetRules">
        <el-form-item prop="username">
          <el-input v-model="resetForm.username" size="large" auto-complete="on" :placeholder="t('resetPassword.username')">
            <template #prefix>
              <el-icon>
                <Avatar />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="email">
          <el-input v-model="resetForm.email" type="email" size="large" auto-complete="on" :placeholder="t('resetPassword.email')">
            <template #prefix>
              <el-icon>
                <Message />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="code">
          <div class="flex">
            <el-input v-model="resetForm.code" type="text" size="large" auto-complete="off" :placeholder="t('resetPassword.code')"
              class="flex-grow mr-4">
              <template #prefix>
                <el-icon>
                  <Key />
                </el-icon>
              </template>
            </el-input>
            <el-button :disabled="isGettingCode" size="large" @click="getVerificationCode">
              {{ isGettingCode ? `${countdown}s` : t('resetPassword.getVerificationCode') }}
            </el-button>
          </div>
        </el-form-item>

        <el-form-item prop="newPassword">
          <el-input v-model="resetForm.newPassword" type="password" size="large" auto-complete="off" :placeholder="t('resetPassword.newPassword')"
            show-password>
            <template #prefix><el-icon>
                <Lock />
              </el-icon></template>
          </el-input>
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input v-model="resetForm.confirmPassword" type="password" size="large" auto-complete="off"
            :placeholder="t('resetPassword.confirmPassword')" show-password>
            <template #prefix><el-icon>
                <Lock />
              </el-icon></template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <button :disabled="loading" type="submit" class="w-full text-lg font-bold py-3 bg-black dark:bg-white text-white dark:text-black rounded-lg hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 disabled:opacity-50"
            @click.prevent="handleResetPassword">
            <span v-if="!loading">{{ t('resetPassword.reset') }}</span>
            <span v-else>{{ t('resetPassword.resetting') }}</span>
          </button>
        </el-form-item>
      </el-form>

      <div class="mt-4 text-center text-sm ">
        <router-link class="hover:underline" to="/login">{{ t('resetPassword.backToLogin') }}</router-link>
      </div>
    </el-card>
  </div>
</template>
  
<script setup>
import { ref } from 'vue'
import { Avatar, Message, Key, Lock } from '@element-plus/icons-vue'
import { ElNotification } from 'element-plus'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n';
import * as api from '@/api/user.js'
import Cookies from 'js-cookie'
import { encrypt } from '@/utils/encrypt'

const { t } = useI18n();

const router = useRouter();  // 使用vue-router
const resetForm = ref({
  username:'',
  email: "",
  code: "",
  newPassword: "",
  confirmPassword: ""
});

const resetFormRef = ref()

const resetRules = {
  username: [{ required: true, trigger: "blur", message: t('resetPassword.errors.usernameRequired') }],
  email: [
    { required: true, message: t('resetPassword.errors.emailRequired'), trigger: "blur" },
    { type: "email", message: t('resetPassword.errors.emailInvalid'), trigger: "blur" }
  ],
  code: [
    { required: true, message: t('resetPassword.errors.codeRequired'), trigger: "blur" },
    { len: 6, message: t('resetPassword.errors.codeLength'), trigger: "blur" }
  ],
  newPassword: [
    { required: true, message: t('resetPassword.errors.newPasswordRequired'), trigger: "blur" },
    { min: 6, message: t('resetPassword.errors.newPasswordMinLength'), trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: t('resetPassword.errors.confirmPasswordRequired'), trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== resetForm.value.newPassword) {
          callback(new Error(t('resetPassword.errors.passwordMismatch')));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
};

const loading = ref(false);
const isGettingCode = ref(false);
const countdown = ref(60);

const getVerificationCode = async() => {
  if (isGettingCode.value) return;

  // 验证邮箱
  if (!resetForm.value.email) {
    ElNotification.error(t('resetPassword.errors.emailMissing'));
    return;
  }
  // 设置倒计时的持续时间（秒）
  const countdownDuration = 60;

  // 记录倒计时的结束时间
  const endTime = Date.now() + countdownDuration * 1000;
  localStorage.setItem('countdownEndTime', endTime);

  isGettingCode.value = true;

  // 开始倒计时
  const updateCountdown = () => {
    const remainingTime = Math.max(0, Math.floor((endTime - Date.now()) / 1000));
    countdown.value = remainingTime;

    if (remainingTime <= 0) {
      clearInterval(timer);
      isGettingCode.value = false;
      localStorage.removeItem('countdownEndTime'); // 清除存储的倒计时
    }
  };

  const timer = setInterval(updateCountdown, 1000);

  // 初始化时更新一次倒计时
  updateCountdown();

  // 在这里添加发送验证码的逻辑
  const param = { 
    username: resetForm.value.username,
    email: resetForm.value.email
   }
  await api.getResetPasswordCode(param)
  ElNotification.success(t('resetPassword.errors.codeSent'));
}

const handleResetPassword = () => {
  resetFormRef.value.validate(async valid => {
    if (valid) {
      loading.value = true;
      try {
        const res = await api.resetPassword(resetForm.value)
        ElNotification({
          message: t('resetPassword.errors.passwordResetSuccess'),
          type: 'success',
        })

        // 将用户名和密码更新到cookie中
        Cookies.set("username", resetForm.value.username, { expires: 30 });
        Cookies.set('password', encrypt(resetForm.value.newPassword), { expires: 30, secure: true });


        router.push('/login');
      } finally { 
        loading.value = false;
      }
    }
  });
}
// 页面加载时检查是否有未完成的倒计时
const savedEndTime = localStorage.getItem('countdownEndTime');
if (savedEndTime) {
  const remainingTime = Math.max(0, Math.floor((savedEndTime - Date.now()) / 1000));
  if (remainingTime > 0) {
    isGettingCode.value = true;
    countdown.value = remainingTime;

    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
        isGettingCode.value = false;
        localStorage.removeItem('countdownEndTime');
      }
    }, 1000);
  } else {
    localStorage.removeItem('countdownEndTime');
  }
}
</script>

import type { ComponentPublicInstance } from 'vue';

declare const HtmlEditor: ComponentPublicInstance & {
  openDialog: (content: string) => void;
};

export default HtmlEditor;

declare module '@/components/html-editor/HtmlEditor.vue' {
  import type { DefineComponent } from 'vue';
  const component: DefineComponent<
    {
      modelValue: {
        type: string;
        required: true;
      };
    },
    {
      updateContent: (content: string) => void;
    }
  >;
  export default component;
}

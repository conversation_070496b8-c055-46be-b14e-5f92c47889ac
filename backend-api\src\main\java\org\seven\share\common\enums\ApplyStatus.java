package org.seven.share.common.enums;

import lombok.Getter;

/**
 * @ClassName: ApplyStatus
 * @Description:
 * @Author: Seven
 * @Date: 2024/10/18
 */
@Getter
public enum ApplyStatus {
    APPLY_PROCESSED(0, "审批中"),
    APPLY_APPROVE(1, "审批通过"),
    APPLY_REJECT(2, "驳回申请");

    private final int code;

    private final String desc;

    ApplyStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}

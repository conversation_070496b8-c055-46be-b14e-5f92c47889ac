package org.seven.share.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.enums.ApplyStatus;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.pojo.entity.SysUser;
import org.seven.share.common.util.EmailUtil;
import org.seven.share.common.util.SecurityUtil;
import org.seven.share.mapper.ChatGptAffRecordMapper;
import org.seven.share.mapper.ChatGptUserMapper;
import org.seven.share.mapper.ChatGptWithdrawalsMapper;
import org.seven.share.common.pojo.dto.ApplyWithdrawalDto;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.common.pojo.entity.ChatGptWithdrawalsEntity;
import org.seven.share.common.pojo.vo.InviteDetailsVo;
import org.seven.share.service.ChatGptConfigService;
import org.seven.share.service.ChatGptUserService;
import org.seven.share.service.ChatGptWithdrawalsService;
import org.seven.share.service.EmailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.seven.share.common.util.ConstantUtil.SYS_TENANT_ID;
import static org.seven.share.common.util.ConstantUtil.WITHDRAW_SUCCESS_SUBJECT;


/**
 * @ClassName: ChatGptWithdrawalsServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/10/15
 */
@Slf4j
@Service
public class ChatGptWithdrawalsServiceImpl extends ServiceImpl<ChatGptWithdrawalsMapper, ChatGptWithdrawalsEntity> implements ChatGptWithdrawalsService {

    @Resource
    private ChatGptUserService chatGptUserService;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private EmailService emailService;

    @Resource
    private ChatGptUserMapper chatGptUserMapper;

    @Resource
    private ChatGptAffRecordMapper chatGptAffRecordMapper;

    @Resource
    private ChatGptWithdrawalsMapper chatGptWithdrawalsMapper;

    @Override
    public void applyWithdrawal(ApplyWithdrawalDto dto) {
        // 判断是否开启了推广功能
        Map<String, String> keyVlaueMap = chatGptConfigService.getKeyValueMapByKeys(Arrays.asList("enablePromotion", "threshold"));
        String enablePromotion = keyVlaueMap.get("enablePromotion");
        if (!Objects.equals(enablePromotion, "true")) {
            throw new CustomException("管理员还未开启返现功能");
        }
        // 是否存在待审核的提现记录
        List<ChatGptWithdrawalsEntity> list = this.lambdaQuery().eq(ChatGptWithdrawalsEntity::getUserId, dto.getUserId())
                .eq(ChatGptWithdrawalsEntity::getStatus, 0)
                .list();
        if (CollectionUtil.isNotEmpty(list)) {
            throw new CustomException("存在待审核的提现申请，请勿重复提交");
        }
        // 先验证提现密码是否正确
        ChatGptUserEntity user = chatGptUserService.getByIdWithoutTenant(dto.getUserId());

        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("用户信息不存在");
        }

        // 先校验一下体现金额是否大于可提现金额
        Double txMoney = dto.getMoney();
        String kyMoney = user.getAffQuota();
        BigDecimal txMoneyDecimal = BigDecimal.valueOf(txMoney);
        BigDecimal kyMoneyDecimal = BigDecimal.valueOf(StrUtil.isEmpty(kyMoney) ? 0L : Double.parseDouble(kyMoney));

        if (txMoneyDecimal.compareTo(kyMoneyDecimal) > 0) {
            log.warn("提现申请金额：{}，可提现金额：{}", txMoneyDecimal, kyMoneyDecimal);
            throw new CustomException("提现申请金额大于可提现金额，请修改提现金额");
        }

        if (!Objects.equals(user.getPassword(), DigestUtils.md5DigestAsHex(dto.getPassword().getBytes()))){
            throw new CustomException("提现密码错误，请确认");
        }

        // 判断是否满足最低门槛
        String threshold = keyVlaueMap.get("threshold");
        if (StrUtil.isNotEmpty(threshold)) {
            BigDecimal thresholdDecimal = BigDecimal.valueOf(Double.parseDouble(threshold));
            if (thresholdDecimal.compareTo(txMoneyDecimal) > 0) {
                log.info("提现申请金额：{}，最低门槛：{}", txMoneyDecimal, thresholdDecimal);
                throw new CustomException("暂未达到提现门槛：" + thresholdDecimal + "元");
            }
        }
        // 未设置门槛或者已经达到门槛，则生成提现记录信息
        saveWithdrawalRecord(dto, user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalWithdrawal(String id) {
        log.info("开始审批提现申请: {}", id);
        if (StrUtil.isEmpty(id)) {
            throw new CustomException("请求参数错误：id为空");
        }
        ChatGptWithdrawalsEntity withdrawalsEntity = getById(id);
        if (ObjectUtil.isEmpty(withdrawalsEntity)) {
            throw new CustomException("提现信息不存在");
        }
        if (withdrawalsEntity.getStatus() == 1) {
            throw new CustomException("当前提现申请已审批通过");
        }
        Long userId = withdrawalsEntity.getUserId();
        // 计算累积提现金额
        ChatGptUserEntity user = chatGptUserService.getByIdWithoutTenant(userId);
        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("用户信息不存在");
        }
        String affHistoryQuota = user.getAffHistoryQuota();
        // 历史提现金额
        BigDecimal historyQuotaDecimal = new BigDecimal(Optional.ofNullable(affHistoryQuota).orElse("0"));
        // 本次提现金额
        Double withdrawalMoney = withdrawalsEntity.getWithdrawalMoney();
        BigDecimal withdrawalMoneyDecimal = BigDecimal.valueOf(withdrawalMoney);
        // 新的历史提现金额
        BigDecimal newAffHistoryQuota = historyQuotaDecimal.add(withdrawalMoneyDecimal);
        user.setAffHistoryQuota(newAffHistoryQuota.toString());

        // 减少用户可提现余额
        String affQuota = user.getAffQuota();
        BigDecimal affQuotaDecimal = BigDecimal.valueOf(StrUtil.isEmpty(affQuota) ? 0L : Double.parseDouble(affQuota));
        if (affQuotaDecimal.compareTo(BigDecimal.ZERO) == 0 || affQuotaDecimal.compareTo(withdrawalMoneyDecimal) < 0) {
            log.warn("用户可提现余额为:{}, 申请提现的金额为：{}",affQuotaDecimal,withdrawalMoneyDecimal );
            throw new CustomException("用户可提现余额为0或提现金额小于申请提现的金额。");
        }
        // 更新用户提现历史提现金额和可提现金额
        user.setAffQuota(affQuotaDecimal.subtract(withdrawalMoneyDecimal).toString());
        chatGptUserService.updateByIdWithoutTenant(user);
        // 更新提现状态
        withdrawalsEntity.setStatus(ApplyStatus.APPLY_APPROVE.getCode());
        withdrawalsEntity.setRemark("打款成功，请注意查收");
        updateById(withdrawalsEntity);

        // 更新aff记录结算状态
        updateUserInviterData(userId);

        // 发送邮件通知
        sendWithdrawalEmail(user);
    }

    private void sendWithdrawalEmail(ChatGptUserEntity user) {
        String emailNotification = chatGptConfigService.getValueByKey("emailNotification");
        if (Objects.equals(emailNotification, "true")) {
            String email = user.getEmail();
            if(StrUtil.isNotEmpty(email) && EmailUtil.emailFormat(email)) {
                emailService.sendEmailNotTemplate(email, WITHDRAW_SUCCESS_SUBJECT, "您好，返佣审核通过，请查收到账信息。");
            }
        }
    }

    private void updateUserInviterData(Long id) {
        chatGptAffRecordMapper.updateAffRecordData(id);
    }

    @Override
    public void rejectWithdrawal(String id, String reason) {
        if (StrUtil.isEmpty(reason)) {
            throw new CustomException("请填写驳回原因");
        }
        if (StrUtil.isNotEmpty(id)) {
            this.lambdaUpdate()
                    .eq(ChatGptWithdrawalsEntity::getId, id)
                    .set(ChatGptWithdrawalsEntity::getStatus, ApplyStatus.APPLY_REJECT.getCode())
                    .set(ChatGptWithdrawalsEntity::getRemark, "驳回原因：" + reason)
                    .update();
        }
    }

    @Override
    public List<InviteDetailsVo> getInviteDetails(String id) {
        return chatGptUserMapper.getInviteDetails(id);
    }

    /**
     * 查询已审批和待审批数据
     * @return
     */
    @Override
    public Map<String, Integer> getToDoData() {
        String tenantId = Optional.ofNullable(SecurityUtil.getSysUser())
                .map(SysUser::getTenantId).orElse(null);
        if (!SYS_TENANT_ID.equals(tenantId)) {
            return null;
        }
        return chatGptWithdrawalsMapper.getToDoData();
    }


    private void saveWithdrawalRecord(ApplyWithdrawalDto dto, ChatGptUserEntity user) {
        ChatGptWithdrawalsEntity entity = new ChatGptWithdrawalsEntity();
        entity.setUserId(user.getId());
        entity.setUsername(user.getUserToken());
        entity.setWithdrawalTime(LocalDateTime.now());
        entity.setWithdrawalMoney(dto.getMoney());
        entity.setWithdrawalQrcode(user.getReceiptFile());
        entity.setRemark("提现申请");
        entity.setStatus(ApplyStatus.APPLY_PROCESSED.getCode());
        entity.setContact(dto.getContact());
        save(entity);
    }
}

<script setup lang="ts">
import { computed, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { useForm } from '@/hooks/common/form';
import { createBatchUsers } from '@/service/api';

defineOptions({ name: 'BatchCreateModal' });
interface Props {
  plans: any[];
}
const props = defineProps<Props>();

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', { default: false });

const { formRef, validate } = useForm();

const isMobile = computed(() => window.innerWidth < 768);
const batchCreateForm = reactive({
  loginType: 2,
  tokenPrefix: '',
  password: '',
  nums: 1,
  subtypeId: '',
  remark: '',
  affRate: 0
});

// 关闭弹窗
function handleClose() {
  batchCreateForm.tokenPrefix = '';
  batchCreateForm.password = '';
  batchCreateForm.nums = 1;
  batchCreateForm.subtypeId = '';
  batchCreateForm.remark = '';
  batchCreateForm.affRate = 0;
  visible.value = false;
}

// 批量创建用户
async function submitBatchCreate() {
  try {
    await validate();
    // await createBatchUsers(batchCreateForm);
    // ElMessage({
    //   message: '创建成功，并已复制到粘贴板',
    //   type: 'success'
    // });
    // visible.value = false;
    // emit('submitted');
    await validate();
    const keys = await createBatchUsers(batchCreateForm);
    const keysString = keys.data.join('\n');
    // 使用Clipboard API将格式化后的keys复制到粘贴板
    navigator.clipboard
      .writeText(keysString)
      .then(() => {
        // 复制成功后显示成功提示
        ElMessage({
          message: '生成成功，并已复制到粘贴板',
          type: 'success',
          plain: true
        });
      })
      .catch(() => {
        // 复制失败的处理
        ElMessage({
          message: '复制失败，请手动复制',
          type: 'error',
          plain: true
        });
      });
    emit('submitted');
  } catch {}
}
</script>

<template>
  <ElDialog
    v-model="visible"
    title="批量创建用户"
    :width="isMobile ? '95%' : '45%'"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <ElForm ref="formRef" :model="batchCreateForm" label-width="auto">
      <ElFormItem label="登录方式" prop="loginType">
        <div>
          <ElRadioGroup v-model="batchCreateForm.loginType">
            <ElRadio :value="1" border>账号密码登录</ElRadio>
            <ElRadio :value="2" border>授权码登录</ElRadio>
          </ElRadioGroup>
        </div>
      </ElFormItem>
      <div v-if="batchCreateForm.loginType === 1">
        <ElFormItem label="用户名前缀" prop="tokenPrefix">
          <ElInput v-model="batchCreateForm.tokenPrefix" placeholder="请输入用户名前缀" />
        </ElFormItem>

        <ElFormItem label="初始密码" prop="password">
          <ElInput v-model="batchCreateForm.password" type="password" placeholder="为空则为123456" show-password />
        </ElFormItem>
      </div>
      <ElFormItem label="返现比例" prop="affRate">
        <ElInputNumber
          v-model="batchCreateForm.affRate"
          :precision="2"
          :step="0.1"
          :min="0.1"
          :max="1"
          placeholder="请输入返现比例"
        />
      </ElFormItem>
      <ElFormItem label="创建用户数" prop="nums">
        <ElInputNumber v-model="batchCreateForm.nums" :min="1" placeholder="请输入创建用户数量" />
      </ElFormItem>

      <ElFormItem label="选择套餐" prop="subtypeId">
        <ElSelect v-model="batchCreateForm.subtypeId" placeholder="请选择套餐">
          <ElOption v-for="plan in props.plans" :key="plan.id" :label="plan.name" :value="plan.id" />
        </ElSelect>
      </ElFormItem>

      <ElFormItem label="备注" prop="remark">
        <ElInput v-model="batchCreateForm.remark" type="textarea" placeholder="请输入备注" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="handleClose">取 消</ElButton>
        <ElButton type="primary" @click="submitBatchCreate">确 定</ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<style scoped></style>

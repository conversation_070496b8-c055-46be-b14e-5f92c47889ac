package org.seven.share.common.pojo.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: UserPayLogsVo
 * @Description:
 * @Author: Seven
 * @Date: 2024/12/4
 */
@Data
public class UserPayLogsVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    private LocalDateTime updateTime;

    private Double money;

    private String tradeNo;

    private String status;

}

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { resolve } from 'path'
// https://vitejs.dev/config/
export default defineConfig({
  base: '/app/',
  build: {
    outDir: resolve(__dirname, '../backend-api/src/main/resources/static/app'), // 输出到后端 static/app
    emptyOutDir: true, // 清空目标目录
    chunkSizeWarningLimit: 1600 // 调整这个值以增加警告的阈值
  },
  plugins: [
    vue(),
    createSvgIconsPlugin({
      // Specify the icon folder to be cached
      iconDirs: [path.resolve(process.cwd(), 'src/assets/icons')],
      // Specify symbolId format
      symbolId: 'icon-[dir]-[name]',
    }),
  ],
  resolve: {
    // https://cn.vitejs.dev/config/#resolve-alias
    alias: {
      // 设置路径
      '~': path.resolve(__dirname, './'),
      // 设置别名
      '@': path.resolve(__dirname, './src'),
      vue: 'vue/dist/vue.esm-bundler.js' // 使用包含编译器的Vue完整版
    },
    // https://cn.vitejs.dev/config/#resolve-extensions
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
  },
  server: {
    host: "0.0.0.0",
    port: 9526,
    open: true,
    hmr: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8888', //目标源，目标服务器，真实请求地址
        changeOrigin: true, //支持跨域
        rewrite: (path) => path.replace(/^\/api/, "/api"), //重写真实路径,替换/api
      },
    }
  }
})

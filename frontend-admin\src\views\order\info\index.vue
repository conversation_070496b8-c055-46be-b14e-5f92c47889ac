<script setup lang="tsx">
import { ref } from 'vue';
import { ElButton, ElPopconfirm, ElTag } from 'element-plus';
import { fetchPayLogsPage, handlePayStatus, removePayLogsBatchByIds } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import { usePermission } from '@/hooks/business/auth';
import OrderSearch from './modules/order-search.vue';
const { hasPermission } = usePermission();
// Define the proper type for your table data
interface Order {
  id: number;
  userToken: string;
  tradeNo: string;
  money: string;
  status: string;
  createTime: string;
  updateTime: string;
}

const wrapperRef = ref<HTMLElement | null>(null);

const { columns, columnChecks, data, loading, pagination, getData, searchParams, getDataByPage, resetSearchParams } =
  useTable<Order>({
    apiFn: fetchPayLogsPage,
    columns: () => [
      { type: 'selection', width: 48 },
      { prop: 'index', label: $t('common.index'), width: 64 },
      { prop: 'userToken', label: '用户名', minWidth: 200, showOverflowTooltip: true },
      { prop: 'tradeNo', label: '订单号', minWidth: 200 },
      { prop: 'money', label: '订单金额', minWidth: 100 },
      {
        prop: 'status',
        label: '订单状态',
        minWidth: 120,
        align: 'center',
        sortable: true,
        formatter: (row: Order) =>
          row.status === 'success' ? <ElTag type="success">已支付</ElTag> : <ElTag type="primary">未支付</ElTag>
      },
      { prop: 'days', label: '套餐天数', minWidth: 100 },
      { prop: 'remark', label: '备注', minWidth: 200, showOverflowTooltip: true },
      { prop: 'createTime', label: '创建时间', width: 180 },
      { prop: 'updateTime', label: '更新时间', width: 180 },
      {
        prop: 'operate',
        label: $t('common.operate'),
        align: 'center',
        fixed: 'right',
        width: 110,
        formatter: (row: Order) => (
          <ElPopconfirm title="用户权益将会直接到账， 请确认是否继续?" onConfirm={() => handlePayStatusLocal(row.id)}>
            {{
              reference: () => (
                <ElButton
                  type="danger"
                  plain
                  size="small"
                  v-show={hasPermission('payLogs/change-status')}
                  disabled={row.status === 'success'}
                >
                  {'手工处理'}
                </ElButton>
              )
            }}
          </ElPopconfirm>
        )
      }
    ]
  });

const { checkedRowKeys, onBatchDeleted, onDeleted } = useTableOperate(data, getData);

async function handleBatchDelete() {
  await removePayLogsBatchByIds(checkedRowKeys.value);
  onBatchDeleted();
}

async function handlePayStatusLocal(id: number) {
  await handlePayStatus(id);
  window.$message?.success('处理完成');
  onDeleted();
}
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <OrderSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            :show-delete="hasPermission('payLogs/delete')"
            @delete="handleBatchDelete"
            @refresh="getData"
          />
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="(selection: Order[]) => checkedRowKeys = selection.map(item => item.id)"
        >
          <ElTableColumn
            v-for="col in columns"
            :key="col.prop"
            v-bind="col"
            :show-overflow-tooltip="col.showOverflowTooltip"
            :fixed="col.fixed"
          />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>

package org.seven.share.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.R;
import org.seven.share.common.api.Result;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.service.ChatGptConfigService;
import org.seven.share.service.LicenseValidator;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @ClassName: ChatGptConfigController
 * @Description: 配置信息控制器
 * @Author: Seven
 * @Date: 2024/8/1
 */
@RestController
@RequestMapping("/expander-api/sys")
public class ChatGptConfigController {

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private LicenseValidator licenseValidator;


    @PostMapping("/saveForm")
    @SysLogInterface(title = "保存系统配置", businessType = BusinessType.INSERT)
    public R saveForm(@RequestBody Map<String, Object> map) {
        chatGptConfigService.saveForm(map);
        return R.ok();
    }

    @GetMapping("/getForm")
    @SysLogInterface(title = "查询系统配置", businessType = BusinessType.QUERY)
    public R getForm() {
        return R.ok(chatGptConfigService.getConfigMap());
    }


    @GetMapping("/test")
    public R testEmail(String email) {
        chatGptConfigService.testEmail(email);
        return R.ok();
    }

    @Operation(summary = "查询系统授权信息")
    @GetMapping(value = "/licenseInfo")
    public Result<Map<String, Object>> licenseInfo() {
        return Result.success(licenseValidator.getLicenseInfo());
    }
}

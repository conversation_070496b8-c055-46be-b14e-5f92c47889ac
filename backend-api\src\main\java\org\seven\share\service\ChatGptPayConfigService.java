package org.seven.share.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.pojo.entity.ChatGptPayConfigEntity;
import org.seven.share.common.pojo.vo.ChatGptPayConfigVo;

import java.util.List;

/**
 * @InterfaceName: ChatGptPayConfigService
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/12
 */
public interface ChatGptPayConfigService extends IService<ChatGptPayConfigEntity> {
    List<ChatGptPayConfigVo> listPayments();

    void savePayConfig(ChatGptPayConfigEntity config);
}

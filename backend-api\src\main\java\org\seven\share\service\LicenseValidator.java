package org.seven.share.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.stream.StreamUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.util.AESUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.seven.share.common.util.LicenseUtil.generateServerHash;

/**
 * @ClassName: LicenseValidator
 * @Description:
 * @Author: Seven
 * @Date: 2025/5/12
 *
 * */
@Slf4j
@Service
@RequiredArgsConstructor
public class LicenseValidator {

    private static final String SECRET_KEY = "kR24gT0WsxNnkLO0p+YZl+H65S92LK5iGFIFXaTZlwI=";

    private final ChatGptConfigService configService;

    private final ObjectMapper objectMapper;

    @Value("${spring.profiles.active:default}")
    private String activeProfile;

    // 缓存解析后的授权信息，避免重复解密和解析
    private static Map<String, Object> authInfoCache = new HashMap<>();

    public boolean isValid() {
        try {
            if ("dev".equals(activeProfile)) {
                return true;
            }
            // 获取授权码
            String authCode = configService.getValueByKey("licenseCode");
            if (StrUtil.isEmpty(authCode)) {
                throw new CustomException("还未填写授权码信息");
            }
            // 解密授权码
            String deAuthCode = AESUtil.decrypt(authCode.trim(), SECRET_KEY);
            authInfoCache = objectMapper.readValue(deAuthCode, Map.class);

            // 校验hash是否相等
            String hash = (String) authInfoCache.get("hash");
            if (!Objects.equals(hash, generateServerHash())) {
                throw new CustomException("授权信息异常");
            }

            // 校验是否过期
            long expireTime = (long) authInfoCache.get("expireTime");
            if (System.currentTimeMillis() > expireTime) {
                log.error("授权码已过期，currentTime：{}, expireTime：{}", System.currentTimeMillis(), expireTime);
                throw new CustomException("授权已过期");
            }
        } catch (Exception e) {
            authInfoCache.clear();
            throw new CustomException("授权失败： " + e.getMessage());
        }

        return true;
    }

    public boolean haveAccess(String type) {
        if ("dev".equals(activeProfile)) {
            return true;
        }
        if (authInfoCache == null && !isValid()) {
            throw new CustomException("无效的授权码，无法获取权限列表");
        }
        try {
            // 解析 access 字段为 List<String>
            Object accessObj = authInfoCache.get("access");
            if (accessObj == null) {
                throw new CustomException("授权码中缺少 access 字段");
            }
            List<String> list = objectMapper.convertValue(accessObj, List.class);
            return CollectionUtil.isNotEmpty(list) && list.contains(type);
        } catch (Exception e) {
            throw new CustomException("解析权限列表失败: " + e.getMessage());
        }
    }

    public Map<String, Object> getLicenseInfo() {
        isValid();
        return authInfoCache;
    }
}

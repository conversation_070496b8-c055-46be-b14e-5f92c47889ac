package org.seven.share.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.api.R;
import org.seven.share.common.pojo.entity.ChatGptSubTypeEntity;
import org.seven.share.service.ChatGptSubTypeService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: ChatGptSubTypeController
 * @Description: 订阅管理控制器
 * @Author: Seven
 * @Date: 2024/7/11
 */
@RestController
@RequestMapping("/expander-api/subtype")
public class ChatGptSubTypeController {

    @Resource
    private ChatGptSubTypeService chatGptSubTypeService;

    @GetMapping("/page")
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer current,
                  @RequestParam(value = "size", defaultValue = "10") Integer size,
                  String name){
        Page<ChatGptSubTypeEntity> pageInfo = chatGptSubTypeService.getPage(current, size, name);
        return R.ok(pageInfo);
    }

    @PostMapping("/add")
    @CacheEvict(cacheNames = "subtype", allEntries = true)
    public R saveSubType(@RequestBody ChatGptSubTypeEntity chatGptSubType){
        chatGptSubTypeService.saveSubType(chatGptSubType);
        return R.ok();
    }

    @PostMapping("/update")
    @CacheEvict(cacheNames = "subtype", allEntries = true)
    public R updateSubType(@RequestBody ChatGptSubTypeEntity chatGptSubType){
        chatGptSubTypeService.updateSubType(chatGptSubType);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @CacheEvict(cacheNames = "subtype", allEntries = true)
    public R deleteSubType(@RequestBody List<String> ids){
        int count = chatGptSubTypeService.logicDeleteSubtype(ids);
        if (count > 0) {
            return R.ok();
        }
        return R.error();
    }

    @GetMapping("/list")
    public R getAdminSubTypeList() {
        List<ChatGptSubTypeEntity> collect = chatGptSubTypeService.listAdminSubType();
        return R.ok(collect);
    }

    @PutMapping("/change")
    @CacheEvict(cacheNames = "subtype", allEntries = true)
    public R changeSwitch(@RequestParam("id") Long id, @RequestParam("isNotValued") Integer isNotValued){
        chatGptSubTypeService.updateSubtypeStatus(id, isNotValued);
        return R.ok();
    }

    @PostMapping("/model/sync")
    public R syncModelLimits(){
        chatGptSubTypeService.syncModelLimits();
        return R.ok();
    }
}

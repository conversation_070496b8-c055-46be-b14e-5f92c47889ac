package org.seven.share.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.exception.CustomException;
import org.seven.share.mapper.ChatGptPayLogsMapper;
import org.seven.share.common.pojo.entity.ChatGptEPayLogsEntity;
import org.seven.share.common.pojo.entity.ChatGptSubTypeEntity;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.mapper.ChatGptSubTypeMapper;
import org.seven.share.service.ChatGptPayLogsService;
import org.seven.share.service.ChatGptSubTypeService;
import org.seven.share.service.ChatGptUserService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ChatGptPayLogsServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/5
 */
@Service
@Slf4j
public class ChatGptPayLogsServiceImpl extends ServiceImpl<ChatGptPayLogsMapper, ChatGptEPayLogsEntity>
        implements ChatGptPayLogsService {

    @Resource
    private ChatGptPayLogsMapper chatGptPayLogsMapper;

    @Resource
    private ChatGptUserService chatGptUserService;

    @Resource
    private ChatGptSubTypeMapper chatGptSubTypeMapper;

    @Override
    public boolean changeStatus(String id) {
        log.info("开始手动更新订单状态, orderId: {}", id);
        // 1. 参数校验
        if (StringUtils.isBlank(id)) {
            log.error("订单ID不能为空");
            throw new CustomException("订单ID不能为空");
        }
        ChatGptEPayLogsEntity payLogsEntity = chatGptPayLogsMapper.selectById(id);
        String payStatus = payLogsEntity.getStatus();
        if ("success".equals(payStatus)) {
            log.warn("操作失败：当前订单{}已支付", payLogsEntity.getTradeNo());
            return true;
        }
        // 获取用户信息
        ChatGptUserEntity user = chatGptUserService.getUserInfoByUsername(payLogsEntity.getUserToken());
        if (ObjectUtils.isEmpty(user)) {
            log.error("根据订单中的用户名查询到的用户信息为空");
            return false;
        }
        // 获取订阅信息
        ChatGptSubTypeEntity subType = chatGptSubTypeMapper.getSubtypeByIdWithoutTenant(payLogsEntity.getSubTypeId());
        if (ObjectUtils.isEmpty(subType)) {
            log.error("根据订单中的套餐id查询套餐信息为空");
        }
        // 更新用户信息（模型速率）、返现信息、订单状态
        int i = chatGptUserService.updateUserInfoAndPayStatus(subType, user, payLogsEntity);
        log.info("手动更新订单状态完成");
        return i == 1;
    }

    @Override
    public List<Map<String, Object>> getLast15DaysIncome() {
        return chatGptPayLogsMapper.getDailyIncome();
    }

    @Override
    public List<Map<String, Object>> getLast12MonthsIncome() {
        return chatGptPayLogsMapper.getMonthlyIncome();
    }

    @Override
    public List<Map<String, Object>> getYearlyIncome() {
        return chatGptPayLogsMapper.getYearlyIncome();
    }
}

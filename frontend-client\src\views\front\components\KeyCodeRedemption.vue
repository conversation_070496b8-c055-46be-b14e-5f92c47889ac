<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'
import { redeemCode, getRedemptionHistory } from '@/api/redemption.js';
import { useI18n } from 'vue-i18n'  // Add this import
import { getToken } from '@/utils/auth.js'
const { t } = useI18n()  // Add this line
const cardCode = ref('')
const loading = ref(false)
const userStore = useUserStore()
const redemptionHistory = ref([])
const historyLoading = ref(false)
const activeTab = ref('redeem')

const handleRedeem = async () => {
  if (!cardCode.value.trim()) {
    ElMessage.warning(t('redeem_warning'))
    return
  }

  loading.value = true
  try {
    const params = {
    key: cardCode.value,
    userId: userStore.id,
  };
    await redeemCode(params);
    ElMessage.success(t('redeem_success'))
    cardCode.value = ''
    await fetchRedemptionHistory()
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

const fetchRedemptionHistory = async () => {
  historyLoading.value = true
  try {
    if(!getToken()) return
    const data = await getRedemptionHistory(userStore.id)
    redemptionHistory.value = data
  } catch (error) {
    console.error('Failed to fetch redemption history:', error)
  } finally {
    historyLoading.value = false
  }
}

onMounted(() => {
  fetchRedemptionHistory()
})
</script>

<template>
  <div>
    <h2 class="text-xl sm:text-2xl md:text-3xl font-bold mb-4 sm:mb-6">{{ $t('card_redeem_title') }}</h2>

    <!-- Tab 导航 -->
    <div class="mb-6 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg max-w-md mx-auto">
      <div class="flex space-x-1">
        <button
          @click="activeTab = 'redeem'"
          :class="[
            'flex-1 px-4 py-2 text-sm font-medium rounded-md transition-all duration-200',
            activeTab === 'redeem'
              ? 'bg-black dark:bg-white text-white dark:text-black'
              : 'text-gray-600 dark:text-gray-400 hover:bg-white dark:hover:bg-gray-700 hover:text-black dark:hover:text-white'
          ]"
        >
          {{ $t('card_redeem_title') }}
        </button>
        <button
          @click="activeTab = 'history'"
          :class="[
            'flex-1 px-4 py-2 text-sm font-medium rounded-md transition-all duration-200',
            activeTab === 'history'
              ? 'bg-black dark:bg-white text-white dark:text-black'
              : 'text-gray-600 dark:text-gray-400 hover:bg-white dark:hover:bg-gray-700 hover:text-black dark:hover:text-white'
          ]"
        >
          {{ $t('redemption_history') }}
        </button>
      </div>
    </div>

    <!-- 内容区域 - 自动扩展，无滚动条 -->
    <div class="w-full">
      <!-- 兑换Tab -->
      <div v-if="activeTab === 'redeem'" class="max-w-md mx-auto">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-200 dark:border-gray-700">
          <div class="space-y-4">
            <div>
              <label for="cardCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {{ $t('redeem_card_code') }}
              </label>
              <input
                id="cardCode"
                v-model.trim="cardCode"
                type="text"
                :placeholder="$t('redeem_card_code')"
                class="w-full px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-black dark:focus:ring-white focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-sm"
                :disabled="loading"
                @keyup.enter="handleRedeem"
              />
            </div>
            <button
              @click="handleRedeem"
              :disabled="loading"
              class="w-full px-4 py-2.5 bg-black dark:bg-white text-white dark:text-black rounded-lg font-medium hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ loading ? $t('redeem_in_progress') : $t('redeem_button_text') }}
            </button>
          </div>

          <!-- 使用说明 -->
          <div class="mt-6 text-xs text-gray-500 dark:text-gray-400">
            <h3 class="font-medium mb-2">{{ $t('instructions_title') }}</h3>
            <ol class="list-decimal list-inside space-y-1 ml-4">
              <li>{{ $t('instructions_list_first') }}</li>
              <li>{{ $t('instructions_list_second') }}</li>
              <li>{{ $t('instructions_list_third') }}</li>
              <li>{{ $t('instructions_list_fourth') }}</li>
            </ol>
          </div>
        </div>
      </div>

      <!-- 历史记录Tab -->
      <div v-else-if="activeTab === 'history'" class="max-w-2xl mx-auto">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <div v-if="historyLoading" class="px-6 py-4">
            <p class="text-gray-500 dark:text-gray-400 text-sm">{{ $t('redeem_loading') }}...</p>
          </div>

          <template v-else>
            <div v-if="redemptionHistory.length === 0" class="px-6 py-8 text-center">
              <p class="text-gray-500 dark:text-gray-400">{{ $t('no_redemption_history') }}</p>
            </div>

            <div v-else class="divide-y dark:divide-gray-700">
              <div v-for="record in redemptionHistory"
                   :key="record.id"
                   class="px-6 py-4 flex justify-between items-center hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">{{ record.key }}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {{ $t('redeem_time') }} {{ record.redeemedTime }}
                  </p>
                </div>
                <span
                  :class="[
                    'px-2 py-1 rounded text-xs font-medium',
                    record.isPlus
                      ? 'bg-black dark:bg-white text-white dark:text-black'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                  ]"
                >
                  {{ record.isPlus === 1 ? 'Advance' :'Standard' }}
                </span>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 基础样式保持不变 */
input:focus {
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2);
}

.dark input:focus {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
}

button {
  position: relative;
  overflow: hidden;
}

button:disabled::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.1);
}

/* 添加移动端触摸优化 */
@media (max-width: 640px) {
  input, button {
    -webkit-tap-highlight-color: transparent;
  }
  
  button {
    touch-action: manipulation;
  }
}
</style>
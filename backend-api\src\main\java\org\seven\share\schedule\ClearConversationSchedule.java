package org.seven.share.schedule;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.mapper.ChatGptConversationsMapper;
import org.seven.share.service.ChatGptConfigService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @ClassName: ClearConversationSchedule
 * @Description: 定时清理会话信息
 * @Author: Seven
 * @Date: 2024/9/25
 */

@Slf4j
@Component
public class ClearConversationSchedule {

    @Resource
    private ChatGptConversationsMapper conversationsDao;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Scheduled(cron = "0 0 0 * * ?") // Cron 表达式：每天零点
    public void clearConversation() {
        log.info("开始定时清理会话信息...");
        clear(); // 清空对话信息
        log.info("对话信息删除定时任务完成");
    }

    private void clear(){
        String saveLogDays = chatGptConfigService.getValueByKey("saveLogDays");
        try{
            if (StrUtil.isNotEmpty(saveLogDays) && !Objects.equals(saveLogDays, "1")) {
                int days = Integer.parseInt(saveLogDays);
                if (days > 0) {
                    LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);
                    log.info("正在归档 {} 天前的数据，归档截止日期为: {}", days, cutoffDate);
                    int deletedCount = conversationsDao.achiveData(cutoffDate);
                    log.info("成功归档 {} 条过期的会话记录", deletedCount);
                }
            }
        }catch (NumberFormatException e) {
            log.error("解析 saveLogDays 配置项失败: {}", saveLogDays, e);
        }

    }

}

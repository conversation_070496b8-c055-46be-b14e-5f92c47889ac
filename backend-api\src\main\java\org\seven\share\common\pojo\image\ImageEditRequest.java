package org.seven.share.common.pojo.image;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Objects;

@Data
public class ImageEditRequest {
    @NotEmpty(message = "Prompt cannot be empty")
    private String prompt;

    @NotEmpty(message = "At least one image file is required")
    private List<MultipartFile> images;

    private MultipartFile mask;

    @Min(value = 1, message = "Number of images must be at least 1")
    @Max(value = 10, message = "Number of images cannot exceed 10")
    private Integer n;

    @Pattern(regexp = "256x256|512x512|1024x1024", message = "Size must be 256x256, 512x512, or 1024x1024")
    private String size;

    @Pattern(regexp = "url|b64_json", message = "Response format must be url or b64_json")
    private String responseFormat;

    @NotEmpty
    private String model;

    private String quality;

    // 自定义校验方法
    public void validateFiles() {
        // For gpt-image-1, each image should be a png, webp, or jpg file less than 25MB.
        // For dall-e-2, you can only provide one image, and it should be a square png file less than 4MB.
        if (images != null) {
            for (MultipartFile image : images) {
                if (!Objects.equals(image.getContentType(), "image/png")
                        && !Objects.equals(image.getContentType(), "image/jpg")
                        && !Objects.equals(image.getContentType(), "image/webp")
                        && !Objects.equals(image.getContentType(), "image/jpeg")) {
                    throw new IllegalArgumentException("Each image should be in PNG, WEBP, JPG, JPEG");
                }
                if (image.getSize() > 25 * 1024 * 1024) {
                    throw new IllegalArgumentException("Each image size must not exceed 25MB");
                }
            }
        }
    }
}

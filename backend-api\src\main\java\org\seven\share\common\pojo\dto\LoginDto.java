package org.seven.share.common.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: LoginDto
 * @Description: 登录实体dto
 * @Author: Seven
 * @Date: 2024/7/30
 */
@Data
public class LoginDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "用户名不能为空")
    private String username;

    private String code;

    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 登录类型：1、用户名密码，2：授权码
     */
    private int loginType;
}

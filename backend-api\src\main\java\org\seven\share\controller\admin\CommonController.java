package org.seven.share.controller.admin;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.seven.share.common.api.Result;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.api.R;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.service.ChatGptUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

import static org.seven.share.common.util.ConstantUtil.ALLOWED_FILE_TYPES;
import static org.seven.share.common.util.HttpUtils.getBaseUrlFromRequest;

/**
 * @ClassName: CommonController
 * @Description: 文件上传下载
 * @Author: Seven
 * @Date: 2024/3/18
 */
@RestController
public class CommonController {
    private final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Resource
    private ChatGptUserService chatGptUserService;

    @Resource
    private HttpServletRequest request;

    @PostMapping("/client-api/common/upload-qrcode")
    public R upload(MultipartFile file, String userId) {
        if (file.isEmpty()) {
            return R.error("上传的文件为空");
        }

        // 获取文件的 MIME 类型并验证
        String contentType = file.getContentType();
        if (!ALLOWED_FILE_TYPES.contains(contentType)) {
            return R.error("上传的文件格式不被支持。只允许上传 JPEG 或 PNG 图片");
        }

        try {
            // 确保存储目录存在
            String basePath = System.getProperty("user.dir") + File.separator +"data"+File.separator + "upload" + File.separator;  // 相对于项目根目录
            log.info("Upload basePath: {}", basePath);


            // 生成唯一文件名并保存
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                return R.error("文件名不能为空");
            }
            String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
            String fileName = UUID.randomUUID() + suffix;
            File dir = new File(basePath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String finalFileName = basePath + fileName;
            file.transferTo(new File(finalFileName));

            // 更新用户收款码信息
            ChatGptUserEntity user = chatGptUserService.getById(userId);
            if (ObjectUtil.isEmpty(user)) {
                return R.error("用户信息不存在");
            }
            user.setReceiptFile(fileName);
            chatGptUserService.updateById(user);
            log.info("文件上传成功，文件名: {}, 用户ID: {}", originalFilename, userId);
            return R.ok(fileName);

        } catch (IOException e) {
            log.error("文件上传失败，文件名: {}, 用户ID: {}", file.getOriginalFilename(), userId, e);
            throw new CustomException("文件上传失败");
        }
    }


    @PostMapping("/expander-api/common/upload-logo")
    public Result<?> uploadLogo(MultipartFile file) {
        if (file.isEmpty()) {
            return Result.failed("上传的文件为空");
        }
        String domain = getBaseUrlFromRequest(request);
        // 获取文件的 MIME 类型并验证
        String contentType = file.getContentType();
        if (!ALLOWED_FILE_TYPES.contains(contentType)) {
            return Result.failed("上传的文件格式不被支持。只允许上传 JPEG 或 PNG 图片");
        }
        try {
            // 确保存储目录存在
            String basePath = System.getProperty("user.dir")
                    + File.separator
                    + "data" + File.separator + "images" + File.separator;
            log.info("Upload basePath: {}", basePath);
            // 生成唯一文件名并保存
            String fileName =  System.currentTimeMillis() + ".png";
            File dir = new File(basePath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String finalFileName = basePath + fileName;
            file.transferTo(new File(finalFileName));

            log.info("文件上传成功，文件名: {}, ", finalFileName);
            String url = domain + "/expander-api/images/" + fileName;
            log.info(url);
            return Result.success(url);

        } catch (IOException e) {
            log.error("文件上传失败，文件名: {}，", file.getOriginalFilename(), e);
            throw new CustomException("文件上传失败");
        }
    }

    @GetMapping("/client-api/common/download")
    public void download(String name, HttpServletResponse response) {
        if(StrUtil.isEmpty(name)) {
            return;
        }
        String basePath = System.getProperty("user.dir") + File.separator +"data"+File.separator + "upload" + File.separator;  // 相对于项目根目录
        Path filePath = Paths.get(basePath, name);

        // 检查文件是否存在
        if (!Files.exists(filePath)) {
            log.warn("文件不存在: {}", name);
            return;
        }

        // 设置文件类型
        response.setContentType("image/jpeg");

        try (FileInputStream fis = new FileInputStream(filePath.toFile());
             ServletOutputStream outputStream = response.getOutputStream()) {

            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }

            outputStream.flush();
            log.info("文件下载成功: {}", name);

        } catch (IOException e) {
            log.warn("文件下载失败: {}", name, e);
        }
    }
    @GetMapping("/expander-api/download")
    public void downloadAdmin(String name, HttpServletResponse response) {
        if(StrUtil.isEmpty(name)) {
            return;
        }
        String basePath = System.getProperty("user.dir") + File.separator +"data"+File.separator + "upload" + File.separator;  // 相对于项目根目录
        Path filePath = Paths.get(basePath, name);

        // 检查文件是否存在
        if (!Files.exists(filePath)) {
            log.warn("文件不存在: {}", name);
            return;
        }

        // 设置文件类型
        response.setContentType("image/jpeg");

        try (FileInputStream fis = new FileInputStream(filePath.toFile());
             ServletOutputStream outputStream = response.getOutputStream()) {

            byte[] buffer = new byte[1024];
            int len;
            while ((len = fis.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }

            outputStream.flush();
            log.info("文件下载成功: {}", name);

        } catch (IOException e) {
            log.warn("文件下载失败: {}", name, e);
        }
    }
}

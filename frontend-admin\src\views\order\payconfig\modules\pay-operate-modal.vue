<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { addPayConfig, updatePayConfig } from '@/service/api';

defineOptions({ name: 'PayOperateModal' });

export type OperateType = 'add' | 'update';

interface Props {
  /** the type of operation */
  operateType: OperateType;
  /** the edit pay config data */
  rowData?: any | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useForm();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: '新增支付',
    update: '修改支付'
  };
  return titles[props.operateType];
});

interface FormData {
  id?: number;
  title: string;
  appId: string;
  appKey: string;
  paymentsType: number;
  status: number;
  sort: number;
  extraData: {
    payGatewayUrl: string;
    notifyUrl: string;
    privateKeyPem: string;
    merchantId?: string;
    serialNumber?: string;
    enableH5?: boolean;
  };
  iconUrl?: string;
}

const model = ref<FormData>(createDefaultModel());

function createDefaultModel(): FormData {
  const domain = window.location.origin;
  return {
    title: '',
    appId: '',
    appKey: '',
    paymentsType: 1,
    status: 1,
    sort: 1,
    extraData: {
      payGatewayUrl: '',
      notifyUrl: `${domain}/client-api/callBack/ePayNotify`,
      privateKeyPem: '',
      merchantId: '',
      serialNumber: '',
      enableH5: false
    },
    iconUrl: ''
  };
}

const rules = {
  paymentsType: [{ required: true, message: '请选择支付方式', trigger: 'blur' }],
  title: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  appId: [{ required: true, message: '请输入AppId', trigger: 'blur' }],
  appKey: [{ required: true, message: '请输入AppKey', trigger: 'blur' }],
  'extraData.notifyUrl': [{ required: true, message: '请输入回调地址', trigger: 'blur' }],
  'extraData.payGatewayUrl': [{ required: true, message: '请输入网关地址', trigger: 'blur' }]
};

const options = [
  { value: 1, label: '易支付（支付宝）' },
  { value: 2, label: '易支付（微信）' },
  { value: 3, label: '支付宝（当面付）' },
  { value: 4, label: '虎皮椒（微信）' },
  { value: 5, label: '蓝兔微信支付' },
  { value: 6, label: '易支付（usdt）' },
  { value: 7, label: '微信支付（native）' },
  { value: 8, label: '虎皮椒（支付宝）' },
  { value: 9, label: '支付宝（网站支付）' }
];

function handleInitModel() {
  if (props.operateType === 'update' && props.rowData) {
    // 对于更新操作，直接使用传入的数据
    model.value = { ...props.rowData };
  } else {
    // 对于新增操作，使用默认值
    model.value = createDefaultModel();
  }
}

function closeDrawer() {
  visible.value = false;
  Object.assign(model.value, createDefaultModel());
}

const handleChange = (val: number) => {
  const domain = window.location.origin; // 使用当前域名
  const notifyUrls: Record<number, { payGatewayUrl: string; notifyUrl: string }> = {
    1: { payGatewayUrl: '', notifyUrl: `${domain}/client-api/callBack/ePayNotify` },
    2: { payGatewayUrl: '', notifyUrl: `${domain}/client-api/callBack/ePayNotify` },
    3: {
      payGatewayUrl: 'https://openapi.alipay.com/gateway.do',
      notifyUrl: `${domain}/client-api/callBack/aliPayNotify`
    },
    4: {
      payGatewayUrl: 'https://api.xunhunet.com/payment/do.html',
      notifyUrl: `${domain}/client-api/callBack/xunHuPayNotify`
    },
    5: {
      payGatewayUrl: 'https://api.ltzf.cn/client-api/wxpay/native',
      notifyUrl: `${domain}/client-api/callBack/lanTuPayNotify`
    },
    6: { payGatewayUrl: '', notifyUrl: `${domain}/client-api/callBack/ePayNotify` },
    7: { payGatewayUrl: '', notifyUrl: `${domain}/client-api/callBack/wxNativeNotify` },
    8: {
      payGatewayUrl: 'https://api.xunhunet.com/payment/do.html',
      notifyUrl: `${domain}/client-api/callBack/xunHuAliPayNotify`
    },
    9: {
      payGatewayUrl: 'https://openapi.alipay.com/gateway.do',
      notifyUrl: `${domain}/client-api/callBack/aliPayWapNotify`
    }
  };

  const config = notifyUrls[val] || { payGatewayUrl: '', notifyUrl: '' };
  model.value.extraData.payGatewayUrl = config.payGatewayUrl;
  model.value.extraData.notifyUrl = config.notifyUrl;
};

async function handleSubmit() {
  await validate();

  try {
    if (props.operateType === 'add') {
      await addPayConfig(model.value);
    } else {
      await updatePayConfig(model.value);
    }
    ElMessage({
      message: '保存成功',
      type: 'success',
      plain: true
    });
    closeDrawer();
    emit('submitted');
  } catch {}
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <ElDialog v-model="visible" :title="title" preset="card" class="w-800px">
    <ElScrollbar class="h-480px pr-20px">
      <ElForm ref="formRef" :model="model" :rules="rules" :label-width="100">
        <ElRow>
          <ElCol>
            <ElFormItem label="支付方式" prop="paymentsType">
              <ElSelect v-model="model.paymentsType" placeholder="请选择支付方式" @change="handleChange">
                <ElOption v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol>
            <ElFormItem label="名称" prop="title">
              <ElInput v-model="model.title" placeholder="请输入名称（前端显示）" />
            </ElFormItem>
          </ElCol>
          <ElCol>
            <ElFormItem label="AppId" prop="appId">
              <ElInput v-model="model.appId" placeholder="请输入AppId" />
            </ElFormItem>
          </ElCol>
          <ElCol v-if="model.paymentsType !== 3 && model.paymentsType !== 9">
            <ElFormItem label="AppKey" prop="appKey">
              <ElInput v-model="model.appKey" placeholder="请输入AppKey" />
            </ElFormItem>
          </ElCol>
          <ElCol v-if="model.paymentsType === 7">
            <ElFormItem label="商户号" prop="extraData.merchantId">
              <ElInput v-model="model.extraData.merchantId" placeholder="请输入商户号" />
            </ElFormItem>
          </ElCol>
          <ElCol v-if="model.paymentsType === 7">
            <ElFormItem label="商户证书序列号" prop="extraData.serialNumber">
              <ElInput v-model="model.extraData.serialNumber" placeholder="请输入商户证书序列号" />
            </ElFormItem>
          </ElCol>
          <ElCol v-if="[1, 2, 6].includes(model.paymentsType)">
            <ElFormItem label="支付地址" prop="extraData.payGatewayUrl">
              <ElInput
                v-model="model.extraData.payGatewayUrl"
                placeholder="请输入易支付地址，如https://pay.xxx.com即可"
              />
            </ElFormItem>
          </ElCol>
          <ElCol v-if="[3, 9].includes(model.paymentsType)">
            <ElFormItem label="公钥" prop="appKey">
              <ElInput v-model="model.appKey" type="textarea" placeholder="请粘贴复制的公钥" />
            </ElFormItem>
          </ElCol>
          <ElCol v-if="[3, 7, 9].includes(model.paymentsType)">
            <ElFormItem label="应用私钥" prop="extraData.privateKeyPem">
              <ElInput
                v-model="model.extraData.privateKeyPem"
                type="textarea"
                placeholder="请粘贴复制的私钥，支付宝需要用 PKCS8 格式"
              />
            </ElFormItem>
          </ElCol>
          <ElCol v-if="[3, 4, 9].includes(model.paymentsType)">
            <ElFormItem label="网关" prop="extraData.payGatewayUrl">
              <ElInput v-model="model.extraData.payGatewayUrl" placeholder="请输入网关" />
            </ElFormItem>
          </ElCol>
          <ElCol>
            <ElFormItem label="回调" prop="extraData.notifyUrl">
              <ElInput v-model="model.extraData.notifyUrl" placeholder="请输入回调地址" />
            </ElFormItem>
          </ElCol>
          <ElCol>
            <ElFormItem label="支付图标" prop="iconUrl">
              <ElInput v-model="model.iconUrl" placeholder="请输入支付图标地址" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="状态" prop="status">
              <ElSwitch v-model="model.status" :active-value="1" :inactive-value="0" />
            </ElFormItem>
          </ElCol>
          <ElCol v-if="![3, 8, 6, 7].includes(model.paymentsType)" :span="12">
            <ElFormItem label="开启H5支付" prop="extraData.enableH5">
              <ElSwitch v-model="model.extraData.enableH5" />
            </ElFormItem>
          </ElCol>
          <ElCol>
            <ElFormItem label="排序" prop="sort">
              <ElInputNumber v-model="model.sort" :min="1" :max="9999" />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </ElScrollbar>
    <template #footer>
      <ElSpace :size="16" class="float-right">
        <ElButton @click="closeDrawer">取 消</ElButton>
        <ElButton type="primary" @click="handleSubmit">保 存</ElButton>
      </ElSpace>
    </template>
  </ElDialog>
</template>

<style scoped></style>

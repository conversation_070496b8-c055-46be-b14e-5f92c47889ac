package org.seven.share.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.R;
import org.seven.share.common.api.Result;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.dto.BatchSaveRequest;
import org.seven.share.common.pojo.dto.ChatGptSessionDto;
import org.seven.share.common.pojo.entity.ChatGptSessionEntity;
import org.seven.share.common.pojo.vo.CarInfoVo;
import org.seven.share.common.pojo.vo.ChatGptSessionVo;
import org.seven.share.service.ChatGptSessionService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @ClassName: ChatGptSessionController
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/14
 */
@RestController
@RequestMapping("/expander-api/session")
public class ChatGptSessionController {

    @Resource
    private ChatGptSessionService chatGptSessionService;


    @GetMapping("/page")
    @SysLogInterface(title = "分页查询gpt账号信息", businessType = BusinessType.QUERY)
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer current,
                  @RequestParam(value = "size", defaultValue = "10") Integer size,
                  String query,
                  String sortProp,
                  String sortOrder){

        // 执行分页查询
        Page<ChatGptSessionDto> pageInfo = chatGptSessionService.getPage(current, size, query, sortProp, sortOrder);
        // 返回分页查询结果
        return R.ok(pageInfo);
    }

    @GetMapping("/{id}")
    @SysLogInterface(title = "根据id查找gpt账号信息", businessType = BusinessType.QUERY)
    public R getSessionById(@PathVariable("id") Long id) {
        ChatGptSessionEntity chatGptSession = chatGptSessionService.getById(id);
        return R.ok(chatGptSession);
    }

    @PostMapping("/batch/save")
    @SysLogInterface(title = "批量新增gpt账号", businessType = BusinessType.INSERT)
    public R create(@RequestBody BatchSaveRequest request) {
        chatGptSessionService.saveAccessTokenBatch(request.getAccounts(), request.getOptType());
        return R.ok();
    }

    @PostMapping("/create")
    @SysLogInterface(title = "新增gpt账号信息", businessType = BusinessType.INSERT)
    public R create(@RequestBody ChatGptSessionEntity chatGptSession) {
        chatGptSessionService.insertAccessToken(chatGptSession);
        return R.ok(chatGptSession);
    }

    @PutMapping("/update")
    @SysLogInterface(title = "修改gpt账号信息", businessType = BusinessType.UPDATE)
    public R update(@RequestBody ChatGptSessionEntity chatGptSession) {
        chatGptSessionService.updateAccessToken(chatGptSession);
        return R.ok(chatGptSession);
    }

    @DeleteMapping("/delete")
    @SysLogInterface(title = "批量删除gpt账号信息", businessType = BusinessType.DELETE)
    public Result<?> delete(@RequestBody List<String> ids) {
        chatGptSessionService.removeCarInfoBatch(ids);
        return Result.success();
    }

    /**
     * 获取未绑定的gpt账号列表
     * @return
     */
    @GetMapping("/list")
    @SysLogInterface(title = "查询gpt账号列表", businessType = BusinessType.QUERY)
    public R list(){
        List<ChatGptSessionVo> list = chatGptSessionService.listNoBindCarInfo();
        return R.ok(list);
    }

    @GetMapping("/export-sess")
    @SysLogInterface(title = "导出gpt账号信息", businessType = BusinessType.EXPORT)
    public void exportGptSession(HttpServletResponse response) {
        chatGptSessionService.exportGptSession(response);
    }

    @GetMapping("/fetchAllCarList")
    @SysLogInterface(title = "查询所有gpt账号信息列表", businessType = BusinessType.QUERY)
    public R fetchAllCarList() {
        List<CarInfoVo> list = chatGptSessionService.fetchAllCarList();
        return R.ok(list);
    }

    @PutMapping("/updateStatus")
    @SysLogInterface(title = "更新用户状态", businessType = BusinessType.UPDATE)
    public R updateStatus(@RequestParam("id") Long id,
                             @RequestParam("status") Integer status) {
        chatGptSessionService.updateGptSessionStatus(id, status);
        return R.ok();
    }

    @PostMapping("/unbind/{id}")
    @SysLogInterface(title = "解绑gpt账号与用户的关联关系", businessType = BusinessType.OTHER)
    public R  unbindSession(@PathVariable("id") Long id) {
        chatGptSessionService.unbindSession(id);
        return R.ok();
    }
}

package org.seven.share.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.seven.share.common.pojo.entity.DrawRecordEntity;

/**
 * @InterfaceName: DrawRecordDao
 * @Description:
 * @Author: Seven
 * @Date: 2025/4/25
 */
@Mapper
public interface DrawRecordMapper extends BaseMapper<DrawRecordEntity> {
    void updateDrawTimeOut();

    IPage<DrawRecordEntity> selectDrawRecodePage(Page<DrawRecordEntity> page,
                                           @Param("ew") Wrapper<DrawRecordEntity> queryWrapper);
}

package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ChatGptCouponEntity
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/13
 */
@Data
@TableName("chatgpt_coupon")
public class ChatGptCouponEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    private String coupon;

    private Double discount;

    private Integer status;

    @NotNull
    @TableField("expireTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireTime;

    @TableField(value = "createTime", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "updateTime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    private String remark;


    @TableField("subTypeIds")
    private String subTypeIds;

    @TableField(value = "discountAmount", updateStrategy = FieldStrategy.ALWAYS)
    private Double discountAmount;

    @TableField(exist = false)
    private List<String> ids;

    @TableField("discountCount")
    private Integer discountCount;

    @TableField("usageDiscountCount")
    private Integer usageDiscountCount;
}

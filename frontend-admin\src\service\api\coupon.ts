import { request } from '../request';

export function fetchCouponPage (params) {
    return request({
        url: '/coupon/page',
        method: 'get',
        params
    });
}

export function saveCoupon (data) {
    return request({
        url: '/coupon/create',
        data,
        method: 'post',
    });
}

export function updateCoupon (data) {
    return request({
        url: '/coupon/update',
        data,
        method: 'put',
    });
}


export function removeCouponBatchByIds (data) {
    return request({
        url: '/coupon/delete',
        data,
        method: 'delete',
    });
}

export function generateCoupon () {
    return request({
        url: '/coupon/generate',
        method: 'get',
    });
}

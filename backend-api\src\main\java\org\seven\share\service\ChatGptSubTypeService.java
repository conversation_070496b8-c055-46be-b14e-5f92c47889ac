package org.seven.share.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.pojo.entity.ChatGptSubTypeEntity;

import java.util.List;

/**
 * @InterfaceName: ChatGptSubTypeService
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/11
 */
public interface ChatGptSubTypeService  extends IService<ChatGptSubTypeEntity> {
    Double calcOfferAmount(Long planId, String coupon, double originalMoney, boolean paySuccess);

    void updateSubType(ChatGptSubTypeEntity chatGptSubType);

    void saveSubType(ChatGptSubTypeEntity chatGptSubType);

    void updateSubtypeStatus(Long id, Integer status);

    List<ChatGptSubTypeEntity> listAdminSubType();

    Page<ChatGptSubTypeEntity> getPage(Integer page, Integer size, String query);

    void syncModelLimits();

    List<ChatGptSubTypeEntity> listSubTypeByDomain();

    int logicDeleteSubtype(List<String> ids);
}

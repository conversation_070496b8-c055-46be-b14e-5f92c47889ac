package org.seven.share.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {
    int value() default 100;    // 限流阈值
    int time() default 60;      // 时间窗口(秒)
    String key() default "";    // 限流key，为空则使用IP
}

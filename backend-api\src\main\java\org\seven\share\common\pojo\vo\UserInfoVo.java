package org.seven.share.common.pojo.vo;

import lombok.Data;
import org.seven.share.common.pojo.dto.ModelLimitDto;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * @ClassName: UserInfoVo
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/31
 */

@Data
public class UserInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    private String userToken;

    private Integer isAdmin;

    private LocalDateTime expireTime;

    private LocalDateTime plusExpireTime;

    private LocalDateTime claudeExpireTime;

    private LocalDateTime claudeProExpireTime;

    private LocalDateTime grokExpireTime;

    private LocalDateTime grokSuperExpireTime;

    private Integer isPlus;

    private String email;

    private String affCode;

    private String createTime;

    private String limit;

    private String per;

    private String claudeLimit;

    private String claudePer;

    private Double affRate;

    private Integer affCount;

    private String affTotalQuota;

    private String affHistoryQuota;

    private String affQuota;

    private Map<String, ModelLimitDto> modelLimits;

    private Integer loginType;

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.seven.share.mapper.CreateTableMapper">

    <!-- 直接使用 CREATE TABLE IF NOT EXISTS -->
    <insert id="createCouponTable">
    CREATE TABLE IF NOT EXISTS `chatgpt_coupon` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
        `coupon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '优惠卷',
        `discount` decimal(5,2) DEFAULT NULL COMMENT '折扣',  -- 建议使用 DECIMAL 代替 DOUBLE
        `status` tinyint(1) DEFAULT 0 COMMENT '状态(0：启用，1：废弃)',
        `expireTime` datetime DEFAULT NULL COMMENT '过期时间',
        `createTime` datetime DEFAULT NULL COMMENT '创建时间',
        `updateTime` datetime DEFAULT NULL COMMENT '更新时间',
        `discountAmount` decimal(10,2) DEFAULT NULL COMMENT '优惠金额',  -- 明确精度
        `subTypeIds` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品ids',
        `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
        `discountCount` int DEFAULT 0 COMMENT '优惠次数',  -- 添加默认值
        `usageDiscountCount` int DEFAULT 0 COMMENT '已优惠次数',
        PRIMARY KEY (`id`) USING BTREE  -- 删除末尾逗号
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=Dynamic;
    </insert>

    <insert id="createPayLogsTable">
        CREATE TABLE IF NOT EXISTS `chatgpt_epaylogs`  (
         `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
         `createTime` datetime(3) NOT NULL COMMENT '创建时间',
         `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
         `deleted_at` datetime(3) DEFAULT NULL,
         `userToken` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'userToken',
         `money` double NOT NULL COMMENT '支付金额',
         `tradeNo` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
         `subTypeId` bigint(20) NOT NULL COMMENT '订阅套餐',
         `days` bigint(20) NOT NULL COMMENT '有效天数',
         `status` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单状态',
         `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
         PRIMARY KEY (`id`) USING BTREE,
         INDEX `idx_chatgpt_epaylogs_deleted_at`(`deleted_at`) USING BTREE,
         INDEX `idx_chatgpt_epaylogs_trade_no`(`tradeNo`) USING BTREE,
         INDEX `idx_chatgpt_epaylogs_user_token`(`userToken`) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;
    </insert>

    <insert id="createPayConfigTable">
        CREATE TABLE IF NOT EXISTS `chatgpt_pay_config`  (
       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Primary Key',
       `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Title of the configuration',
       `appId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'App ID',
       `appKey` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'App Key',
       `extraData` json COMMENT 'Extra data stored in JSON format',
       `paymentsType` int(11) DEFAULT NULL COMMENT 'Type of payment',
       `status` int(11) DEFAULT NULL COMMENT 'Status of the configuration',
       `sort` int(11) DEFAULT NULL COMMENT 'Sort order',
       `iconUrl` varchar(255)  COMMENT 'icon图标地址',
       `createTime` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation time',
       `updateTime` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT 'Record last update time',
       PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Table to store ChatGPT payment configuration' ROW_FORMAT = Dynamic;
    </insert>


    <insert id="createSubTypeTable">
        CREATE TABLE IF NOT EXISTS `chatgpt_subtype`  (
        `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
        `createTime` datetime(3) NOT NULL COMMENT '创建时间',
        `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
        `deleted_at` datetime(3) DEFAULT NULL,
        `name` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
        `validDays` bigint(20) NOT NULL COMMENT '有效天数',
        `money` double NOT NULL COMMENT '订阅金额',
        `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
        `limit` bigint(20) NOT NULL DEFAULT 20 COMMENT '额度',
        `per` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1h' COMMENT '限制',
        `isPlus` tinyint(1) DEFAULT 1 COMMENT 'PLUS',
        `isPro` tinyint(1)  COMMENT 'Pro',
        `isNotValued` tinyint(1) DEFAULT 0 COMMENT '不计价值',
        `subType` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '订阅类型，chatgpt或者claude',
        `sort` bigint(20) DEFAULT 0 COMMENT '排序',
        `model_limits` json COMMENT '模型限制',
        `exclusive` tinyint(1) default 0 comment '是否独显：0-否，1-是',
        `exclusiveType` tinyint(1) default 0 comment '是否独显：0-free，1-plus,2-team,3-pro',
        `isHotSale` tinyint(1) default 0 comment '显示热卖标志：0-否，1-是',
        `isSuper` tinyint(1)  COMMENT 'grok super标志',
        `draw_quota` bigint NOT NULL DEFAULT 0 COMMENT '绘画额度',
        `tenant_id` VARCHAR(20) NOT NULL DEFAULT '000000' COMMENT '租户编号',
         PRIMARY KEY (`id`) USING BTREE,
        INDEX `idx_chatgpt_subtype_deleted_at`(`deleted_at`) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;
    </insert>

    <insert id="createSysNoticeTable">
        CREATE TABLE IF NOT EXISTS `chatgpt_sys_notice`  (
       `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
       `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '公告内容',
       `publishTime` datetime(0) DEFAULT NULL COMMENT '发布时间',
       `createTime` datetime(0) DEFAULT NULL COMMENT '创建时间',
       `updateTime` datetime(0) DEFAULT NULL COMMENT '更新时间',
       `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
       `tenant_id` VARCHAR(20) NOT NULL DEFAULT '000000' COMMENT '租户编号',
       `type` VARCHAR(10) NOT NULL DEFAULT 'system' COMMENT '通知类型，站内公告：site，系统通知：system，登录公告：login',
        PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;
    </insert>


    <insert id="createClaudeSessionTable">
        CREATE TABLE IF NOT EXISTS `claude_session` (
          `id` bigint unsigned NOT NULL AUTO_INCREMENT,
          `createTime` datetime(3) NOT NULL COMMENT '创建时间',
          `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
          `deleted_at` datetime(3) DEFAULT NULL,
          `email` longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
          `status` tinyint(1) DEFAULT '0' COMMENT '状态',
          `isPro` tinyint(1) DEFAULT '0' COMMENT 'isPro',
          `carID` longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '展示ID',
          `officialSession` longtext COLLATE utf8mb4_unicode_ci COMMENT '官方session',
          `remark` longtext COLLATE utf8mb4_unicode_ci COMMENT '备注',
          `remaining` varchar(10) COLLATE utf8mb4_unicode_ci COMMENT '剩余次数',
          `resetsAt` varchar(20) COLLATE utf8mb4_unicode_ci COMMENT '重置时间',
          `sort` bigint DEFAULT '0' COMMENT '排序',
          `count` bigint DEFAULT '0' COMMENT '统计',
          `password` longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
          PRIMARY KEY (`id`),
          KEY `idx_chatgpt_session_deleted_at` (`deleted_at`)
        ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    </insert>

    <insert id="createAuthoriseTable">
        CREATE TABLE IF NOT EXISTS `authorise` (
          `id` bigint unsigned NOT NULL AUTO_INCREMENT,
          `create_time` datetime(3) NOT NULL COMMENT '创建时间',
          `update_time` datetime(3) NOT NULL COMMENT '更新时间',
          `authorise` longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '授权码',
          `remark` longtext COLLATE utf8mb4_unicode_ci COMMENT '备注',
          PRIMARY KEY (`id`) USING BTREE
          ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;
    </insert>

    <insert id="createChatGptConfigTable">
        CREATE TABLE  IF NOT EXISTS  `chatgpt_config`  (
           `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
           `createTime` datetime(3) NOT NULL COMMENT '创建时间',
           `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
           `deleted_at` datetime(3) DEFAULT NULL,
           `key` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'key',
           `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '值',
           `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
           PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB  CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;
    </insert>

    <insert id="createChatGptRedemptionTable">
        CREATE TABLE IF NOT EXISTS `chatgpt_redemption` (
          `id` bigint unsigned NOT NULL AUTO_INCREMENT,
          `createTime` datetime(3) NOT NULL COMMENT '创建时间',
          `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
          `deleted_at` datetime(3) DEFAULT NULL,
          `userId` bigint NOT NULL COMMENT '创建用户id',
          `key` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'key',
          `status` bigint NOT NULL COMMENT '状态',
          `subTypeId` bigint NOT NULL COMMENT '订阅类型',
          `redeemedTime` datetime(3) DEFAULT NULL COMMENT '兑换时间',
          `usedUserId` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '使用者',
          `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
          `limit` bigint NOT NULL DEFAULT '20' COMMENT '额度',
          `per` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1h' COMMENT '限制',
          `isPlus` tinyint(1) DEFAULT '1' COMMENT 'PLUS',
          PRIMARY KEY (`id`),
          KEY `idx_chatgpt_redemption_deleted_at` (`deleted_at`)
        ) ENGINE=InnoDB AUTO_INCREMENT=236 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    </insert>

    <insert id="createSensitiveWordTable">
        CREATE TABLE IF NOT EXISTS `chatgpt_sensitive_word` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `createTime` datetime(3) NOT NULL COMMENT '创建时间',
            `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
            `word` longtext NOT NULL COMMENT '敏感词',
            `status` bigint NOT NULL DEFAULT 1 COMMENT '状态',
            `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    </insert>

    <insert id="createWithdrawalsTable">
        CREATE TABLE IF NOT EXISTS `chatgpt_withdrawals` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `createTime` datetime(3) NOT NULL COMMENT '创建时间',
            `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
            `withdrawalTime` datetime(3) NOT NULL COMMENT '提现时间',
            `username` longtext NOT NULL COMMENT '提现人',
            `withdrawalMoney` double NOT NULL COMMENT '提现金额',
            `userId` longtext NOT NULL COMMENT '提现人id',
            `contact` varchar(20) NOT NULL COMMENT '联系方式',
            `withdrawalQrcode` longtext NOT NULL COMMENT '提现二维码',
            `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '提现状态（0：未处理，1：提现成功）',
            `remark` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    </insert>

    <update id="createSignInRecordsTable">
        CREATE TABLE IF NOT EXISTS `sign_in_records` (
             `id` bigint unsigned NOT NULL AUTO_INCREMENT,
             `createTime` datetime(3) NOT NULL COMMENT '创建时间',
            `userId` longtext NOT NULL COMMENT '签到人id',
            `signedDate` DATE NOT NULL COMMENT '签到日期',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    </update>
    <update id="createChatGptAffRecordTable">
        CREATE TABLE IF NOT EXISTS `chatgpt_aff_record` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `createTime` datetime(3) NOT NULL COMMENT '创建时间',
            `inviterId` bigint NOT NULL COMMENT '邀请人id',
            `inviteeId` bigint NOT NULL COMMENT '被邀请人id',
            `affMoney` double NOT NULL COMMENT '推广金额',
            `orderType` tinyint(1) COMMENT '订单类型1-充值 2-兑换',
            `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（0：未处理，1：已处理）',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    </update>

    <update id="createRiskControlRecordTable">
        CREATE TABLE if NOT EXISTS risk_control_record (
            id BIGINT PRIMARY KEY AUTO_INCREMENT,
            username varchar(100) NOT NULL COMMENT '用户名',
            prompt text NOT NULL COMMENT '对话内容',
            keyword text NOT NULL COMMENT '关键词',
            content_type varchar(50)  COMMENT '内容类型',
            created_at datetime(3) NOT NULL COMMENT '创建时间',
            updated_at datetime(3) NOT NULL COMMENT '更新时间',
            remark text COMMENT '备注'
            );
    </update>


    <update id="createDrawRecord">
        CREATE TABLE if NOT EXISTS draw_record (
           id BIGINT PRIMARY KEY AUTO_INCREMENT,
           user_id BIGINT NOT NULL COMMENT '用户ID',
           prompt text NOT NULL COMMENT '对话内容',
           draw_type tinyint(1) not null COMMENT '画图方式：1：生图、2：改图',
           task_id varchar(50) NOT NULL COMMENT '任务id',
           task_status varchar(10) COMMENT '任务状态（PENDING，COMPLETED，FAILED）',
           image_url text  COMMENT '图片地址',
           model varchar(20) COMMENT '模型',
           image_size varchar(20) COMMENT '图片大小',
           num tinyint(2) COMMENT '图片数量',
           response text COMMENT '响应结果',
           response_format varchar(10) COMMENT '图片响应格式：url或者b64_json',
           created_at datetime(3) NOT NULL COMMENT '创建时间',
           updated_at datetime(3) NOT NULL COMMENT '更新时间',
           description text COMMENT '绘图详情',
           INDEX draw_record_user_id (user_id));
    </update>

    <update id="crateUserDrawQuota">
        CREATE TABLE  if NOT EXISTS user_drawing_quota (
               id BIGINT AUTO_INCREMENT PRIMARY KEY,
               user_id BIGINT NOT NULL COMMENT '用户ID',
               total_quota BIGINT NOT NULL DEFAULT 0 COMMENT '总配额',
               used_quota BIGINT NOT NULL DEFAULT 0 COMMENT '已使用配额',
               remaining_quota BIGINT NOT NULL DEFAULT 0 COMMENT '剩余配额',
               reset_at datetime(3) COMMENT '到期时间',
               created_at datetime(3) NOT NULL COMMENT '创建时间',
               updated_at datetime(3) NOT NULL COMMENT '更新时间',
               UNIQUE KEY uk_user_id (user_id)
        ) COMMENT '用户绘图配额表';
    </update>


    <update id="createQuotaChangeRecord">
        CREATE TABLE if NOT EXISTS quota_change_record (
           id BIGINT AUTO_INCREMENT PRIMARY KEY,
           user_id BIGINT NOT NULL COMMENT '用户ID',
           change_type TINYINT NOT NULL COMMENT '变更类型：1-套餐购买，2-绘图消耗，3-管理员调整，4-套餐重置',
           change_amount BIGINT NOT NULL COMMENT '变更数量，正数为增加，负数为减少',
           remark VARCHAR(255) COMMENT '备注',
           created_at datetime(3) NOT NULL COMMENT '创建时间',
           INDEX idx_user_id (user_id),
           INDEX idx_create_time (created_at)
        ) COMMENT '配额变更记录表';
    </update>

    <update id="createSysTenantTable">
        CREATE TABLE IF NOT EXISTS t_sys_tenant (
        `id` int(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
        `tenant_id` VARCHAR(20) NOT NULL DEFAULT '000000' UNIQUE COMMENT '租户编号',
        `contact_user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '联系人',
        `contact_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '联系电话',
        `tenant_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '租户名称',
        `domain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '域名',
        `site_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '站点名称',
        `site_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '站点logo',
        `commission_ratio` DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '佣金比例',
        `commission_balance` DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '佣金余额',
        `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '备注',
        `package_id` int(0) DEFAULT NULL COMMENT '租户套餐编号',
        `expire_time` datetime(0) DEFAULT NULL COMMENT '过期时间',
        `status` int(0) DEFAULT 0 COMMENT '租户状态（0正常 1停用）',
        `is_deleted` int(0) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
        `create_id` int(20)    DEFAULT NULL COMMENT '创建者ID',
        `create_by` varchar(30) DEFAULT NULL COMMENT '创建者',
        `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `update_id`  int(20)    DEFAULT NULL COMMENT '创建者ID',
        `update_by` varchar(30) DEFAULT NULL COMMENT '更新者',
        `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
        `delete_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '删除时间',
            PRIMARY KEY (id)
        ) COMMENT '租户表';

    </update>
    <update id="createGrokConversationsTable">
        CREATE TABLE IF NOT EXISTS grok_conversations (
          `id` bigint unsigned NOT NULL AUTO_INCREMENT,
          `createTime` datetime(3) NOT NULL COMMENT '创建时间',
          `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
          `deleted_at` datetime(3) DEFAULT NULL,
          `usertoken` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户token',
          `convid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话id',
          `title` text COLLATE utf8mb4_unicode_ci COMMENT '会话标题',
          `sso` longtext COLLATE utf8mb4_unicode_ci COMMENT 'grok sso',
          `content` longtext COLLATE utf8mb4_unicode_ci COMMENT '会话内容',
          PRIMARY KEY (`id`),
          KEY `idx_grok_conversations_deleted_at` (`deleted_at`),
          KEY `idx_grok_conversations_user_token` (`usertoken`),
          KEY `idx_grok_conversations_conv_id` (`convid`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    </update>

    <update id="createGrokSessionTable">
        CREATE TABLE IF NOT EXISTS grok_session (
        `id` bigint unsigned NOT NULL AUTO_INCREMENT,
        `createTime` datetime(3) NOT NULL COMMENT '创建时间',
        `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
        `deleted_at` datetime(3) DEFAULT NULL,
        `email` longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
        `password` longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
        `status` tinyint(1) DEFAULT '0' COMMENT '状态',
        `isPro` tinyint(1) DEFAULT '0' COMMENT 'Pro',
        `officialSession` longtext COLLATE utf8mb4_unicode_ci COMMENT '官方session',
        `remark` longtext COLLATE utf8mb4_unicode_ci COMMENT '备注',
        `sort` bigint DEFAULT '0' COMMENT '排序',
        `count` bigint DEFAULT '0' COMMENT '统计',
        PRIMARY KEY (`id`),
        KEY `idx_grok_session_deleted_at` (`deleted_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    </update>

    <update id="createSysOperatorLogTable">
        CREATE TABLE IF NOT EXISTS t_sys_operation_log (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `business_type` tinyint(0) NOT NULL COMMENT '类型（0=其它,1=新增,2=修改,3=删除,4=授权,5=导出,6=导入,7=强退,8=生成代码,9=清空数据）',
            `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '方法名称',
            `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '请求方式',
            `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '描述',
            `req_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '请求IP',
            `req_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '请求信息',
            `resp` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '响应信息',
            `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '错误消息',
            `create_id` bigint(0) DEFAULT NULL COMMENT '创建者ID',
            `create_by` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '创建者名称',
            `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
            `update_id` bigint(0) DEFAULT NULL COMMENT '修改者ID',
            `update_by` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '修改者名称',
            `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
            `is_deleted` tinyint(0) DEFAULT NULL COMMENT '是否已删除：0->未删除；1->已删除',
            `delete_time` datetime(0) DEFAULT NULL COMMENT '删除时间',
            `oper_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '操作人',
            `oper_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '操作地点',
            `status` tinyint(0) DEFAULT NULL COMMENT '状态 1-可用，2-禁用',
            PRIMARY KEY (`id`) USING BTREE,
            INDEX `t_sys_operation_log_id_idx`(`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 68 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '系统操作日志' ROW_FORMAT = Dynamic;

    </update>

    <update id="createSysResourceTable">
        CREATE TABLE IF NOT EXISTS t_sys_resource  (
               `id` bigint unsigned NOT NULL AUTO_INCREMENT,
               `parent_id` int(0) NOT NULL COMMENT '父节点id',
               `ui_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '唯一标识路径',
               `menu_type` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '1：菜单路由；2：资源（按钮）3: 基础资源',
               `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '状态；1:可用，2:禁用',
               `menu_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
               `route_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '路由名称',
               `route_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '菜单路由为path，其他为唯一标识',
               `component` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
               `meta` varchar(455) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '元数据',
               `weight` int(0) DEFAULT NULL COMMENT '权重顺序',
               `create_id` bigint(0) NOT NULL COMMENT '创建者ID',
               `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者名称',
               `update_id` bigint(0) DEFAULT NULL COMMENT '修改者ID',
               `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改者名称',
               `create_time` datetime(0) NOT NULL COMMENT '创建时间',
               `update_time` datetime(0) DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `is_deleted` tinyint(0) NOT NULL DEFAULT 0 COMMENT '是否已删除：0->未删除；1->已删除',
               `delete_time` datetime(0) DEFAULT NULL COMMENT '删除时间',
               PRIMARY KEY (`id`) USING BTREE,
               INDEX `t_sys_resource_ui_path_IDX`(`ui_path`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 153 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '资源表' ROW_FORMAT = Dynamic;

    </update>

    <update id="createSysRoleTable">
        CREATE TABLE IF NOT EXISTS t_sys_role  (
           `id` bigint unsigned NOT NULL AUTO_INCREMENT,
           `role_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
           `role_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色code',
           `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '状态；1:可用，2:禁用',
           `role_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
           `type` tinyint(0) NOT NULL COMMENT '类型：1:公共角色；2:特殊角色',
           `create_id` bigint(0) NOT NULL COMMENT '创建者ID',
           `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者名称',
           `update_id` bigint(0) DEFAULT NULL COMMENT '修改者ID',
           `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改者名称',
           `create_time` datetime(0) NOT NULL COMMENT '创建时间',
           `update_time` datetime(0) DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
           `is_deleted` tinyint(0) DEFAULT 0 COMMENT '是否已删除：0->未删除；1->已删除',
           `delete_time` datetime(0) DEFAULT NULL COMMENT '删除时间',
           PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '角色表' ROW_FORMAT = Dynamic;

    </update>

    <update id="createSysRoleResourceTable">
        CREATE TABLE IF NOT EXISTS t_sys_role_resource  (
            `id` bigint(0) NOT NULL AUTO_INCREMENT,
            `role_id` bigint(0) NOT NULL,
            `resource_id` bigint(0) NOT NULL,
            `create_id` bigint unsigned,
            `create_by` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
            `update_id` bigint(0) DEFAULT NULL,
            `update_by` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
            `create_time` datetime(0) DEFAULT NULL,
            `update_time` datetime(0) DEFAULT NULL,
            `is_deleted` tinyint(0) NOT NULL DEFAULT 0,
            `delete_time` datetime(0) DEFAULT NULL,
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 718 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '角色资源关联表' ROW_FORMAT = Dynamic;

    </update>

    <update id="createSysUserTable">
        CREATE TABLE IF NOT EXISTS t_sys_user  (
           `id` bigint unsigned NOT NULL AUTO_INCREMENT,
           `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '账户昵称',
           `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '用户名',
           `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '密码',
           `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '状态；1:可用，2:禁用',
           `otp_secret` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
           `user_gender` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '性别',
           `user_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '电话',
           `user_email` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '电子邮箱',
           `last_login_time` datetime(0) DEFAULT NULL,
           `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
           `create_id` bigint(0) DEFAULT NULL,
           `create_by` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
           `create_time` datetime(0) DEFAULT NULL,
           `update_id` bigint(0) DEFAULT NULL,
           `update_by` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
           `update_time` datetime(0) DEFAULT NULL,
           `is_deleted` tinyint(0) DEFAULT NULL,
           `delete_time` datetime(0) DEFAULT NULL,
           `tenant_id` VARCHAR(20) NOT NULL DEFAULT '000000' UNIQUE COMMENT '租户编号',
           PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

    </update>
    <update id="createSysUserRoleTable">
        CREATE TABLE IF NOT EXISTS t_sys_user_role  (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'Id',
            `user_id` bigint(0) NOT NULL COMMENT '用户id',
            `role_id` bigint(0) NOT NULL COMMENT '角色id',
            `create_id` bigint unsigned,
            `create_by` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
            `update_id` bigint unsigned,
            `update_by` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
            `create_time` datetime(0) NOT NULL,
            `update_time` datetime(0) DEFAULT NULL,
            `is_deleted` tinyint(0) NOT NULL DEFAULT 0,
            `delete_time` datetime(0) DEFAULT NULL,
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 71 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

    </update>

    <update id="createClaudeConversationsTable">
        CREATE TABLE IF NOT EXISTS claude_conversations (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `createTime` datetime(3) NOT NULL COMMENT '创建时间',
            `updateTime` datetime(3) NOT NULL COMMENT '更新时间',
            `deleted_at` datetime(3) DEFAULT NULL,
            `usertoken` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户token',
            `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话id',
            `title` text COLLATE utf8mb4_unicode_ci COMMENT '会话标题',
            `content` longtext COLLATE utf8mb4_unicode_ci COMMENT '会话内容',
            PRIMARY KEY (`id`),
            KEY `idx_claude_conversations_deleted_at` (`deleted_at`),
            KEY `idx_claude_conversations_user_token` (`usertoken`),
            KEY `idx_claude_conversations_conv_id` (`uuid`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    </update>

    <update id="addTenantIdColumn">
        ALTER TABLE chatgpt_subtype
            ADD COLUMN tenant_id VARCHAR(20) NOT NULL DEFAULT '000000' COMMENT '租户编号'
    </update>

    <update id="addDrawQuotaColumn">
        ALTER TABLE chatgpt_subtype
            ADD COLUMN draw_quota BIGINT NOT NULL DEFAULT 0 COMMENT '绘画额度'
    </update>

    <update id="addIsSuperColumn">
        ALTER TABLE chatgpt_subtype
            ADD COLUMN isSuper TINYINT(1) COMMENT 'grok super标志'
    </update>

    <update id="addUserTokenIndex">
        CREATE INDEX index_userToken ON chatgpt_user (userToken(100));
    </update>
    <update id="addGptSessionColumn">
        alter table chatgpt_session
            add column user_id varchar(100) comment '用户id',
            add column exclusive_expire_time datetime(3) comment '独享到期时间';
    </update>

    <update id="addClaudeSessionColumn">
        ALTER TABLE claude_session
            ADD COLUMN `sort` BIGINT DEFAULT '0' COMMENT '排序',
            ADD COLUMN `count` BIGINT DEFAULT '0' COMMENT '统计',
            ADD COLUMN `password` LONGTEXT COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
            ADD COLUMN `remaining` varchar(10) COLLATE utf8mb4_unicode_ci COMMENT '剩余次数',
            ADD COLUMN `resetsAt` varchar(20) COLLATE utf8mb4_unicode_ci COMMENT '重置时间';
    </update>

    <update id="addSysNoticeColumn">
        ALTER TABLE chatgpt_sys_notice
            ADD COLUMN `type` VARCHAR(10) NOT NULL DEFAULT 'system' COMMENT '通知类型，站内公告：site，系统通知：system，登录公告：login, 前台脚本：script',
            ADD COLUMN `tenant_id` VARCHAR(20) NOT NULL DEFAULT '000000' COMMENT '租户编号';
    </update>

    <insert id="insertSysUser">
        INSERT INTO `t_sys_user` VALUES (1, 'admin', 'admin', '$2a$10$x2PFc5dCRorVzAiDuoR02eujjcetDb9Dyimpt5m8x3EU.XIphyZW2', '1', NULL, '1', '', '', '2025-05-05 21:47:11', '***********', NULL, NULL, '2025-05-05 19:05:29', NULL, NULL, '2025-05-05 19:05:29', 0, NULL, '000000');
    </insert>

    <insert id="insertSysRole">
        INSERT INTO `t_sys_role` VALUES (1, '超级管理员', 'SUPER_ADMIN', '1', '权限超级大，拥有所有权限', 2, 1, 'admin', 1, 'admin', '2024-03-09 10:21:23', NULL, 0, NULL);
        INSERT INTO `t_sys_role` VALUES (2, '普通管理员', 'ADMIN', '1', '只拥有部分管理权限', 2, 1, 'admin', 1, 'admin', '2024-03-09 10:21:23', '2024-03-09 10:21:25', 0, NULL);
        INSERT INTO `t_sys_role` VALUES (3, '租户管理员', 'TENANT', '1', '只拥有租户管理权限', 2, 1, 'admin', 1, 'admin', '2025-05-05 13:58:33', NULL, 0, NULL);
    </insert>

    <insert id="insertSysUserRole">
        INSERT INTO `t_sys_user_role` VALUES (1, 1, 1, 1, 'admin', 1, 'admin', '2024-03-09 10:37:52', '2024-03-09 10:38:04', 0, NULL);
    </insert>

    <insert id="insertSysResource">
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(1, 0, 'home/', '2', '1', '首页', 'home', '/home', 'layout.base$view.home', '{"iconType": "1","icon": "mdi:monitor-dashboard", "order": 0, "title": "home", "i18nKey": "route.home"}', 1, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(2, 0, '/user-center', '2', '1', '个人中心', 'user-center', '/user-center', 'layout.base$view.user-center', '{"iconType": "1","icon": "mdi:monitor-dashboard", "title": "个人中心", "i18nKey": "route.user-center", "hideInMenu": true}', 1, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-04-03 05:38:50', 0, '2024-03-09 08:49:34');
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(3, 0, '/manage', '1', '1', '系统管理', 'manage', '/manage', 'layout.base', '{"iconType": "1","icon": "carbon:cloud-service-management", "order": 9, "title": "系统管理", "i18nKey": "route.manage",}', 9, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(4, 3, '/manage/user', '2', '1', '用户管理', 'manage_user', '/manage/user', 'view.manage_user', '{"iconType": "1","icon": "ic:round-manage-accounts", "order": 1, "title": "用户管理", "i18nKey": "route.manage_user"}', 1, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(5, 3, '/manage/role', '2', '1', '角色管理', 'manage_role', '/manage/role', 'view.manage_role', '{"iconType": "1","icon": "carbon:user-role", "order": 2, "title": "角色管理", "i18nKey": "route.manage_role"}', 2, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(6, 3, '/manage/menu', '2', '1', '菜单管理', 'manage_menu', '/manage/menu', 'view.manage_menu', '{"iconType": "1","icon": "material-symbols:route", "order": 3, "title": "菜单管理", "i18nKey": "route.manage_menu"}', 3, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-03-09 08:49:30', 0, '2024-03-09 08:49:34');
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(7, 3, '/manage/user-detail/:id', '3', '1', '用户详情', 'manage_user-detail', '/manage/user-detail/:id', 'view.manage_user-detail', '{"title": "manage_user-detail", "i18nKey": "route.manage_user-detail", "hideInMenu": true}', 4, 1, 'admin', '2024-03-09 08:49:27', 1, 'admin', '2024-03-09 08:49:30', 1, '2024-03-09 08:49:34');
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(8, 3, '/manage/log', '2', '1', '操作日志', 'manage_log', '/manage/log', 'view.manage_log', '{"localIcon":"","keepAlive":false,"constant":false,"multiTab":false,"query":[],"icon":"mdi:foot-print","title":"操作日志","hideInMenu":false,"activeMenu":"","i18nKey":"route.manage_log","iconType":"1","href":"","order":4}', NULL, 1, 'admin', '2025-05-10 22:43:22', 1, 'admin', '2025-05-10 22:43:22', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(9, 0, '403', '4', '1', '403', '403', '/403', 'layout.blank$view.403', '{"constant": true, "hideInMenu": true, "title": "403", "i18nKey": "route.403"}', 1, 1, 'admin', '2024-03-26 08:49:27', 1, 'admin', '2024-03-26 08:49:30', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(10, 0, '404', '4', '1', '404', '404', '/404', 'layout.blank$view.404', '{"constant": true, "hideInMenu": true, "title": "404", "i18nKey": "route.404"}', 1, 1, 'admin', '2024-03-26 08:49:27', 1, 'admin', '2024-03-26 08:49:30', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(11, 0, '500', '4', '1', '500', '500', '/500', 'layout.blank$view.500', '{"constant": true, "hideInMenu": true, "title": "500", "i18nKey": "route.500"}', 1, 1, 'admin', '2024-03-26 08:49:27', 1, 'admin', '2024-03-26 08:49:30', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(12, 0, 'login', '4', '1', '登录', 'login', '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?', 'layout.blank$view.login', '{"constant": true, "hideInMenu": true, "title": "login", "i18nKey": "route.login"}', 1, 1, 'admin', '2024-03-26 08:49:27', 1, 'admin', '2024-03-26 08:49:30', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(13, 0, '/user', '1', '1', '用户管理', 'user', '/user', 'layout.base$view.user', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.user","iconType":"1","query":[],"icon":"ic:round-manage-accounts","title":"用户管理","order":1}', NULL, 1, 'admin', '2025-05-10 22:45:18', 1, 'admin', '2025-05-10 22:46:04', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(14, 0, '/account', '1', '1', '账号管理', 'account', '/account', 'layout.base', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.account","iconType":"1","query":[],"icon":"material-symbols:assignment-add","title":"账号管理","order":2}', NULL, 1, 'admin', '2025-05-10 22:48:52', 1, 'admin', '2025-05-10 22:49:07', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(15, 14, '/account/openai', '2', '1', 'openai', 'account_openai', '/account/openai', 'view.account_openai', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.account_openai","iconType":"1","query":[],"icon":"material-symbols:assignment-add","title":"openai","order":0}', NULL, 1, 'admin', '2025-05-10 22:49:51', 1, 'admin', '2025-05-10 22:50:36', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(16, 14, '/account/claude', '2', '1', 'claude', 'account_claude', '/account/claude', 'view.account_claude', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.account_claude","iconType":"1","query":[],"icon":"material-symbols:assignment-add","title":"claude","order":2}', NULL, 1, 'admin', '2025-05-10 22:51:28', 1, 'admin', '2025-05-10 22:52:26', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(17, 14, '/account/grok', '2', '1', 'grok', 'account_grok', '/account/grok', 'view.account_grok', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.account_grok","iconType":"1","query":[],"icon":"material-symbols:assignment-add","title":"grok","order":2}', NULL, 1, 'admin', '2025-05-10 22:51:53', 1, 'admin', '2025-05-10 22:52:26', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(18, 0, '/subtype', '1', '1', '套餐管理', 'subtype', '/subtype', 'layout.base$view.subtype', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype","iconType":"1","query":[],"icon":"material-symbols-light:shopping-cart-off","title":"套餐管理","order":3}', NULL, 1, 'admin', '2025-05-10 22:53:48', 1, 'admin', '2025-05-10 22:56:10', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(19, 0, '/marketing', '1', '1', '营销管理', 'marketing', '/marketing', 'layout.base', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.marketing","iconType":"1","query":[],"icon":"hugeicons:marketing","title":"营销管理","order":4}', NULL, 1, 'admin', '2025-05-10 22:55:55', 1, 'admin', '2025-05-10 22:56:10', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(20, 19, '/marketing/cdkey', '2', '1', '激活码', 'marketing_cdkey', '/marketing/cdkey', 'view.marketing_cdkey', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.marketing_cdkey","iconType":"1","query":[],"icon":"ic:baseline-key","title":"激活码","order":0}', NULL, 1, 'admin', '2025-05-10 22:58:31', 1, 'admin', '2025-05-10 23:02:11', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(21, 19, '/marketing/coupon', '2', '1', '优惠券', 'marketing_coupon', '/marketing/coupon', 'view.marketing_coupon', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.marketing_coupon","iconType":"1","query":[],"icon":"ri:coupon-2-fill","title":"优惠券","order":1}', NULL, 1, 'admin', '2025-05-10 22:59:20', 1, 'admin', '2025-05-10 23:02:11', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(22, 19, '/marketing/notice', '2', '1', '通知公告', 'marketing_notice', '/marketing/notice', 'view.marketing_notice', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.marketing_notice","iconType":"1","query":[],"icon":"fe:notice-off","title":"通知公告","order":2}', NULL, 1, 'admin', '2025-05-10 23:00:08', 1, 'admin', '2025-05-10 23:02:11', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(23, 19, '/marketing/withdrawals', '2', '1', '推广返现', 'marketing_withdrawals', '/marketing/withdrawals', 'view.marketing_withdrawals', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.marketing_withdrawals","iconType":"1","query":[],"icon":"ph:money-bold","title":"推广返现","order":3}', NULL, 1, 'admin', '2025-05-10 23:01:53', 1, 'admin', '2025-05-10 23:02:11', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(24, 0, '/draw', '1', '1', '绘图管理', 'draw', '/draw', 'layout.base', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.draw","iconType":"1","query":[],"icon":"material-symbols:photo-library-rounded","title":"绘图管理","order":5}', NULL, 1, 'admin', '2025-05-10 23:03:36', 1, 'admin', '2025-05-10 23:08:54', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(25, 24, '/draw/record', '2', '1', '绘图记录', 'draw_record', '/draw/record', 'view.draw_record', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.draw_record","iconType":"1","query":[],"icon":"material-symbols:draw-outline-sharp","title":"绘图记录","order":0}', NULL, 1, 'admin', '2025-05-10 23:05:13', 1, 'admin', '2025-05-10 23:08:54', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(26, 24, '/draw/quota-manage', '2', '1', '额度管理', 'draw_quota-manage', '/draw/quota-manage', 'view.draw_quota-manage', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.draw_quota-manage","iconType":"1","query":[],"icon":"material-symbols-light:table-rows-narrow-outline","title":"额度管理","order":1}', NULL, 1, 'admin', '2025-05-10 23:06:38', 1, 'admin', '2025-05-10 23:08:54', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(27, 24, '/draw/quota-change', '2', '1', '额度变更', 'draw_quota-change', '/draw/quota-change', 'view.draw_quota-change', '{"localIcon":"","keepAlive":false,"constant":false,"multiTab":false,"query":[],"icon":"pajamas:quota","title":"额度变更","hideInMenu":false,"activeMenu":"","i18nKey":"route.draw_quota-change","iconType":"1","href":"","order":2}', NULL, 1, 'admin', '2025-05-10 23:36:06', 1, 'admin', '2025-05-10 23:36:06', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(28, 0, '/order', '1', '1', '订单管理', 'order', '/order', 'layout.base', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.order","iconType":"1","query":[],"icon":"material-symbols:order-approve-sharp","title":"订单管理","order":6}', NULL, 1, 'admin', '2025-05-10 23:10:16', 1, 'admin', '2025-05-10 23:13:21', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(29, 28, '/order/info', '2', '1', '订单信息', 'order_info', '/order/info', 'view.order_info', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.order_info","iconType":"1","query":[],"icon":"material-symbols-light:data-info-alert","title":"订单信息","order":0}', NULL, 1, 'admin', '2025-05-10 23:11:32', 1, 'admin', '2025-05-10 23:13:21', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(30, 28, '/order/payconfig', '2', '1', '支付配置', 'order_payconfig', '/order/payconfig', 'view.order_payconfig', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.order_payconfig","iconType":"1","query":[],"icon":"lineicons:amazon-pay","title":"支付配置","order":1}', NULL, 1, 'admin', '2025-05-10 23:12:53', 1, 'admin', '2025-05-10 23:13:21', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(31, 0, '/tenant', '1', '1', '租户管理', 'tenant', '/tenant', 'layout.base$view.tenant', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.tenant","iconType":"1","query":[],"icon":"akar-icons:people-group","title":"租户管理","order":7}', NULL, 1, 'admin', '2025-05-10 23:16:23', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(32, 0, '/platform', '1', '1', '平台管理', 'platform', '/platform', 'layout.base', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.platform","iconType":"1","query":[],"icon":"mdi:monitor-dashboard","title":"平台管理","order":8}', NULL, 1, 'admin', '2025-05-10 23:17:21', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(33, 32, '/platform/sysconfig', '2', '1', '系统设置', 'platform_sysconfig', '/platform/sysconfig', 'view.platform_sysconfig', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.platform_sysconfig","iconType":"1","query":[],"icon":"icon-park-outline:database-config","title":"系统设置","order":0}', NULL, 1, 'admin', '2025-05-10 23:18:26', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(34, 32, '/platform/word', '2', '1', '敏感词', 'platform_word', '/platform/word', 'view.platform_word', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.platform_word","iconType":"1","query":[],"icon":"ri:file-word-2-fill","title":"敏感词","order":1}', NULL, 1, 'admin', '2025-05-10 23:19:22', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(35, 32, '/platform/riskcontrol', '2', '1', '风控记录', 'platform_riskcontrol', '/platform/riskcontrol', 'view.platform_riskcontrol', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.platform_riskcontrol","iconType":"1","query":[],"icon":"iconoir:warning-triangle-solid","title":"风控记录","order":2}', NULL, 1, 'admin', '2025-05-10 23:20:19', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(36, 32, '/platform/topic', '2', '1', 'openai对话', 'platform_topic', '/platform/topic', 'view.platform_topic', '{"localIcon":"","keepAlive":false,"constant":false,"multiTab":false,"query":[],"icon":"icon-park-solid:topic-discussion","title":"openai对话","hideInMenu":false,"activeMenu":"","i18nKey":"route.platform_topic","iconType":"1","href":"","order":3}', NULL, 1, 'admin', '2025-05-10 23:36:52', 1, 'admin', '2025-05-10 23:36:52', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(37, 32, '/platform/claude-topic', '2', '1', 'claude对话', 'platform_claude-topic', '/platform/claude-topic', 'view.platform_claude-topic', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.platform_claude-topic","iconType":"1","query":[],"icon":"icon-park-solid:topic-discussion","title":"claude对话","order":4}', NULL, 1, 'admin', '2025-05-10 23:22:21', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(38, 32, '/platform/grok-topic', '2', '1', 'grok对话', 'platform_grok-topic', '/platform/grok-topic', 'view.platform_grok-topic', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.platform_grok-topic","iconType":"1","query":[],"icon":"icon-park-solid:topic-discussion","title":"grok对话","order":5}', NULL, 1, 'admin', '2025-05-10 23:23:13', 1, 'admin', '2025-05-10 23:24:23', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(39, 13, '/user/page', '3', '1', '用户查询', 'user/page', '/user/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.user/page","iconType":"1","query":[],"icon":"","title":"用户查询","order":0}', NULL, 1, 'admin', '2025-05-16 11:47:08', 1, 'admin', '2025-05-16 11:47:08', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(40, 13, '/user/create', '3', '1', '用户新增', 'user/create', '/user/create', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.user/create","iconType":"1","query":[],"icon":"","title":"用户新增","order":0}', NULL, 1, 'admin', '2025-05-16 11:47:48', 1, 'admin', '2025-05-16 11:47:48', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(41, 13, '/user/update', '3', '1', '用户编辑', 'user/update', '/user/update', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.user/update","iconType":"1","query":[],"icon":"","title":"用户编辑","order":0}', NULL, 1, 'admin', '2025-05-16 11:48:04', 1, 'admin', '2025-05-16 11:48:04', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(42, 13, '/user/batchCreate', '3', '1', '用户批量创建', 'user/batchCreate', '/user/batchCreate', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.user/batchCreate","iconType":"1","query":[],"icon":"","title":"用户批量创建","order":0}', NULL, 1, 'admin', '2025-05-16 11:48:23', 1, 'admin', '2025-05-16 11:48:23', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(43, 13, '/user/delete', '3', '1', '用户删除', 'user/delete', '/user/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.user/delete","iconType":"1","query":[],"icon":"","title":"用户删除","order":0}', NULL, 1, 'admin', '2025-05-16 11:48:45', 1, 'admin', '2025-05-16 11:48:45', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(44, 13, '/subtype/list', '3', '1', '用户套餐列表', 'subtype/list', '/subtype/list', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype/list","iconType":"1","query":[],"icon":"","title":"用户套餐列表","order":0}', NULL, 1, 'admin', '2025-05-16 11:49:07', 1, 'admin', '2025-05-16 11:49:07', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(45, 31, '/tenant/add', '3', '1', '租户增加', 'tenant/add', '/tenant/add', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.tenant/add","iconType":"1","query":[],"icon":"","title":"租户增加","order":0}', NULL, 1, 'admin', '2025-05-16 11:51:37', 1, 'admin', '2025-05-16 11:51:37', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(46, 31, '/tenant/update', '3', '1', '租户编辑', 'tenant/update', '/tenant/update', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.tenant/update","iconType":"1","query":[],"icon":"","title":"租户编辑","order":0}', NULL, 1, 'admin', '2025-05-16 11:51:55', 1, 'admin', '2025-05-16 11:51:55', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(47, 31, '/tenant/getTenantPage', '3', '1', '租户查询', 'tenant/getTenantPage', '/tenant/getTenantPage', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.tenant/getTenantPage","iconType":"1","query":[],"icon":"","title":"租户查询","order":0}', NULL, 1, 'admin', '2025-05-16 11:52:16', 1, 'admin', '2025-05-16 11:52:16', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(48, 31, '/tenant/delete', '3', '1', '租户删除', 'tenant/delete', '/tenant/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.tenant/delete","iconType":"1","query":[],"icon":"","title":"租户删除","order":0}', NULL, 1, 'admin', '2025-05-16 11:52:32', 1, 'admin', '2025-05-16 11:52:32', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(49, 31, '/tenant/deleteByIds', '3', '1', '租户批量删除', 'tenant/deleteByIds', '/tenant/deleteByIds', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.tenant/deleteByIds","iconType":"1","query":[],"icon":"","title":"租户批量删除","order":0}', NULL, 1, 'admin', '2025-05-16 11:52:54', 1, 'admin', '2025-05-16 11:52:54', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(50, 18, '/subtype/page', '3', '1', '套餐查询', 'subtype/page', '/subtype/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype/page","iconType":"1","query":[],"icon":"","title":"套餐查询","order":0}', NULL, 1, 'admin', '2025-05-16 11:54:36', 1, 'admin', '2025-05-16 11:54:36', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(51, 18, '/subtype/list', '3', '1', '套餐明细列表查询', 'subtype/list', '/subtype/list', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype/list","iconType":"1","query":[],"icon":"","title":"套餐明细列表查询","order":0}', NULL, 1, 'admin', '2025-05-16 11:54:57', 1, 'admin', '2025-05-16 11:54:57', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(52, 18, '/subtype/update', '3', '1', '套餐更新', 'subtype/update', '/subtype/update', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype/update","iconType":"1","query":[],"icon":"","title":"套餐更新","order":0}', NULL, 1, 'admin', '2025-05-16 11:55:14', 1, 'admin', '2025-05-16 11:55:14', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(53, 18, '/subtype/add', '3', '1', '套餐新增', 'subtype/add', '/subtype/add', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype/add","iconType":"1","query":[],"icon":"","title":"套餐新增","order":0}', NULL, 1, 'admin', '2025-05-16 11:55:27', 1, 'admin', '2025-05-16 11:55:27', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(54, 18, '/subtype/delete', '2', '1', '套餐删除', 'subtype/delete', '/subtype/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.subtype/delete","iconType":"1","query":[],"icon":"","title":"套餐删除","order":0}', NULL, 1, 'admin', '2025-05-16 11:55:45', 1, 'admin', '2025-05-16 11:55:45', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(55, 20, '/codes/page', '3', '1', '激活码查询', 'codes/page', '/codes/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.codes/page","iconType":"1","query":[],"icon":"","title":"激活码查询","order":0}', NULL, 1, 'admin', '2025-05-16 11:56:47', 1, 'admin', '2025-05-16 11:56:47', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(57, 20, '/codes/generate', '3', '1', '激活码批量新增', 'codes/generate', '/codes/generate', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.codes/generate","iconType":"1","query":[],"icon":"","title":"激活码批量新增","order":0}', NULL, 1, 'admin', '2025-05-16 11:57:30', 1, 'admin', '2025-05-16 11:57:30', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(58, 20, '/codes/delete', '3', '1', '激活码删除', 'codes/delete', '/codes/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.codes/delete","iconType":"1","query":[],"icon":"","title":"激活码删除","order":0}', NULL, 1, 'admin', '2025-05-16 11:57:42', 1, 'admin', '2025-05-16 11:57:42', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(59, 20, '/codes/export-key', '3', '1', '激活码导出', 'codes/export-key', '/codes/export-key', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.codes/export-key","iconType":"1","query":[],"icon":"","title":"激活码导出","order":0}', NULL, 1, 'admin', '2025-05-16 11:57:55', 1, 'admin', '2025-05-16 11:57:55', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(60, 20, '/codes/recycle', '3', '1', '激活码回收', 'codes/recycle', '/codes/recycle', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.codes/recycle","iconType":"1","query":[],"icon":"","title":"激活码回收","order":0}', NULL, 1, 'admin', '2025-05-16 11:58:09', 1, 'admin', '2025-05-16 11:58:09', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(61, 21, '/coupon/page', '3', '1', '优惠卷查询', 'coupon/page', '/coupon/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.coupon/page","iconType":"1","query":[],"icon":"","title":"优惠卷查询","order":0}', NULL, 1, 'admin', '2025-05-16 11:59:25', 1, 'admin', '2025-05-16 11:59:25', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(62, 21, '/coupon/create', '3', '1', '优惠卷新增', 'coupon/create', '/coupon/create', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.coupon/create","iconType":"1","query":[],"icon":"","title":"优惠卷新增","order":0}', NULL, 1, 'admin', '2025-05-16 11:59:42', 1, 'admin', '2025-05-16 11:59:42', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(63, 21, '/coupon/update', '3', '1', '优惠卷更新', 'coupon/update', '/coupon/update', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.coupon/update","iconType":"1","query":[],"icon":"","title":"优惠卷更新","order":0}', NULL, 1, 'admin', '2025-05-16 11:59:53', 1, 'admin', '2025-05-16 11:59:53', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(64, 21, '/coupon/delete', '3', '1', '优惠卷删除', 'coupon/delete', '/coupon/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.coupon/delete","iconType":"1","query":[],"icon":"","title":"优惠卷删除","order":0}', NULL, 1, 'admin', '2025-05-16 12:00:06', 1, 'admin', '2025-05-16 12:00:06', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(65, 22, '/notice/page', '3', '1', '通知公告查询', 'notice/page', '/notice/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.notice/page","iconType":"1","query":[],"icon":"","title":"通知公告查询","order":0}', NULL, 1, 'admin', '2025-05-16 14:06:42', 1, 'admin', '2025-05-16 14:06:42', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(66, 22, '/notice/create', '3', '1', '通知公告新增', 'notice/create', '/notice/create', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.notice/create","iconType":"1","query":[],"icon":"","title":"通知公告新增","order":0}', NULL, 1, 'admin', '2025-05-16 14:06:57', 1, 'admin', '2025-05-16 14:06:57', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(67, 22, '/notice/update', '3', '1', '通告公告编辑', 'notice/update', '/notice/update', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.notice/update","iconType":"1","query":[],"icon":"","title":"通告公告编辑","order":0}', NULL, 1, 'admin', '2025-05-16 14:07:15', 1, 'admin', '2025-05-16 14:07:15', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(69, 22, '/notice/delete', '3', '1', '通知公告删除', 'notice/delete', '/notice/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.notice/delete","iconType":"1","query":[],"icon":"","title":"通知公告删除","order":0}', NULL, 1, 'admin', '2025-05-16 14:08:12', 1, 'admin', '2025-05-16 14:08:12', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(70, 23, '/withdrawals/getWithdrawPage', '3', '1', '推广返现查询', 'withdrawals/getWithdrawPage', '/withdrawals/getWithdrawPage', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.withdrawals/getWithdrawPage","iconType":"1","query":[],"icon":"","title":"推广返现查询","order":0}', NULL, 1, 'admin', '2025-05-16 14:08:30', 1, 'admin', '2025-05-16 14:08:30', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(71, 23, '/withdrawals/delete', '3', '1', '推广提现删除', 'withdrawals/delete', '/withdrawals/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.withdrawals/delete","iconType":"1","query":[],"icon":"","title":"推广提现删除","order":0}', NULL, 1, 'admin', '2025-05-16 14:09:17', 1, 'admin', '2025-05-16 14:09:17', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(72, 23, '/withdrawals/getInviteDetails', '3', '1', '推广返现详情', 'withdrawals/getInviteDetails', '/withdrawals/getInviteDetails', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.withdrawals/getInviteDetails","iconType":"1","query":[],"icon":"","title":"推广返现详情","order":0}', NULL, 1, 'admin', '2025-05-16 14:09:42', 1, 'admin', '2025-05-16 14:09:42', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(73, 29, '/payLogs/page', '3', '1', '订单信息查询', 'payLogs/page', '/payLogs/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.payLogs/page","iconType":"1","query":[],"icon":"","title":"订单信息查询","order":0}', NULL, 1, 'admin', '2025-05-16 14:11:00', 1, 'admin', '2025-05-16 14:11:00', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(74, 29, '/payLogs/delete', '3', '1', '订单信息删除', 'payLogs/delete', '/payLogs/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.payLogs/delete","iconType":"1","query":[],"icon":"","title":"订单信息删除","order":0}', NULL, 1, 'admin', '2025-05-16 14:11:18', 1, 'admin', '2025-05-16 14:11:18', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(75, 29, '/payLogs/change-status', '2', '1', '订单信息手工处理', 'payLogs/change-status', '/payLogs/change-status', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.payLogs/change-status","iconType":"1","query":[],"icon":"","title":"订单信息手工处理","order":0}', NULL, 1, 'admin', '2025-05-16 14:11:35', 1, 'admin', '2025-05-16 14:11:35', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(78, 30, '/pay/page', '3', '1', '支付配置查询', 'pay/page', '/pay/page', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.pay/page","iconType":"1","query":[],"icon":"","title":"支付配置查询","order":0}', NULL, 1, 'admin', '2025-05-16 14:12:39', 1, 'admin', '2025-05-16 14:12:39', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(79, 30, '/pay/add', '3', '1', '支付配置新增', 'pay/add', '/pay/add', '', '{"localIcon":"","keepAlive":false,"constant":false,"multiTab":false,"query":[],"icon":"","title":"支付配置新增","hideInMenu":false,"activeMenu":"","i18nKey":"route.pay/add","iconType":"1","href":"","order":0}', NULL, 1, 'admin', '2025-05-16 14:13:33', 1, 'admin', '2025-05-16 14:13:33', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(80, 30, '/pay/update', '3', '1', '支付配置编辑', 'pay/update', '/pay/update', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.pay/update","iconType":"1","query":[],"icon":"","title":"支付配置编辑","order":0}', NULL, 1, 'admin', '2025-05-16 14:13:13', 1, 'admin', '2025-05-16 14:13:13', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(81, 30, '/pay/delete', '3', '1', '支付配置删除', 'pay/delete', '/pay/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.pay/delete","iconType":"1","query":[],"icon":"","title":"支付配置删除","order":0}', NULL, 1, 'admin', '2025-05-16 14:13:55', 1, 'admin', '2025-05-16 14:13:55', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(82, 0, '/systemManage/update', '3', '1', '修改个人信息', 'pay/delete', '/pay/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.pay/delete","iconType":"1","query":[],"icon":"","title":"支付配置删除","order":0}', NULL, 1, 'admin', '2025-05-16 14:13:55', 1, 'admin', '2025-05-16 14:13:55', 0, NULL);
        INSERT INTO t_sys_resource (id, parent_id, ui_path, menu_type, status, menu_name, route_name, route_path, component, meta, weight, create_id, create_by, create_time, update_id, update_by, update_time, is_deleted, delete_time) VALUES(83, 0, '/auth/updatePassword', '3', '1', '修改用户密码', 'pay/delete', '/pay/delete', '', '{"hideInMenu":false,"keepAlive":false,"constant":false,"multiTab":false,"i18nKey":"route.pay/delete","iconType":"1","query":[],"icon":"","title":"支付配置删除","order":0}', NULL, 1, 'admin', '2025-05-16 14:13:55', 1, 'admin', '2025-05-16 14:13:55', 0, NULL);
  </insert>

    <insert id="insertSysRoleResource">
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (724, 2, 1, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (725, 2, 2, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (726, 2, 3, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (727, 2, 4, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (728, 2, 5, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (729, 2, 6, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (730, 2, 8, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (731, 2, 13, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (732, 2, 14, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (733, 2, 15, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (734, 2, 16, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (735, 2, 17, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (736, 2, 18, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (737, 2, 19, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (738, 2, 20, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (739, 2, 21, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (740, 2, 22, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (741, 2, 23, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (742, 2, 24, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (743, 2, 25, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (744, 2, 26, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (745, 2, 27, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (746, 2, 28, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (747, 2, 29, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (748, 2, 30, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (749, 2, 31, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (750, 2, 32, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (751, 2, 33, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (752, 2, 34, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (753, 2, 35, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (754, 2, 36, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (755, 2, 37, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (756, 2, 38, 1, 'admin', 1, 'admin', '2025-05-10 23:27:14', '2025-05-10 23:27:14', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (757, 1, 1, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (758, 1, 2, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (759, 1, 3, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (760, 1, 4, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (761, 1, 5, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (762, 1, 6, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (763, 1, 8, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (764, 1, 13, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (765, 1, 14, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (766, 1, 15, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (767, 1, 16, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (768, 1, 17, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (769, 1, 18, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (770, 1, 19, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (771, 1, 20, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (772, 1, 21, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (773, 1, 22, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (774, 1, 23, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (775, 1, 24, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (776, 1, 25, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (777, 1, 26, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (778, 1, 27, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (779, 1, 28, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (780, 1, 29, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (781, 1, 30, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (782, 1, 31, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (783, 1, 32, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (784, 1, 33, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (785, 1, 34, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (786, 1, 35, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (787, 1, 36, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (788, 1, 37, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (789, 1, 38, 1, 'admin', 1, 'admin', '2025-05-10 23:27:22', '2025-05-10 23:27:22', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (790, 3, 1, 1, 'admin', 1, 'admin', '2025-05-11 10:09:17', '2025-05-11 10:09:17', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (791, 3, 2, 1, 'admin', 1, 'admin', '2025-05-11 10:09:17', '2025-05-11 10:09:17', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (792, 3, 13, 1, 'admin', 1, 'admin', '2025-05-11 10:09:17', '2025-05-11 10:09:17', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (793, 3, 19, 1, 'admin', 1, 'admin', '2025-05-11 10:09:17', '2025-05-11 10:09:17', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (794, 3, 22, 1, 'admin', 1, 'admin', '2025-05-11 10:09:17', '2025-05-11 10:09:17', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (795, 3, 65, 1, 'admin', 1, 'admin', '2025-05-11 10:09:17', '2025-05-11 10:09:17', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (796, 3, 66, 1, 'admin', 1, 'admin', '2025-05-11 10:09:17', '2025-05-11 10:09:17', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (797, 3, 67, 1, 'admin', 1, 'admin', '2025-05-11 10:09:17', '2025-05-11 10:09:17', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (798, 3, 69, 1, 'admin', 1, 'admin', '2025-05-11 10:09:17', '2025-05-11 10:09:17', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (800, 3, 50, 1, 'admin', 1, 'admin', '2025-05-11 10:09:17', '2025-05-11 10:09:17', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (801, 3, 51, 1, 'admin', 1, 'admin', '2025-05-11 10:09:17', '2025-05-11 10:09:17', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (802, 3, 52, 1, 'admin', 1, 'admin', '2025-05-11 10:09:17', '2025-05-11 10:09:17', 0, null);
        INSERT INTO t_sys_role_resource (id, role_id, resource_id, create_id, create_by, update_id, update_by, create_time, update_time, is_deleted, delete_time) VALUES (803, 3, 53, 1, 'admin', 1, 'admin', '2025-05-11 10:09:17', '2025-05-11 10:09:17', 0, null);
    </insert>
    <update id="addConfigKeyUnique">
        ALTER TABLE chatgpt_config ADD UNIQUE INDEX idx_config_key (`key`(255));
    </update>
</mapper>

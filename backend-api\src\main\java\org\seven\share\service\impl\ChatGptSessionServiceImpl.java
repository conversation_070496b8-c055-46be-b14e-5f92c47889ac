package org.seven.share.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.util.CarIdUtils;
import org.seven.share.common.util.IpUtils;
import org.seven.share.mapper.ChatGptSessionMapper;
import org.seven.share.common.pojo.dto.CarStatus;
import org.seven.share.common.pojo.dto.ChatGptSessionDto;
import org.seven.share.common.pojo.entity.ChatGptSessionEntity;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.common.pojo.vo.CarInfoVo;
import org.seven.share.common.pojo.vo.ChatGptSessionVo;
import org.seven.share.service.ChatGptConfigService;
import org.seven.share.service.ChatGptSessionService;
import org.seven.share.service.ChatGptUserService;
import org.seven.share.service.xyhelper.ApiService;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.seven.share.common.util.ConstantUtil.*;

/**
 * @ClassName: ChatGptSessionServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/14
 */
@Service
@Slf4j
public class ChatGptSessionServiceImpl extends ServiceImpl<ChatGptSessionMapper, ChatGptSessionEntity> implements ChatGptSessionService {
    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ApiService apiService;

    @Resource
    private HttpServletRequest request;

    @Resource
    private ChatGptUserService chatGptUserService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private ChatGptSessionMapper chatGptSessionMapper;

    @Override
    public void saveAccessTokenBatch(String accounts, int optType) {
        if (StrUtil.isEmpty(accounts)) {
            log.error("账号信息为空:{}",accounts);
            throw new CustomException("账号信息为空");
        }
        if (BATCH_SAVE_ACCOUNTS == optType) {
            batchSaveAccounts(accounts);
        }
    }


    private void batchSaveAccounts(String accounts) {
        long startTime = System.currentTimeMillis();
        // 解析accounts为list集合对象
        List<ChatGptSessionEntity> sessionEntityList = parseAccounts(accounts);
        // 批量插入
        if (!CollectionUtils.isEmpty(sessionEntityList)) {
            for (ChatGptSessionEntity sessionEntity : sessionEntityList) {
                apiService.sendSaveOrUpdateRequest(sessionEntity, "add", "gpt");
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("批量插入账号信息完成，耗时：{}，插入{}条数据", (endTime - startTime) + "ms", sessionEntityList.size());
    }

    /**
     * 将account根据换行符号处理成数组，再根据stream流创建sessionEntity对象List集合
     * @param accounts 账号
     * @return 实体集合
     */
    private List<ChatGptSessionEntity> parseAccounts(String accounts) {
        return Arrays.stream(accounts.replace("\\n", "\n").split("\\n"))
                .map(this::createSessionEntity)
                .collect(Collectors.toList());
    }

    /**
     * 提取每一行数据中的账号密码，创建sessionEntity对象
     * @param line 每行数据
     * @return 实体
     */
    private ChatGptSessionEntity createSessionEntity(String line) {
        String[] parts = line.split(",");
        if (parts.length != 2) {
            throw new CustomException("格式错误：每行一个账号密码，账号密码之间用英文逗号隔开");
        }
        ChatGptSessionEntity sessionEntity = new ChatGptSessionEntity();
        sessionEntity.setEmail(parts[0].trim().replace("\"", ""));
        sessionEntity.setPassword(parts[1].trim().replace("\"", ""));
        sessionEntity.setCarID(CarIdUtils.generatorCarID());
        return sessionEntity;
    }

    @Override
    public void updateAccessToken(ChatGptSessionEntity chatGptSession) {
        apiService.sendSaveOrUpdateRequest(chatGptSession, "update", "gpt");
    }

    @Override
    public void removeCarInfoBatch(List<String> ids) {
        apiService.sendDeleteRequest(ids, "gpt");
    }

    @Override
    public void insertAccessToken(ChatGptSessionEntity chatGptSession) {
        apiService.sendSaveOrUpdateRequest(chatGptSession, "add", "gpt");
    }


    @Override
    public List<ChatGptSessionVo> listNoBindCarInfo() {
        List<ChatGptSessionEntity> list = this.lambdaQuery()
                .eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS)
//                .and("create".equals(type), wrapper -> wrapper.lt(ChatGptSessionEntity::getExclusiveExpireTime, LocalDateTime.now()).or().isNull(ChatGptSessionEntity::getExclusiveExpireTime))
                .isNull(ChatGptSessionEntity::getDeletedAt)
                .orderByAsc(ChatGptSessionEntity::getIsPlus)
                .list();
        return list.stream().map(e -> {
            ChatGptSessionVo vo = new ChatGptSessionVo();
            vo.setLabel("车号：" + e.getCarID() + "[" + (getPlanType(e.getIsPlus())) + "]");
            vo.setId(e.getId());
            return vo;
        }).collect(Collectors.toList());
    }

    private String getPlanType(Integer isPlus) {
        return switch (isPlus) {
            case 0 -> "Free账号";
            case 1 -> "Plus账号";
            case 2 -> "Team账号";
            case 3 -> "Pro账号";
            default -> "未知";
        };
    }

    @Override
    public void exportGptSession(HttpServletResponse response) {
        try {
            // 设置响应头，指定文件名和类型
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=session.txt");

            List<ChatGptSessionEntity> list = this.lambdaQuery().list();
            StringBuilder csvBuilder = new StringBuilder();
            list.forEach(e -> csvBuilder.append(e.getEmail())
                    .append("----")
                    .append(e.getPassword())
                    .append("----")
                    .append(e.getOfficialSession())
                    .append("\n"));
            response.getWriter().write(csvBuilder.toString());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, String> getIdleCar(String username) {
        log.info("自动选车中，用户名：{}", username);
        // 查询用户权益
        ChatGptUserEntity user = chatGptUserService.getUserInfoByUsername(username);
        if (ObjectUtil.isNotEmpty(user)) {
            // 判断用户的计划类型和节点类型
            Integer planType;
            String nodeType;
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expireTime = user.getExpireTime();
            LocalDateTime plusExpireTime = user.getPlusExpireTime();
            if (ObjectUtil.isNotEmpty(plusExpireTime) && plusExpireTime.isAfter(now)) {
                planType = PLAN_TYPE_PLUS;
                nodeType = "plus";
            } else if (ObjectUtil.isNotEmpty(expireTime) && expireTime.isAfter(now)) {
                planType = PLAN_TYPE_FREE;
                nodeType = "4o";
            } else {
                planType = PLAN_TYPE_FREE;
                nodeType = "free";
            }
            // 根据权限过滤出账号信息
            String carID = fetchUserLessCarList(planType);
            if (carID != null) {
                return Map.of("carID",carID, "nodeType",nodeType, "planType", String.valueOf(planType));
            } else {
                throw new CustomException("暂时没有空闲的节点，请联系管理员处理");
            }
        }
        log.warn("根据用户名查询信息失败,用户名为空");
        return null;
    }

    /**
     * 根据账号类型获取次数最少得车
     * @param planType
     * @return
     */
    private String fetchUserLessCarList(Integer planType) {
        List<ChatGptSessionEntity> list = this.list(new LambdaQueryWrapper<ChatGptSessionEntity>()
                .eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS)
                .isNull(ChatGptSessionEntity::getDeletedAt)
                .and(w -> w.lt(ChatGptSessionEntity::getExclusiveExpireTime, LocalDateTime.now())  // 时间已过期
                        .or().isNull(ChatGptSessionEntity::getExclusiveExpireTime)));
        List<ChatGptSessionEntity> filterList = list.stream()
                .filter(e -> planType.equals(e.getIsPlus()))
                .toList();
        String baseUrl = IpUtils.getRefererDomain(request);
        List<CarInfoVo> minCarInfo = filterList.parallelStream()
                .map(e -> {
                    // 获取车的状态
                    String statusUrl = String.format("%s/status?carid=%s", baseUrl, e.getCarID());
                    CarInfoVo carInfoVo = new CarInfoVo();
                    carInfoVo.setCarID(e.getCarID());
                    CarStatus carStatus = restTemplate.getForObject(statusUrl, CarStatus.class);
                    carInfoVo.setCarStatus(carStatus);
                    return carInfoVo;
                })
                // 过滤掉没有count值的对象
                .filter(e -> e.getCarStatus() != null && Objects.equals(e.getCarStatus().getClears_in(), 0)).toList();
        // 依次查找次数最小的车
        List<Integer> thresholds = Arrays.asList(5, 10, 20, 40, 60, 80, 100, 120, 9999);
        List<CarInfoVo> filteredList = filterCarsByCount(minCarInfo, thresholds);

        Optional<CarInfoVo> randomCarInfo = filteredList.isEmpty()
                ? Optional.empty()
                : Optional.of(filteredList.get(new Random().nextInt(filteredList.size())));
        if (randomCarInfo.isPresent()) {
            CarInfoVo carInfoVo = randomCarInfo.get();
            String carID = carInfoVo.getCarID();
            log.info("空闲的车号为：{}",carID);
            return carID;
        }
        return null;
    }

    private List<CarInfoVo> filterCarsByCount(List<CarInfoVo> minCarInfo, List<Integer> thresholds) {
        for (int threshold : thresholds) {
            List<CarInfoVo> filteredList = minCarInfo.stream()
                    .filter(carInfoVo -> carInfoVo.getCarStatus().getCount() <= threshold)
                    .toList();
            log.info("小于等于{}次的节点有：{}", threshold, filteredList.size());
            if (!CollectionUtils.isEmpty(filteredList)) {
                return filteredList;
            }
        }
        return Collections.emptyList();
    }

    @Override
    public Map<String, String> getIdleCarByUserExpire(String userId) {
        ChatGptUserEntity user = chatGptUserService.getByIdWithoutTenant(Long.parseLong(userId));
        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("获取用户信息失败，请重新登录");
        }
        // 用户权益类型
        boolean isPlus = false;
        String nodeType = null;
        LocalDateTime plusExpireTime = user.getPlusExpireTime();
        LocalDateTime expireTime = user.getExpireTime();
        // 先判断plus过期了吗，没有则返回plus的车号。
        if (ObjectUtil.isNotEmpty(plusExpireTime) && plusExpireTime.isAfter(LocalDateTime.now())) {
            isPlus = true;
            nodeType = "plus";
            // plus过期或者plus时间为空则判断普通过期时间
        } else if (ObjectUtil.isNotEmpty(expireTime)) {
            if (expireTime.isAfter(LocalDateTime.now())) {
                nodeType = "4o";
            } else {
                // 如果用户的普通时间也过期了，则用免费节点的账号进行使用。
                nodeType = "free";
            }
        }
        // 随机获取一个
        LambdaQueryWrapper<ChatGptSessionEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS);
        queryWrapper.and(w -> w.lt(ChatGptSessionEntity::getExclusiveExpireTime, LocalDateTime.now())  // 时间已过期
                .or().isNull(ChatGptSessionEntity::getExclusiveExpireTime));
        if (isPlus) {
            queryWrapper.in(ChatGptSessionEntity::getIsPlus, PLAN_TYPE_PLUS, PLAN_TYPE_TEAM, PLAN_TYPE_PRO);
        } else {
            queryWrapper.eq(ChatGptSessionEntity::getIsPlus, PLAN_TYPE_FREE);
        }
        queryWrapper.isNull(ChatGptSessionEntity::getDeletedAt);
        queryWrapper.eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS);
        queryWrapper.last("ORDER BY RAND() LIMIT 1");
        ChatGptSessionEntity session = this.getOne(queryWrapper);
        if (ObjectUtil.isNotEmpty(session)) {
            String randomCar = session.getCarID();
            Map<String, String> map = new HashMap<>();
            map.put("carId", randomCar);
            map.put("nodeType", nodeType);
            return map;
        }else if(isPlus && ObjectUtil.isEmpty(session)) {
            ChatGptSessionEntity one = this.lambdaQuery().
                    eq(ChatGptSessionEntity::getIsPlus, PLAN_TYPE_FREE)
                    .isNull(ChatGptSessionEntity::getDeletedAt)
                    .eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS)
                    .last("ORDER BY RAND() LIMIT 1").one();
            if (ObjectUtil.isNotEmpty(one)) {
                return  Map.of("carId", one.getCarID(), "nodeType", "free");
            }
        }
        return null;
    }

    @Override
    public List<CarInfoVo> fetchAllCarList() {
        List<ChatGptSessionEntity> list = this.lambdaQuery()
                .eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS)
                .isNull(ChatGptSessionEntity::getDeletedAt)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        String baseUrl = IpUtils.getRefererDomain(request);
        return list.parallelStream().map(e -> {
            CarInfoVo carInfoVo = new CarInfoVo();
            carInfoVo.setCarID(e.getCarID());
            carInfoVo.setIsPlus(e.getIsPlus());
            carInfoVo.setCount(e.getCount());
            String url = String.format("%s/endpoint?carid=%s", baseUrl, e.getCarID());
            Map map = restTemplate.getForObject(url, Map.class);
            if (ObjectUtil.isNotEmpty(map)) {
                String message = (String) map.getOrDefault("message", "翻车|获取状态失败");
                Pattern pattern = Pattern.compile("\\s*([^｜|]+)\\s*[｜|]\\s*(.+)\\s*");
                Matcher matcher = pattern.matcher(message);
                if (matcher.find()) {
                    String leftPart = matcher.group(1);
                    String rightPart = matcher.group(2);
                    if (!"翻车".equals(leftPart)) {
                        if ("TEAM停运".equals(leftPart)) {
                            carInfoVo.setStatus(leftPart.replace("TEAM", ""));
                            carInfoVo.setDetail(rightPart.replace("将于", ""));
                        } else {
                            carInfoVo.setStatus(leftPart.replace("PLUS", ""));
                            carInfoVo.setDetail(rightPart.replace("将于", ""));
                        }
                    }
                }
            }
            return carInfoVo;
        }).sorted(Comparator.comparing(CarInfoVo::getIsPlus)).toList();
    }

    @Override
    public String getOaiLoginToken(String carId, String userToken) throws JsonProcessingException {
        ChatGptSessionEntity session = this.lambdaQuery().eq(ChatGptSessionEntity::getCarID, carId).one();

        ChatGptUserEntity user = checkUserAndCarValid(userToken, carId);
        String limit;
        if (ObjectUtil.isNotEmpty(user)) {
            // 检查车与用户权限是否一致
            checkUserAndSessionRelation(carId, session, user);
            limit = String.valueOf(Objects.requireNonNull(user).getLimit());
        } else {
            // 说明是游客
            String visitorLimit = chatGptConfigService.getValueByKey("visitorLimit");
            if (StrUtil.isEmpty(visitorLimit) || "0".equals(visitorLimit)) {
                limit = "-1";
            } else {
                limit = visitorLimit;
            }
        }
        // 一致则用session获取登录地址
        String officialSession = session.getOfficialSession();
        if (StrUtil.isEmpty(officialSession)) {
            log.error("根据车号：{}，查询到session为空",carId);
            throw new CustomException("登录token获取失败");
        }
        JsonNode jsonNode = objectMapper.readTree(officialSession);
        if (jsonNode.has("accessToken") && StrUtil.isNotEmpty( jsonNode.get("accessToken").asText())) {
            String oaiUrl = chatGptConfigService.getValueByKey("oaiUrl");
            if (StrUtil.isEmpty(oaiUrl)) {
                throw new CustomException("管理员还未配置备用站地址");
            }

            String accessToken = jsonNode.get("accessToken").asText();

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);  // 设置表单提交

            // 设置表单数据
            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("unique_name", userToken);
            formData.add("access_token", accessToken);
            formData.add("expires_in", "86400"); // 为0取Access Token过期时间,一天
            formData.add("site_limit", "");
            formData.add("gpt4_limit", limit);
            formData.add("gpt4o_limit", limit);
            formData.add("gpt4o_mini_limit", "-1"); // 为0无法使用，负数不限制
            formData.add("o1_limit", "50");
            formData.add("o1_mini_limit", "50");
            formData.add("show_conversations", "false"); // 会话无需隔离
            formData.add("temporary_chat", "true"); // 强制临时聊天
            formData.add("reset_limit", "false"); // 重置已用次数

            // 封装 HttpEntity
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);

            // 发送 POST 请求
            ResponseEntity<String> response = restTemplate.exchange(OAI_CHAT_TOKEN, HttpMethod.POST, requestEntity, String.class);

            log.info("获取oai登录share-token请求结果为：{}", response);
            if (response.getStatusCode().is2xxSuccessful()) {
                String body = response.getBody();
                JsonNode dataNode = objectMapper.readTree(body);
                if (dataNode.has("token_key") && StrUtil.isNotEmpty(dataNode.get("token_key").asText())){
                    String tokenKey = dataNode.get("token_key").asText();
                    log.info("share-token:{}", tokenKey);
                    return oaiUrl + "/auth/login_share?token=" + tokenKey;
                }
            } else {
                log.error("获取oai登录失败,失败原因：{}", response);
            }
        }
        return null;
    }

    @Override
    public void updateGptSessionStatus(Long id, Integer status) {
        this.lambdaUpdate().set(ChatGptSessionEntity::getStatus, status).eq(ChatGptSessionEntity::getId, id).update();
    }

    @Override
    public void updateGptStatus(String carId) {
        log.info("账号报错401，开始禁用车号为：{}的账号状态", carId);
        ChatGptSessionEntity one = this.lambdaQuery()
                .eq(ChatGptSessionEntity::getCarID, carId)
                .eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS)
                .isNull(ChatGptSessionEntity::getDeletedAt)
                .one();
        if (ObjectUtil.isEmpty(one)) {
            throw new CustomException("禁用失败：车号查询失败");
        }
        one.setStatus(CAR_BAN_STATUS);
        one.setRemark("账号token失效，系统自动禁用");
        apiService.sendSaveOrUpdateRequest(one, "update", "gpt");
        updateById(one);
    }

    @Override
    public Page<ChatGptSessionDto> getPage(Integer page, Integer size, String query, String sortProp, String sortOrder) {
        // 创建查询条件封装器
        return chatGptSessionMapper.selectPageInfo(new Page<>(page, size), query, sortOrder, sortProp);
    }

    @Override
    public void unbindSession(Long id) {
        ChatGptSessionEntity entity = chatGptSessionMapper.selectById(id);
        if (ObjectUtil.isEmpty(entity)) {
            throw new CustomException("根据id查询信息失败");
        }
        LocalDateTime exclusiveExpireTime = entity.getExclusiveExpireTime();
        if (ObjectUtil.isEmpty(exclusiveExpireTime) || exclusiveExpireTime.isBefore(LocalDateTime.now())) {
            throw new CustomException("该账号未绑定，无需解绑");
        }
        entity.setExclusiveExpireTime(null);
        entity.setUserId(null);
        chatGptSessionMapper.updateById(entity);
    }

    private static void checkUserAndSessionRelation(String carId, ChatGptSessionEntity session, ChatGptUserEntity user) {
        if (ObjectUtil.isNotEmpty(session)) {
            Integer carType = session.getIsPlus();
            // 校验用户套餐的有效期
            // 如果是选的是plus或者team，则校验plus的过期时间
            if (PLAN_TYPE_PLUS.equals(carType) || PLAN_TYPE_TEAM.equals(carType)) {
                LocalDateTime plusExpireTime = user.getPlusExpireTime();
                if (ObjectUtil.isEmpty(plusExpireTime) ) {
                    log.error("plus过期时间为空:{}", plusExpireTime);
                    throw new CustomException("您还未购买高级套餐，请购买高级权益");
                }
                if (plusExpireTime.isBefore(LocalDateTime.now())) {
                    log.error("您的高级权益已过期:{}", plusExpireTime);
                    throw new CustomException("您的高级权益已过期，请购买高级权益");
                }
                //如果选择的是4o车队，则需要校验plus时间或者普通时间是否过期
            } else if (PLAN_TYPE_FREE.equals(carType)){
                // 先判断普通的过期时间
                if (user.getExpireTime().isBefore(LocalDateTime.now())) {
                    // 普通的也过期则判断plus的过期时间
                    LocalDateTime plusExpireTime = user.getPlusExpireTime();
                    // plus过期时间为空，则报错
                    if (ObjectUtil.isEmpty(plusExpireTime)) {
                        log.error("普通会员过期了且plus过期时间为空:{}", plusExpireTime);
                        throw new CustomException("您的普通权益已过期，请充值");
                    } else {
                        // plus也过期了
                        if (plusExpireTime.isBefore(LocalDateTime.now())) {
                            log.error("您的所有权益都已过期:{}", plusExpireTime);
                            throw new CustomException("您的所有权益已过期，请充值");
                        }
                    }
                }
            }
        }else {
            log.error("车号不存在,{}", carId);
            throw new CustomException("车号不存在");
        }
    }

    private ChatGptUserEntity checkUserAndCarValid(String userToken, String carId) {
        if (StrUtil.isEmpty(carId)) {
            log.error("车牌不能为空");
            throw new CustomException("车牌不能为空");
        }
        if (StrUtil.isEmpty(userToken)) {
            log.error("UserToken不能为空");
            throw new CustomException("用户名不能为空");
        }
        // 判断用户token是否有效
        ChatGptUserEntity tokenInfo = chatGptUserService.getUserInfoByUsername(userToken);
        if (ObjectUtil.isEmpty(tokenInfo)) {
            log.error("访客模式：{}",tokenInfo);
            return null;
        }
        return tokenInfo;
    }
}

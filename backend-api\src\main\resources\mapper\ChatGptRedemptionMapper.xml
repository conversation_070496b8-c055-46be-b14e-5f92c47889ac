<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.seven.share.mapper.ChatGptRedemptionMapper">

    <select id="selectPageWithJoin" resultType="org.seven.share.common.pojo.dto.ChatGptRedemptionDto">
        SELECT
        r.*,
        u.userToken,
        s.name,
        s.validDays,
        s.isPro
        FROM
        chatgpt_redemption r
        LEFT JOIN
        chatgpt_user u ON r.usedUserId = u.id
        LEFT JOIN chatgpt_subtype s ON r.subTypeId = s.id
        <where>
            1= 1
            <if test="key != null and key != ''">
                and r.key LIKE CONCAT('%', #{key}, '%')
            </if>
            <if test="userToken != null and userToken != ''">
                and u.userToken LIKE CONCAT('%', #{userToken}, '%')
            </if>
            <if test="status != null">
                and r.status LIKE CONCAT('%', #{status}, '%')
            </if>
        </where>
        ORDER BY
        r.updateTime DESC,
        r.redeemedTime DESC
    </select>

    <select id="listRedemptionHistory" resultType="org.seven.share.common.pojo.vo.RedemptionHistoryVo">
        select id, `key`, redeemedTime, status, isPlus
        from chatgpt_redemption where usedUserId = #{userId} order by redeemedTime desc limit 10;
    </select>

</mapper>

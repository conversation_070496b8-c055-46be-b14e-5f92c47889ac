package org.seven.share.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;
import org.seven.share.common.pojo.entity.ChatGptConversationEntity;
import org.seven.share.common.pojo.entity.GrokConversationEntity;

import java.time.LocalDateTime;

/**
 * @InterfaceName: GrokConversationsMapper
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/11
 */
@Mapper
public interface GrokConversationsMapper extends BaseMapper<GrokConversationEntity> {

}

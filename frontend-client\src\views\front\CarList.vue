<script setup>
import { ref, provide, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useSiteStore } from '@/store/modules/site.js'
import { useDark, useToggle } from '@vueuse/core'
import { useI18n } from 'vue-i18n'
import { getToken } from '@/utils/auth'
import { ElNotification, ElMessageBox } from 'element-plus'
import {
  LayoutDashboard,
  ShoppingBag,
  Key,
  CreditCard,
  Handshake,
  BookOpen,
  Megaphone,
  User,
  Moon,
  Sun,
  Menu,
  LayoutGrid,
  Languages,
  ChevronDown,
  Bell,
  Calendar
} from 'lucide-vue-next'

// 组件导入
import Instances from './components/Instances.vue'
import Announcements from './components/Announcements.vue'
import Shop from './components/Shop.vue'
import KeyCodeRedemption from './components/KeyCodeRedemption.vue'
import KeyCodePurchase from './components/KeyCodePurchase.vue'
import Instructions from './components/Instructions.vue'
import Promotion from './components/Promotion.vue'
import CustomerSidebar from './components/CustomerSidebar.vue'
import UserProfileDialog from './components/UserProfileDialog.vue'
import NotificationDialog from './components/NotificationDialog.vue'
import SignInDialog from './components/SignInDialog.vue'

// 基础设置
const siteStore = useSiteStore()
const router = useRouter()
const { t, locale } = useI18n()



// 响应式状态
const activeTab = ref('dashboard')  // 默认显示 dashboard
const sidebarOpen = ref(false)
const contentKey = ref(0)
const noticeVisible = ref(false)
const enableSignIn = computed(() => siteStore.enableSignIn === 'true')

// 主题相关
const isDark = useDark()
const toggleDark = () => {
  useToggle(isDark)()
}

// 多语言相关
const languages = [
  { code: 'en', name: 'English' },
  { code: 'zh', name: '中文' },
  { code: 'fa', name: 'فارسی' },
  { code: 'my', name: 'မြန်မာ' }
]

const changeLanguage = (lang) => {
  locale.value = lang
  localStorage.setItem('language', lang)
}

// 引用
const userProfileDialogRef = ref()
const signInDialogRef = ref()

// 计算属性
const logoUrl = computed(() => {
  return siteStore.logoUrl || '/app/logo.svg'
})
const siteName = computed(() => siteStore.siteName || 'AI助手平台')



// Logo错误处理 - 防止无限循环
const logoErrorHandled = ref(false)
const handleLogoError = (event) => {
  if (!logoErrorHandled.value) {
    logoErrorHandled.value = true
    event.target.src = '/app/logo.svg' // 尝试PNG格式
  } else {
    // 如果所有logo都失败，隐藏图片
    event.target.src = '/app/logo.svg' // 尝试PNG格式
  }
}

// 响应式断点检测
const isMobile = ref(false)
const isTablet = ref(false)

// 检测屏幕尺寸
const checkScreenSize = () => {
  const width = window.innerWidth
  isMobile.value = width < 768
  isTablet.value = width >= 768 && width < 1024
}

// 监听窗口大小变化
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize)
})

// 导航菜单项配置 - 使用正确的 siteStore 属性
const navItems = computed(() => [
  {
    name: t('sidebar.dashboard'),
    value: 'dashboard',
    icon: LayoutDashboard,
    visible: true
  },
  {
    name: t('sidebar.shop'),
    value: 'shop',
    icon: ShoppingBag,
    visible: siteStore.enableSiteShop === 'true'
  },
  {
    name: t('sidebar.redeem'),
    value: 'redeem',
    icon: Key,
    visible: siteStore.closeCardExchange !== 'true'
  },
  {
    name: t('sidebar.buy'),
    value: 'buy',
    icon: CreditCard,
    visible: siteStore.fkAddress ? true : false
  },
  {
    name: t('sidebar.instructions'),
    value: 'instructions',
    icon: BookOpen,
    visible: siteStore.userGuideUrl ? true : false
  },
  {
    name: t('sidebar.announcements'),
    value: 'announcements',
    icon: Megaphone,
    visible: siteStore.siteAnnouncement ? true : false
  },
  {
    name: t('sidebar.promotion'),
    value: 'promotion',
    icon: Handshake,
    visible: siteStore.enableInvite === 'true'
  },
  {
    name: siteStore.customerSidebarName,
    value: 'customerSidebar',
    icon: LayoutGrid,
    visible: siteStore.customerSidebarName ? true : false
  }
])

// 添加调试模式，显示所有菜单项
const debugMode = ref(false)
const visibleNavItems = computed(() => {
  if (debugMode.value) {
    // 调试模式下显示所有菜单项
    return navItems.value
  }
  return navItems.value.filter(item => item.visible)
})

// 切换调试模式的函数
const toggleDebugMode = () => {
  debugMode.value = !debugMode.value
}
// 新增函数
const handleShowProfile = () => {
  if (!getToken()) {
    loginConfirm()
    return
  }
  userProfileDialogRef.value?.openDialog()
}

// 显示通知对话框
const handleShowNotifications = () => {
  noticeVisible.value = true
}

// 显示签到对话框
const handleShowSignIn = () => {
  if (!getToken()) {
    loginConfirm()
    return
  }
  signInDialogRef.value?.openDialog()
}

// 初始化
onMounted(async () => {
  sidebarOpen.value = window.innerWidth > 768 ? siteStore.collapseSidebar !== "true" : false
})
const setActiveTab = async(tab) => {
  // 移动端点击菜单项后自动关闭菜单
  if (isMobile.value) {
    sidebarOpen.value = false
  }

  switch (tab) {
    case 'adminPanel':
      router.push('/expander')
      return;
    case 'customerSidebar':
      if(siteStore.customerSidebarUrl && siteStore.sidebarOpenType === '_blank'){
        window.open(siteStore.customerSidebarUrl, siteStore.sidebarOpenType);
        return;
      } else {
        activeTab.value = 'customerSidebar'
        contentKey.value++
      }
      break;
    case 'instructions':
      if(siteStore.userGuideUrlOpenType === '_blank' && siteStore.userGuideUrl){
        window.open(siteStore.userGuideUrl, siteStore.userGuideUrlOpenType);
        return;
      } else {
        activeTab.value = 'instructions'
        contentKey.value++
      }
      break;
    case 'buy':
      if(siteStore.fkAddressOpenType === '_blank' && siteStore.fkAddress){
        window.open(siteStore.fkAddress, siteStore.fkAddressOpenType);
        return;
      } else {
        activeTab.value = 'buy'
        contentKey.value++
      }
      break;
    default:
      activeTab.value = tab
      contentKey.value++ // 每次切换标签时增加 key 值
      break;
  }
}
const loginConfirm = () => {
  ElMessageBox.confirm(t('auth.loginPrompt'), t('auth.notLoggedIn'), {
    confirmButtonText: t('common.login'),
    cancelButtonText: t('common.register'),
    type: 'warning',
    distinguishCancelAndClose: true, // 区分取消和关闭
  })
    .then(() => {
      // 点击登录按钮
      router.push('/login');
    })
    .catch((action) => {
      // 只有点击注册按钮时才跳转，关闭弹窗时不执行任何操作
      if (action === 'cancel') {
        router.push('/register');
      }
      // 如果 action === 'close'，则不执行任何操作
    });
};

// 移除 toggleDarkMode 函数，使用 toggleDark 代替

const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

provide('darkMode', isDark)

const isRTL = computed(() => {
  // 检查当前语言是否为波斯语或阿拉伯语等RTL语言
  const rtlLanguages = ['fa', 'ar', 'he']
  return rtlLanguages.includes(document.documentElement.lang)
})

// 在provide中添加RTL状态
provide('isRTL', isRTL)
</script>

<template>
  <div class="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300 overflow-x-hidden" :dir="isRTL ? 'rtl' : 'ltr'">
    <!-- 极简黑白设计的顶部导航栏 -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-800">
      <div class="max-w-10xl mx-auto px-3 sm:px-4 lg:px-6 xl:px-8">
        <div class="flex items-center justify-between h-14 sm:h-16 w-full">
          <!-- 极简 Logo 区域 -->
          <div class="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
            <div class="flex items-center space-x-2 sm:space-x-3">
              <img
                :src="logoUrl"
                alt="Logo"
                class="w-8 h-8 sm:w-10 sm:h-10 object-contain filter dark:invert transition-all duration-300 hover:scale-105"
                @error="handleLogoError"
              />
              <div>
                <h1 class="text-lg sm:text-xl font-bold text-gray-900 dark:text-white whitespace-nowrap" :title="siteName">
                  {{ siteName }}
                </h1>
              </div>
            </div>
          </div>

          <!-- 极简导航菜单 - 桌面端 -->
          <nav class="hidden md:flex items-center space-x-1 flex-1 justify-start ml-8">
            <button
              v-for="item in visibleNavItems"
              :key="item.value"
              @click="setActiveTab(item.value)"
              :class="[
                'relative px-4 py-2 text-sm font-medium transition-all duration-200 flex items-center space-x-2 rounded-lg',
                activeTab === item.value
                  ? 'bg-black dark:bg-white text-white dark:text-black'
                  : 'text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800'
              ]"
            >
              <component :is="item.icon" class="w-4 h-4" />
              <span>{{ item.name }}</span>
            </button>
          </nav>

          <!-- 极简右侧功能区 -->
          <div class="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
            <!-- 移动端功能按钮 -->
            <div class="flex md:hidden items-center space-x-0.5">
              <!-- 开始使用 -->
              <button
                @click="setActiveTab('dashboard')"
                :class="[
                  'p-1.5 rounded-md transition-all duration-200',
                  activeTab === 'dashboard'
                    ? 'bg-black dark:bg-white text-white dark:text-black'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
                ]"
                :title="t('sidebar.dashboard')"
              >
                <LayoutDashboard class="w-3.5 h-3.5" />
              </button>

              <!-- 在线商店 -->
              <button
                v-if="visibleNavItems.find(item => item.value === 'shop')"
                @click="setActiveTab('shop')"
                :class="[
                  'p-1.5 rounded-md transition-all duration-200',
                  activeTab === 'shop'
                    ? 'bg-black dark:bg-white text-white dark:text-black'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
                ]"
                :title="t('sidebar.shop')"
              >
                <ShoppingBag class="w-3.5 h-3.5" />
              </button>

              <!-- 卡密兑换 -->
              <button
                v-if="visibleNavItems.find(item => item.value === 'redeem')"
                @click="setActiveTab('redeem')"
                :class="[
                  'p-1.5 rounded-md transition-all duration-200',
                  activeTab === 'redeem'
                    ? 'bg-black dark:bg-white text-white dark:text-black'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
                ]"
                :title="t('sidebar.redeem')"
              >
                <Key class="w-3.5 h-3.5" />
              </button>

              <!-- 签到按钮 - 仅在启用签到功能且登录后显示 -->
              <button
                v-if="enableSignIn"
                @click="handleShowSignIn"
                class="p-1.5 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
                :title="t('mobile.dailyCheckIn')"
              >
                <Calendar class="w-3.5 h-3.5" />
              </button>
            </div>

            <!-- 桌面端功能按钮 -->
            <div class="hidden md:flex items-center space-x-2">
              <!-- 签到按钮 - 仅在启用签到功能时显示 -->
              <button
                v-if="enableSignIn"
                @click="handleShowSignIn"
                class="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
                :title="t('mobile.dailyCheckIn')"
              >
                <Calendar class="w-4 h-4" />
              </button>

              <!-- 通知按钮 -->
              <button
                @click="handleShowNotifications"
                class="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
                :title="t('mobile.notifications')"
              >
                <Bell class="w-4 h-4" />
              </button>

              <!-- 多语言切换 -->
              <div class="relative">
                <el-dropdown trigger="click" @command="changeLanguage" :hide-on-click="false">
                  <button class="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 flex items-center space-x-1">
                    <Languages class="w-4 h-4" />
                    <ChevronDown class="w-3 h-3" />
                  </button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-for="lang in languages"
                        :key="lang.code"
                        :command="lang.code"
                        :class="{ 'text-black dark:text-white font-semibold bg-gray-100 dark:bg-gray-800': locale === lang.code }"
                      >
                        {{ lang.name }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>

              <!-- 主题切换 -->
              <button
                @click="toggleDark"
                class="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
                :title="isDark ? '切换到浅色模式' : '切换到深色模式'"
              >
                <component :is="isDark ? Sun : Moon" class="w-4 h-4" />
              </button>
            </div>

            <!-- 用户菜单 - 桌面端 -->
            <button
              @click="handleShowProfile"
              class="hidden md:flex items-center space-x-2 px-3 py-2 rounded-lg bg-black dark:bg-white text-white dark:text-black hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200"
            >
              <User class="w-4 h-4" />
              <span class="text-sm font-medium">{{ getToken() ? t('common.personalCenter') : t('common.login') + '/' + t('common.register') }}</span>
            </button>

            <!-- 移动端用户菜单和更多菜单 -->
            <div class="flex md:hidden items-center space-x-0.5">
              <button
                v-if="getToken()"
                @click="handleShowProfile"
                class="p-1.5 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
                :title="t('common.personalCenter')"
              >
                <User class="w-3.5 h-3.5" />
              </button>

              <!-- 登录/注册按钮 - 未登录时显示 -->
              <button
                v-if="!getToken()"
                @click="handleShowProfile"
                class="p-1.5 rounded-md bg-black dark:bg-white text-white dark:text-black hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 text-xs font-medium"
                :title="t('common.login') + '/' + t('common.register')"
              >
                {{ t('common.login') }}
              </button>

              <!-- 更多菜单按钮 -->
              <button
                @click="toggleSidebar"
                class="p-1.5 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
                :title="t('mobile.more')"
              >
                <Menu class="w-3.5 h-3.5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 极简移动端导航菜单 -->
      <div
        v-if="sidebarOpen && isMobile"
        class="md:hidden border-t border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900"
      >
        <div class="px-4 py-3 space-y-1">
          <!-- 其他导航菜单项 - 排除已在顶部显示的 -->
          <button
            v-for="item in visibleNavItems.filter(item => !['dashboard', 'shop', 'redeem'].includes(item.value))"
            :key="item.value"
            @click="setActiveTab(item.value)"
            :class="[
              'w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200',
              activeTab === item.value
                ? 'bg-black dark:bg-white text-white dark:text-black'
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800'
            ]"
          >
            <component :is="item.icon" class="w-4 h-4" />
            <span class="flex-1 text-left">{{ item.name }}</span>
          </button>

          <!-- 简约分隔线 -->
          <div class="border-t border-gray-200 dark:border-gray-800 my-3"></div>

          <!-- 极简功能按钮 -->
          <div class="space-y-1">
            <!-- 通知 - 仅登录后显示 -->
            <button
              @click="handleShowNotifications"
              class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
            >
              <Bell class="w-4 h-4" />
              <span class="flex-1 text-left">{{ t('mobile.notifications') }}</span>
            </button>

            <!-- 主题切换 -->
            <button
              @click="toggleDark"
              class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
            >
              <component :is="isDark ? Sun : Moon" class="w-4 h-4" />
              <span class="flex-1 text-left">{{ isDark ? t('mobile.lightMode') : t('mobile.darkMode') }}</span>
            </button>

            <!-- 多语言切换 -->
            <div class="w-full">
              <el-dropdown trigger="click" @command="changeLanguage" :hide-on-click="false" class="w-full">
                <button class="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200">
                  <Languages class="w-4 h-4" />
                  <span class="flex-1 text-left">{{ t('mobile.language') }}</span>
                  <ChevronDown class="w-3 h-3" />
                </button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="lang in languages"
                      :key="lang.code"
                      :command="lang.code"
                      :class="{ 'text-black dark:text-white font-semibold bg-gray-100 dark:bg-gray-800': locale === lang.code }"
                    >
                      {{ lang.name }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="w-full">
        <transition name="fade" mode="out-in">
          <component :is="activeTab === 'dashboard' ? Instances :
            activeTab === 'shop' ? Shop :
              activeTab === 'redeem' ? KeyCodeRedemption :
                activeTab === 'buy' ? KeyCodePurchase :
                  activeTab === 'instructions' ? Instructions :
                    activeTab === 'announcements' ? Announcements :
                      activeTab === 'promotion' ? Promotion :
                        activeTab === 'customerSidebar' ? CustomerSidebar :
                          Instances"
            :key="contentKey"
            @switchTab="setActiveTab">
          </component>
        </transition>
      </div>
    </main>

    <!-- 保留原有的对话框组件 -->
    <UserProfileDialog ref="userProfileDialogRef" />
    <NotificationDialog v-model:visible="noticeVisible" />
    <SignInDialog ref="signInDialogRef" />
  </div>
</template>



<style scoped>
/* 极简页面过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 为固定导航栏添加顶部间距 */
main {
  margin-top: 4rem; /* 64px - 桌面端导航栏高度 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .max-w-7xl {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  /* 移动端导航栏高度调整 */
  main {
    margin-top: 3.5rem; /* 56px - 移动端导航栏高度 */
  }


  /* 移动端文字大小调整 */
  .text-xl {
    font-size: 1.125rem;
  }

  /* 移动端导航菜单优化 */
  .md\:hidden {
    display: block !important;
  }

  /* 移动端间距优化 */
  .space-x-1 > * + * {
    margin-left: 0.25rem;
  }
}

/* 平板端优化 */
@media (min-width: 768px) and (max-width: 1024px) {
  /* 平板端导航栏高度 */
  header .h-14 {
    height: 4rem;
  }

  main {
    margin-top: 4rem; /* 平板端使用桌面端高度 */
  }
}

/* 大屏幕优化 */
@media (min-width: 1024px) {
  /* 桌面端导航栏间距优化 */
  nav {
    margin-left: 2rem;
    margin-right: 2rem;
  }
}

/* 毛玻璃效果 */
.backdrop-blur-md {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* 按钮过渡效果 */
button {
  transition: all 0.2s ease;
}

/* 导航栏阴影 */
header {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.dark header {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
}

/* 主内容区域 */
main {
  width: 100%;
  padding-bottom: 2rem;
  overflow-x: hidden;
}

/* 移动端调整 */
@media (max-width: 768px) {
  main {
    padding-bottom: 1rem;
  }
}

/* 简约设计原则 */
* {
  box-sizing: border-box;
}

/* 防止页面抖动和不必要的滚动条 */
html {
  overflow-x: hidden;
  scroll-behavior: smooth;
}

body {
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

/* 防止拖拽时的选择和抖动 */
* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* 允许输入框和文本选择 */
input, textarea, [contenteditable] {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* 防止图片拖拽 */
img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  pointer-events: none;
}

/* 允许按钮和链接的点击 */
button, a, [role="button"] {
  pointer-events: auto;
}

/* 隐藏所有滚动条但保持滚动功能 */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: transparent;
}

/* Firefox */
* {
  scrollbar-width: none;
}

/* IE and Edge */
* {
  -ms-overflow-style: none;
}

/* 去除不必要的装饰 */
.no-decoration {
  text-decoration: none;
  border: none;
  outline: none;
}

/* 统一的圆角 */
.rounded-standard {
  border-radius: 0.5rem;
}

/* 统一的间距 */
.spacing-standard {
  padding: 0.5rem 1rem;
}

/* 黑白主题色彩 */
.bg-primary {
  background-color: #000;
}

.dark .bg-primary {
  background-color: #fff;
}

.text-primary {
  color: #000;
}

.dark .text-primary {
  color: #fff;
}

.bg-secondary {
  background-color: #f5f5f5;
}

.dark .bg-secondary {
  background-color: #1a1a1a;
}

.text-secondary {
  color: #666;
}

.dark .text-secondary {
  color: #999;
}

/* 简约边框 */
.border-standard {
  border: 1px solid #e5e5e5;
}

.dark .border-standard {
  border: 1px solid #333;
}

/* 简约阴影 */
.shadow-standard {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .shadow-standard {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 悬停效果 */
.hover-lift:hover {
  transform: translateY(-1px);
}

/* 焦点状态 */
.focus-ring:focus {
  outline: 2px solid #000;
  outline-offset: 2px;
}

.dark .focus-ring:focus {
  outline-color: #fff;
}

/* 禁用状态 */
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 加载状态 */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* 文字选择 */
::selection {
  background-color: #000;
  color: #fff;
}

.dark ::selection {
  background-color: #fff;
  color: #000;
}


</style>



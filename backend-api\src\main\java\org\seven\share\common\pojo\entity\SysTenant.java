package org.seven.share.common.pojo.entity;


import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sys_tenant")
public class SysTenant extends BaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "租户编号")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String tenantId;

    @Schema(description = "联系人")
    private String contactUserName;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "域名")
    private String domain;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "租户套餐编号")
    private Long packageId;

    @Schema(description = "过期时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime expireTime;

    @Schema(description = "租户状态（0正常 1停用）")
    private String status;

    @Schema(description = "佣金余额")
    private Double commissionBalance;

    @Schema(description = "佣金比例")
    private Double commissionRatio;

    @Schema(description = "站点logo")
    private String siteLogo;

    @Schema(description = "站点名称")
    private String siteName;
}

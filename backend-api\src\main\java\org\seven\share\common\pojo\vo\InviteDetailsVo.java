package org.seven.share.common.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: InviterRecordVo
 * @Description:
 * @Author: Seven
 * @Date: 2024/12/30
 */
@Data
public class InviteDetailsVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String userToken;

    private Double affMoney;

    private int orderType;

    private int status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
}

package org.seven.share.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.seven.share.common.annotation.TenantIsolation;
import org.seven.share.common.pojo.dto.UserDto;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.common.pojo.vo.InviteDetailsVo;
import org.seven.share.common.pojo.vo.UserPayLogsVo;


import java.util.List;
import java.util.Map;

/**
 * @ClassName: UserTokenDao
 * @Description:
 * @Author: Seven
 * @Date: 2024/6/22
 */
@Mapper
public interface ChatGptUserMapper extends BaseMapper<ChatGptUserEntity> {
   List<String> getTableColumns(@Param("tableName") String tableName, @Param("schema") String schema);

   void executeSql(@Param("sql") String sql);

   void updatePlusExpireTime();

   @TenantIsolation("u1.tenant_id")
   IPage<UserDto> selectUserWithInviterPage(Page<?> page,
                                            @Param("query") String query,
                                            @Param("sortProp") String sortProp,
                                            @Param("sortOrder") String sortOrder);

   void resetDailyConversations();

    List<UserPayLogsVo> listPaymentHistory(@Param("userToken") String userToken);

    void updateIsDeletedByIds(@Param("ids") List<Long> ids);

    Map<String, Long> calculateUserStats(@Param("oneHourAgo") String oneHourAgo);

    void updateBatchUserExpireTime();

    @InterceptorIgnore(tenantLine = "true")
    List<InviteDetailsVo> getInviteDetails(@Param("id") String id);

    @InterceptorIgnore(tenantLine = "true")
    int updateByCondition(@Param("keyValueMap")String keyValueMap);

    @InterceptorIgnore(tenantLine = "true")
    @Select("select * from chatgpt_user where deleted_at is null and (userToken = #{username} or email = #{email})")
    ChatGptUserEntity selectOneByNameOrEmail(@Param("username") String username, @Param("email") String email);

    @InterceptorIgnore(tenantLine = "true")
    ChatGptUserEntity getUserByUserToken(@Param("userToken") String userToken);

    @InterceptorIgnore(tenantLine = "true")
    ChatGptUserEntity getUserByLoginToken(@Param("loginToken") String loginToken);

    @InterceptorIgnore(tenantLine = "true")
    ChatGptUserEntity getByIdWithoutTenant(@Param("userId") long userId);

    @InterceptorIgnore(tenantLine = "true")
    void updateByIdWithoutTenant(@Param("entity") ChatGptUserEntity entity);

    @InterceptorIgnore(tenantLine = "true")
    ChatGptUserEntity selectOneByNameAndLoginType(@Param("username") String username, @Param("type") int type);

    @InterceptorIgnore(tenantLine = "true")
    String getLoginTokenById(Long uid);
}

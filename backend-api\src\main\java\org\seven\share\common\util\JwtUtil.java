package org.seven.share.common.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.exception.ServiceException;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.service.ChatGptConfigService;
import org.seven.share.service.ChatGptUserService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static org.seven.share.common.util.HttpUtils.getCookieValue;
import static org.seven.share.constant.CacheConstant.USER_SESSION_PREFIX;
import static org.seven.share.common.util.ConstantUtil.*;

/**
 * @ClassName: JwtUtil
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/30
 */
@Component
@Slf4j
public class JwtUtil {
    /**
     * 静态方法调用非静态接口层(Service层)
     */
    private static ChatGptUserService staticChatGptUserService;

    private static ChatGptConfigService ststicChatGptConfigService;

    private static StringRedisTemplate staticStringRedisTemplate; // 静态 RedisTemplate

    private static HttpServletRequest staticRequest;

    @PostConstruct //初始化
    public void init() {
        staticChatGptUserService = chatGptUserService;
        ststicChatGptConfigService = chatGptConfigService;
        staticStringRedisTemplate = stringRedisTemplate; // 初始化静态 RedisTemplate
        staticRequest = request;
    }

    @Resource
    private ChatGptUserService chatGptUserService;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private StringRedisTemplate stringRedisTemplate; // 注入 RedisTemplate

    @Resource
    private HttpServletRequest request;

    /**
     * 根据用户token获取用户角色
     * @param token
     */
    public static String getUserRole(String token) {
        if (StrUtil.isEmpty(token)) {
            throw new CustomException("角色认证失败：token 未携带");
        }
        Map<String, Claim> map = parseToken(token);
        Integer isAdmin = Optional.ofNullable(map.get("role"))
                .filter(claim -> !claim.isNull())
                .map(Claim::asInt)
                .orElse(0);
        return isAdmin == 0? "user" : "admin";
    }

    public static Long getUid(String token) {
        if (StrUtil.isEmpty(token)) {
            throw new CustomException("角色认证失败：token 未携带");
        }
        Map<String, Claim> map = parseToken(token);
        return Optional.ofNullable(map.get("uid")).filter(claim -> !claim.isNull()).map(Claim::asLong).orElse(null);
    }

    /**
     * 生成token
     * @param uid
     * @param uname
     * @return
     */
    public static String create(Long uid, String uname, Integer role){
        //过期时间
        Map<String, String> keyVlaueMap = ststicChatGptConfigService.getKeyValueMapByKeys(List.of("expireTime", "maxDevices"));
        log.info("keyVlaueMap:{}", keyVlaueMap);
        String expireTime = Optional.ofNullable(keyVlaueMap.get("expireTime"))
                .filter(e -> StrUtil.isNotEmpty(e) && !"null".equals(e))
                .orElse(String.valueOf(EXPIRE_TIME));
        log.info("expireTime:{}",expireTime);
        String maxDevices = Optional.ofNullable(keyVlaueMap.get("maxDevices"))
                .filter(e -> StrUtil.isNotEmpty(e) && !"null".equals(e))
                .orElse("3");
        log.info("maxDevices:{}",maxDevices);
        Date date = new Date(System.currentTimeMillis() + EXPIRE_TIME);
        if (StrUtil.isNotBlank(expireTime)) {
            date = new Date(System.currentTimeMillis() + Long.parseLong(expireTime) * 60 * 1000);
        }
        //私钥及加密算法
        Algorithm algorithm = Algorithm.HMAC256(JWT_SECRET);
        // 生成登录id
        String loginId = UUID.randomUUID().toString();

        // 保存到redis中的set
        String userSessionKey = USER_SESSION_PREFIX + uid;
        Long size = Optional.ofNullable(staticStringRedisTemplate.opsForSet().size(userSessionKey)).orElse(0L);
        if (size >= Long.parseLong(maxDevices)) {
            // 清理最早的会话
            staticStringRedisTemplate.opsForSet().pop(userSessionKey);
        }
        staticStringRedisTemplate.opsForSet().add(userSessionKey, loginId);

        return JWT.create()
                .withClaim("uid", uid)
                .withClaim("uname", uname)
                .withClaim("role", role)
                .withClaim("loginId", loginId)
                .withExpiresAt(date)
                .sign(algorithm);
    }

    public static void decode(String token) {
        try {
            Map<String, Claim> map = parseToken(token);
            Long uid = Optional.ofNullable(map.get("uid"))
                    .filter(claim -> !claim.isNull())
                    .map(Claim::asLong)
                    .orElse(0L);

            // 获取当前token的loginId
            String loginId = Optional.ofNullable(map.get("loginId"))
                    .filter(claim -> !claim.isNull())
                    .map(Claim::asString)
                    .orElse("");

            // 使用新的key前缀检查
            String userSessionKey = USER_SESSION_PREFIX + uid;
            if (!Boolean.TRUE.equals(staticStringRedisTemplate.opsForSet().isMember(userSessionKey, loginId))) {
                staticStringRedisTemplate.delete("gfsession:" + getCookieValue(staticRequest, "gfsession"));
                throw new ServiceException(401, "登录已失效，请重新登录");
            }

            // 获取 uname
            String uname = Optional.ofNullable(map.get("uname"))
                    .filter(claim -> !claim.isNull())
                    .map(Claim::asString)
                    .orElse(null);

            ChatGptUserEntity user = staticChatGptUserService.getUserInfoByUsername(uname);
            if (ObjectUtil.isEmpty(user)) {
                log.info("token 中的用户信息查询失败");
                throw new ServiceException(401,"token错误，请重新登录");
            }
        } catch (TokenExpiredException e) {
            log.error("认证异常：",e);
            throw new ServiceException(401, "token已过期，请重新登录");
        } catch (JWTVerificationException e) {
            log.error("认证异常：",e);
            throw new ServiceException(401, "token认证失败，请重新登录");
        }
    }

    static Map<String, Claim> parseToken(String token) {
        //私钥及加密算法
        Algorithm algorithm = Algorithm.HMAC256(JWT_SECRET);
        JWTVerifier verifier = JWT.require(algorithm).build();

        // 认证token
        int index = token.indexOf("Bearer ");
        try {
            DecodedJWT verify = verifier.verify(index >= 0 ? token.substring(index + 7).trim() : token);

            // 获取附带信息，并进行自定义验证
            return verify.getClaims();
        } catch (TokenExpiredException e) {
            log.error("认证异常：",e);
            throw new ServiceException(401, "token已过期，请重新登录");
        } catch (JWTVerificationException e) {
            log.error("认证异常：",e);
            throw new ServiceException(401, "token认证失败，请重新登录");
        }
    }
}

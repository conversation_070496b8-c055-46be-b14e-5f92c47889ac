package org.seven.share.common.pojo.vo;

import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: StatisticVo
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/11
 */
@Data
@Builder
public class StatisticVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long addNum;

    private Long payNum;

    private Long totalNum;

    private Long unPayNum;

    private Double totalMoney;

    private Long topics;

    private Long normalUsage;

    private Long advanceUsage;

    private Long claudeUsage;

    private Long visitorUsage;

    private Double monthTotalMoney;

    private Double unsettledCommission; // 待结算佣金

    private int onlineNums;
}

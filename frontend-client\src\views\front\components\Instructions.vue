<template>
  <div class="instructions-container">
    <h2 class="text-2xl md:text-3xl font-bold mb-6">{{ $t('instructions.title') }}</h2>
    <p class="mb-1">{{ $t('instructions.description') }}</p>
    <div class="rounded shadow-lg">
      <iframe
        :src="instructionsUrl" 
        frameborder="0"
        class="w-full h-screen overflow-y-auto"
        :title="$t('instructions.title')"
      ></iframe>
    </div>
  </div>
</template>

<style scoped>
.instructions-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

@media (max-width: 768px) {
  .instructions-container {
    height: auto;
    overflow: visible;
  }
}
</style>

<script setup>
import { computed } from 'vue';
import { useSiteStore } from '@/store/modules/site';

const siteStore = useSiteStore();
const instructionsUrl = computed(() => siteStore.userGuideUrl);
</script>
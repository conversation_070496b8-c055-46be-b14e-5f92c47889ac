import { request } from '../request';

export function fetchCdkeyPage(params: any) {
  return request({
    url: '/codes/page',
    method: 'get',
    params
  });
}

export function generateCodes(subTypeId: number, count: number) {
  return request({
    url: '/codes/generate',
    method: 'get',
    params: { subTypeId, count }
  });
}

export function removeCdkeyBatchByIds(data: any) {
  return request({
    url: '/codes/delete',
    method: 'delete',
    data
  });
}

export function recycleKey(data: any) {
  return request({
    url: '/codes/recycle',
    method: 'post',
    data
  });
}

/**
 * 导出未使用的cdkey
 *
 * @param subtypeId
 * @returns
 */
export function exportCdkey(subTypeId: number) {
  return request({
    url: '/codes/export-key',
    method: 'get',
    params: { subTypeId },
    responseType: 'text',
    headers: {
      Accept: 'text/plain'
    }
  });
}

package org.seven.share.common.util;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.pojo.dto.ModelLimitDto;
import org.seven.share.common.pojo.entity.ModelLimitsEntity;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: ConstantUtil
 * @Description: 常量配置
 * @Author: Seven
 * @Date: 2024/7/29
 */

@Slf4j
public class ConstantUtil {

    /**
     * Claude 普通账号
     */
    public static final Integer PLAN_TYPE_CLAUDE_FREE = 0;
    /**
     * Claude pro账号
     */
    public static final Integer PLAN_TYPE_CLAUDE_PRO = 1;

    /**
     * <PERSON> max账号
     */
    public static final Integer PLAN_TYPE_CLAUDE_MAX = 2;
    /**
     * grok 普通账号
     */
    public static final Integer PLAN_TYPE_GROK_NORMAL = 0;
    /**
     * grok super账号
     */
    public static final Integer PLAN_TYPE_GROK_SUPER = 1;
    /**
     * Plus账号
     */
    public static final Integer PLAN_TYPE_PLUS = 1;

    /**
     * Team账号
     */
    public static final Integer PLAN_TYPE_TEAM = 2;

    /**
     * PRO账号
     */
    public static final Integer PLAN_TYPE_PRO = 3;

    /**
     * 免费账号
     */
    public static final Integer PLAN_TYPE_FREE = 0;


    /**
     * 启用状态
     */
    public static final Integer CLAUDE_ENABLE_STATUS = 1;

    /**
     * 禁用状态
     */
    public static final Integer CLAUDE_BAN_STATUS = 0;

    /**
     * 开启了claude权限
     */
    public static final Integer ENABLE_CLAUDE_ACCESS = 1;
    /**
     * 启用
     */
    public static final Integer CAR_ENABLE_STATUS = 1;

    /**
     * 禁用
     */
    public static final Integer CAR_BAN_STATUS = 0;

    public static final int USER_TYPE_NORMAL = 1;

    public static final int USER_TYPE_FREE = 2;

    public static final int USER_TYPE_ADVANCE = 3;


    /**
     * 邮件注册html模板名称
     */
    public static final String EMAIL_REGISTER_TEMPLATE = "register";

    /**
     * 邮件重置密码html模板名称
     */
    public static final String EMAIL_REST_PASSWORD_TEMPLATE = "forgot-password";

    /**
     * 支付成功通知html模板名称
     */
    public static final String EMAIL_PAY_SUCCESS_NOTIFY_TEMPLATE = "pay-success-notify";

    /**
     * 会员过期提醒html模板名称
     */
    public static final String MEMBERSHIP_EXPIRATION = "membership-expiration";

    /**
     * 邮件发送主题
     */
    public static final String EMAIL_SUBJECT = "邮件验证码";

    /**
     * 提现成功邮件
     */
    public static final String WITHDRAW_SUCCESS_SUBJECT = "佣金到账提醒";

    /**
     * 支付成功通知主题
     */
    public static final String EMAIL_PAY_SUBJECT = "支付通知";

    /**
     * token 过期时间 先设置60分钟，后期使用配置的方式读取
     */
    public static final long EXPIRE_TIME = 15 * 24 * 60 * 60 * 1000;

    /**
     * jwt 秘钥
     */
    public static final String JWT_SECRET = "chatgpt-share-server-111";

    /**
     * 请求头
     */
    public static final String AUTH_HEADER = "Authorization";

    /**
     * 获取claude登录地址后缀
     */
    public static final String CLAUDE_LOGIN_SUFFIX = "/manage-api/auth/oauth_token";

    /**
     * 当面付format
     */
    public static final String ALIPAY_FORMAT = "json";

    /**
     * 当面付签名类型
     */
    public static final String ALIPAY_SIGN_TYPE = "RSA2";

    /**
     * 当面付字符集
     */
    public static final String ALIPAY_CHARSET = "utf-8";

    /**
     * 虎皮椒订单状态查询接口
     */
    public static final String XUN_HU_PAY_STATUS_QUERY_URL = "https://api.xunhupay.com/payment/query.html";

    /**
     * 旧虎皮椒订单状态查询接口
     */
    public static final String OLD_XUN_HU_PAY_STATUS_QUERY_URL = "https://api.dpweixin.com/payment/query.html";

    /**
     * 旧虎皮椒网关支付接口
     */
    public static final String OLD_XUN_HU_PAY_GATEWAY_URL = "https://api.dpweixin.com/payment/do.html";

    /**
     * 蓝兔微信订单状态查询接口
     */
    public static final String LANG_TU_WX_PAY_STATUS_QUERY_URL = "https://api.ltzf.cn/api/wxpay/get_pay_order";

    /**
     * 蓝兔微信h5地址
     */
    public static final String LANG_TU_WX_H5_URL = "https://api.ltzf.cn/api/wxpay/jump_h5";

    public static final List<String> ALLOWED_FILE_TYPES = Arrays.asList("image/jpeg", "image/png");


    public static final String ACCESS_ROLE_ADMIN = "admin";

    public static final String OAI_CHAT_TOKEN = "https://chat.oaifree.com/token/register";

    public static final int BATCH_SAVE_ACCOUNTS = 1;

    public static final int BATCH_SAVE_REFRESH_TOKENS = 2;

    public final static String API_AUTH_HEADER = "apiauth";

    public final static int PAYMENT_STATUS_ENABLE = 1;

    public final static int COUPON_HAVE_USED = 1;

    public final static int COUPON_HAVE_NO_USED = 0;

    public final static int ENABLE_SENSITIVE_WORDS = 1;

    /**
     * 未处理
     */
    public final static int AFF_RECORD_STATUS_UN_HANDLE = 0;

    /**
     * 已处理
     */
    public final static int AFF_RECORD_STATUS_HANDLED = 1;

    /**
     * 充值
     */
    public final static int AFF_RECORD_TYPE_RECHARGE = 1;

    /**
     * 兑换
     */
    public final static int AFF_RECORD_TYPE_EXCHANGE = 2;

    public static final int MAX_ATTEMPTS = 5; // 最大失败次数

    public static final long LOCK_TIME = 10;  // 锁定时间（分钟）

    public static final String SYS_TENANT_ID = "000000";

    public static Map<String, ModelLimitDto> getSysModelLimitMap(String jsonString) {
        if (StrUtil.isEmpty(jsonString)) {
            throw new CustomException("模型速率json数据为空");
        }
        // 创建ObjectMapper实例
        ObjectMapper objectMapper = new ObjectMapper();
        // 存储解析后的数据
        Map<String, ModelLimitDto> map = new HashMap<>();
        try {
            // 将JSON字符串解析为JsonNode
            JsonNode rootNode = objectMapper.readTree(jsonString);
            Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();

            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> entry = fields.next();
                String key = entry.getKey();
                JsonNode value = entry.getValue();

                // 遍历每个子字段
                Iterator<Map.Entry<String, JsonNode>> subFields = value.fields();
                int limit = 0;
                String per = "";
                while (subFields.hasNext()) {
                    Map.Entry<String, JsonNode> subField = subFields.next();
                    String subKey = subField.getKey();
                    JsonNode subValue = subField.getValue();

                    if ("limit".equals(subKey)) {
                        limit = subValue.asInt();
                    } else if ("per".equals(subKey)) {
                        per = subValue.asText();
                    }
                }
                // 将每个模型的limit和per值存储到ModelLimitDto中，并添加到Map
                map.put(key, new ModelLimitDto(limit, per));
            }
            return map;
        } catch (Exception e) {
            log.error("解析JSON字符串出错：{}", e.getMessage());
        }

        return null;
    }

    public static final String VERSION_FILE = "data/version.txt";  // 定义版本号文件的路径

}

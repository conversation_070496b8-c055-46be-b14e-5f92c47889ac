import { defineStore } from 'pinia';
import { login, fetchLoginWithAuthCode } from '@/api/user.js'
import { getToken, setToken, removeToken} from '@/utils/auth'
import Cookies from 'js-cookie'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    id: '',
    username:'',
    isAdmin: '',
    isPlus:'',
    expireTime: '',
    isLogin: false,
    email: '',
    affCode: '',
    plusExpireTime: '',
    claudeExpireTime: '',
    claudeProExpireTime: '',
    grokExpireTime: '',
    grokSuperExpireTime:'',
    loginType:'',
  }),
  actions: {
    async login(loginForm) {
      try {
        const res = await login(loginForm);
        this.token = res.token
        setToken(this.token)
        this.id = res.userInfo.id
        this.username = res.userInfo.userToken
        this.isAdmin = res.userInfo.isAdmin
        this.isPlus = res.userInfo.isPlus
        this.expireTime = res.userInfo.expireTime
        this.isLogin = true
        this.email = res.userInfo.email
        this.affCode = res.userInfo.affCode
        this.plusExpireTime = res.userInfo.plusExpireTime
        this.claudeExpireTime = res.userInfo.claudeExpireTime
        this.claudeProExpireTime = res.userInfo.claudeProExpireTime
        this.loginType = res.userInfo.loginType
      } catch (error) {
        console.error('Failed to fetch site data:', error);
        return Promise.reject(error)
      }
    },
    async logOut() {
      removeToken()
      Cookies.remove('gfsessionid');
      Cookies.set('visitor', true, { expires: 30 });
      this.token = ''
      this.id = ''
      this.username = ''
      this.isAdmin = ''
      this.isPlus = ''
      this.expireTime = ''
      this.isLogin = false
      this.email = ''
      this.affCode = ''
      this.plusExpireTime = ''
      this.claudeExpireTime = ''
      this.claudeProExpireTime = ''
      this.grokExpireTime = ''
      this.grokSuperExpireTime = ''
      this.loginType = ''
    },
    async loginWithAuthCode (code) { 
      const res = await fetchLoginWithAuthCode({ code })
      this.token = res.token
      setToken(this.token)
      this.id = res.user.id
      this.username = res.user.userToken
      this.isAdmin = res.user.isAdmin
      this.isPlus = res.user.isPlus
      this.expireTime = res.user.expireTime
      this.isLogin = true
      this.affCode = res.user.affCode
      this.plusExpireTime = res.user.plusExpireTime
      this.claudeExpireTime = res.user.claudeExpireTime
      this.claudeProExpireTime = res.user.claudeProExpireTime
      this.loginType = res.user.loginType
    }
  },
  persist: {
    enabled: true
  },
});

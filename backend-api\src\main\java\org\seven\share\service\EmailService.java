package org.seven.share.service;

import java.util.Map;

/**
 * @InterfaceName: EmailService
 * @Description: 邮件发送服务
 * @Author: Seven
 * @Date: 2024/7/29
 */
public interface EmailService {
    void sendEmail(String email, String template, String subject, Map<String, String> contentMap);

    void sendEmailNotTemplate (String email, String subject, String content);

    void sendPaySuccessEmail(String template, String subject, Map<String, String> contentMap);
}

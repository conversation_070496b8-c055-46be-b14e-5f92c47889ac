package org.seven.share.common.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
public class ServletUtils {

    public static ServletRequestAttributes getRequestAttributes() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        return (ServletRequestAttributes) attributes;
    }

    /**
     * 获取request
     */
    public static HttpServletRequest getRequest() {
        return getRequestAttributes().getRequest();
    }

    /**
     * 获取response
     */
    public static HttpServletResponse getResponse() {
        return getRequestAttributes().getResponse();
    }

    /**
     * 获取请求方法（GET、POST等）
     * @return 请求方法字符串
     */
    public static String getRequestMethod() {
        return getRequest().getMethod();
    }

    /**
     * 获取请求URI
     * @return 请求URI字符串
     */
    public static String getRequestURI() {
        return getRequest().getRequestURI();
    }


    public static String getHost() {
        HttpServletRequest request = getRequest();
        // 优先获取 X-Forwarded-Host（反向代理传递的客户端域名）
        String domain = request.getHeader("X-Forwarded-Host");
        if (StrUtil.isBlank(domain)) {
            // 回退到 Host 头
            domain = request.getHeader("Host");
        }
        log.info("domain:{}", domain);
        return domain;
    }

}

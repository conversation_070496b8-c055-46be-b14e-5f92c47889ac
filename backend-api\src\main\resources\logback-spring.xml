<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <springProperty scope="context" name="springName" source="spring.application.name"/>
    <property name="log.colorPattern"
              value="%magenta(%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight(%-5level) %boldCyan(${springName})) %yellow(%thread) %green(%logger{2}) %msg%n"
    />
    <!-- 设置控制台 日志输出 -->
    <appender name="consoleApp" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>
                ${log.colorPattern}
            </pattern>
        </layout>
    </appender>
    <!-- 设置info以及info以上 warn，error（不包括），fatal  级别 输出到一个文件 -->
    <appender name="fileInfoApp" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>
        <encoder>
            <pattern>
                ${log.colorPattern}
            </pattern>
        </encoder>
        <append>true</append>
        <!-- 滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 路径 -->
            <fileNamePattern>data/logs/info/app.info.%d.log</fileNamePattern>
        </rollingPolicy>
    </appender>
    <!-- 设置error级别 输出到一个文件 -->
    <appender name="fileErrorApp" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <encoder>
            <pattern>
                ${log.colorPattern}
            </pattern>
        </encoder>

        <!-- 设置滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 路径 -->
            <fileNamePattern>data/logs/error/app.err.%d.log</fileNamePattern>

            <!-- 控制保留的归档文件的最大数量，超出数量就删除旧文件 -->
            <MaxHistory>1</MaxHistory>

        </rollingPolicy>
    </appender>
    <root level="INFO">
        <appender-ref ref="consoleApp"/>
        <appender-ref ref="fileInfoApp"/>
        <appender-ref ref="fileErrorApp"/>
    </root>
</configuration>

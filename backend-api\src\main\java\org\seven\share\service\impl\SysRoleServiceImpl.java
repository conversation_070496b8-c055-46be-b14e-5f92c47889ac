package org.seven.share.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.api.Result;
import org.seven.share.common.api.ResultCode;
import org.seven.share.common.enums.DelStatusEnums;
import org.seven.share.common.enums.StatusEnums;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.exception.ServiceException;
import org.seven.share.common.pojo.entity.SysResource;
import org.seven.share.common.pojo.entity.SysRole;
import org.seven.share.common.pojo.entity.SysRoleResource;
import org.seven.share.common.pojo.entity.SysUser;
import org.seven.share.common.pojo.vo.SysRoleVO;
import org.seven.share.common.util.JwtTokenUtil;
import org.seven.share.common.util.SecurityUtil;
import org.seven.share.mapper.CreateTableMapper;
import org.seven.share.mapper.SysRoleMapper;
import org.seven.share.service.SysRoleResourceService;
import org.seven.share.service.SysRoleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole>
        implements SysRoleService {

    final JwtTokenUtil jwtTokenUtil;

    final SysRoleResourceService sysRoleResourceService;

    @Resource
    private CreateTableMapper createTableMapper;

    @PostConstruct
    public void init() {
        createTableMapper.createSysRoleTable();
        if (count() == 0) {
            createTableMapper.insertSysRole();
        }
    }

    @Override
    public IPage<SysRole> getPage(Map<String, Object> params) {
        int pageSize = Integer.parseInt(String.valueOf(params.get("size")));
        int pageNum = Integer.parseInt(String.valueOf(params.get("current")));
        LambdaQueryWrapper<SysRole> wrapper = createWrapper(params);

        return page(new Page<>(pageNum, pageSize), wrapper);
    }

    private LambdaQueryWrapper<SysRole> createWrapper(Map<String, Object> params) {
        String roleName = (String) params.get("roleName");
        String roleCode = (String) params.get("roleCode");
        String status = (String) params.get("status");

        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotEmpty(roleName), SysRole::getRoleName, roleName);
        wrapper.like(StrUtil.isNotEmpty(roleCode), SysRole::getRoleCode, roleCode);
        wrapper.eq(StrUtil.isNotEmpty(status), SysRole::getStatus, status);
        wrapper.eq( SysRole::getIsDeleted, DelStatusEnums.DISABLE.getCode());

        return wrapper;
    }

    @Override
    public Result<List<SysRoleVO>> getAllRoles(String authorizationHeader) {
        if (authorizationHeader == null || !authorizationHeader.startsWith(jwtTokenUtil.getTokenHead())) {
            throw new ServiceException(401, "登录失败");
        }

        List<SysRoleVO> sysRoleVOS = new ArrayList<>();

        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getStatus, StatusEnums.ENABLE.getCode());

        List<SysRole> list = list(wrapper);
        if (!list.isEmpty()) {
            for (SysRole sysRole : list) {
                SysRoleVO sysRoleVO = new SysRoleVO();
                sysRoleVO.setRoleName(sysRole.getRoleName());
                sysRoleVO.setRoleCode(sysRole.getRoleCode());
                sysRoleVO.setRoleDesc(sysRole.getRoleDesc());
                sysRoleVOS.add(sysRoleVO);
            }
            return Result.success(sysRoleVOS);
        }
        return Result.success();
    }

    @Override
    public List<String> getUserRole(Long id) {
        return baseMapper.getUserRole(id);
    }

    @Override
    public List<SysRole> queryRoleListByRoleCode(List<String> roleCode) {

        LambdaQueryWrapper<SysRole> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(SysRole::getRoleCode, roleCode);
        lambdaQueryWrapper.eq(SysRole::getStatus,StatusEnums.ENABLE.getCode());

        return list(lambdaQueryWrapper);
    }

    @Override
    public Result<Boolean> add(SysRole sysRole) {

        if (sysRole.getId() != null && sysRole.getId() > 0) {
            log.info("修改角色对象入参: {}", JSONUtil.parse(sysRole));
            return Result.success(updateById(sysRole));
        } else {
            SysUser sysUser = SecurityUtil.getSysUser();
            if (sysUser == null) {
                return Result.failed(ResultCode.FORBIDDEN);
            }
            sysRole.setStatus(StatusEnums.ENABLE.getCode());
            sysRole.setIsDeleted(DelStatusEnums.DISABLE.getCode());
            sysRole.setType(1);
            sysRole.setCreateId(sysUser.getId());
            sysRole.setCreateBy(sysUser.getUserName());
            sysRole.setUpdateId(sysUser.getId());
            sysRole.setUpdateBy(sysUser.getUserName());
            log.info("添加角色对象入参: {}", JSONUtil.parse(sysRole));

            save(sysRole);

            LambdaQueryWrapper<SysRole> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(SysRole::getRoleCode, sysRole.getRoleCode());
            lambdaQueryWrapper.eq(SysRole::getIsDeleted, DelStatusEnums.DISABLE.getCode());

            SysRole sysRole1 = getOne(lambdaQueryWrapper);


            // 给角色绑定基础资源权限
            sysRoleResourceService.bindingRoleBasicResource(sysRole1.getId(), new ArrayList<>(Collections.singletonList(1L)));

            return Result.success();
        }
    }

    @Override
    public Result<List<Long>> getRoleResourceId(Long roleId) {
        return sysRoleResourceService.getRoleResourceId(roleId);
    }


    /**
     * 绑定角色与资源的关联关系
     * @param roleId 角色id
     * @param ids 资源集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindRoleResource(Long roleId, List<Long> ids) {
        log.info("绑定角色资源：角色id: {}， 资源ID: {}", roleId, JSONUtil.parse(ids));

        // 删除角色资源信息
        deleteRoleResourceByRoleId(roleId);

        if (CollectionUtil.isEmpty(ids)) {
            return;
        }

        SysUser sysUser = SecurityUtil.getSysUser();
        if (sysUser == null) {
            throw new CustomException(ResultCode.FORBIDDEN.getMessage());
        }

        List<SysRoleResource> roleResources = new ArrayList<>();
        for (Long id : ids) {
            SysRoleResource sysRoleResource = new SysRoleResource();
            sysRoleResource.setResourceId(id);
            sysRoleResource.setCreateId(sysUser.getId());
            sysRoleResource.setCreateBy(sysUser.getUserName());
            sysRoleResource.setUpdateId(sysUser.getId());
            sysRoleResource.setUpdateBy(sysUser.getUserName());
            sysRoleResource.setRoleId(roleId);
            sysRoleResource.setIsDeleted(DelStatusEnums.DISABLE.getCode());
            roleResources.add(sysRoleResource);
        }
        sysRoleResourceService.saveBatch(roleResources);
    }

    /**
     * 删除角色资源信息
     * @param roleId 角色id
     */
    private void deleteRoleResourceByRoleId(Long roleId) {
        LambdaQueryWrapper<SysRoleResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleResource::getRoleId, roleId);
        sysRoleResourceService.remove(wrapper);
    }

    /**
     * 根据角色id查找资源id集合
     * @param roleId 角色id
     * @return 资源集合
     */
    @Override
    public List<Long> getRoleResource(Long roleId) {
        LambdaQueryWrapper<SysRoleResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleResource::getRoleId, roleId);
        List<SysRoleResource> list = sysRoleResourceService.list(wrapper);
        return list.stream().map(SysRoleResource::getResourceId).toList();
    }

    /**
     * 删除角色信息
     * @param ids 角色id集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRoleBatch(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        // 先删除角色与资源的关系数据
        sysRoleResourceService.removeRoleResourceBatchByIds(ids);

        // 批量删除删除角色
        removeByIds(ids);
    }

}

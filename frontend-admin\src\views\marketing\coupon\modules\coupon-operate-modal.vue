<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { ElDatePicker, ElMessage, ElSwitch, ElCheckbox } from 'element-plus';
import type { CheckboxValueType } from 'element-plus';
import { useForm } from '@/hooks/common/form';
import { fetchSubTypeList, generateCoupon, saveCoupon, updateCoupon } from '@/service/api';

defineOptions({ name: 'CouponOperateModal' });

export type OperateType = 'add' | 'update';

interface Props {
  /** the type of operation */
  operateType: OperateType;
  /** the edit pay config data */
  rowData?: any | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useForm();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: '新增优惠券',
    update: '修改优惠券'
  };
  return titles[props.operateType];
});

interface FormData {
  id?: string;
  ids: Array<string>;
  coupon: string;
  discount: number;
  expireTime: undefined;
  status: number;
  sort: number;
  discountAmount: number;
  discountCount: number;
  remark?: string;
}

interface SubType {
  id: string;
  name: string;
}

const subTypeList = ref<SubType[]>([]);
const model = ref<FormData>(createDefaultModel());

function createDefaultModel(): FormData {
  return {
    ids: [],
    coupon: '',
    discount: 0.1,
    expireTime: undefined,
    status: 0,
    sort: 0,
    discountAmount: 0,
    discountCount: 0,
    remark: ''
  };
}

const rules = {
  coupon: [{ required: true, message: '请输入优惠券', trigger: 'blur' }],
  expireTime: [{ required: true, message: '请选择过期时间', trigger: 'blur' }]
};

// 全选相关计算属性和方法
const isAllSelected = computed(() => {
  return subTypeList.value.length > 0 && model.value.ids.length === subTypeList.value.length;
});

const isIndeterminate = computed(() => {
  return model.value.ids.length > 0 && model.value.ids.length < subTypeList.value.length;
});

function handleSelectAll(checked: CheckboxValueType) {
  if (checked) {
    model.value.ids = subTypeList.value.map(item => String(item.id));
  } else {
    model.value.ids = [];
  }
}

async function handleInitModel() {
  Object.assign(model.value, createDefaultModel());
  const response = await fetchSubTypeList();
  subTypeList.value = response.data as SubType[];
  if (props.operateType === 'update') {
    if (!props.rowData) return;
    Object.assign(model.value, props.rowData);
  } else {
    const couponResponse = await generateCoupon();
    model.value.coupon = couponResponse.data.couponCode as string;
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  try {
    if (props.operateType === 'add') {
      await saveCoupon(model.value);
    } else {
      await updateCoupon(model.value);
    }
    ElMessage({
      message: '保存成功',
      type: 'success',
      plain: true
    });
    closeDrawer();
    emit('submitted');
  } catch (error) {
    console.error('保存失败', error);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <ElDialog v-model="visible" :title="title" preset="card" class="w-700px">
    <ElForm ref="formRef" :model="model" :rules="rules" :label-width="100">
      <ElRow>
        <ElCol :span="24">
          <ElFormItem label="优惠商品" prop="ids">
            <div class="mb-2">
              <ElCheckbox
                :model-value="isAllSelected"
                :indeterminate="isIndeterminate"
                @change="handleSelectAll"
              >
                全选
              </ElCheckbox>
            </div>
            <ElSelect v-model="model.ids" placeholder="请输入优惠商品" multiple clearable>
              <ElOption
                v-for="plan in subTypeList"
                :key="plan.id"
                :label="plan.name"
                :value="String(plan.id)"
              ></ElOption>
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="优惠卷码" prop="coupon">
            <ElInput v-model="model.coupon" placeholder="请输入优惠卷码" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="优惠金额" prop="discountAmount">
            <ElInputNumber v-model="model.discountAmount" style="width: 100%" placeholder="请输入优惠金额" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="优惠折扣" prop="discount">
            <ElInputNumber
              v-model="model.discount"
              :precision="2"
              :step="0.01"
              :min="0.1"
              :max="1"
              placeholder="请输入优惠折扣"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="优惠次数" prop="discountCount">
            <ElInputNumber v-model="model.discountCount" placeholder="请输入优惠次数" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="过期时间" prop="expireTime">
            <ElDatePicker v-model="model.expireTime" value-format="YYYY-MM-DD HH:mm:ss" type="datetime" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="是否生效" prop="status">
            <ElSwitch v-model="model.status" :active-value="0" :inactive-value="1" />
          </ElFormItem>
        </ElCol>
        <ElCol>
          <ElFormItem label="备注" prop="remark">
            <ElInput
              v-model="model.remark"
              :autosize="{ minRows: 4, maxRows: 6 }"
              type="textarea"
              placeholder="请输入备注"
            />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElSpace :size="16" class="float-right">
        <ElButton @click="closeDrawer">取 消</ElButton>
        <ElButton type="primary" @click="handleSubmit">保 存</ElButton>
      </ElSpace>
    </template>
  </ElDialog>
</template>

<style scoped></style>

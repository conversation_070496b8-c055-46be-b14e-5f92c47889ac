package org.seven.share.common.pojo.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: BatchUserDto
 * @Description:
 * @Author: Seven
 * @Date: 2024/10/25
 */
@Data
public class BatchUserDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自定义用户名前缀
     */
    private String tokenPrefix;

    /**
     * 生成数量
     */
    private int nums;

    private String password;

    private Long subtypeId;

    private String remark;

    private Double affRate;

    private Integer loginType;

}

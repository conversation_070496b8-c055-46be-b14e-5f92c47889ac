package org.seven.share.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.seven.share.common.pojo.dto.ChatGptSessionDto;
import org.seven.share.common.pojo.entity.ChatGptSessionEntity;
import org.seven.share.common.pojo.entity.GrokSessionEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @InterfaceName: GrokSessionMapper
 * @Description:
 * @Author: Seven
 * @Date: 2024/6/23
 */
@Mapper
public interface GrokSessionMapper extends BaseMapper<GrokSessionEntity> {
}

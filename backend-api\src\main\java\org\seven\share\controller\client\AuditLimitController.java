package org.seven.share.controller.client;

import org.seven.share.service.audit.AuditLimitService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @ClassName: AuditLimitController
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/5
 */
@RequestMapping({"/client-api", "/api"}) // 支持多个路径前缀
@RestController
public class AuditLimitController {

    @Resource
    private AuditLimitService auditLimitService;

    @PostMapping("/auditLimit")
    public ResponseEntity<?> auditLimit(HttpServletRequest request, @RequestBody Map<String, Object> requestData) {
        return auditLimitService.gptConversationRateLimit(request, requestData, true);
    }

    @PostMapping("/auditLimitPro")
    public ResponseEntity<?> auditLimitPro(HttpServletRequest request, @RequestBody Map<String, Object> requestData) {
        return auditLimitService.gptConversationRateLimit(request, requestData, false);
    }

    @PostMapping("/claudeLimit")
    public ResponseEntity<?> claudeLimit(HttpServletRequest request, @RequestBody Map<String, Object> requestData) {
        return auditLimitService.claudeConversationLimit(request, requestData);
    }

    @PostMapping("/grok/auditLimit")
    public ResponseEntity<?> grokAuditLimit(HttpServletRequest request, @RequestBody Map<String, Object> requestData) {
        return auditLimitService.grokConversationAuditLimit(request, requestData);
    }

}

package org.seven.share.common.pojo.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: ClaudeCarInfoVo
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/12
 */
@Data
public class ClaudeCarInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    private String carID;

    private Integer status;

    private Integer isPro;

    private String remaining;

    private String resetsAt;
}


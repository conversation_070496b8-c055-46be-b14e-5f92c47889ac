<template>
  <div class="p-2 rounded-lg">
    <el-table
      :data="records"
      border
      v-loading="loading"
      :empty-text="$t('table.noData')"
    >
      <el-table-column type="index" :label="$t('table.index')" width="70" align="center" />

      <template v-if="type === 'invite'">
        <el-table-column prop="userToken" :label="$t('table.username')" align="center" />
        <el-table-column prop="email" :label="$t('table.email')" align="center" />
        <el-table-column prop="isPlus" :label="$t('table.memberType')" align="center">
          <template #default="scope">
            <span
              :class="[
                'px-2 py-1 rounded text-xs font-medium',
                scope.row.isPlus === 1 ? 'bg-black text-white dark:bg-white dark:text-black' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
              ]"
            >
            {{ scope.row.isPlus === 1 ? $t('table.types.plus') : $t('table.types.normal') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="$t('table.inviteTime')" align="center">
          <template #default="scope">
            <span>{{ formatDate(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </template>

      <template v-else>
        <el-table-column
          prop="withdrawalMoney"
          :label="$t('table.withdrawAmount')"
          align="center"
        />
        <el-table-column prop="withdrawalTime" :label="$t('table.withdrawTime')" align="center">
          <template #default="scope">
            <span>{{ formatDate(scope.row.withdrawalTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status"  :label="$t('table.approvalStatus')" sortable align="center">
          <template #default="scope">
            <span
              :class="[
                'px-2 py-1 rounded text-xs font-medium',
                getStatusClass(scope.row.status)
              ]"
            >
              {{ getStatusText(scope.row.status) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="remark" :label="$t('table.approvalResult')" align="center" />
      </template>
    </el-table>

    <el-pagination
      class="mt-4 custom-pagination"
      :current-page="pageInfo.page"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      background
      :prev-text="$t('pagination.prev')"
  :next-text="$t('pagination.next')"
      layout="prev, pager, next"
    />
  </div>
</template>

<script setup>
import { formatDate } from '@/utils/date';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

defineProps({
  records: {
    type: Array,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  pageInfo: {
    type: Object,
    required: true,
  },
  type: {
    type: String,
    required: true,
    validator: (value) => ['invite', 'withdraw'].includes(value),
  },
});

const emit = defineEmits(['size-change', 'current-change']);

const handleSizeChange = (val) => emit('size-change', val);
const handleCurrentChange = (val) => emit('current-change', val);

const getStatusType = (status) => {
  switch (status) {
    case 1:
      return 'success';
    case 0:
      return 'primary';
    case 2:
      return 'warning';
    default:
      return 'info';
  }
};

const getStatusClass = (status) => {
  switch (status) {
    case 1:  // 已批准
      return 'bg-black text-white dark:bg-white dark:text-black';
    case 0:  // 待审批
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    case 2:  // 已拒绝
      return 'bg-gray-400 text-white dark:bg-gray-600 dark:text-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
  }
};

const getStatusText = (status) => {
  switch (status) {
    case 1:
      return t('table.statuses.approved');
    case 0:
      return t('table.statuses.pending');
    case 2:
      return t('table.statuses.rejected');
    default:
      return t('table.statuses.unknown');
  }
};
</script>

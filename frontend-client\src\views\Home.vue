<template></template>
<script setup>
import { computed, onMounted } from 'vue';
import { goToChat, getDeviceId } from '@/utils/login-auth';
import { getIdleByUser } from '@/api/chatgpt.js';
import { useUserStore } from '@/store/modules/user';
import { fetchAccessToken } from '@/api/user.js';
import { useRouter } from 'vue-router';
import { useSiteStore } from '@/store/modules/site';
import Cookies from 'js-cookie';

const siteStore = useSiteStore();
const router = useRouter(); // 使用vue-router
const enableNoLogin = computed(() => siteStore.enableNoLogin === 'true');

const userStore = useUserStore();

onMounted(() => {
  init();
});

const init = async () => {
  try {
    const visitor = Cookies.get('visitor');
    let loginToken, loginCarid, loginType;
    
    if (enableNoLogin.value) {
      if (!visitor || visitor == 'true') {
        // 说明是游客,则获取免费用户名和车
        const { carId } = await fetchAccessToken();
        if (!carId) {
          router.push('/home')
          return
        }
        // 生成一个随机设备id作为用户名
        loginToken = await getDeviceId();
        loginCarid = carId;
        loginType = 'free';
        Cookies.set('visitor', true, { expires: 30 })
      } else {
        Cookies.set('visitor', false, { expires: 30 })
        const username = userStore.username;
        // 不是游客，需要登录。
        if (!username) {
          router.push('/home');
          return
        } else {
          // 已存在则获取用户当前权益最高的车和用户和节点类型
          const data = await getIdleByUser({ userId: userStore.id });
          if (!data) {
            router.push('/home');
            return
          }
          loginToken = username;
          loginCarid = data.carId;
          loginType = data.nodeType;
        }
      }
      goToChat(loginCarid, loginType, loginToken, router);
    } else { 
      router.push("/home")
      return
    } 
  } catch (error) {
    router.push('/home');
    return
  }
};
</script>

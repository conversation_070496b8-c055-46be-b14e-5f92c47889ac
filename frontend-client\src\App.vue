<template>
  <div :dir="currentDirection"> <!-- 添加 dir 属性控制文字方向 -->
    <router-view></router-view>
    <!-- Copyright notice -->
    <footer class="w-full text-center footer pb-1">
      <p class="text-sm" v-if="showVersion">
        &copy; {{ currentYear }} {{ siteName }} {{ version }} All Rights Reserved.
      </p>
    </footer>
  </div>
  <el-dialog v-model="isWeixinBrowser" title="注意" width="350" :align-center="true" :close-on-click-modal="false"
    :show-close="false" :close-on-press-escape="false">
    <span class="text-base">本系统不支持在微信浏览器中使用，请点击右上角使用其他浏览器打开。</span>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useSiteStore } from '@/store/modules/site.js';
import { getDeviceId } from '@/utils/login-auth';
import { useI18n } from 'vue-i18n'

const siteStore = useSiteStore();
const currentYear = computed(() => new Date().getFullYear());
const siteName = ref()
const version = siteStore.version
const showVersion = computed(() => siteStore.showVersion === 'true')

const isWeixinBrowser = ref(false);

const i18n = useI18n()

// 添加文字方向控制
const currentDirection = computed(() => {
  return i18n.locale.value === 'fa' ? 'rtl' : 'ltr'
})
// 加载站点js脚本
const fetchScript = async () => {
  await siteStore.fetchScript();
  loadScripts(localStorage.getItem('script'))  
}
// 加载站点配置
const fetchConfig = async () => {
  await siteStore.fetchSiteData();
  siteName.value = siteStore.siteName
  changeFavicon()
}

const loadScripts = async (scripts) => {
  if (!scripts) {
    return
  }
  try {
    // 检查是否包含 <script> 标签
    const hasScriptTags = scripts.includes('<script');

    if (hasScriptTags) {
      // 如果包含 <script> 标签，按原来的方式处理
      const div = document.createElement('div');
      div.innerHTML = scripts;

      // 获取所有的 <script> 标签并插入到 body 中
      const scriptTags = div.querySelectorAll('script');
      scriptTags.forEach(scriptTag => {
        const scriptElement = document.createElement('script');

        // 复制原 <script> 标签的属性和内容
        scriptElement.textContent = scriptTag.textContent; // 如果是内联脚本
        if (scriptTag.src) {
          scriptElement.src = scriptTag.src; // 如果是外部脚本
        }

        // 复制其他属性（如 type, async, defer 等）
        Array.from(scriptTag.attributes).forEach(attr => {
          if (attr.name !== 'src') {
            scriptElement.setAttribute(attr.name, attr.value);
          }
        });

        document.body.appendChild(scriptElement);
      });
    } else {
      // 如果不包含 <script> 标签，直接作为 JavaScript 代码处理
      const scriptElement = document.createElement('script');
      scriptElement.type = 'text/javascript';
      scriptElement.textContent = scripts;
      document.body.appendChild(scriptElement);
    }
  } catch (error) {
    console.error('加载脚本时出错:', error);
  }
}
const changeFavicon = () => {
  if (siteStore.logoUrl) {
    let favicon = document.querySelector('link[rel="icon"]');

    // 如果 favicon 不存在，则创建一个新的 <link rel="icon"> 元素
    if (!favicon) {
      favicon = document.createElement('link');
      favicon.rel = 'icon';
      document.head.appendChild(favicon);
    }
    favicon.href = siteStore.logoUrl;
  }
};

// 修改语言检测函数
const detectUserLanguage = () => {
  if (localStorage.getItem('language')) {
    i18n.locale.value = localStorage.getItem('language')
  } else {
    const browserLang = navigator.language.toLowerCase()
    if (browserLang.includes('zh')) {
      i18n.locale.value = 'zh-CN'
    } else if (browserLang.includes('fa')) {
      i18n.locale.value = 'fa'
      document.documentElement.setAttribute('dir', 'rtl') // 设置整个文档的方向
    } else {
      i18n.locale.value = 'en'
    }
  }
}

// 异步加载配置和检测微信浏览器
const initialize = async () => {
  // 检测并设置默认语言
  detectUserLanguage()
  // 加载站点脚本
  await fetchScript();
  // 加载站点配置
  await fetchConfig();

  // 检查是否微信登录
  await checkWeixinBrowser()
}
// 检测是否是微信浏览器
const checkWeixinBrowser = async () => {
  // 等待配置加载完后，获取 enableWechat 的值
  const enableWechat = computed(() => {
    return siteStore.openWechat === '' ? true : siteStore.openWechat === 'true' ? true : false;
  });

  // 如果不允许在微信浏览器中打开，则进行微信浏览器检测
  if (!enableWechat.value) {
    isWeixinBrowser.value = /MicroMessenger/i.test(navigator.userAgent);
  }
}

onMounted(() => {
  getDeviceId()
  initialize()
})
</script>
<style scoped>
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  font-size: 12px;
}

.weixin-alert {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
}

.alert-content {
  width: 90%;
  background: white;
  padding: 20px;
  border-radius: 8px;
  font-size: 18px;
}

/* 暗黑模式过渡动画 */
html {
  transition: background-color 0.3s ease;
}

html * {
  transition: background-color 0.3s ease, 
              border-color 0.3s ease,
              color 0.3s ease;
}

/* 添加 RTL 支持的样式 */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .el-dialog__title {
  text-align: right;
}

/* RTL 布局时反转 padding 和 margin */
[dir="rtl"] .el-dialog__body {
  padding: 30px 20px 30px 20px;
}

/* 如果使用了 Element Plus，需要引入其 RTL CSS */
/* 在 main.js 中添加: import 'element-plus/theme-chalk/rtl.css' */
</style>

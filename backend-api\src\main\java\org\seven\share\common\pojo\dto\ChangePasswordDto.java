package org.seven.share.common.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: ChangePasswordDto
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/17
 */
@Data
public class ChangePasswordDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "用户名不能为空")
    private String userName;

    @NotNull(message = "密码不能为空")
    private String currentPassword;

    @NotNull(message = "新密码不能为空")
    private String newPassword;
}

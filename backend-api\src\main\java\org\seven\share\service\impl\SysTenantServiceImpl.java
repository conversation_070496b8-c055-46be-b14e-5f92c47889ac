package org.seven.share.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.api.Result;
import org.seven.share.common.api.ResultCode;
import org.seven.share.common.enums.DelStatusEnums;
import org.seven.share.common.enums.StatusEnums;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.pojo.dto.SysTenantDto;
import org.seven.share.common.pojo.entity.*;
import org.seven.share.common.util.SecurityUtil;
import org.seven.share.mapper.SysTenantMapper;
import org.seven.share.service.SysTenantService;
import org.springframework.stereotype.Service;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.seven.share.common.util.ConstantUtil.SYS_TENANT_ID;
import static org.seven.share.common.util.ServletUtils.getHost;

@Slf4j
@Service
@RequiredArgsConstructor
public class SysTenantServiceImpl extends ServiceImpl<SysTenantMapper, SysTenant>
        implements SysTenantService {

    @Override
    public IPage<SysTenant> getTenantPage(Map<String, Object> params) {
        int pageNum = 1;
        int pageSize = 10;

        if (ObjectUtil.isNotEmpty(params)) {
            pageSize = Integer.parseInt(String.valueOf(params.get("size")));
            pageNum = Integer.parseInt(String.valueOf(params.get("current")));
        }
        LambdaQueryWrapper<SysTenant> wrapper = createWrapper(params);
        return page(new Page<>(pageNum, pageSize), wrapper);
    }

    private LambdaQueryWrapper<SysTenant> createWrapper(Map<String, Object> params) {
        String tenantId = (String) params.get("tenantId");
        String tenantName = (String) params.get("tenantName");
        String domain = (String) params.get("domain");
        LambdaQueryWrapper<SysTenant> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotEmpty(tenantId), SysTenant::getTenantId, tenantId);
        wrapper.like(StrUtil.isNotEmpty(tenantName), SysTenant::getTenantName, tenantName);
        wrapper.eq(StrUtil.isNotEmpty(domain), SysTenant::getDomain, domain);
        wrapper.eq( SysTenant::getIsDeleted, DelStatusEnums.DISABLE.getCode());
        return wrapper;
    }

    @Override
    @CacheEvict(cacheNames = "tenant", allEntries = true)
    public Result<String> addTenant(SysTenantDto sysTenantDto) {
        SysUser sysUser = SecurityUtil.getSysUser();
        if (sysUser == null) {
            return Result.failed(ResultCode.FORBIDDEN);
        }
        // 校验返现比例是否正确
        Double ratio = sysTenantDto.getCommissionRatio();
        if (ratio == null || ratio < 0.01 || ratio > 1.0) {
            throw new CustomException("佣金比例设置错误");
        }
        // 校验租户编码是否唯一
        Long count = this.lambdaQuery()
                .and(w -> w.eq(SysTenant::getTenantId, sysTenantDto.getTenantId())
                .or()
                .eq(SysTenant::getDomain, sysTenantDto.getDomain())).count();
        if (count > 0) {
            throw new CustomException("租户编号或者域名重复");
        }
        if (SYS_TENANT_ID.equals(sysTenantDto.getTenantId())) {
            throw new CustomException("租户id不能为管理员的租户id：000000");
        }
        SysTenant sysTenant = new SysTenant();
        BeanUtil.copyProperties(sysTenantDto, sysTenant);
        sysTenant.setCreateId(sysUser.getId());
        sysTenant.setCreateBy(sysUser.getUserName());
        sysTenant.setUpdateBy(sysUser.getUserName());
        sysTenant.setUpdateId(sysUser.getId());
        sysTenant.setCreateTime(LocalDateTime.now());
        sysTenant.setUpdateTime(LocalDateTime.now());
        save(sysTenant);
        return Result.success();
    }

    @Override
    @CacheEvict(cacheNames = "tenant", allEntries = true)
    public Result<String> updateTenant(SysTenantDto sysTenantDto) {
        SysUser sysUser = SecurityUtil.getSysUser();
        if (sysUser == null) {
            return Result.failed(ResultCode.FORBIDDEN);
        }
        // 校验佣金比例是否正确
        Double ratio = sysTenantDto.getCommissionRatio();
        if (ratio == null || ratio < 0.01 || ratio > 1.0) {
            throw new CustomException("佣金比例设置错误");
        }
        if (SYS_TENANT_ID.equals(sysTenantDto.getTenantId())) {
            throw new CustomException("租户id不能为管理员的租户id：000000");
        }
        // 从数据库获取当前租户
        SysTenant existingTenant = this.getById(sysTenantDto.getId());
        if (existingTenant == null) {
            return Result.failed("租户不存在");
        }
        // 租户编号不允许修改
        if (!Objects.equals(sysTenantDto.getTenantId(), existingTenant.getTenantId())) {
            return Result.failed("租户编号不允许修改");
        }
        SysTenant sysTenant = new SysTenant();
        BeanUtil.copyProperties(sysTenantDto, sysTenant);
        sysTenant.setUpdateId(sysUser.getId());
        sysTenant.setUpdateBy(sysUser.getUserName());
        sysTenant.setUpdateTime(LocalDateTime.now());
        boolean ifSuccess = updateById(sysTenant);
        if (ifSuccess) {
            return Result.success();
        }else {
            return Result.failed();
        }
    }

    @Override
    @CacheEvict(cacheNames = "tenant", allEntries = true)
    public Result<String> deleteTenant(Long id) {
        boolean ifSuccess = removeById(id);
        if (ifSuccess) {
            return Result.success();
        }else {
            return Result.failed();
        }
    }

    @Override
    @CacheEvict(cacheNames = "tenant", allEntries = true)
    public Result<String> batchDeleteTenant(List<String> ids) {
        List<Long> longIds = ids.stream()
                .map(s -> {
                    try {
                        return Long.parseLong(s);
                    } catch (NumberFormatException e) {
                        return 0L;  // 或其它默认值
                    }
                }).collect(Collectors.toList());
        boolean ifSuccess = removeByIds(longIds);
        if (ifSuccess) {
            return Result.success();
        }else {
            return Result.failed();
        }
    }

    @Override
    @Cacheable(cacheNames = "tenant", key = "'tenant_list'")
    public List<SysTenant> getTenantList() {
        return this.lambdaQuery().eq(SysTenant::getStatus, StatusEnums.ENABLE.getCode()).list();
    }

    /**
     * 计算租户的佣金
     * @param user 普通用户
     * @param money
     */
    @Override
    public void calcTenantBalance(ChatGptUserEntity user, Double money) {
        String tenantId = user.getTenantId();
        log.info("calcTenantBalance tenantId:{}", tenantId);
        if (money == null) {
            throw new CustomException("订单中的支付金额为空");
        }
        if (StrUtil.isNotEmpty(tenantId) && !SYS_TENANT_ID.equals(tenantId)) {
            SysTenant sysTenant = this.lambdaQuery().eq(SysTenant::getTenantId, tenantId).one();
            if (ObjectUtil.isNotEmpty(sysTenant)) {
                Double commissionRatio = sysTenant.getCommissionRatio();
                log.info("commissionRatio:{}", commissionRatio);
                if (commissionRatio !=null && commissionRatio >= 0.01 && commissionRatio <=1.0) {
                    log.info("处理租户的佣金：租户id：{}, money：{}, 佣金比例：{}", tenantId, money, commissionRatio);
                    BigDecimal moneyDecimal = BigDecimal.valueOf(money);
                    BigDecimal commissionRatioDecimal = BigDecimal.valueOf(commissionRatio);
                    BigDecimal balanceBigDecimal = moneyDecimal.multiply(commissionRatioDecimal);
                    BigDecimal originalBalance = BigDecimal.valueOf(sysTenant.getCommissionBalance());
                    BigDecimal finalAffQuotaDecimal = originalBalance.add(balanceBigDecimal);
                    // 更新
                    sysTenant.setCommissionBalance(finalAffQuotaDecimal.doubleValue());
                    sysTenant.setUpdateTime(LocalDateTime.now());
                    SysUser sysUser = SecurityUtil.getSysUser();
                    sysTenant.setUpdateBy(Optional.ofNullable(sysUser).map(SysUser::getUserName).orElse(""));
                    sysTenant.setUpdateId(Optional.ofNullable(sysUser).map(SysUser::getId).orElse(null));
                    updateById(sysTenant);
                    log.info("租户佣金计算入库完成");
                }
            }
        }

    }

    /**
     * 根据域名获取租户编码
     * @return 租户编码
     */
    @Override
    @Cacheable(cacheNames = "tenant", key = "'host_' + #host")
    public String getTenantIdByHost(String host) {
        log.debug("从数据库查询租户ID，host: {}", host);
        SysTenant tenant = this.lambdaQuery().eq(SysTenant::getDomain, host).one();
        if (tenant != null) {
            return tenant.getTenantId();
        }
        return SYS_TENANT_ID;
    }

    /**
     * 根据域名获取租户信息
     * @return
     */
    @Override
    @Cacheable(cacheNames = "tenant", key = "'info_' + #host")
    public SysTenant getTenantInfoByHost(String host) {
        log.debug("从数据库查询租户信息，host: {}", host);
        return this.lambdaQuery().eq(SysTenant::getDomain, host).one();
    }

    /**
     * 根据租户id获取佣金比例
     * @param tenantId
     * @return
     */
    @Override
    public Double getUnsettledCommission(String tenantId) {
        if (StrUtil.isNotEmpty(tenantId)) {
            SysTenant one = this.lambdaQuery().eq(SysTenant::getTenantId, tenantId).one();
            return Optional.ofNullable(one).map(SysTenant::getCommissionBalance).orElse(0.0);
        }
        return 0.0;
    }
}

package org.seven.share.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Schema(description = "用户信息VO")
@Data
@EqualsAndHashCode(callSuper = false)
public class UserInfoVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "登录ID")
    private Long userId;

    @Schema(description = "登录用户名")
    private String userName;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "性别")
    private String userGender;

    @Schema(description = "电话")
    private String userPhone;

    @Schema(description = "电子邮箱")
    private String userEmail;

    @Schema(description = "角色")
    private List<String> roles;

    @Schema(description = "菜单权限")
    private List<String> permissions;

    @Schema(description = "租户id")
    private String tenantId;

}

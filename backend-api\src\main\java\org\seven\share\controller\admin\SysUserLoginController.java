package org.seven.share.controller.admin;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.annotation.RateLimit;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.Result;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.vo.CaptchaVo;
import org.seven.share.common.pojo.vo.LoginResult;
import org.seven.share.param.SysUserLoginParam;
import org.seven.share.service.SysCaptchaService;
import org.seven.share.service.SysUserService;
import org.seven.share.vo.UserInfoVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "SysUserLoginController")
@RequestMapping("/expander-api/auth")
public class SysUserLoginController {

    private final SysUserService sysUserService;

    private final SysCaptchaService sysCaptchaService;

    @Operation(summary = "登录")
    @PostMapping(value = "/login")
    @SysLogInterface(title = "登录", businessType = BusinessType.GRANT)
    @RateLimit(10)
    public Result<LoginResult> login(@RequestBody SysUserLoginParam sysUserLoginParam) {
        // 验证码校验
         boolean captcha = sysCaptchaService.validate(sysUserLoginParam.getUuid(),
                 sysUserLoginParam.getCode());
         if (!captcha) {
             return Result.failed("验证码不正确, 请刷新验证码重试");
         }
        return Result.success(sysUserService.login(sysUserLoginParam.getUserName(),
                sysUserLoginParam.getPassword()));
    }

    @Operation(summary = "获取用户信息")
    @GetMapping(value = "/getUserInfo")
    public Result<UserInfoVO> getUserInfo(@RequestHeader("Authorization") String authorizationHeader) {
        return sysUserService.getUserInfo(authorizationHeader);
    }

    @Operation(summary = "修改系统用户密码")
    @PostMapping(value = "/updatePassword")
    @SysLogInterface(title = "修改系统用户密码", businessType = BusinessType.UPDATE)
    public Result<Boolean> updateSysUserPwd(String oldPassword, String newPassword) {
        return Result.success(sysUserService.updateSysUserPwd(oldPassword, newPassword));
    }

    @Operation(summary = "获取系统用户登录验证码")
    @GetMapping("/code")
    public Result<CaptchaVo> getSysUserCode() {
        return Result.success(sysCaptchaService.getCaptcha());
    }

}

package org.seven.share.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.seven.share.common.pojo.dto.ChatGptSessionDto;
import org.seven.share.common.pojo.entity.ChatGptSessionEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @InterfaceName: ChatGptSessionDao
 * @Description:
 * @Author: Seven
 * @Date: 2024/6/23
 */
@Mapper
public interface ChatGptSessionMapper extends BaseMapper<ChatGptSessionEntity> {
    void bindUserGptSession(@Param("id") Long id, @Param("ids")List<String> ids, @Param("expireTime") LocalDateTime expireTime);

    void cancelUserGptSessionRelation(@Param("id") Long id, @Param("ids")List<String> ids);

    Page<ChatGptSessionDto> selectPageInfo(Page<ChatGptSessionEntity> page,
                                           @Param("query") String query,
                                           @Param("sortOrder") String sortOrder,
                                           @Param("sortProp") String sortProp);
}

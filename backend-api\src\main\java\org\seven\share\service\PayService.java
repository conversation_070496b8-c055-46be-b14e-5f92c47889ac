package org.seven.share.service;

import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * @ClassName: EpayService
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/4
 */
public interface PayService {

    String getOrderStatus(String tradeNo);

    Map<String, Object> getQrCode(String typeId, Integer payType, String userToken, String coupon, boolean isMobile);

    void ePayNotify(HttpServletRequest request) ;

    String aliPayNotify(HttpServletRequest request, int payMethod);

    String queryF2FOrderStatus(String outTradeNo);

    String xunHuPayNotify(HttpServletRequest request, int payMethod);

    String queryHpPayStatus(String tradeNo, int payMethod);

    String queryLanTuPayStatus(String tradeNo);

    ResponseEntity<?> lanTuPayNotify(Map<String, String> paramMap);

    String wxNativeNotify(HttpServletRequest request, HttpServletResponse response) throws IOException;

    String queryNativeStatus(String tradeNo);
}

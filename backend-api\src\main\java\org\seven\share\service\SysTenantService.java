package org.seven.share.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.api.Result;
import org.seven.share.common.pojo.dto.SysTenantDto;
import org.seven.share.common.pojo.entity.ChatGptEPayLogsEntity;
import org.seven.share.common.pojo.entity.ChatGptSubTypeEntity;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.common.pojo.entity.SysTenant;

import java.util.List;
import java.util.Map;

public interface SysTenantService extends IService<SysTenant> {

    IPage<SysTenant>getTenantPage(Map<String, Object> params);

    Result<String> addTenant(SysTenantDto sysTenantDto);

    Result<String> updateTenant(SysTenantDto sysTenantDto);

    Result<String> deleteTenant(Long id);

    Result<String> batchDeleteTenant(List<String> ids);

    List<SysTenant> getTenantList();

    void calcTenantBalance(ChatGptUserEntity user, Double money);

    String getTenantIdByHost(String host);

    SysTenant getTenantInfoByHost(String host);

    Double getUnsettledCommission(String tenantId);
}

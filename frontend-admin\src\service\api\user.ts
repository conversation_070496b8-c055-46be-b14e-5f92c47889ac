import { request } from '../request';

// 用户列表分页
export function fetchUserPage(params: any) {
  return request({
    url: '/user/page',
    method: 'get',
    params
  });
}

// 创建用户
export function createUser(data: any) {
  return request({
    url: '/user/create',
    method: 'post',
    data
  });
}

// 更新用户
export function updateUser(data: any) {
  return request({
    url: '/user/update',
    method: 'put',
    data
  });
}

// 批量删除用户
export function removeUserBatchByIds(data: any) {
  return request({
    url: '/user/delete',
    method: 'delete',
    data
  });
}

// 批量创建用户
export function createBatchUsers(data: any) {
  return request({
    url: '/user/batchCreate',
    method: 'post',
    data
  });
}

// 切换用户状态
export function changeUserStatus(params: any) {
  return request({
    url: '/user/changeStatus',
    method: 'put',
    params
  });
}

// 重置用户密码
export function resetUserPassword(id: number | string) {
  return request({
    url: '/user/reset-pwd',
    method: 'post',
    params: { id }
  });
}

// 补偿时长
export function compensateUserTime(params: any) {
  return request({
    url: '/user/compensate',
    method: 'post',
    params
  });
}

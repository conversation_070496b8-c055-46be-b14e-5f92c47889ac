package org.seven.share.common.util;

import java.util.Random;

/**
 * @ClassName: CarIdUtils
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/20
 */
public class CarIdUtils {
    public static String generatorCarID() {
        final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        final int LENGTH = 8;
        Random random = new Random();
        StringBuilder sb = new StringBuilder(LENGTH);
        for (int i = 0; i < LENGTH; i++) {
            int index = random.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(index));
        }
         return sb.toString();
    }
}

interface I18nSchema {
  page: {
    login: {
      common: {
        loginOrRegister: string;
        userNamePlaceholder: string;
        phonePlaceholder: string;
        codePlaceholder: string;
        codeLengthError: string;
        passwordPlaceholder: string;
        confirmPasswordPlaceholder: string;
        codeLogin: string;
        confirm: string;
        back: string;
        validateSuccess: string;
        loginSuccess: string;
        welcomeBack: string;
      };
      // ... existing code ...
    };
    // ... existing code ...
  };
  // ... existing code ...
} 
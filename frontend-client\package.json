{"name": "share-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fingerprintjs/fingerprintjs": "^4.5.1", "@vueuse/core": "^10.11.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.7.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "default-passive-events": "^2.0.0", "echarts": "^5.5.1", "element-plus": "^2.7.6", "js-cookie": "^3.0.5", "lucide-vue-next": "^0.454.0", "nprogress": "^0.2.0", "pinia": "^2.2.2", "pinia-plugin-persist": "^1.0.0", "pinia-plugin-persistedstate": "^3.2.1", "vue": "^3.4.29", "vue-i18n": "10.0.0-beta.5", "vue-qrcode": "^2.2.2", "vue-router": "4"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "sass": "^1.77.8", "tailwindcss": "^3.4.4", "unplugin-icons": "^0.19.2", "vite": "^5.3.1", "vite-plugin-svg-icons": "^2.0.1"}, "postcss": {"plugins": {"tailwindcss": {}, "autoprefixer": {}}}}
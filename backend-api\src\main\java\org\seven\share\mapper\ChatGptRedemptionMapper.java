package org.seven.share.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.seven.share.common.pojo.dto.ChatGptRedemptionDto;
import org.seven.share.common.pojo.entity.ChatGptRedemptionEntity;
import org.seven.share.common.pojo.vo.RedemptionHistoryVo;


import java.util.List;

/**
 * @InterfaceName: ChatGptRedemptionDao
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/28
 */
@Mapper
public interface ChatGptRedemptionMapper extends BaseMapper<ChatGptRedemptionEntity> {
    IPage<ChatGptRedemptionDto> selectPageWithJoin(Page<?> page, @Param("key") String key,
                                                   @Param("userToken") String userToken,
                                                   @Param("status") Integer status);

    List<RedemptionHistoryVo> listRedemptionHistory(@Param("userId") Long userId);
}

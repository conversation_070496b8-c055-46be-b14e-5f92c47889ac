package org.seven.share.controller.admin;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.api.R;
import org.seven.share.common.api.Result;
import org.seven.share.common.pojo.entity.ClaudeSessionEntity;
import org.seven.share.common.pojo.vo.ClaudeCarInfoVo;
import org.seven.share.service.ClaudeSessionService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: ClaudeSessionController
 * @Description: Claude sess控制器
 * @Author: Seven
 * @Date: 2024/8/12
 */
@RestController
@RequestMapping("/expander-api/claude")
public class ClaudeSessionController {
    @Resource
    private ClaudeSessionService claudeSessionService;

    @GetMapping("/page")
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer current,
                  @RequestParam(value = "size", defaultValue = "10") Integer size,
                  String query,
                  String sortProp,
                  String sortOrder){
        // 创建查询条件封装器
        LambdaQueryWrapper<ClaudeSessionEntity> queryWrapper = new LambdaQueryWrapper<>();
        // 如果查询关键字不为空，则根据用户令牌或邮箱进行查询
        if (StrUtil.isNotBlank(query)) {
            queryWrapper.like(ClaudeSessionEntity::getCarID, query).or().like(ClaudeSessionEntity::getEmail, query);
        }
        // 设置删除时间为null，即未删除
        queryWrapper.isNull(ClaudeSessionEntity::getDeletedAt);
        // 根据前端传过来的字段排序
        if (StrUtil.isNotEmpty(sortProp)) {
            queryWrapper.last("order by " + sortProp + " " + sortOrder);
        }
        // 执行分页查询
        Page<ClaudeSessionEntity> pageInfo = claudeSessionService.page(new Page<>(current,size), queryWrapper);
        // 返回分页查询结果
        return R.ok(pageInfo);
    }


    @GetMapping("/{id}")
    public R getClaudeSession(@PathVariable("id") Long id) {
        ClaudeSessionEntity claudeSession = claudeSessionService.getById(id);
        ClaudeCarInfoVo claudeCarInfoVo = new ClaudeCarInfoVo();
        claudeCarInfoVo.setId(claudeSession.getId());
        claudeCarInfoVo.setCarID(claudeSession.getCarID());
        claudeCarInfoVo.setStatus(claudeSession.getStatus());
        return R.ok(claudeCarInfoVo);
    }

    @PostMapping("/create")
    public R create(@RequestBody ClaudeSessionEntity claudeSession) {
        claudeSessionService.saveClaudeSession(claudeSession);
        return R.ok(claudeSession);
    }

    @PutMapping("/update")
    public R update(@RequestBody ClaudeSessionEntity claudeSession) {
        claudeSessionService.updateClaudeSession(claudeSession);
        return R.ok(claudeSession);
    }

    @DeleteMapping("/delete")
    public Result<?> delete(@RequestBody List<String> ids) {
        claudeSessionService.removeCarInfoBatch(ids);
        return Result.success();
    }


    /**
     * 自动生成claude车号
     * @return 车号
     */
    @GetMapping("/getCarID")
    public R getCarID()  {
        return R.ok(claudeSessionService.generatorCarID());
    }

}

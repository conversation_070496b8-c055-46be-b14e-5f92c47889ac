package org.seven.share.controller.admin;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.R;
import org.seven.share.common.api.Result;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.dto.BatchSaveRequest;
import org.seven.share.common.pojo.entity.GrokSessionEntity;

import org.seven.share.service.GrokSessionService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: GrokSessionController
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/1
 */
@RestController
@RequestMapping("/expander-api/grok/session")
public class GrokSessionController {

    @Resource
    private GrokSessionService grokSessionService;

    @GetMapping("/page")
    @SysLogInterface(title = "分页查询grok账号信息", businessType = BusinessType.QUERY)
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer current,
                  @RequestParam(value = "size", defaultValue = "10") Integer size,
                  String query){

        // 执行分页查询
        LambdaQueryWrapper<GrokSessionEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotEmpty(query), GrokSessionEntity::getEmail, query);
        Page<GrokSessionEntity> pageInfo = grokSessionService.page(new Page<>(current, size), wrapper);
        // 返回分页查询结果
        return R.ok(pageInfo);
    }

    @PostMapping("/batch/save")
    @SysLogInterface(title = "批量新增gpt账号", businessType = BusinessType.INSERT)
    public R create(@RequestBody BatchSaveRequest request) {
//        grokSessionService.saveAccessTokenBatch(request.getAccounts(), request.getOptType());
        return R.ok();
    }

    @PostMapping("/create")
    @SysLogInterface(title = "新增grok账号信息", businessType = BusinessType.INSERT)
    public R create(@RequestBody GrokSessionEntity grokSession) {
        grokSessionService.insertAccessToken(grokSession);
        return R.ok(grokSession);
    }


    @PutMapping("/update")
    @SysLogInterface(title = "修改grok账号信息", businessType = BusinessType.UPDATE)
    public R update(@RequestBody GrokSessionEntity grokSession) {
        grokSessionService.updateAccessToken(grokSession);
        return R.ok(grokSession);
    }

    @DeleteMapping("/delete")
    @SysLogInterface(title = "批量删除grok账号信息", businessType = BusinessType.DELETE)
    public Result<?> delete(@RequestBody List<String> ids) {
        grokSessionService.removeGrokSessionBatch(ids);
        return Result.success();
    }

}

package org.seven.share.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.seven.share.common.enums.CacheConstants;
import org.seven.share.common.pojo.entity.SysUser;
import org.seven.share.common.util.SecurityUtil;
import org.seven.share.service.LicenseValidator;
import org.seven.share.service.SysTenantService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static org.seven.share.common.util.ConstantUtil.SYS_TENANT_ID;
import static org.seven.share.common.util.ServletUtils.getHost;

/**
 * @ClassName: MybatisPlusConfig
 * @Description:
 * @Author: Seven
 * @Date: 2024/6/22
 */
@Slf4j
@Configuration
@MapperScan("org.seven.share.mapper")
public class MybatisPlusConfig {

    private static final List<String> TENANT_TABLES = Arrays.asList("chatgpt_sys_notice","chatgpt_user","chatgpt_subtype");

    @Lazy
    @Resource
    private SysTenantService sysTenantService;

    @Lazy
    @Resource
    private LicenseValidator licenseValidator;

    // 新增租户拦截器配置
    @Lazy
    @Bean
    public TenantLineInnerInterceptor tenantLineInnerInterceptor() {
        return new TenantLineInnerInterceptor(new TenantLineHandler() {
            @Override
            public Expression getTenantId() {
                try {
                    // 获取当前请求，判断是否前台请求，如果是，根据域名查找租户id，如果租户id不存在则显示主站的内容
                    HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
                    boolean isFrontRequest = request.getRequestURI().contains("/client-api/");

                    // 如果是前端请求，则需要根据host来隔离数据
                    if (isFrontRequest) {
                        String tenantIdByHost = sysTenantService.getTenantIdByHost(getHost());
                        if (StringUtils.isNotBlank(tenantIdByHost)) {
                            log.debug("前端请求，使用域名租户ID: {}", tenantIdByHost);
                            return new StringValue(tenantIdByHost);
                        }
                        // 前端请求但没有找到租户ID，返回系统租户ID
                        log.debug("前端请求但未找到域名租户ID，使用系统租户ID: {}", SYS_TENANT_ID);
                        return new StringValue(SYS_TENANT_ID);
                    } else {
                        // 如果是后台，没有租户id的用户返回系统租户ID以确保数据安全
                        SysUser sysUser = SecurityUtil.getSysUser();
                        if (sysUser == null) {
                            log.debug("后台请求但无法获取当前用户信息，使用系统租户ID: {}", SYS_TENANT_ID);
                            return new StringValue(SYS_TENANT_ID);
                        }
                        String tenantId = sysUser.getTenantId();
                        if (StringUtils.isBlank(tenantId)) {
                            log.debug("后台请求，用户 {} 没有租户ID，使用系统租户ID: {}", sysUser.getUserName(), SYS_TENANT_ID);
                            return new StringValue(SYS_TENANT_ID);
                        }
                        log.info("租户插件生效 - 后台请求，用户 {} 使用租户ID: {}", sysUser.getUserName(), tenantId);
                        return new StringValue(tenantId);
                    }
                } catch (Exception e) {
                    log.error("获取租户ID时发生异常，使用系统租户ID {}: {}", SYS_TENANT_ID, e.getMessage());
                    return new StringValue(SYS_TENANT_ID);
                }
            }

            @Override
            public String getTenantIdColumn() {
                return "tenant_id"; // 数据库租户字段名
            }

            @Override
            public boolean ignoreTable(String tableName) {
                try {
                    log.debug("检查表 {} 是否需要租户隔离", tableName);

                    // 未开启租户模式，跳过数据隔离
                    if (!licenseValidator.haveAccess("tenant")) {
                        log.debug("租户模式未开启，跳过表 {} 的租户隔离", tableName);
                        return true;
                    }

                    SysUser sysUser = SecurityUtil.getSysUser();
                    if (sysUser != null) {
                        String tenantId = sysUser.getTenantId();
                        // 管理员跳过所有表的租户过滤
                        if (SYS_TENANT_ID.equals(tenantId)) {
                            log.debug("管理员用户，跳过表 {} 的租户隔离", tableName);
                            return true;
                        }
                    }

                    // 只有租户表才需要隔离
                    boolean needTenantFilter = TENANT_TABLES.contains(tableName);
                    if (needTenantFilter) {
                        log.info("租户插件生效 - 表 {} 需要租户隔离", tableName);
                    } else {
                        log.debug("表 {} 不需要租户隔离", tableName);
                    }
                    return !needTenantFilter;
                } catch (Exception e) {
                    log.error("检查表 {} 租户隔离时发生异常，跳过租户过滤: {}", tableName, e.getMessage());
                    return true;
                }
            }
        });
    }


    /**
     * 添加分页、多租户插件
     */
    @Lazy
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
         MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 多租户插件必须作为第一个插件
         interceptor.addInnerInterceptor(tenantLineInnerInterceptor());

        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL)); // 如果配置多个插件, 切记分页最后添加
        return interceptor;
    }

}

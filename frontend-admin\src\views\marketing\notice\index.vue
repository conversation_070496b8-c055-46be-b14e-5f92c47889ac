<script setup lang="tsx">
import { ref } from 'vue';
import { <PERSON><PERSON><PERSON><PERSON>, El<PERSON>opconfirm, ElTag } from 'element-plus';
import { useBoolean } from '@sa/hooks';
import { fetchNoticePage, removeNoticeBatchByIds } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import { usePermission } from '@/hooks/business/auth';
import NoticeOperateModel, { type OperateType } from './modules/notice-operate-modal.vue';
const { hasPermission } = usePermission();

// Define the proper type for your table data
interface Notice {
  id: string;
  content: string;
  publishTime: string;
  type: string;
}

const wrapperRef = ref<HTMLElement | null>(null);
const operateType = ref<OperateType>('create');
const currentNoticeId = ref<string>('');
const { bool: visible, setTrue: openModal } = useBoolean();

const noticeTypeDict: Record<string, { label: string; type: string }> = {
  gptSite: { label: 'GPT站内公告', type: 'success' },
  claudeSite: { label: 'Claude站内公告', type: 'primary' },
  grokSite: { label: 'Grok站内公告', type: 'warning' },
  drawSite: { label: 'Draw站内公告', type: 'danger' },
  system: { label: '系统通知', type: 'info' },
  script: { label: '前台脚本', type: 'danger' }
};

const { columns, columnChecks, data, loading, pagination, getData, getDataByPage } = useTable<UI.TableApiFn<Notice>>({
  apiFn: fetchNoticePage,
  columns: () => [
    { type: 'selection', width: 48 },
    { prop: 'index', label: $t('common.index'), width: 64 },
    {
      prop: 'type',
      label: '公告类型',
      width: 120,
      formatter: (row: Notice) => {
        const dict = noticeTypeDict[row.type] || { label: row.type, type: 'default' };
        return <ElTag type={dict.type}>{dict.label}</ElTag>;
      }
    },
    { prop: 'content', label: '公告内容', minWidth: 300, showOverflowTooltip: true },
    { prop: 'tenantName', label: '所属分站', minWidth: 120, showOverflowTooltip: true },
    { prop: 'publishTime', label: '发布时间', minWidth: 180, showOverflowTooltip: true },
    { prop: 'createTime', label: '创建时间', width: 180 },
    { prop: 'updateTime', label: '更新时间', width: 180 },
    {
      prop: 'operate',
      label: $t('common.operate'),
      align: 'center',
      fixed: 'right',
      minWidth: '120',
      formatter: (row: any) => (
        <div class="flex-center">
          {hasPermission('notice/update') && (
            <ElButton type="primary" plain size="small" onClick={() => handleEdit(String(row.id))}>
              {$t('common.edit')}
            </ElButton>
          )}
          <ElPopconfirm title={$t('common.confirmDelete')} onConfirm={() => handleDelete(String(row.id))}>
            {{
              reference: () => (
                <ElButton type="danger" plain size="small" v-show={hasPermission('notice/delete')}>
                  {$t('common.delete')}
                </ElButton>
              )
            }}
          </ElPopconfirm>
        </div>
      )
    }
  ]
});

const { checkedRowKeys, onBatchDeleted, onDeleted } = useTableOperate(data as any, getData);

async function handleDelete(id: string) {
  await removeNoticeBatchByIds([id]);
  onDeleted();
}

async function handleBatchDelete() {
  await removeNoticeBatchByIds(checkedRowKeys.value.map(String));
  onBatchDeleted();
}

function handleAdd() {
  operateType.value = 'create';
  currentNoticeId.value = '';
  openModal();
}

function handleEdit(id: string) {
  operateType.value = 'edit';
  currentNoticeId.value = id;
  openModal();
}
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <div class="flex items-center">
            <TableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              :show-add="hasPermission('notice/create')"
              :show-delete="hasPermission('notice/delete')"
              @add="handleAdd"
              @delete="handleBatchDelete"
              @refresh="getData"
            />
          </div>
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="(selection: Notice[]) => checkedRowKeys = selection.map(item => String(item.id))"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
      <NoticeOperateModel
        v-model:visible="visible"
        :operate-type="operateType"
        :notice-id="currentNoticeId"
        @submitted="getDataByPage"
      />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>

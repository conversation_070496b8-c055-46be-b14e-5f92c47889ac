<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.seven.share.mapper.DrawRecordMapper">
    <update id="updateDrawTimeOut">
        UPDATE draw_record
        SET task_status = 'FAILED',
            description = '绘图失败',
            updated_at = NOW()
        WHERE created_at &lt; NOW() - INTERVAL 15 MINUTE
          AND task_status NOT IN ('FAILED', 'COMPLETED'); -- 可选，防止重复更新
    </update>

    <select id="selectDrawRecodePage" resultType="org.seven.share.common.pojo.entity.DrawRecordEntity">
        SELECT dr.*, u.userToken as username
        FROM draw_record dr
                 LEFT JOIN chatgpt_user u ON dr.user_id = u.id
            ${ew.customSqlSegment}
    </select>
</mapper>

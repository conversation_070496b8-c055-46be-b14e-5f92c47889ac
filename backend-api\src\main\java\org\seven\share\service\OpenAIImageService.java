package org.seven.share.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.enums.DrawType;
import org.seven.share.common.enums.QuotaChangeType;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.pojo.entity.DrawRecordEntity;
import org.seven.share.common.pojo.entity.QuotaChangeRecordEntity;
import org.seven.share.common.pojo.image.ImageEditRequest;
import org.seven.share.common.pojo.image.ImageGenerationRequest;
import org.seven.share.common.pojo.image.ImageGenerationResponse;
import org.seven.share.common.pojo.image.TaskStatus;
import org.seven.share.common.util.JwtUtil;
import org.seven.share.config.ImageTaskCache;

import org.seven.share.mapper.CreateTableMapper;
import org.seven.share.mapper.DrawRecordMapper;
import org.seven.share.mapper.QuotaChangeRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

import static org.seven.share.common.util.ConstantUtil.AUTH_HEADER;
import static org.seven.share.common.util.HttpUtils.getBaseUrlFromRequest;
import static org.seven.share.constant.TaskStatusConstants.*;

@Slf4j
@Service
public class OpenAIImageService {

    private String apiKey;

    private String apiUrl;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ApplicationContext context;

    @Resource
    private CreateTableMapper createTableDao;

    @Resource
    private ObjectMapper objectMapper;
    @Autowired
    private QuotaChangeRecordMapper quotaChangeRecordMapper;

    @PostConstruct
    public void init() {
        createTableDao.createDrawRecord();
        createTableDao.createQuotaChangeRecord();
    }

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private DrawRecordMapper drawRecordMapper;


    @Resource
    private ImageTaskCache imageTaskCache;

    @Resource
    private UserDrawingQuotaService quotaService;

    @Resource
    private HttpServletRequest httpRequest;

    private String domain;

    private void getApiKeyAndUrl() {
        Map<String, String> openaiConfig = chatGptConfigService.getKeyValueMapByKeys(Arrays.asList("apiKey", "apiUrl"));
        apiKey = openaiConfig.get("apiKey");
        apiUrl = openaiConfig.get("apiUrl");
        if (StrUtil.isEmpty(apiKey)) {
            throw new CustomException("管理员还未配置apiKey");
        }
        if (StrUtil.isEmpty(apiUrl)) {
            apiUrl = "https://api.openai.com";
        }
    }

    /**
     * 创建图片
     * @param request
     * @return
     */
    public String submitImageGenerationTask(ImageGenerationRequest request, HttpServletRequest servletRequest) {
        log.info("submitImageGenerationTask start");
        String accessToken = servletRequest.getHeader(AUTH_HEADER);
        Long uid = JwtUtil.getUid(accessToken);
        // 获取api key 配置
        getApiKeyAndUrl();
        String taskId = System.currentTimeMillis() + "";
        TaskStatus taskStatus = new TaskStatus(taskId, PENDING);

        // 消耗1次画图
        quotaService.consumeUserDrawQuota(uid, request.getN());

        // 生成画图记录
        DrawRecordEntity draw = new DrawRecordEntity();
        draw.setDrawType(DrawType.GEN.getCode());
        draw.setUserId(uid);
        draw.setPrompt(request.getPrompt());
        draw.setTaskId(taskId);
        draw.setModel(request.getModel());
        draw.setTaskStatus(PENDING);
        draw.setNum(request.getN());
        draw.setImageSize(request.getSize());
        draw.setResponseFormat(request.getResponseFormat());
        draw.setDescription("正在生图中...");
        // 入库
        drawRecordMapper.insert(draw);
        // 保存缓存
        imageTaskCache.saveTask(taskId, taskStatus);
        // 异步绘图
        OpenAIImageService contextBean = context.getBean(OpenAIImageService.class);
        domain = getBaseUrlFromRequest(httpRequest);
        contextBean.generateImageAsync(taskId, request);
        return taskId;
    }

    @Async("commonAsyncExecutor")
    public void generateImageAsync(String taskId, ImageGenerationRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("generateImageAsync submit, taskId: {}", taskId);
        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + apiKey);

            // 创建请求实体
            HttpEntity<ImageGenerationRequest> requestEntity = new HttpEntity<>(request, headers);

            // 发送 POST 请求
            ResponseEntity<ImageGenerationResponse> response = restTemplate.exchange(
                    apiUrl + "/v1/images/generations",
                    HttpMethod.POST,
                    requestEntity,
                    ImageGenerationResponse.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.info("Open AI response success: {}", response.getBody());
                updateDrawRecord(taskId, COMPLETED, response.getBody(), null);
            } else {
                log.error("OpenAI API returned non-success status for task response taskId:{} response{}", taskId, response.getBody());
                updateDrawRecord(taskId, FAILED, null, String.valueOf(response.getBody()));
            }
        } catch (Exception e) {
            log.error("Error processing task {}: {}", taskId, e.getMessage());
            updateDrawRecord(taskId, FAILED, null, "OpenAI API returned status: " + e.getMessage());
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            log.info("generateImageAsync request finish, taskId: {}, duration: {} s", taskId, (duration / 1000));
        }
    }

    /**
     * 更新绘图状态
     * @param taskId 任务ID
     * @param status 任务状态
     * @param response 成功时的响应
     * @param errorMsg 失败时的错误信息
     */
    private void updateDrawRecord(String taskId, String status, ImageGenerationResponse response, String errorMsg) {
        // 查询任务id是否存在
        DrawRecordEntity record = drawRecordMapper.selectOne(new LambdaQueryWrapper<DrawRecordEntity>().eq(StringUtils.isNotEmpty(taskId), DrawRecordEntity::getTaskId, taskId));
        if (ObjectUtil.isEmpty(record)) {
            throw new CustomException("绘图任务查询失败");
        }
        StringBuilder imageUrlStr = new StringBuilder();
        if (COMPLETED.equals(status) && response != null && response.getData() != null && !response.getData().isEmpty()) {
            // 处理图片的下载
            List<ImageGenerationResponse.ImageData> imageDataList = new ArrayList<>();
            for (ImageGenerationResponse.ImageData image : response.getData()) {
                // 处理URL格式图片
                String accessibleUrl = null;
                if (StringUtils.isNotBlank(image.getUrl())) {
                    String imageUrl = image.getUrl();

                    // 下载图片并保存到本地
                    String localPath = downloadImageFromUrl(imageUrl, taskId);
                    if (localPath != null) {
                        // 直接生成可访问的URL并替换原来的URL
                        accessibleUrl = convertToAccessibleUrl(localPath);

                    }
                }

                // 处理base64格式图片
                if (StringUtils.isNotBlank(image.getB64_json())) {
                    // 校验是不是为base64格式

                    // 保存base64图片到本地
                    String localPath = saveBase64Image(image.getB64_json(), taskId);
                    if (localPath != null) {
                        // 直接生成可访问的URL并替换原来的URL
                        accessibleUrl = convertToAccessibleUrl(localPath);
                    }
                }

                // 创建新的ImageData对象，使用可访问URL替换原始URL
                imageDataList.add(new ImageGenerationResponse.ImageData(accessibleUrl, null));
                imageUrlStr.append(accessibleUrl)
                        .append(",");
            }
            imageUrlStr.deleteCharAt(imageUrlStr.length()-1);
            response.setData(imageDataList);
        }
        String jsonResponse;
        try {
            jsonResponse = objectMapper.writeValueAsString(response);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        record.setResponse(ObjectUtil.isNotEmpty(jsonResponse) ? jsonResponse : null);
        record.setImageUrl(imageUrlStr.toString());
        record.setUpdatedAt(LocalDateTime.now());
        record.setDescription(COMPLETED.equals(status)? "绘图完成" : "绘图失败");
        record.setTaskStatus(status);
        // 更新Redis缓存
        imageTaskCache.updateTaskStatus(taskId, status, response, errorMsg);

        drawRecordMapper.updateById(record);

        // 任务失败时返还用户额度
        if (!COMPLETED.equals(status)) {
            // 获取任务关联的用户ID
            Long userId = record.getUserId();
            if (ObjectUtil.isNotEmpty(userId)) {
                // 调用返还用户额度的方法
                refundUserDrawQuota(userId, record.getNum());
            }
        }
    }

    private void refundUserDrawQuota(Long userId, int num) {
        // 返还用户额度
        quotaService.refundUserDrawQuota(userId, num);
        // 新增一条返还明细
        QuotaChangeRecordEntity changeRecord = new QuotaChangeRecordEntity();
        changeRecord.setChangeAmount((long) num);
        changeRecord.setChangeType(QuotaChangeType.REFUND.getCode());
        changeRecord.setUserId(userId);
        changeRecord.setCreatedAt(LocalDateTime.now());
        changeRecord.setRemark("变更说明：绘图失败，额度返还");
        quotaChangeRecordMapper.insert(changeRecord);
    }

    /**
     * 将本地路径转换为可访问的URL
     */
    private String convertToAccessibleUrl(String localPath) {
        if (localPath == null) {
            return null;
        }

        try {
            // 获取日期部分和文件名
            File file = new File(localPath);
            String fileName = file.getName();

            // 获取包含日期的目录名
            String parentDir = file.getParentFile().getName(); // 这应该是yyyyMMdd格式的日期

            log.info("domain:{}", domain);
            // 构建相对URL路径 - 使用API路径
            return domain + "/expander-api/images/" + parentDir + "/" + fileName;
        } catch (Exception e) {
            log.error("Error converting local path to URL: {}", e.getMessage());
            return localPath; // 如果转换失败，至少返回原始路径
        }
    }

    /**
     * 从URL下载图片
     */
    private String downloadImageFromUrl(String imageUrl, String taskId) {
        String localPath = null;
        try {
            // 创建目录
            String dirPath = getDirPath();
            File dir = new File(dirPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成文件名
            String fileName = taskId + "_" + System.currentTimeMillis() + ".png";
            localPath = dirPath + File.separator + fileName;

            // 下载图片
            URL url = new URL(imageUrl);
            try (InputStream inputStream = url.openStream();
                 FileOutputStream outputStream = new FileOutputStream(localPath)) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            log.info("Image downloaded successfully from URL to {}", localPath);
            return localPath;
        } catch (IOException e) {
            log.error("Failed to download image from URL for task imageUrl:{}, taskId:{}, cause: {}", imageUrl, taskId, e.getMessage());
            return null;
        }
    }

    private static String getDirPath() {
        String dirPath = System.getProperty("user.dir")
                + File.separator
                + "data" + File.separator + "images"
                + File.separator + new SimpleDateFormat("yyyyMMdd").format(new Date());
        ;  // 相对于项目根目录
        return dirPath;
    }

    /**
     * 保存Base64编码的图片
     */
    private String saveBase64Image(String base64Image, String taskId) {
        String localPath;
        try {
            // 创建目录
            String dirPath = getDirPath();
            File dir = new File(dirPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成文件名
            String fileName = taskId + "_b64_" + System.currentTimeMillis() + ".png";
            localPath = dirPath + File.separator + fileName;

            // 解码Base64并保存图片
            byte[] imageBytes = Base64.getDecoder().decode(base64Image);
            try (FileOutputStream outputStream = new FileOutputStream(localPath)) {
                outputStream.write(imageBytes);
            }

            log.info("Image saved successfully from Base64 to {}", localPath);
            return localPath;
        } catch (IOException e) {
            log.error("Failed to save Base64 image for task {}: {}", taskId, e.getMessage(), e);
            return null;
        }
    }


    /**
     * 获取绘图状态
     * @param taskId
     * @return
     */
    public TaskStatus getTaskStatus(String taskId) {
        return imageTaskCache.getTask(taskId);
    }


    /**
     * 图片修改
     * @param request
     * @return
     */
    public String submitImageEditTask(ImageEditRequest request, HttpServletRequest servletRequest) {
        log.info("submitImageEditTask start");

        String taskId = System.currentTimeMillis() + "";
        TaskStatus taskStatus = new TaskStatus(taskId, PENDING);

        // 校验文件格式和大小
        try {
            request.validateFiles();
        } catch (IllegalArgumentException e) {
            throw new CustomException("File validation failed: " + e.getMessage());
        }
        // 获取api配置信息
        getApiKeyAndUrl();

        // 生成画图记录
        String accessToken = servletRequest.getHeader(AUTH_HEADER);
        Long uid = JwtUtil.getUid(accessToken);
        // 消耗次数
        quotaService.consumeUserDrawQuota(uid, request.getN());

        // 保存 MultipartFile 数据
        List<ImageFileData> imageFileDataList = new ArrayList<>();
        ImageFileData maskData = null;
        try {
            for (MultipartFile imageFile : request.getImages()) {
                // 使用 record 构造 ImageFileData
                ImageFileData fileData = new ImageFileData(imageFile.getOriginalFilename(), imageFile.getBytes());
                imageFileDataList.add(fileData);
            }
            // 读取mask文件
            MultipartFile mask = request.getMask();
            if (mask != null) {
                maskData = new ImageFileData(mask.getOriginalFilename(), mask.getBytes());
            }
        } catch (IOException e) {
            throw new CustomException("读取图片文件失败: " + e.getMessage());
        }

        // 调用异步方法
        OpenAIImageService contextBean = context.getBean(OpenAIImageService.class);
        domain = getBaseUrlFromRequest(httpRequest);
        contextBean.editImageAsync(taskId, request, uid, taskStatus, imageFileDataList, maskData);

        return taskId;
    }


    public record ImageFileData(String fileName, byte[] content) {
    }

    @Async("commonAsyncExecutor")
    public void editImageAsync(String taskId, ImageEditRequest request, Long uid, TaskStatus taskStatus, List<ImageFileData> imageFileDataList, ImageFileData maskData) {
        long startTime = System.currentTimeMillis();
        // 生成画图记录
        DrawRecordEntity draw = new DrawRecordEntity();
        draw.setDrawType(DrawType.EDIT.getCode());
        draw.setUserId(uid);
        draw.setPrompt(request.getPrompt());
        draw.setTaskId(taskId);
        draw.setModel(request.getModel());
        draw.setTaskStatus(PENDING);
        draw.setNum(request.getN());
        draw.setImageSize(request.getSize());
        draw.setResponseFormat(request.getResponseFormat());
        draw.setDescription("正在改图中...");
        drawRecordMapper.insert(draw);
        imageTaskCache.saveTask(taskId, taskStatus);
        log.info("editImageAsync submit, taskId: {}", taskId);
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.set("Authorization", "Bearer " + apiKey);

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("prompt", request.getPrompt());
            body.add("n", String.valueOf(request.getN()));
            body.add("size", request.getSize());
            body.add("model", request.getModel());
            body.add("quality", request.getQuality());
            // 使用保存的文件数据
            for (ImageFileData fileData : imageFileDataList) {
                body.add("image", new ByteArrayResource(fileData.content()) {
                    @Override
                    public String getFilename() {
                        return fileData.fileName();
                    }
                });
            }

            // 增加附加文件
            if (maskData != null) {
                body.add("mask", new ByteArrayResource(maskData.content()){
                    @Override
                    public String getFilename() {
                        return maskData.fileName();
                    }
                });
            }

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            ResponseEntity<ImageGenerationResponse> response = restTemplate.exchange(
                    apiUrl + "/v1/images/edits",
                    HttpMethod.POST,
                    requestEntity,
                    ImageGenerationResponse.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.info("Open AI response success: {}", response.getBody());
                updateDrawRecord(taskId, COMPLETED, response.getBody(), null);
            } else {
                log.error("OpenAI API returned non-success status for task {}: {}", taskId, response.getStatusCode());
                updateDrawRecord(taskId, FAILED, null, "OpenAI API returned status: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error processing task {}: {}", taskId, e.getMessage());
            updateDrawRecord(taskId, FAILED, null, "OpenAI API returned status: " + e.getMessage());
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            log.info("generateImageAsync request finish, taskId: {}, duration: {} s", taskId, (duration / 1000));
        }
    }

    public IPage<DrawRecordEntity> pageDrawRecord(int page, int size, String prompt, String username) {
        LambdaQueryWrapper<DrawRecordEntity> wrapper = new LambdaQueryWrapper<DrawRecordEntity>()
                .like(StrUtil.isNotEmpty(prompt), DrawRecordEntity::getPrompt, prompt)
                .orderByDesc(DrawRecordEntity::getCreatedAt)
                .and(StrUtil.isNotEmpty(username), w -> w.apply("u.userToken LIKE {0}", "%" + username + "%"));
        return drawRecordMapper.selectDrawRecodePage(new Page<>(page, size), wrapper);
    }

    public void removeByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        drawRecordMapper.deleteByIds(ids);
    }

    public List<DrawRecordEntity> listDrawRecordByUid(Long uid) {
        return drawRecordMapper.selectList(new LambdaQueryWrapper<DrawRecordEntity>()
                .eq(DrawRecordEntity::getUserId, uid)
                .orderByDesc(DrawRecordEntity::getCreatedAt));
    }
}

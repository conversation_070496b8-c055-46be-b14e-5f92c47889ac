package org.seven.share.mapper;

import org.apache.ibatis.annotations.Mapper;


/**
 * @InterfaceName: CreateTableDao
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/19
 */
@Mapper
public interface CreateTableMapper {
    void createCouponTable();

    void createPayLogsTable();

    void createPayConfigTable();

    void createSubTypeTable();

    void createSysNoticeTable();

    void createClaudeSessionTable();

    void createSensitiveWordTable();

    void createChatGptConfigTable();

    void createChatGptRedemptionTable();

    void createWithdrawalsTable();

    void createSignInRecordsTable();

    void createChatGptAffRecordTable();

    void addUserTokenIndex();

    void createRiskControlRecordTable();

    void addGptSessionColumn();

    void createDrawRecord();

    void createQuotaChangeRecord();

    void crateUserDrawQuota();

//    void addSupeTypeColumn();
    void addTenantIdColumn();

    void addDrawQuotaColumn();

    void addIsSuperColumn();

    void createSysTenantTable();

    void createGrokSessionTable();

    void createGrokConversationsTable();

    void createClaudeConversationsTable();

    void addClaudeSessionColumn();

    void addSysNoticeColumn();

    void createSysOperatorLogTable();

    void createSysResourceTable();

    void createSysRoleTable();

    void createSysRoleResourceTable();

    void createSysUserTable();

    void createSysUserRoleTable();

    void insertSysResource();

    void insertSysRoleResource();

    void insertSysUserRole();

    void insertSysRole();

    void insertSysUser();

    void addConfigKeyUnique();
}

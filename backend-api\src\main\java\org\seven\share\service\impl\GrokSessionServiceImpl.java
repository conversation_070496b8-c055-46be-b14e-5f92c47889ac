package org.seven.share.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.seven.share.common.pojo.dto.ChatGptSessionDto;
import org.seven.share.common.pojo.entity.GrokConversationEntity;
import org.seven.share.common.pojo.entity.GrokSessionEntity;
import org.seven.share.mapper.GrokConversationsMapper;
import org.seven.share.mapper.GrokSessionMapper;
import org.seven.share.service.GrokConversationsService;
import org.seven.share.service.GrokSessionService;
import org.seven.share.service.xyhelper.ApiService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: GrokSessionServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/11
 */
@Service
public class GrokSessionServiceImpl extends ServiceImpl<GrokSessionMapper, GrokSessionEntity>
        implements GrokSessionService {

    @Resource
    private ApiService apiService;

    @Override
    public Page<ChatGptSessionDto> getPage(Integer current, Integer size, String query, String sortProp, String sortOrder) {
        return null;
    }

    @Override
    public void updateAccessToken(GrokSessionEntity grokSession) {
        apiService.sendSaveOrUpdateRequest(grokSession, "update", "grok");
    }

    @Override
    public void removeGrokSessionBatch(List<String> ids) {
        apiService.sendDeleteRequest(ids, "grok");
    }

    @Override
    public void insertAccessToken(GrokSessionEntity grokSession) {
        apiService.sendSaveOrUpdateRequest(grokSession, "add", "grok");
    }
}

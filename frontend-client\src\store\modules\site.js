import { defineStore } from 'pinia';
import { fetchSiteData, fetchScript} from '@/api/config.js'

export const useSiteStore = defineStore('site', {
  state: () => ({
    siteName: '',
    enableRegister: '',
    customScriptContent: '',
    gptAnnouncement:'',
    claudeAnnouncement:'',
    grokAnnouncement:'',
    drawAnnouncement:'',
    userGuideUrl: '',
    fkAddress: '',
    freeNodes: '',
    enableClaudeNode: '',
    openWechat: '',
    freeNodeName: '',
    normalNodeName: '',
    plusNodeName: '',
    claudeNodeName: '',
    downloadUrl: '',
    affRate: '',
    enablePromotion: '',
    threshold: '',
    showVersion: '',
    enableBackNode: '',
    enableEmailCheck:'',
    enableNoLogin: '',
    themeName: '',
    logoUrl: '',     // 站点 Logo URL
    useBackNode: '',
    enableSiteShop: '',
    customerSidebarName: '',
    sidebarOpenType: '',
    showPaymentHistory: '',
    enableInvite: '',
    enableShowRemaining: '',
    enableInviteCode: '',
    enableSignIn: '',
    signInAnnouncement: '',
    fkAddressOpenType: '',
    userGuideUrlOpenType: '',
    enableCashbackForPaidUsers: '',
    collapseSidebar:'',
    closeCardExchange: '',
    showModelRate: '',
    closeSubTypeShow: '',
    sxClaudeUrl: '',
    enableSxClaude: '',
    backupSites: [],
    soruxGptSideBarUrl: '',
    soruxGptSideBarName: '',
    soruxGptSideBarOpenType: '',
    grokUrl: '',
    grokNodeName: '',
    grokOpenType:'',
    version: '',
    sassName :'',
    showSubTypeDetail: '',
    enableDraw: '',
    drawAnnouncement: '',
    drawNodeName: '',    // 绘图节点自定义名称
    nodeOrder: '', // 节点排序配置，可以是JSON字符串或数组
    drawCount:'',
    drawModel:'',
    enableBackupSitesForSubSite:'',
    enableSidebarForSubSite:'',
    enableUserGuideForSubSite: '',
    claudeUrl:'',
  }),
  actions: {
    async fetchSiteData () {
      try {
        // 先尝试从本地缓存加载（仅在首次加载时）
        const cachedData = localStorage.getItem('site');
        if (cachedData) {
          const parsedData = JSON.parse(cachedData);
          Object.assign(this, parsedData);
        }

        // 无论是否有缓存，都发起请求获取最新数据
        const data = await fetchSiteData();
        
        // 更新 store 数据
        Object.assign(this, data);
        
        // 更新本地缓存
        localStorage.setItem('site', JSON.stringify(data));
      } catch (error) {
        console.error('Failed to fetch site data:', error);
        throw error;
      }
    },
    async fetchScript () {
      const data = await fetchScript();
      const scriptContent = data.script;
      // 更新本地缓存
      localStorage.setItem('script', scriptContent);
    },
  },
  persist: {
    enabled: true
  },
});

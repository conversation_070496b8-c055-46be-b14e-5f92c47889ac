import { request } from '../request';

export const fetchGetTenantList = async () => {
  return request<Api.TenantManage.TenantList>({
    url: '/tenant/getTenantList',
    method: 'get'
  });
};

export const fetchGetTenantPage = async (params?: Api.TenantManage.TenantSearchParams) => {
  return request<Api.TenantManage.TenantList>({
    url: '/tenant/getTenantPage',
    method: 'get',
    params
  });
};

export const createTenant = async (data: any) => {
  return request({
    url: '/tenant/add',
    method: 'post',
    data
  });
};

export const updateTenant = async (data: any) => {
  return request({
    url: '/tenant/update',
    method: 'put',
    data
  });
};

export const fetchDeleteTenants = async (data: string | number) => {
  return request({
    url: `/tenant/delete/${data}`,
    method: 'delete',
    data
  });
};

export const fetchDeleteTenantByIds = async (data: string | number | Array<string | number>) => {
  return request({
    url: '/tenant/deleteByIds',
    method: 'delete',
    data
  });
};

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.seven.share.mapper.QuotaChangeRecordMapper">
    <select id="selectChangeRecordPage" resultType="org.seven.share.common.pojo.entity.QuotaChangeRecordEntity">
        SELECT r.id, r.created_at, r.user_id, r.change_type, r.change_amount, r.remark, u.userToken as username
        FROM quota_change_record r
        LEFT JOIN chatgpt_user u ON r.user_id = u.id
        ${ew.customSqlSegment}
    </select>
</mapper>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import {
  ElButton,
  ElCol,
  ElDescriptions,
  ElDescriptionsItem,
  ElDivider,
  ElForm,
  ElFormItem,
  ElIcon,
  ElImage,
  ElInputNumber,
  ElMessage,
  ElRadioGroup,
  ElRow,
  ElSelect,
  ElSwitch,
  ElTabPane,
  ElTabs,
  ElTooltip,
  ElUpload
} from 'element-plus';
import { Delete, InfoFilled, Plus, Upload } from '@element-plus/icons-vue';
import type { Ref } from 'vue';
import { fetchFormData, fetchLicenseInfo, fetchSaveFormData } from '@/service/api/config';
import { getToken } from '@/store/modules/auth/shared';
import HtmlEditor from '@/components/html-editor/HtmlEditor.vue';

interface BackupSite {
  name: string;
  url: string;
  openType: string;
  iconUrl: string;
}

interface FormData {
  siteName: string;
  logoUrl: string;
  theme: string;
  enableGuest: boolean;
  enableRegister: boolean;
  enablePlus: boolean;
  enableSSO: boolean;
  enableEmailVerify: boolean;
  enablePromotion: boolean;
  smtpServer: string;
  smtpPort: number;
  smtpUsername: string;
  smtpPassword: string;
  smtpSender: string;
  loginAnnouncement: string;
  siteAnnouncement: string;
  backupSites: BackupSite[];
  enableBackupNode: boolean;
  nodeOrder: string;
  freeNodeCount: number;
  enableCashback: boolean;
  cashbackThreshold: number;
  cashbackTime: number;
  enableSignIn: boolean;
  enablePlusSignIn: boolean;
  enableFreeSignIn: boolean;
  signInTime: number;
  enableDraw: boolean;
  virtualNo: number;
  virtualTeamNo: number;
  virtualProNo: number;
  virtualClaudeNo: number;
  virtualClaudeMaxNo: number;
  virtualClaudeMaxNameList: string;
  delivery: boolean;
  enableSxClaude: boolean;
  openWechat: boolean;
  enableSiteShop: boolean;
  enableEmailCheck: boolean;
  emailNotification: boolean;
  affRate: number;
  enableCalcCarCodes: boolean;
  showVersion: boolean;
  enableBackNode: boolean;
  enableNoLogin: boolean;
  visitorLimit: number;
  enableExpirationReminder: boolean;
  visitorUsePlus: boolean;
  enablePaySuccessNotice: boolean;
  useBackNode: boolean;
  showPaymentHistory: boolean;
  enableInvite: boolean;
  enableShowRemaining: boolean;
  enableCashbackForPaidUsers: boolean;
  enableNoSelectCar: boolean;
  enableInviteCode: boolean;
  enableRepeatRegister: boolean;
  maxDevices: number;
  saveLogDays: number;
  threshold: number;
  affTime: number;
  freeTime: number;
  usageCount: number;
  freeNodes: number;
  collapseSidebar: boolean;
  closeCardExchange: boolean;
  showModelRate: boolean;
  closeSubTypeShow: boolean;
  signInAnnouncement: string;
  drawAnnouncement: string;
  drawCount: number;
  signInType: string;
  apiUrl: string;
  apiKey: string;
  themeName: string;
  visitorUsagePeriod: string;
  siteShopName: string;
  claudeUrl: string;
  sxClaudeUrl: string;
  thirdClaudeType: string;
  userGuideUrl: string;
  userGuideUrlOpenType: string;
  fkAddress: string;
  fkAddressOpenType: string;
  customerSidebarName: string;
  customerSidebarUrl: string;
  sidebarOpenType: string;
  email: string;
  emailPassword: string;
  emailHost: string;
  emailWhitelist: string;
  emailPort: string;
  customScriptContent: string;
  virtualNameList: string;
  virtualTeamNameList: string;
  virtualProNameList: string;
  virtualClaudeNameList: string;
  grokNum: number;
  grokName: string;
  sassNum: number;
  sassName: string;
  modelLimits: string;
  dialogModelMultiplier: string;
  thirdModelLimits: string;
  freeNodeName: string;
  normalNodeName: string;
  plusNodeName: string;
  claudeNodeName: string;
  grokNodeName: string;
  grokUrl: string;
  grokOpenType: string;
  soruxGptSideBarName: string;
  soruxGptSideBarUrl: string;
  soruxGptSideBarOpenType: string;
  drawNodeName: string;
  backupNode: string;
  backupUrl: string;
  backupIconUrl: string;
  licenseCode: string;
  expireTime: string;
  drawModel: string;
  drawRegisterCount: number;
  backupOpenType: string;
  enableUserGuideForSubSite: boolean;
  enableSidebarForSubSite: boolean;
  enableBackupSitesForSubSite: boolean;
}

interface ApiResponse {
  code: number;
  data: {
    backupSites?: string;
    delivery?: string;
    enableSxClaude?: string;
    openWechat?: string;
    enableRegister?: string;
    enablePlus?: string;
    enableSiteShop?: string;
    enableSSO?: string;
    enableEmailCheck?: string;
    enablePromotion?: string;
    emailNotification?: string;
    affRate?: string;
    enableCalcCarCodes?: string;
    showVersion?: string;
    enableBackNode?: string;
    enableNoLogin?: string;
    visitorLimit?: string;
    enableExpirationReminder?: string;
    visitorUsePlus?: string;
    enablePaySuccessNotice?: string;
    useBackNode?: string;
    showPaymentHistory?: string;
    enableInvite?: string;
    enableShowRemaining?: string;
    enableCashbackForPaidUsers?: string;
    enableNoSelectCar?: string;
    enableInviteCode?: string;
    enableRepeatRegister?: string;
    maxDevices?: string;
    signInTime?: string;
    enableSignIn?: string;
    enablePlusSignIn?: string;
    enableFreeSignIn?: string;
    saveLogDays?: string;
    threshold?: string;
    affTime?: string;
    freeTime?: string;
    usageCount?: string;
    virtualNo?: string;
    virtualTeamNo?: string;
    virtualProNo?: string;
    virtualClaudeNo?: string;
    freeNodes?: string;
    collapseSidebar?: string;
    closeCardExchange?: string;
    showModelRate?: string;
    closeSubTypeShow?: string;
    nodeOrder?: string;
    enableDraw?: string;
    signInAnnouncement?: string;
    drawAnnouncement?: string;
    signInType?: string;
    apiUrl?: string;
    apiKey?: string;
    [key: string]: any;
  };
  message?: string;
}

const wrapperRef = ref<HTMLElement | null>(null);

interface UploadFile {
  type: string;
  size: number;
}

const importUrl = `${import.meta.env.VITE_SERVICE_BASE_URL}/common/upload-logo`;
const htmlEditorRef = ref<InstanceType<typeof HtmlEditor> | null>(null);
const currentEditType = ref<string>('');
const activeTab = ref('1');
const formData = ref<FormData>({
  siteName: '',
  logoUrl: '',
  theme: 'light',
  enableGuest: false,
  enableRegister: false,
  enablePlus: false,
  enableSSO: false,
  enableEmailVerify: false,
  enablePromotion: false,
  smtpServer: '',
  smtpPort: 465,
  smtpUsername: '',
  smtpPassword: '',
  smtpSender: '',
  loginAnnouncement: '',
  siteAnnouncement: '',
  backupSites: [],
  enableBackupNode: false,
  nodeOrder: '',
  freeNodeCount: 0,
  enableCashback: false,
  cashbackThreshold: 0,
  cashbackTime: 0,
  enableSignIn: false,
  enablePlusSignIn: false,
  enableFreeSignIn: false,
  signInTime: 0,
  enableDraw: false,
  virtualNo: 0,
  virtualTeamNo: 0,
  virtualProNo: 0,
  virtualClaudeNo: 0,
  virtualClaudeMaxNo: 0,
  virtualClaudeMaxNameList: '',
  delivery: false,
  enableSxClaude: false,
  openWechat: false,
  enableSiteShop: false,
  enableEmailCheck: false,
  emailNotification: false,
  affRate: 0,
  enableCalcCarCodes: false,
  showVersion: false,
  enableBackNode: false,
  enableNoLogin: false,
  visitorLimit: 0,
  enableExpirationReminder: false,
  visitorUsePlus: false,
  enablePaySuccessNotice: false,
  useBackNode: false,
  showPaymentHistory: false,
  enableInvite: false,
  enableShowRemaining: false,
  enableCashbackForPaidUsers: false,
  enableNoSelectCar: false,
  enableInviteCode: false,
  enableRepeatRegister: false,
  maxDevices: 0,
  saveLogDays: 0,
  threshold: 0,
  affTime: 0,
  freeTime: 0,
  usageCount: 0,
  freeNodes: 0,
  collapseSidebar: false,
  closeCardExchange: false,
  showModelRate: false,
  closeSubTypeShow: false,
  signInAnnouncement: '',
  drawAnnouncement: '',
  drawCount: 0,
  signInType: '1',
  apiUrl: '',
  apiKey: '',
  themeName: '',
  visitorUsagePeriod: '',
  siteShopName: '',
  claudeUrl: '',
  sxClaudeUrl: '',
  thirdClaudeType: '',
  userGuideUrl: '',
  userGuideUrlOpenType: '',
  fkAddress: '',
  fkAddressOpenType: '',
  customerSidebarName: '',
  customerSidebarUrl: '',
  sidebarOpenType: '',
  email: '',
  emailPassword: '',
  emailHost: '',
  emailWhitelist: '',
  emailPort: '',
  customScriptContent: '',
  virtualNameList: '',
  virtualTeamNameList: '',
  virtualProNameList: '',
  virtualClaudeNameList: '',
  grokNum: 0,
  grokName: '',
  sassNum: 0,
  sassName: '',
  modelLimits: '',
  dialogModelMultiplier: '',
  thirdModelLimits: '',
  freeNodeName: '',
  normalNodeName: '',
  plusNodeName: '',
  claudeNodeName: '',
  grokNodeName: '',
  grokUrl: '',
  grokOpenType: '',
  soruxGptSideBarName: '',
  soruxGptSideBarUrl: '',
  soruxGptSideBarOpenType: '',
  drawNodeName: '',
  backupNode: '',
  backupUrl: '',
  backupIconUrl: '',
  licenseCode: '',
  expireTime: '',
  drawModel: '',
  drawRegisterCount: 0,
  backupOpenType: '_blank',
  enableUserGuideForSubSite: false,
  enableSidebarForSubSite: false,
  enableBackupSitesForSubSite: false
});
const ruleFormRef = ref();

const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${getToken()}`
}));

const themes = [
  { value: 'base', label: '基础版' },
  { value: 'basic', label: '官方同款' }
];

const limits = [
  { value: '1s', label: '每秒' },
  { value: '1m', label: '每分' },
  { value: '1h', label: '每小时' },
  { value: '3h', label: '每3小时' },
  { value: '1d', label: '每天' },
  { value: '1w', label: '每周' },
  { value: '1y', label: '每年' }
];

const handleContentUpdate = (content: string) => {
  if (currentEditType.value === 'login') {
    formData.value.loginAnnouncement = content;
  } else if (currentEditType.value === 'site') {
    formData.value.siteAnnouncement = content;
  } else if (currentEditType.value === 'signin') {
    formData.value.signInAnnouncement = content;
  } else if (currentEditType.value === 'draw') {
    formData.value.drawAnnouncement = content;
  }
};

const beforeAvatarUpload = (rawFile: UploadFile) => {
  if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png') {
    ElMessage.error('图片格式要以jpg/png结尾');
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error('图片大小不能超过2MB，请重新选择!');
    return false;
  }
  return true;
};

const handleFileSuccess = (response: { code: string; data: string }) => {
  if (response.code === '200') {
    formData.value.logoUrl = response.data;
    window.$message?.success('上传成功');
  } else {
    window.$message?.error('上传失败');
  }
};

// Add this interface after the FormData interface
interface FormDataSubmit extends Omit<FormData, 'backupSites'> {
  backupSites: string;
  licenseCode: string;
}

const handleSubmit = async () => {
  // 先校验所有 JSON 字段
  const jsonFields: Array<'modelLimits' | 'dialogModelMultiplier' | 'thirdModelLimits'> = [
    'modelLimits',
    'dialogModelMultiplier',
    'thirdModelLimits'
  ];
  for (const field of jsonFields) {
    try {
      validateJson(field);
    } catch (e) {
      // 如果校验失败，直接阻止提交
      return;
    }
  }

  ruleFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const formDataToSubmit = { ...formData.value } as unknown as FormDataSubmit;
      // 构建所有备用站点，包括默认站点
      const allSites = [];

      // 添加默认站点（如果填写了名称和地址）
      if (formData.value.backupNode && formData.value.backupUrl) {
        allSites.push({
          name: formData.value.backupNode,
          url: formData.value.backupUrl,
          openType: formData.value.backupOpenType || '_blank',
          iconUrl: formData.value.backupIconUrl || ''
        });
      }

      // 添加动态备用站点
      allSites.push(...formData.value.backupSites.filter(site => site.name && site.url));

      const validSites = allSites.map(
        site => `{name=${site.name}, url=${site.url}, openType=${site.openType}, iconUrl=${site.iconUrl || ''}}`
      );

      formDataToSubmit.backupSites = `[${validSites.join(',')}]`;

      if (formDataToSubmit.nodeOrder) {
        try {
          JSON.parse(formDataToSubmit.nodeOrder);
        } catch {
          ElMessage.error('节点排序格式不正确，请检查JSON格式');
          return;
        }
      }

      const numericFields = [
        'affRate',
        'visitorLimit',
        'maxDevices',
        'signInTime',
        'saveLogDays',
        'threshold',
        'freeTime',
        'affTime',
        'usageCount',
        'virtualNo',
        'virtualTeamNo',
        'virtualProNo',
        'virtualClaudeNo',
        'freeNodes'
      ];

      numericFields.forEach(field => {
        if (field in formDataToSubmit) {
          formDataToSubmit[field as keyof FormDataSubmit] = formDataToSubmit[field as keyof FormDataSubmit] || '';
        }
      });

      await fetchSaveFormData(formDataToSubmit);
      await getLicenseInfo(); // Refresh license info after save
      ElMessage.success('保存成功');
    } else {
      ElMessage.warning('您的邮箱信息还未配置，新用户将无法注册。');
    }
  });
};

const parseBackupSites = (data: any): BackupSite[] => {
  let backupSites: BackupSite[] = [];
  try {
    if (data.backupSites) {
      const sitesStr = data.backupSites.replace(/^\[|\]$/g, '');
      if (sitesStr) {
        const siteMatches = sitesStr.match(/{[^}]+}/g) || [];
        backupSites = siteMatches.map((site: string) => {
          const nameMatch = site.match(/name=([^,}]+)/);
          const urlMatch = site.match(/url=([^,}]+)/);
          const openTypeMatch = site.match(/openType=([^,}]+)/);
          const iconUrlMatch = site.match(/iconUrl=([^,}]+)/);
          return {
            name: nameMatch ? nameMatch[1].trim() : '',
            url: urlMatch ? urlMatch[1].trim() : '',
            openType: openTypeMatch ? openTypeMatch[1].trim() : '_blank',
            iconUrl: iconUrlMatch ? iconUrlMatch[1].trim() : ''
          };
        });
      }
    }
  } catch {
    backupSites = [];
  }
  return backupSites;
};

// 解析默认备用站点
const parseDefaultBackupSite = (data: any) => {
  try {
    if (data.backupSites) {
      const sitesStr = data.backupSites.replace(/^\[|\]$/g, '');
      if (sitesStr) {
        const siteMatches = sitesStr.match(/{[^}]+}/g) || [];
        if (siteMatches.length > 0) {
          const firstSite = siteMatches[0];
          const nameMatch = firstSite.match(/name=([^,}]+)/);
          const urlMatch = firstSite.match(/url=([^,}]+)/);
          const openTypeMatch = firstSite.match(/openType=([^,}]+)/);
          const iconUrlMatch = firstSite.match(/iconUrl=([^,}]+)/);

          return {
            name: nameMatch ? nameMatch[1].trim() : '',
            url: urlMatch ? urlMatch[1].trim() : '',
            openType: openTypeMatch ? openTypeMatch[1].trim() : '_blank',
            iconUrl: iconUrlMatch ? iconUrlMatch[1].trim() : ''
          };
        }
      }
    }
  } catch {
    // 忽略错误
  }
  return null;
};

const parseBooleanField = (value: string): boolean => {
  return value === 'true';
};

const parseNumericField = (value: string, defaultValue: number = 0): number => {
  return value ? Number.parseInt(value, 10) : defaultValue;
};

const parseFloatField = (value: string, defaultValue: number = 0): number => {
  return value ? Number.parseFloat(value) : defaultValue;
};

const updateBooleanFields = (data: any) => ({
  delivery: parseBooleanField(data.delivery),
  enableSxClaude: parseBooleanField(data.enableSxClaude),
  openWechat: parseBooleanField(data.openWechat),
  enableRegister: parseBooleanField(data.enableRegister),
  enablePlus: parseBooleanField(data.enablePlus),
  enableSiteShop: parseBooleanField(data.enableSiteShop),
  enableSSO: parseBooleanField(data.enableSSO),
  enableEmailCheck: parseBooleanField(data.enableEmailCheck) || data.enableEmailCheck === '',
  enablePromotion: parseBooleanField(data.enablePromotion),
  emailNotification: parseBooleanField(data.emailNotification),
  enableCalcCarCodes: parseBooleanField(data.enableCalcCarCodes),
  showVersion: parseBooleanField(data.showVersion),
  enableBackNode: parseBooleanField(data.enableBackNode),
  enableNoLogin: parseBooleanField(data.enableNoLogin),
  enableExpirationReminder: parseBooleanField(data.enableExpirationReminder),
  visitorUsePlus: parseBooleanField(data.visitorUsePlus),
  enablePaySuccessNotice: parseBooleanField(data.enablePaySuccessNotice),
  useBackNode: parseBooleanField(data.useBackNode),
  showPaymentHistory: parseBooleanField(data.showPaymentHistory),
  enableInvite: parseBooleanField(data.enableInvite),
  enableShowRemaining: parseBooleanField(data.enableShowRemaining),
  enableCashbackForPaidUsers: parseBooleanField(data.enableCashbackForPaidUsers),
  enableNoSelectCar: parseBooleanField(data.enableNoSelectCar),
  enableInviteCode: parseBooleanField(data.enableInviteCode),
  enableRepeatRegister: parseBooleanField(data.enableRepeatRegister),
  enableSignIn: parseBooleanField(data.enableSignIn),
  enablePlusSignIn: parseBooleanField(data.enablePlusSignIn),
  enableFreeSignIn: parseBooleanField(data.enableFreeSignIn),
  collapseSidebar: parseBooleanField(data.collapseSidebar),
  closeCardExchange: parseBooleanField(data.closeCardExchange),
  showModelRate: parseBooleanField(data.showModelRate),
  closeSubTypeShow: parseBooleanField(data.closeSubTypeShow),
  enableDraw: parseBooleanField(data.enableDraw),
  enableUserGuideForSubSite: parseBooleanField(data.enableUserGuideForSubSite),
  enableSidebarForSubSite: parseBooleanField(data.enableSidebarForSubSite),
  enableBackupSitesForSubSite: parseBooleanField(data.enableBackupSitesForSubSite)
});

const updateNumericFields = (data: any) => ({
  affRate: parseFloatField(data.affRate),
  visitorLimit: parseNumericField(data.visitorLimit),
  maxDevices: parseNumericField(data.maxDevices),
  signInTime: parseNumericField(data.signInTime),
  saveLogDays: parseNumericField(data.saveLogDays),
  threshold: parseNumericField(data.threshold),
  affTime: parseNumericField(data.affTime),
  freeTime: parseNumericField(data.freeTime),
  usageCount: parseNumericField(data.usageCount),
  virtualNo: parseNumericField(data.virtualNo),
  virtualTeamNo: parseNumericField(data.virtualTeamNo),
  virtualProNo: parseNumericField(data.virtualProNo),
  virtualClaudeNo: parseNumericField(data.virtualClaudeNo),
  freeNodes: parseNumericField(data.freeNodes),
  grokNum: parseNumericField(data.grokNum),
  sassNum: parseNumericField(data.sassNum)
});

const updateStringFieldsPartOne = (data: any) => ({
  nodeOrder: data.nodeOrder || '',
  signInAnnouncement: data.signInAnnouncement || '',
  drawAnnouncement: data.drawAnnouncement || '',
  signInType: data.signInType || '1',
  apiUrl: data.apiUrl || '',
  apiKey: data.apiKey || '',
  themeName: data.themeName || '',
  visitorUsagePeriod: data.visitorUsagePeriod || '',
  siteShopName: data.siteShopName || '',
  claudeUrl: data.claudeUrl || '',
  sxClaudeUrl: data.sxClaudeUrl || '',
  thirdClaudeType: data.thirdClaudeType || '',
  userGuideUrl: data.userGuideUrl || '',
  userGuideUrlOpenType: data.userGuideUrlOpenType || '',
  fkAddress: data.fkAddress || '',
  fkAddressOpenType: data.fkAddressOpenType || '',
  customerSidebarName: data.customerSidebarName || '',
  customerSidebarUrl: data.customerSidebarUrl || '',
  sidebarOpenType: data.sidebarOpenType || ''
});

const updateStringFieldsPartTwo = (data: any) => ({
  email: data.email || '',
  emailPassword: data.emailPassword || '',
  emailHost: data.emailHost || '',
  emailWhitelist: data.emailWhitelist || '',
  emailPort: data.emailPort || '',
  customScriptContent: data.customScriptContent || '',
  virtualNameList: data.virtualNameList || '',
  virtualTeamNameList: data.virtualTeamNameList || '',
  virtualProNameList: data.virtualProNameList || '',
  virtualClaudeNameList: data.virtualClaudeNameList || '',
  grokName: data.grokName || '',
  sassName: data.sassName || '',
  modelLimits: data.modelLimits || '',
  dialogModelMultiplier: data.dialogModelMultiplier || '',
  thirdModelLimits: data.thirdModelLimits || '',
  freeNodeName: data.freeNodeName || '',
  normalNodeName: data.normalNodeName || '',
  plusNodeName: data.plusNodeName || ''
});

const updateStringFieldsPartThree = (data: any) => ({
  claudeNodeName: data.claudeNodeName || '',
  grokNodeName: data.grokNodeName || '',
  grokUrl: data.grokUrl || '',
  grokOpenType: data.grokOpenType || '',
  soruxGptSideBarName: data.soruxGptSideBarName || '',
  soruxGptSideBarUrl: data.soruxGptSideBarUrl || '',
  soruxGptSideBarOpenType: data.soruxGptSideBarOpenType || '',
  drawNodeName: data.drawNodeName || '',
  backupNode: data.backupNode || '',
  backupUrl: data.backupUrl || ''
});

const updateStringFields = (data: any) => ({
  ...updateStringFieldsPartOne(data),
  ...updateStringFieldsPartTwo(data),
  ...updateStringFieldsPartThree(data)
});

const updateFormData = (data: any) => {
  // 解析默认备用站点
  const defaultSite = parseDefaultBackupSite(data);

  formData.value = {
    ...formData.value,
    ...data,
    ...updateBooleanFields(data),
    ...updateNumericFields(data),
    ...updateStringFields(data),
    // 设置默认站点信息
    backupNode: defaultSite ? defaultSite.name : '',
    backupUrl: defaultSite ? defaultSite.url : '',
    backupOpenType: defaultSite ? defaultSite.openType : '_blank',
    backupIconUrl: defaultSite ? defaultSite.iconUrl : '',
    // 解析其他备用站点（跳过第一个，因为它是默认站点）
    backupSites: parseBackupSites(data).slice(1)
  };
};

const fetchData = async () => {
  try {
    const res = await fetchFormData();
    updateFormData(res.data);
  } catch {}
};

onMounted(() => {
  fetchData();
  getLicenseInfo();
});

const openEditor = (type: string, content: string | undefined): void => {
  currentEditType.value = type;
  htmlEditorRef.value?.openDialog(content || '');
};

const addSite = (): void => {
  formData.value.backupSites.push({ name: '', url: '', openType: '_blank', iconUrl: '' });
};

const removeSite = (index: number): void => {
  formData.value.backupSites.splice(index, 1);
};

const sendTestEmail = async (): Promise<void> => {
  try {
    // Implement test email sending logic here
    ElMessage.success('测试邮件发送成功');
  } catch {
    ElMessage.error('测试邮件发送失败');
  }
};

const licenseInfo: Ref<Record<string, string>> = ref({});

const getLicenseInfo = async () => {
  try {
    const res = await fetchLicenseInfo();
    licenseInfo.value = res.data;
  } catch {}
};

// Add after licenseInfo ref:
const formattedLicenseInfo = computed(() => {
  const info = { ...licenseInfo.value };

  // Format expireTime if it exists
  if (info.expireTime) {
    const date = new Date(Number(info.expireTime));
    info.expireTime = date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  }

  // Format access list if it exists
  if (info.access) {
    try {
      const accessList = JSON.parse(info.access);
      info.access = Array.isArray(accessList) ? accessList.join(', ') : info.access;
    } catch {
      // If parsing fails, keep original value
    }
  }

  return info;
});

// Add the formatLabel function before the template:
const formatLabel = (key: string): string => {
  const labelMap: Record<string, string> = {
    expireTime: '过期时间',
    access: '权限列表',
    hash: '授权哈希'
    // Add more mappings as needed
  };
  return labelMap[key] || key;
};

// Add this script section at the top of the file, after the imports
const validateJson = (field: 'modelLimits' | 'dialogModelMultiplier' | 'thirdModelLimits') => {
  try {
    const value = formData.value[field];
    if (!value) return; // 如果为空则不校验

    const parsed = JSON.parse(value);

    // 根据不同字段进行特定的格式校验
    if (field === 'modelLimits' || field === 'thirdModelLimits') {
      // 检查每个模型的格式
      Object.entries(parsed).forEach(([model, config]: [string, any]) => {
        if (!config || typeof config !== 'object') {
          throw new Error(`模型 ${model} 的配置格式不正确`);
        }
        if (!('limit' in config) || !('per' in config)) {
          throw new Error(`模型 ${model} 缺少必要的 limit 或 per 字段`);
        }
        if (typeof config.limit !== 'number') {
          throw new TypeError(`模型 ${model} 的 limit 必须是数字`);
        }
        if (config.per && typeof config.per !== 'string') {
          throw new Error(`模型 ${model} 的 per 必须是字符串`);
        }
      });
    } else if (field === 'dialogModelMultiplier') {
      // 检查倍率配置
      Object.entries(parsed).forEach(([model, multiplier]: [string, any]) => {
        if (typeof multiplier !== 'number') {
          throw new TypeError(`模型 ${model} 的倍率必须是数字`);
        }
      });
    }
  } catch (error: unknown) {
    window.$message?.error(`JSON格式错误: ${error.message}`);
    throw new Error('JSON格式错误');
  }
};
</script>

<template>
  <div>
    <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
      <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
        <ElScrollbar height="calc(100vh-50px)">
          <ElTabs v-model="activeTab" class="border rounded px-4 shadow-md dark:border-gray-700">
            <ElTabPane label="系统配置" name="1">
              <ElForm :model="formData" label-width="auto">
                <ElFormItem label="系统名称">
                  <div class="form-input-group">
                    <ElInput v-model="formData.siteName" class="form-input" />
                    <ElTooltip content="填写后将在前后台logo处显示" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="站点 Logo">
                  <div class="form-input-group">
                    <div class="logo-upload-container">
                      <ElUpload
                        class="logo-uploader"
                        :action="importUrl"
                        :headers="uploadHeaders"
                        :show-file-list="false"
                        :auto-upload="true"
                        :on-success="handleFileSuccess"
                        :before-upload="beforeAvatarUpload"
                      >
                        <div class="upload-button-container">
                          <div class="upload-button">
                            <ElIcon class="upload-icon"><Plus /></ElIcon>
                          </div>
                          <div class="upload-tip">建议尺寸：64x64，格式：PNG或JPG</div>
                        </div>
                      </ElUpload>
                      <div v-if="formData.logoUrl" class="preview-container">
                        <ElImage
                          :src="formData.logoUrl"
                          :zoom-rate="1.2"
                          :max-scale="7"
                          :min-scale="0.2"
                          :preview-src-list="[formData.logoUrl]"
                          show-progress
                          fit="cover"
                          class="preview-image"
                        />
                        <div class="preview-overlay" @click="formData.logoUrl = ''">
                          <ElIcon class="delete-icon"><Delete /></ElIcon>
                          <span>删除</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </ElFormItem>
                <ElFormItem label="主题风格">
                  <div class="form-input-group">
                    <ElSelect v-model="formData.themeName" placeholder="请选择主题" class="form-input">
                      <ElOption v-for="item in themes" :key="item.value" :label="item.label" :value="item.value" />
                    </ElSelect>
                    <ElTooltip content="切换后将更换【登录、注册、忘记密码】页面UI" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="显示版权信息">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.showVersion" />
                    <ElTooltip content="开启后，将在页脚显示站点名称版本号等版权信息。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>

                <ElFormItem label="对话保存天数">
                  <div class="form-input-group">
                    <ElInputNumber
                      v-model="formData.saveLogDays"
                      :min="1"
                      :step="1"
                      :precision="0"
                      class="form-input"
                      placeholder="请输入整数"
                    />
                    <ElTooltip content="自动清理多少天以前创建的会话" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElDivider></ElDivider>
                <ElFormItem label="开启游客模式">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableNoLogin" />
                    <ElTooltip content="开启后，游客不需要登录也可以对话。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="游客对话速率">
                  <div class="form-input-group">
                    <ElInputNumber
                      v-model="formData.visitorLimit"
                      :min="1"
                      :step="1"
                      :precision="0"
                      class="form-input"
                      placeholder="请输入整数"
                    />
                    <ElSelect v-model="formData.visitorUsagePeriod" placeholder="请选择速率限制周期" class="form-input">
                      <ElOption v-for="item in limits" :key="item.value" :label="item.label" :value="item.value" />
                    </ElSelect>
                    <ElTooltip content="默认小时，也就是一小时内能用多少次对话，为空或者为0则不限速。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="游客可使用Plus">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.visitorUsePlus" />
                    <ElTooltip content="开启后，游客使用plus账号进行对话。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="关闭卡密兑换">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.closeCardExchange" />
                    <ElTooltip content="开启后，前台关闭卡密兑换功能。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="模型速率信息">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.showModelRate" />
                    <ElTooltip content="开启后，个人中心会显示模型速率信息。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="开启站内支付">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableSiteShop" />
                    <ElTooltip content="开启后，前台会出现站内支付功能。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="支付商品标题 ">
                  <div class="form-input-group">
                    <ElInput
                      v-model="formData.siteShopName"
                      class="form-input"
                      placeholder="请输入自定义支付商品标题"
                    />
                    <ElTooltip content="请填写自定义支付商品标题，不填写则默认使用套餐中的订阅名称。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="显示支付记录">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.showPaymentHistory" />
                    <ElTooltip content="开启后，用户可以在个人中心查看支付记录。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="支付成功通知">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enablePaySuccessNotice" />
                    <ElTooltip
                      content="开启后，将给管理员邮箱发送支付成功的信息，请先确保邮箱配置是正确的。"
                      placement="top"
                    >
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="微信浏览器使用">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.openWechat" />
                    <ElTooltip content="开启后，微信浏览器可打开本网站，建议关闭。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="折叠前台菜单侧边栏">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.collapseSidebar" />
                    <ElTooltip content="开启后，将侧边栏菜单折叠起来。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="关闭套餐的速率信息">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.closeSubTypeShow" />
                    <ElTooltip content="开启后，将关闭套餐的速率信息显示。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElDivider></ElDivider>
                <ElRow :gutter="20">
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="claude地址">
                      <div class="form-input-group">
                        <ElInput
                          v-model="formData.claudeUrl"
                          class="form-input"
                          placeholder="例：https://xxx.xxx.com，别加/"
                        />
                        <ElTooltip content="填写部署的Claude地址，用于fuclaude跳转。" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="第三方claude地址">
                      <div class="form-input-group">
                        <ElInput
                          v-model="formData.sxClaudeUrl"
                          class="form-input"
                          placeholder="例：https://xxx.xxx.com，别加/"
                        />
                        <ElSelect v-model="formData.thirdClaudeType" class="w-[60px]">
                          <ElOption label="lyy" value="lyy" />
                          <ElOption label="sx" value="sx" />
                        </ElSelect>
                        <ElSwitch v-model="formData.enableSxClaude" />
                        <ElTooltip
                          content="不开启默认使用fuclaude，开启后使用第三方claude，如需使用需要授权"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="使用说明地址">
                      <div class="form-input-group">
                        <ElInput
                          v-model="formData.userGuideUrl"
                          class="form-input"
                          placeholder="例：https://xxx.xxx.com，别加/"
                        />
                        <ElSelect v-model="formData.userGuideUrlOpenType" class="w-[120px]">
                          <ElOption label="站内打开" value="_self" />
                          <ElOption label="新窗口打开" value="_blank" />
                        </ElSelect>
                        <ElTooltip content="填写腾讯文档、飞书类的文档地址，不填前端不显示。" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                      <!--
 <div v-if="isMainSite()" class="form-input-group mt-2">
                        <ElSwitch v-model="formData.enableUserGuideForSubSite" />
                        <span class="text-sm text-gray-600">分站可见</span>
                        <ElTooltip content="开启后，分站也可以看到使用说明地址功能。" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
-->
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="发卡网地址">
                      <div class="form-input-group">
                        <ElInput
                          v-model="formData.fkAddress"
                          class="form-input"
                          placeholder="例：https://xxx.xxx.com，别加/"
                        />
                        <ElSelect v-model="formData.fkAddressOpenType" class="w-[120px]">
                          <ElOption label="站内打开" value="_self" />
                          <ElOption label="新窗口打开" value="_blank" />
                        </ElSelect>
                        <ElTooltip content="填写自己搭建的发卡网站，不填写前端不显示。" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="侧边栏名称">
                      <div class="form-input-group">
                        <ElInput
                          v-model="formData.customerSidebarName"
                          class="form-input"
                          placeholder="请输入侧边栏显示名称"
                        />
                        <ElTooltip content="自定义侧边栏显示的名称，用于前台侧边栏显示" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                      <!--
 <div class="form-input-group mt-2">
                        <ElSwitch v-model="formData.enableSidebarForSubSite" />
                        <span class="text-sm text-gray-600">分站可见</span>
                        <ElTooltip content="开启后，分站也可以看到侧边栏名称功能。" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
-->
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="侧边栏地址">
                      <div class="form-input-group">
                        <ElInput
                          v-model="formData.customerSidebarUrl"
                          class="form-input"
                          placeholder="例：https://xxx.xxx.com，别加/"
                        />
                        <ElSelect v-model="formData.sidebarOpenType" class="w-[120px]">
                          <ElOption label="站内打开" value="_self" />
                          <ElOption label="新窗口打开" value="_blank" />
                        </ElSelect>
                        <ElTooltip
                          content="请填写侧边栏显示的地址，不填则不显示，可用于跳转你的下载页面等"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElDivider></ElDivider>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="登录设备限制">
                      <div class="form-input-group">
                        <ElSwitch v-model="formData.enableSSO" />
                        <ElTooltip content="开启后，用户登录后会过期，需要重新登录。" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                    <ElFormItem label="最大登录设备数">
                      <div class="form-input-group">
                        <ElInputNumber
                          v-model="formData.maxDevices"
                          :min="0"
                          :step="1"
                          :precision="0"
                          class="form-input"
                          placeholder="请输入整数"
                        />
                        <ElTooltip content="用户可以同时对话的设备数，0表示不限制。" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                    <ElFormItem label="登录过期时间">
                      <div class="form-input-group">
                        <ElInput v-model="formData.expireTime" class="form-input">
                          <template #append>
                            <ElButton>分钟</ElButton>
                          </template>
                        </ElInput>
                        <ElTooltip content="用户登录系统后的过期时间" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                </ElRow>
              </ElForm>
            </ElTabPane>
            <ElTabPane label="会员管理" name="2">
              <ElForm :model="formData" label-width="auto">
                <ElFormItem label="开启用户注册">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableRegister" />
                    <ElTooltip content="开启后，用户可以在前台进行注册。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="识别重复注册">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableRepeatRegister" />
                    <ElTooltip content="开启后，将识别重复注册，防止恶意注册。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="需要邀请码">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableInviteCode" />
                    <ElTooltip content="开启后，需要使用正确的邀请码才能成功注册。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="注册体验时长">
                  <div class="form-input-group">
                    <ElInputNumber
                      v-model="formData.freeTime"
                      :min="0"
                      :step="1"
                      :precision="0"
                      class="form-input"
                      placeholder="请输入整数"
                    >
                      <template #suffix>
                        <span>小时</span>
                      </template>
                    </ElInputNumber>
                    <ElTooltip content="新注册用户可体验时长，填写整数。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="邀请赠送时长">
                  <div class="form-input-group">
                    <ElInputNumber
                      v-model="formData.affTime"
                      :min="0"
                      :step="1"
                      :precision="0"
                      class="form-input"
                      placeholder="请输入整数"
                    >
                      <template #suffix>
                        <span>小时</span>
                      </template>
                    </ElInputNumber>
                    <ElTooltip content="邀请可获得的时长奖励，填写整数。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="注册用户赠送绘图张数">
                  <ElInputNumber v-model="formData.drawRegisterCount" class="form-input" :min="1" :max="1000000" />
                </ElFormItem>
                <ElFormItem label="开启Plus权益">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enablePlus" />
                    <ElTooltip content="开启后，新注册用户和邀请用户都是赠送Plus权益时长。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElDivider content-position="left">
                  <span class="text-red-500 font-bold">
                    模型速率限制填写说明：周期或者次数为空则不限制，次数为0则禁止使用
                  </span>
                </ElDivider>
                <ElFormItem label="新用户模型速率限制">
                  <div class="w-full">
                    <ElInput
                      v-model="formData.modelLimits"
                      type="textarea"
                      :autosize="{ minRows: 3, maxRows: 6 }"
                      class="sm:w-full"
                      placeholder='请输入JSON格式，例如：{"gpt-4o": {"limit": 10, "per": "1h"}, "o1": {"limit": 20, "per": "1h"}}'
                      @blur="validateJson('modelLimits')"
                    />
                    <ElTooltip
                      content="系统会默认初始模型，只需要调整limit和per即可，请注意JSON格式。limit为数字，per为字符串，需要加引号。
                      可选值为：1s(每秒)、1m(每分)、1h(每小时)、3h(每3小时)、1d(每天)、1w(每周)、1y(每年)"
                      placement="top"
                    >
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="模型对话倍率">
                  <div class="w-full">
                    <ElInput
                      v-model="formData.dialogModelMultiplier"
                      type="textarea"
                      :autosize="{ minRows: 3, maxRows: 10 }"
                      placeholder='请输入JSON格式，例如：{"gpt-4": 2, "gpt-4o": 3}'
                      @blur="validateJson('dialogModelMultiplier')"
                    />
                    <ElTooltip
                      content="每个模型请求一次消耗的次数，不填写按1次计算，倍率请填写整数,填写0则不扣次数。可设置模型包含：auto,gpt-4,gpt-4o,gpt-4-browsing,gpt-4-plugins,gpt-4-mobile,gpt-4-code-interpreter,gpt-4-dalle,gpt-4-gizmo,gpt-4-magic-create,gpt-4o-canmore,o1,o1-mini,o1-pro"
                      placement="top"
                    >
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="自定义模型速率">
                  <div class="w-full">
                    <ElInput
                      v-model="formData.thirdModelLimits"
                      type="textarea"
                      :autosize="{ minRows: 3, maxRows: 10 }"
                      placeholder='请输入JSON格式，例如：{"gpt-4o": {"limit": 10, "per": "1h"}, "o1": {"limit": 20, "per": "1h"}}'
                      @blur="validateJson('thirdModelLimits')"
                    />
                    <ElTooltip
                      content="系统会默认初始模型，只需要调整limit和per即可，请注意JSON格式。limit为数字，per为字符串，需要加引号。
                      可选值为：1s(每秒)、1m(每分)、1h(每小时)、3h(每3小时)、1d(每天)、1w(每周)、1y(每年)"
                      placement="top"
                    >
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElDivider></ElDivider>
                <ElFormItem label="显示剩余次数">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableShowRemaining" />
                    <ElTooltip content="开启后，将在对话页面显示剩余次数。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="会员到期提醒">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableExpirationReminder" />
                    <ElTooltip content="开启后，将在对话页面提前三天提示用户到期弹窗。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
              </ElForm>
            </ElTabPane>
            <ElTabPane label="邮箱配置" name="3">
              <ElForm ref="ruleFormRef" :model="formData">
                <ElFormItem label="开启邮件验证" prop="email">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableEmailCheck" />
                    <ElTooltip content="开启后，通过邮箱注册找回密码的用户需要填写邮箱验证码。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="发件邮箱" prop="email">
                  <div class="form-input-group">
                    <ElInput v-model="formData.email" placeholder="请输入发件人邮箱" class="form-input" />
                    <ElTooltip content="用于发送邮件的邮箱" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="发件密码" prop="emailPassword">
                  <div class="form-input-group">
                    <ElInput v-model="formData.emailPassword" placeholder="请输入发件密码" class="form-input" />
                    <ElTooltip content="SMTP授权码，非邮箱密码。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="发件Host" prop="emailHost">
                  <div class="form-input-group">
                    <ElInput v-model="formData.emailHost" placeholder="请输入发件host" class="form-input" />
                    <ElTooltip content="发件的服务器主机，如QQ：smtp.qq.com。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="收件人白名单">
                  <div class="form-input-group">
                    <ElInput v-model="formData.emailWhitelist" placeholder="请输入收件人白名单" class="form-input" />
                    <ElTooltip
                      content="1、注册邮箱白名单，用英文逗号隔开（例：@qq.com,@gmail.com）2、不填写时默认可所有邮箱可注册"
                      placement="top"
                    >
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="发件端口" prop="emailPort">
                  <ElRadioGroup v-model="formData.emailPort">
                    <ElRadio value="465">465（SSL）</ElRadio>
                    <ElRadio value="587">587（TLS）</ElRadio>
                    <ElRadio value="994">994（SSL）</ElRadio>
                  </ElRadioGroup>
                  <ElButton class="ml-2" type="danger" @click="sendTestEmail">发送测试邮件</ElButton>
                </ElFormItem>
              </ElForm>
            </ElTabPane>
            <ElTabPane label="节点配置" name="4">
              <ElForm :model="formData" class="px-2">
                <!-- 第一行：虚拟车队个数 -->
                <ElDivider content-position="left">虚拟车设置</ElDivider>

                <ElRow>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟(ChatGPT Plus)个数">
                      <div class="form-input-group">
                        <ElInputNumber
                          v-model="formData.virtualNo"
                          :min="0"
                          :step="1"
                          :precision="0"
                          class="form-input"
                          placeholder="请输入整数"
                        />
                        <ElTooltip
                          content="填写后将在前台plus节点中显示虚拟车队，如果本身没有plus账号则不显示"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟车队名称(ChatGPT Plus)">
                      <div class="form-input-group">
                        <ElInput v-model="formData.virtualNameList" class="form-input" />
                        <ElTooltip
                          content="自定义ChatGPT虚拟车的名字，请用英文逗号隔开车号。不填写系统会自动生成8位随机名称"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟(ChatGPT Team)个数">
                      <div class="form-input-group">
                        <ElInputNumber
                          v-model="formData.virtualTeamNo"
                          :min="0"
                          :step="1"
                          :precision="0"
                          class="form-input"
                          placeholder="请输入整数"
                        />
                        <ElTooltip
                          content="填写后将在前台Team节点中显示虚拟车队，如果本身没有plus账号则不显示"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟车队名称(ChatGPT Team)">
                      <div class="form-input-group">
                        <ElInput v-model="formData.virtualTeamNameList" class="form-input" />
                        <ElTooltip
                          content="自定义ChatGPT虚拟车的名字，请用英文逗号隔开车号。不填写系统会自动生成8位随机名称"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟(ChatGPT Pro)个数">
                      <div class="form-input-group">
                        <ElInputNumber
                          v-model="formData.virtualProNo"
                          :min="0"
                          :step="1"
                          :precision="0"
                          class="form-input"
                          placeholder="请输入整数"
                        />
                        <ElTooltip
                          content="填写后将在前台Pro节点中显示虚拟车队，如果本身没有plus账号则不显示"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟车队名称(ChatGPT Pro)">
                      <div class="form-input-group">
                        <ElInput v-model="formData.virtualProNameList" class="form-input" />
                        <ElTooltip
                          content="自定义ChatGPT虚拟车的名字，请用英文逗号隔开车号。不填写系统会自动生成8位随机名称"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟(Claude Pro)个数">
                      <div class="form-input-group">
                        <ElInputNumber
                          v-model="formData.virtualClaudeNo"
                          :min="0"
                          :step="1"
                          :precision="0"
                          class="form-input"
                          placeholder="请输入Claude虚拟号数量"
                        />
                        <ElTooltip content="设置Claude虚拟号的数量。" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟车队名称(Claude Pro)">
                      <div class="form-input-group">
                        <ElInput v-model="formData.virtualClaudeNameList" class="form-input" />
                        <ElTooltip
                          content="自定义Claude虚拟车的名字，请用英文逗号隔开车号。不填写系统会自动生成8位随机名称"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                </ElRow>
                <ElRow>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟(Claude Max)个数">
                      <div class="form-input-group">
                        <ElInputNumber
                          v-model="formData.virtualClaudeMaxNo"
                          :min="0"
                          :step="1"
                          :precision="0"
                          class="form-input"
                          placeholder="请输入Claude Max虚拟号数量"
                        />
                        <ElTooltip content="设置Claude Max虚拟号的数量。" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟车队名称(Claude Max)">
                      <div class="form-input-group">
                        <ElInput v-model="formData.virtualClaudeMaxNameList" class="form-input" />
                        <ElTooltip
                          content="自定义Claude Max虚拟车的名字，请用英文逗号隔开车号。不填写系统会自动生成8位随机名称"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                </ElRow>
                <ElRow>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟（Grok）车队个数">
                      <div class="form-input-group">
                        <ElInputNumber
                          v-model="formData.grokNum"
                          :min="0"
                          :step="1"
                          :precision="0"
                          class="form-input"
                          placeholder="请输入整数"
                        />
                        <ElTooltip content="填写后将在前台Grok节点中显示虚拟车队" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟车队名称（Grok）">
                      <div class="form-input-group">
                        <ElInput v-model="formData.grokName" class="form-input" />
                        <ElTooltip
                          content="自定义Grok虚拟车的名字，请用英文逗号隔开车号。不填写系统会自动生成8位随机名称"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                </ElRow>
                <ElRow>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟（Grok Super）车队个数">
                      <div class="form-input-group">
                        <ElInputNumber
                          v-model="formData.grokSuperNum"
                          :min="0"
                          :step="1"
                          :precision="0"
                          class="form-input"
                          placeholder="请输入整数"
                        />
                        <ElTooltip content="填写后将在前台GrokSuper节点中显示虚拟车队" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟车队名称（Grok Super）">
                      <div class="form-input-group">
                        <ElInput v-model="formData.grokSuperName" class="form-input" />
                        <ElTooltip
                          content="自定义Grok Super虚拟车的名字，请用英文逗号隔开车号。不填写系统会自动生成8位随机名称"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                </ElRow>
                <ElRow>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟（sass）车队个数">
                      <div class="form-input-group">
                        <ElInputNumber
                          v-model="formData.sassNum"
                          :min="0"
                          :step="1"
                          :precision="0"
                          class="form-input"
                          placeholder="请输入整数"
                        />
                        <ElTooltip content="填写后将在前台sass节点中显示虚拟车队" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="虚拟车队名称（sass）">
                      <div class="form-input-group">
                        <ElInput v-model="formData.sassName" class="form-input" />
                        <ElTooltip
                          content="自定义sass虚拟车的名字，请用英文逗号隔开车号。不填写系统会自动生成8位随机名称"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                </ElRow>
                <!-- 第二行：免费节点个数 -->
                <ElRow>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="普通账号免费节点个数">
                      <div class="form-input-group">
                        <ElInputNumber
                          v-model="formData.freeNodes"
                          :min="0"
                          :step="1"
                          :precision="0"
                          class="form-input"
                          placeholder="请输入整数"
                        />
                        <ElTooltip
                          content="前端将展示免费的节点个数，请填写整数，配合免费节点名称使用。"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="免费节点自定义名称">
                      <ElInput v-model="formData.freeNodeName" class="form-input" />
                    </ElFormItem>
                  </ElCol>
                </ElRow>
                <!-- 第三行：节点名称（四个节点在同一行显示）-->
                <ElDivider content-position="left">节点设置</ElDivider>

                <p class="mb-2 text-sm text-red-500 font-bold">
                  填写说明：不填写前端则不展示节点名称，填写第一个节点还需要配置免费节点的个数，因为第一个节点是免费节点。
                </p>
                <ElRow>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="普通节点自定义名称">
                      <ElInput v-model="formData.normalNodeName" class="form-input" />
                    </ElFormItem>
                  </ElCol>

                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="高级节点自定义名称">
                      <ElInput v-model="formData.plusNodeName" class="form-input" />
                    </ElFormItem>
                  </ElCol>

                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="克劳德节点自定义名称">
                      <ElInput v-model="formData.claudeNodeName" class="form-input" />
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="Grok自定义名称">
                      <ElInput v-model="formData.grokNodeName" class="form-input" />
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="Grok地址">
                      <ElInput
                        v-model="formData.grokUrl"
                        class="form-input"
                        placeholder="例：https://xxx.xxx.com，别加/"
                      />
                      <ElSelect v-model="formData.grokOpenType" class="w-[100px]">
                        <ElOption label="站内打开" value="_self" />
                        <ElOption label="新窗口打开" value="_blank" />
                      </ElSelect>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="SourxGPT名称">
                      <div class="form-input-group">
                        <ElInput
                          v-model="formData.soruxGptSideBarName"
                          class="form-input"
                          placeholder="请输入侧边栏显示名称"
                        />
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="SourxGpt地址">
                      <div class="form-input-group">
                        <ElInput
                          v-model="formData.soruxGptSideBarUrl"
                          class="form-input"
                          placeholder="例：https://xxx.xxx.com，别加/"
                        />
                        <ElSelect v-model="formData.soruxGptSideBarOpenType" class="w-[120px]">
                          <ElOption label="站内打开" value="_self" />
                          <ElOption label="新窗口打开" value="_blank" />
                        </ElSelect>
                        <ElTooltip
                          content="请填写侧边栏显示的地址，不填则不显示，可用于跳转你的下载页面等"
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="绘图节点自定义名称">
                      <ElInput
                        v-model="formData.drawNodeName"
                        class="form-input"
                        placeholder="例：AI绘图、Dalle-3绘图"
                      />
                    </ElFormItem>
                  </ElCol>

                  <!-- 节点排序配置 -->
                  <ElCol :span="24">
                    <ElFormItem label="节点显示顺序">
                      <div class="w-full">
                        <ElInput
                          v-model="formData.nodeOrder"
                          type="textarea"
                          :rows="2"
                          placeholder='[{"type":"free"},{"type":"4o"},{"type":"plus"},{"type":"claude"},{"type":"grok"},{"type":"draw"},{"type":"embedded"}]'
                        />
                        <ElTooltip
                          content='配置节点的显示顺序，格式为JSON数组，例如：[{"type":"free"},{"type":"4o"},{"type":"plus"},{"type":"claude"},{"type":"grok"},{"type":"draw"},{"type":"embedded"}]'
                          placement="top"
                        >
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>

                  <ElCol :xs="24" :sm="12">
                    <ElFormItem label="备用网站管理">
                      <div class="backup-sites-container">
                        <!-- 兼容旧数据的第一个备用站点 -->
                        <div class="backup-site-item">
                          <ElInput v-model="formData.backupNode" class="form-input" placeholder="备用网站名称" />
                          <ElInput v-model="formData.backupUrl" class="form-input" placeholder="备用网站地址" />
                          <ElInput v-model="formData.backupIconUrl" class="form-input" placeholder="图标地址（可选）" />
                          <ElSelect v-model="formData.backupOpenType" class="form-input" placeholder="打开方式">
                            <ElOption label="新窗口打开" value="_blank" />
                            <ElOption label="当前窗口打开" value="_self" />
                          </ElSelect>
                          <ElButton type="primary" :disabled="true">默认站点</ElButton>
                        </div>

                        <!-- 动态备用站点列表 -->
                        <div v-for="(site, index) in formData.backupSites" :key="index" class="backup-site-item">
                          <ElInput v-model="site.name" class="form-input" placeholder="备用网站名称" />
                          <ElInput v-model="site.url" class="form-input" placeholder="备用网站地址" />
                          <ElInput v-model="site.iconUrl" class="form-input" placeholder="图标地址（可选）" />
                          <ElSelect v-model="site.openType" class="form-input" placeholder="打开方式">
                            <ElOption label="新窗口打开" value="_blank" />
                            <ElOption label="当前窗口打开" value="_self" />
                          </ElSelect>
                          <ElButton type="danger" @click="removeSite(index)">删除</ElButton>
                        </div>

                        <!-- 添加新站点按钮 -->
                        <ElButton type="primary" class="add-site-btn" @click="addSite">添加备用站点</ElButton>

                        <!-- 分站可见开关 -->
                        <!--
 <div class="form-input-group mt-3">
                          <ElSwitch v-model="formData.enableBackupSitesForSubSite" />
                          <span class="text-sm text-gray-600">分站可见</span>
                          <ElTooltip content="开启后，分站也可以看到备用网站管理功能。" placement="top">
                            <ElIcon class="tooltip-icon">
                              <InfoFilled />
                            </ElIcon>
                          </ElTooltip>
                        </div>
-->
                      </div>
                    </ElFormItem>
                  </ElCol>

                  <ElCol :span="24">
                    <ElFormItem label="备用节点使用权限" prop="email">
                      <div class="form-input-group">
                        <ElSwitch v-model="formData.useBackNode" />
                        <ElTooltip content="开启后，所有人可以使用备用节点，否则会员可用" placement="top">
                          <ElIcon class="tooltip-icon">
                            <InfoFilled />
                          </ElIcon>
                        </ElTooltip>
                      </div>
                    </ElFormItem>
                  </ElCol>
                </ElRow>
              </ElForm>
            </ElTabPane>
            <ElTabPane label="返现设置" name="5">
              <ElForm :model="formData" class="px-2">
                <ElFormItem label="开启推广功能">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableInvite" />
                    <ElTooltip content="开启后，侧边栏会显示推广二维码和邀请信息" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="开启返现功能">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enablePromotion" />
                    <ElTooltip content="开启后，配合返现比例给用户返佣金的功能" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="付费用户返现">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableCashbackForPaidUsers" />
                    <ElTooltip content="开启后，只有付费用户邀请的人才会计算返现金额。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="返现比例">
                  <div class="form-input-group">
                    <ElInputNumber
                      v-model="formData.affRate"
                      :precision="2"
                      :step="0.1"
                      :min="0.1"
                      :max="1"
                      class="form-input"
                    />
                    <ElTooltip content="需要开启返现功能，返现比例才生效" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="卡密兑换返现">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableCalcCarCodes" />
                    <ElTooltip content="开启后，用户通过卡密兑换的套餐也会计算到返现金额中" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="提现门槛">
                  <div class="form-input-group">
                    <ElInput v-model="formData.threshold" class="form-input">
                      <template #append>
                        <ElButton>元</ElButton>
                      </template>
                    </ElInput>
                    <ElTooltip
                      content="达到提现门槛，用户可以进行提现。需手工处理用户上传的收款码进行打款操作。"
                      placement="top"
                    >
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="开启提现邮件通知">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.emailNotification" />
                    <ElTooltip content="开启用户返现到账邮件通知，需要先配置邮箱信息。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
              </ElForm>
            </ElTabPane>
            <ElTabPane label="签到配置" name="6">
              <ElForm :model="formData" class="px-2">
                <ElFormItem label="开启签到功能">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableSignIn" />
                    <ElTooltip content="开启后，用户可以进行签到，签到后可以获得时长" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="签到时长(分钟)">
                  <div class="form-input-group">
                    <ElInputNumber v-model="formData.signInTime" class="form-input" :min="1" :max="1000000" />
                    <ElTooltip content="签到后可以获得时长，请填写整数，模型速率不变。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="签到类型">
                  <div class="form-input-group">
                    <ElRadioGroup v-model="formData.signInType">
                      <ElRadio value="1">ChatGPT</ElRadio>
                      <ElRadio value="2">Claude</ElRadio>
                      <ElRadio value="3">ChatGPT&Claude</ElRadio>
                      <ElRadio value="4">Grok</ElRadio>
                    </ElRadioGroup>
                    <ElTooltip content="选择签到类型，签到后可以获得对应类型的时长" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="是否高级权益">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enablePlusSignIn" />
                    <ElTooltip content="开启后，用户签到后可以获得高级权益" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="免费用户签到">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableFreeSignIn" />
                    <ElTooltip
                      content="开启后，如果开启了高级权益，免费用户签到后可以获得高级权益，否则只有付费用户签到后可以获得高级权益"
                      placement="top"
                    >
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElFormItem label="签到公告">
                  <div class="w-full">
                    <ElInput
                      v-model="formData.signInAnnouncement"
                      type="textarea"
                      :rows="4"
                      @click="openEditor('signin', formData.signInAnnouncement)"
                    />
                    <ElTooltip content="点击输入框可以打开富文本编辑器" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
              </ElForm>
            </ElTabPane>
            <ElTabPane label="绘图设置" name="7">
              <ElForm :model="formData" class="px-2">
                <ElFormItem label="开启绘图功能">
                  <div class="form-input-group">
                    <ElSwitch v-model="formData.enableDraw" />
                    <ElTooltip content="开启后，用户可以使用api进行绘图，需授权。" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElRow>
                  <ElCol :span="12" :xs="24">
                    <ElFormItem label="中转地址">
                      <ElInput
                        v-model="formData.apiUrl"
                        :show-password="true"
                        placeholder="默认：http://api.openai.com，请填写中转地址的url，参考前面格式，要加https，不用加/v1"
                      />
                    </ElFormItem>
                  </ElCol>
                  <ElCol :span="12" :xs="24">
                    <ElFormItem label="ApiKey">
                      <ElInput v-model="formData.apiKey" placeholder="请输入apiKey，不能为空" :show-password="true" />
                    </ElFormItem>
                  </ElCol>
                </ElRow>
                <ElRow>
                  <ElCol :span="12" :xs="24">
                    <ElFormItem label="绘画格式">
                      <ElSelect v-model="formData.drawModel" placeholder="请选择绘画格式">
                        <ElOption label="gpt-4o-image-vip（逆向）" value="gpt-4o-image-vip" />
                        <ElOption label="gpt-image-1（官方）" value="gpt-image-1" />
                      </ElSelect>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :span="12" :xs="24">
                    <ElFormItem label="生成数量">
                      <ElInputNumber v-model="formData.drawCount" class="w-full" :min="1" :max="10" />
                    </ElFormItem>
                  </ElCol>
                </ElRow>
              </ElForm>
            </ElTabPane>
            <ElTabPane label="授权管理" name="8">
              <ElForm :model="formData" class="px-2">
                <ElFormItem label="授权码">
                  <div class="w-full">
                    <ElInput v-model="formData.licenseCode" type="textarea" :rows="2" placeholder="请输入授权码" />
                    <ElTooltip content="请输入管理员发放的授权码" placement="top">
                      <ElIcon class="tooltip-icon">
                        <InfoFilled />
                      </ElIcon>
                    </ElTooltip>
                  </div>
                </ElFormItem>
                <ElDivider>授权信息</ElDivider>
                <div v-if="licenseInfo" class="license-info">
                  <ElDescriptions :column="2" border>
                    <ElDescriptionsItem
                      v-for="(value, key) in formattedLicenseInfo"
                      :key="key"
                      :label="formatLabel(key)"
                    >
                      {{ value }}
                    </ElDescriptionsItem>
                  </ElDescriptions>
                </div>
                <div v-else class="py-4 text-center text-yellow-500">授权信息不存在或已过期</div>
              </ElForm>
            </ElTabPane>
            <div class="m-4 flex justify-center">
              <ElButton type="primary" @click="handleSubmit">保 存</ElButton>
            </div>
          </ElTabs>
        </ElScrollbar>
      </ElCard>
    </div>
    <HtmlEditor ref="htmlEditorRef" @update-content="handleContentUpdate" />
  </div>
</template>

<style scoped>
.form-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-input {
  width: 300px;
}

.backup-sites-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.backup-site-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  padding: 10px;
  transition: all 0.2s ease;
}

.backup-site-item:hover {
  border-color: #d1d5db;
}

.backup-site-item .form-input {
  flex: 1;
  min-width: 200px;
}

.backup-site-item .el-select {
  min-width: 120px;
}

.add-site-btn {
  align-self: flex-start;
  margin-top: 12px;
  padding: 10px 20px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .backup-site-item {
    flex-direction: column;
    gap: 12px;
    padding: 12px;
  }

  .backup-site-item .form-input,
  .backup-site-item .el-select {
    width: 100%;
    min-width: unset;
  }
}

/* 暗黑模式支持 */
.dark .backup-site-item {
  border-color: #374151;
}

.dark .backup-site-item:hover {
  border-color: #4b5563;
}

/* 输入框样式优化 */
.backup-site-item .form-input :deep(.el-input__wrapper) {
  transition: all 0.2s ease;
}

.backup-site-item .form-input :deep(.el-input__wrapper:hover) {
  border-color: #3b82f6;
}

.backup-site-item .form-input :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* 按钮样式优化 */
.backup-site-item .el-button {
  transition: all 0.2s ease;
  font-weight: 500;
}

.backup-site-item .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 添加按钮样式 */
.add-site-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.add-site-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
}

.add-site-btn:active {
  transform: translateY(0);
}
.demo-image__error .image-slot {
  font-size: 30px;
}
.demo-image__error .image-slot .el-icon {
  font-size: 30px;
}
.demo-image__error .el-image {
  width: 100%;
  height: 200px;
}
</style>

<style lang="scss" scoped>
// ... existing code ...

.logo-uploader {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);

    &:hover {
      border-color: var(--el-color-primary);
    }
  }
}

.upload-container {
  width: 200px;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
    color: white;

    &:hover {
      opacity: 1;
    }
  }
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--el-text-color-secondary);

  .upload-icon {
    font-size: 28px;
    margin-bottom: 8px;
  }
}

.tooltip-icon {
  margin-left: 8px;
  color: var(--el-text-color-secondary);
  cursor: help;
}

.logo-upload-container {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.upload-button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: auto;
}

.upload-button {
  width: 120px;
  height: 120px;
  border: 1px dashed var(--el-border-color);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
  }

  .upload-icon {
    font-size: 24px;
  }

  .upload-text {
    font-size: 14px;
  }
}

.upload-tip {
  font-size: 12px;
  text-align: center;
  line-height: 1.4;
}

.preview-container {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 4px;
  overflow: hidden;

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
    color: white;
    cursor: pointer;

    &:hover {
      opacity: 1;
    }

    .delete-icon {
      font-size: 20px;
      margin-bottom: 4px;
    }
  }
}
</style>

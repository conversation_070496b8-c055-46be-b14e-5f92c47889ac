<script setup lang="ts">
import { $t } from '@/locales';
import { useForm } from '@/hooks/common/form';

defineOptions({ name: 'OrderSearch' });

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}


const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useForm();

const model = defineModel<any>('model', { required: true });


async function reset() {
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
const payStatus = [
  {
    label: '已支付', value: 'success'
  },
  {
    label: '未支付', value: 'pending'
  },
]
</script>

<template>
  <ElCard class="card-wrapper">
    <ElCollapse>
      <ElCollapseItem :title="$t('common.search')" name="user-search">
        <ElForm ref="formRef" :model="model"  label-position="right" :label-width="80">
          <ElRow :gutter="24">
            <ElCol :lg="6" :md="8" :sm="12">
              <ElFormItem label="用户名" prop="username">
                <ElInput v-model="model.username" clearable placeholder="请输入用户名" />
              </ElFormItem>
            </ElCol>
            <ElCol :lg="6" :md="8" :sm="12">
              <ElFormItem label="订单号" prop="tradeNo">
                <ElInput v-model="model.tradeNo" clearable placeholder="请输入订单号" />
              </ElFormItem>
            </ElCol>
            <ElCol :lg="6" :md="8" :sm="12" label="支付状态">
              <ElFormItem label="支付状态" prop="status">
                <ElSelect v-model="model.status" clearable placeholder="请选择支付状态">
                  <ElOption
                    v-for="(item, idx) in payStatus"
                    :key="idx"
                    :label="item.label"
                    :value="item.value"
                  ></ElOption>
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <!-- <ElCol :lg="6" :md="8" :sm="12">
              <ElFormItem :label="$t('page.manage.user.userEmail')" prop="userEmail">
                <ElInput v-model="model.userEmail" :placeholder="$t('page.manage.user.form.userEmail')" />
              </ElFormItem>
            </ElCol> -->
            <!-- <ElCol :lg="6" :md="8" :sm="12">
              <ElFormItem :label="$t('page.manage.user.userStatus')" prop="userStatus">
                <ElSelect v-model="model.userGender" clearable :placeholder="$t('page.manage.user.form.userStatus')">
                  <ElOption
                    v-for="{ label, value } in translateOptions(enableStatusOptions)"
                    :key="value"
                    :label="label"
                    :value="value"
                  ></ElOption>
                </ElSelect>
              </ElFormItem>
            </ElCol>  -->
            <ElCol :lg="6" :md="24" :sm="24">
              <ElSpace class="w-full justify-end" alignment="end">
                <ElButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </ElButton>
                <ElButton type="primary" plain @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </ElButton>
              </ElSpace>
            </ElCol>
          </ElRow>
        </ElForm>
      </ElCollapseItem>
    </ElCollapse>
  </ElCard>
</template>

<style scoped></style>

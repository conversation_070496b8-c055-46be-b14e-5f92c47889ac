package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: UserDrawingQuotaEntity
 * @Description:
 * @Author: Seven
 * @Date: 2025/4/25
 */
@Data
@TableName("quota_change_record")
public class QuotaChangeRecordEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    private Long userId;

    /**
     * 变更类型：1-套餐购买，2-绘图消耗，3-失败返还，4-后台修改
     */
    private int changeType;

    /**
     * 变更数量，正数为增加，负数为减少
     */
    private Long changeAmount;

    private String remark;

    @TableField(exist = false)
    private String username;

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.seven.share.mapper.ChatGptSubTypeMapper">
    <!-- 结果映射 -->
    <resultMap id="subtypeResultMap" type="org.seven.share.common.pojo.entity.ChatGptSubTypeEntity">
            <!-- Primary key -->
            <id property="id" column="id" jdbcType="BIGINT"/>

            <!-- Basic fields -->
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="money" column="money" jdbcType="DOUBLE"/>
            <result property="validDays" column="validDays" jdbcType="INTEGER"/>
            <result property="isPlus" column="isPlus" jdbcType="INTEGER"/>
            <result property="deletedAt" column="deleted_at" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="limit" column="limit" jdbcType="BIGINT"/>
            <result property="per" column="per" jdbcType="VARCHAR"/>
            <result property="isNotValued" column="isNotValued" jdbcType="INTEGER"/>
            <result property="subType" column="subType" jdbcType="VARCHAR"/>
            <result property="isPro" column="isPro" jdbcType="INTEGER"/>
            <result property="exclusive" column="exclusive" jdbcType="INTEGER"/>
            <result property="exclusiveType" column="exclusiveType" jdbcType="INTEGER"/>
            <result property="isHotSale" column="isHotSale" jdbcType="INTEGER"/>
            <result property="isSuper" column="isSuper" jdbcType="INTEGER"/>
            <result property="drawQuota" column="draw_quota" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="tenantName" column="tenant_name" jdbcType="VARCHAR"/>

            <!-- JSON field with JacksonTypeHandler -->
            <result property="modelLimits" column="model_limits" jdbcType="OTHER"
                    typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <update id="logicDeleteSubtype">
        update chatgpt_subtype
            set deleted_at = now() where id in
            <foreach collection='ids' item='id' open='(' separator=',' close=')'>
                 #{id}
            </foreach>
    </update>

    <select id="getSubtypeByIdWithoutTenant" resultMap="subtypeResultMap">
        select * from chatgpt_subtype where deleted_at is null and id = #{id}
    </select>

    <select id="selectPageWithTenant" resultMap="subtypeResultMap">
        select s.*, case s.tenant_id when '000000' then '主站' else t.tenant_name end as tenant_name
        from chatgpt_subtype s left join t_sys_tenant t on s.tenant_id = t.id
        <where>
            <if test="query != null and query != ''">
                s.name LIKE CONCAT('%', #{query}, '%')
            </if>
            s.deleted_at is null
        </where>
        order by s.sort
    </select>

    <select id="listSubtype" resultMap="subtypeResultMap">
        select *
        from chatgpt_subtype
        where isNotValued = 0
          and deleted_at is null
          and tenant_id = #{tenantId}
          order by sort
    </select>
</mapper>

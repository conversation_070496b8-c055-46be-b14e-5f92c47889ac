package org.seven.share.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.seven.share.common.pojo.entity.SysTenant;
import org.seven.share.mapper.ChatGptSysNoticeMapper;
import org.seven.share.common.pojo.entity.ChatGptSysNoticeEntity;
import org.seven.share.service.ChatGptSysNoticeService;
import org.seven.share.service.SysTenantService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName: ChatGptSysNoticeServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/1
 */
@Service
public class ChatGptSysNoticeServiceImpl extends ServiceImpl<ChatGptSysNoticeMapper, ChatGptSysNoticeEntity>
        implements ChatGptSysNoticeService {

    @Resource
    private SysTenantService sysTenantService;

    @Resource ChatGptSysNoticeMapper chatGptSysNoticeMapper;


    /**
     * @param domain
     * @return
     */
    @Override
    @Cacheable(cacheNames = "notice", key = "#domain")
    public ChatGptSysNoticeEntity getDomainNotice(String domain) {
        LambdaQueryWrapper<ChatGptSysNoticeEntity> wrapper = new LambdaQueryWrapper<ChatGptSysNoticeEntity>()
                .eq(ChatGptSysNoticeEntity::getType, "system")
                .orderByDesc(ChatGptSysNoticeEntity::getUpdateTime)
                .last("LIMIT 1");

        // 根据租户ID查询该租户的最新公告
        return getOne(wrapper);
    }

    @Override
    public Page<ChatGptSysNoticeEntity> selectNoticePage(Page<?> page, Long id) {
        return chatGptSysNoticeMapper.selectNoticePage(page, id);
    }
}

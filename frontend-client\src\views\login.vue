<template>
  <div class="flex justify-center items-center min-h-screen">
    <el-card class="w-full max-w-96">
      <div class="text-right flex items-center justify-end gap-2">
      <h4 class="text-xl font-bold m-4">{{ t('login.title') }}</h4>
        <el-switch
          v-model="isDark"
          :active-icon="Moon"
          :inactive-icon="Sunny"
          inline-prompt
          @change="toggleDark"
        />
        <el-dropdown @command="handleLanguageChange">
          <el-button link>
            <svg-icon :name="language" width="20px" height="20px"></svg-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="en">
                English
              </el-dropdown-item>
              <el-dropdown-item command="zh">
                中文
              </el-dropdown-item>
                <el-dropdown-item command="fa">
                  فارسی
                </el-dropdown-item>
                <el-dropdown-item command="my">
                  မြန်မာ
                </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      
      <!-- 登录方式切换 -->
      <div class="mb-4 flex justify-center">
        <div class="w-full flex rounded-lg p-1" :class="isDark ? 'bg-gray-700' : 'bg-gray-100'">
          <div 
            class="flex-1 text-center py-2 cursor-pointer rounded-md transition-all duration-200"
            :class="[
              loginType === 'account' 
                ? isDark 
                  ? 'bg-gray-600 shadow' 
                  : 'bg-white shadow'
                : isDark
                  ? 'hover:bg-gray-600/50'
                  : 'hover:bg-gray-200'
            ]"
            @click="loginType = 'account'"
          >
            <span class="font-bold">{{ t('login.accountLogin') }}</span>
          </div>
          <div 
            class="flex-1 text-center py-2 cursor-pointer rounded-md transition-all duration-200"
            :class="[
              loginType === 'authCode' 
                ? isDark 
                  ? 'bg-gray-600 shadow' 
                  : 'bg-white shadow'
                : isDark
                  ? 'hover:bg-gray-600/50'
                  : 'hover:bg-gray-200'
            ]"
            @click="loginType = 'authCode'"
          >
            <span class="font-bold">{{ t('login.authCodeLogin') }}</span>
          </div>
        </div>
      </div>

      <!-- 账号密码登录 -->
      <el-form v-if="loginType === 'account'" ref="loginRef" :model="loginForm" :rules="loginRules">
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            type="text"
            size="large"
            :placeholder="t('login.username')"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <el-icon>
                <Avatar />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            size="large"
            :placeholder="t('login.password')"
            @keyup.enter="handleLogin"
            show-password
          >
            <template #prefix>
              <el-icon>
                <Lock />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="code">
          <div class="flex">
            <el-input
              v-model="loginForm.code"
              type="text"
              size="large"
              :placeholder="t('login.code')"
              class="flex-2"
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <el-icon>
                  <Key />
                </el-icon>
              </template>
            </el-input>
            <div
              class="ml-8 p-1 text-xl flex border-solid font-bold justify-center items-center cursor-pointerselect-none"
              @click="generateCaptcha"
            >
              {{ captcha }}
            </div>
          </div>
        </el-form-item>
        <div class="flex items-center justify-between">
          <el-checkbox v-model="rememberMe" class="ml-1">
            {{ t('login.rememberMe') }}
          </el-checkbox>
          <router-link
            class="text-sm text-blue-500 hover:underline"
            :to="'/forgot-password'"
          >
            {{ t('login.forgot_password') }}
          </router-link>
        </div>
        <el-form-item>
          <button
            :disabled="loading"
            type="submit"
            class="mt-2 w-full h-10 text-lg bg-black dark:bg-white text-white dark:text-black rounded-lg font-medium hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 disabled:opacity-50"
            @click.prevent="handleLogin"
          >
            <span v-if="!loading">{{ t('login.dologin') }}</span>
            <span v-else>{{ t('login.loging') }}</span>
          </button>
        </el-form-item>
        <div class="mx-2 text-sm text-red" v-if="enableRegister === 'true'">
          <router-link
            class="hover:underline text-blue-500"
            :to="'/register'"
            >{{ t('login.registerNewAccount') }}</router-link
          >
        </div>
      </el-form>

      <!-- 授权码登录 -->
      <el-form v-if="loginType === 'authCode'" ref="authCodeRef" :model="authCodeForm" :rules="authCodeRules">
        <el-form-item prop="authCode">
          <el-input
            v-model="authCodeForm.authCode"
            type="text"
            size="large"
            :placeholder="t('login.enterAuthCode')"
            @keyup.enter="handleAuthCodeLogin"
          >
            <template #prefix>
              <el-icon><Key /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-link
                type="primary" 
                :underline="false"
                @click="goToPurchase"
                class="text-emerald-600"
              >
              <span class="text-black dark:text-white">没有授权码？</span><span class="ml-1 text-emerald-600">去购买</span>
              </el-link>
        <el-form-item>
          <button
            :disabled="loading"
            type="submit"
            class="mt-2 w-full h-10 text-lg bg-black dark:bg-white text-white dark:text-black rounded-lg font-medium hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 disabled:opacity-50"
            @click.prevent="handleAuthCodeLogin"
          >
            <span v-if="!loading">{{ t('login.dologin') }}</span>
            <span v-else>{{ t('login.loging') }}</span>
          </button>
        </el-form-item>
      </el-form>

      <!-- 微信扫码登录 -->
      <div v-if="loginType === 'wechat'" class="text-center">
        <div class="qr-code-container p-4">
          <!-- 这里放置微信二维码组件 -->
          <div class="w-48 h-48 mx-auto bg-gray-200 flex items-center justify-center">
            {{ t('login.wechatQrCode') }}
          </div>
        </div>
        <p class="mt-4 text-gray-600">{{ t('login.scanToLogin') }}</p>
      </div>

    </el-card>
    <AnnouncementPopupVue v-if="loginNotice" :notice="loginNotice" />
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { Avatar, Lock, Key } from '@element-plus/icons-vue';
import { ElNotification, ElMessage } from 'element-plus';
import AnnouncementPopupVue from '../components/AnnouncementPopup.vue';
import { useRouter } from 'vue-router';
import Cookies from 'js-cookie';
import { useI18n } from 'vue-i18n';
import { useDark, useToggle } from '@vueuse/core';
import { Sunny, Moon } from '@element-plus/icons-vue';
import { getTime } from '@/utils/date';
import { getToken } from '@/utils/auth';
import { useSiteStore } from '@/store/modules/site';
import { useUserStore } from '@/store/modules/user';
import { getIdleByUser } from '@/api/chatgpt.js';
import { encrypt, decrypt } from '@/utils/encrypt';
import { fetchAccessToken } from '@/api/user.js';
import { goToChat } from '@/utils/login-auth';
const enableBackNode = computed(() => siteStore.enableBackNode === 'true');

const siteStore = useSiteStore();
const userStore = useUserStore();

const loginNotice = computed(() => siteStore.loginAnnouncement);
const enableRegister = computed(() => siteStore.enableRegister);
const { locale, t } = useI18n();

const isDark = useDark();
const toggleDark = useToggle(isDark);
const language = computed(() => {
  if (isDark.value) {
    return 'language-dark';
  } else {
    return 'language';
  }
});
const goToPurchase = () => {
  window.open(siteStore.fkAddress, '_blank');
};
const router = useRouter(); // 使用vue-router
const loginForm = ref({
  username: '',
  password: '',
  code: '',
});
const rememberMe = ref(true);
const loginRules = {
  username: [
    {
      required: true,
      message: t('login.errors.usernameRequired'),
      trigger: 'blur',
    },
  ],
  code: [
    {
      required: true,
      message: t('login.errors.codeRequired'),
      trigger: 'blur',
    },
  ],
  password: [
    {
      required: true,
      message: t('login.errors.passwordRequired'),
      trigger: 'blur',
    },
  ],
};

const loading = ref(false);
const captcha = ref('');
const loginRef = ref(null);

// 登录方式
const loginType = ref('account');

// 授权码登录表单
const authCodeForm = ref({
  authCode: ''
});

// 授权码登录验证规则
const authCodeRules = {
  authCode: [
    {
      required: true,
      message: t('login.errors.authCodeRequired'),
      trigger: 'blur'
    }
  ]
};

const authCodeRef = ref(null);

function handleLogin() {
  loginRef.value.validate(async (valid) => {
    if (valid) {
      Cookies.remove('gfsessionid');
      // 将用户名和密码存到cookie中
      if (rememberMe.value) {
        Cookies.set('username', loginForm.value.username, { expires: 30 });
        Cookies.set('password', encrypt(loginForm.value.password), {
          expires: 30,
          secure: true,
        });
        Cookies.set('rememberMe', rememberMe.value, { expires: 30 });
        Cookies.set('visitor', 'false',{ expires: 30 });
      } else {
        // 否则移除
        Cookies.remove('username');
        Cookies.remove('password');
        Cookies.remove('rememberMe');
      }
      try {
        loading.value = true;
        if (loginForm.value.code === captcha.value) {
          await userStore.login(loginForm.value);
          if (getToken()) {
            ElMessage({
              message: `${userStore.username}, ${getTime()}`,
              type: 'success',
              plain: true,
            });
            if (siteStore.enableNoLogin == 'true') {
              try {
                if (enableBackNode.value) {
                    ElMessageBox.alert(
                      '官网服务异常，已为您开临时聊天功能，之后的对话记录不会保存，请及时保存重要信息',
                      '注意',
                      {
                        confirmButtonText: '确定',
                        callback: async () => {
                          const url = await getOaiLoginToken({
                            carId: loginCarid,
                            userToken: loginToken,
                          });
                          if (url) {
                            window.location.href = url;
                          } else {
                            router.push('/home');
                          }
                        },
                      }
                    );
                  } else {
                    // 先判断用户是否已经登录
                    let loginCarid, loginType, loginToken;
                    if (userStore.id) {
                      // 已经登录，则从当前会员的最高权益的节点中选择一个。
                      const data = await getIdleByUser({
                        userId: userStore.id,
                      });
                      loginCarid = data.carId;
                      loginType = data.nodeType;
                      loginToken = userStore.username;
                    } else {
                      // 未登录，则使用管理员设置的免费账号进行对话。
                      const data = await fetchAccessToken();
                      loginCarid = data.carId;
                      loginType = 'free';
                      loginToken = data.token;
                    }
                    goToChat(loginCarid, loginType, loginToken, router);
                  }
                } catch (error) {
                  console.log('免登跳转失败，回到选车页面', error);
                  router.push('/home');
                }
              } else {
                router.push('/home');
              }
            }
        } else {
          ElMessage({
            message: t('login.errors.codeError'),
            type: 'error',
            plain: true,
          });
          generateCaptcha();
        }
      } catch (error) {
        console.error(error);
        generateCaptcha();
      } finally {
        loading.value = false;
      }
    }
  });
}

const generateCaptcha = () => {
  captcha.value = Math.floor(1000 + Math.random() * 9000).toString();
};

const getCookie = () => {
  const username = Cookies.get('username');
  const password = Cookies.get('password');
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password:
      password === undefined ? loginForm.value.password : decrypt(password),
  };
};

generateCaptcha();
getCookie();

const handleLanguageChange = (command) => {
  loginRef.value.resetFields();
  locale.value = command;
  localStorage.setItem('language', command);
};

// 授权码登录处理
async function handleAuthCodeLogin() {
  authCodeRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true;
        await userStore.loginWithAuthCode(authCodeForm.value.authCode);
        Cookies.set('username', authCodeForm.value.authCode, { expires: 30 });
        router.push("/home")
      } catch (error) {
        console.error(error);
      } finally {
        loading.value = false;
      }
    }
  });
}
</script>

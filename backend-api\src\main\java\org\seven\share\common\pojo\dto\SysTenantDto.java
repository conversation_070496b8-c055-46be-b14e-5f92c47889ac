package org.seven.share.common.pojo.dto;
import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SysTenantDto  {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "租户编号")
    private String tenantId;

    @Schema(description = "联系人")
    private String contactUserName;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "租户名称")
    private String tenantName;

    @Schema(description = "域名")
    private String domain;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "租户套餐编号")
    private Long packageId;

    @Schema(description = "过期时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime expireTime;

    @Schema(description = "租户状态（0正常 1停用）")
    private String status;

    @Schema(description = "创建者ID")
    private Long createId;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "修改者ID")
    private Long updateId;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "是否已删除：0->未删除；1->已删除")
    private Integer isDeleted;

    @Schema(description = "删除时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "GMT+8")
    private LocalDateTime deleteTime;

    @Schema(description = "佣金余额")
    private Double commissionBalance;

    @Schema(description = "佣金比例")
    private Double commissionRatio;

    @Schema(description = "站点logo")
    private String siteLogo;

    @Schema(description = "站点名称")
    private String siteName;

}

<script setup lang="tsx">
import { computed, ref } from 'vue';
import {
  ElButton,
  ElDialog,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElPopconfirm,
  ElRadio,
  ElRadioGroup,
  ElSwitch,
  ElTag
} from 'element-plus';
import type { TableColumnCtx } from 'element-plus';
import { useBoolean } from '@sa/hooks';
import {
  exportOpenaiData,
  fetchOpenaiPage,
  removeOpenaiBatchByIds,
  saveOpenaiBatch,
  unbindOpenaiSession,
  updateOpenaiSession,
  updateStatus
} from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import SubtypeSearch from './modules/gpt-search.vue';
import TableHeaderOperation from './modules/gpt-table-header-operation.vue';
import OperateModel, { type OperateType } from './modules/gpt-operate-modal.vue';

// Define the proper type for your table data
interface OpenAI {
  id: string;
  carID: string;
  email: string;
  isPlus: number;
  status: number;
  officialSession: string;
  remark: string;
  createTime: string;
  updateTime: string;
  password?: string;
  userToken?: string;
  exclusiveExpireTime?: string;
  count?: number;
}
const sortParams = ref({
  prop: '',
  order: ''
});
// Function to convert Element Plus sort order to API sort order
function convertSortOrder(order: string): string | undefined {
  if (order === 'ascending') return 'asc';
  if (order === 'descending') return 'desc';
  return undefined;
}
const { columns, columnChecks, data, loading, pagination, getData, searchParams, resetSearchParams, getDataByPage } =
  useTable<UI.TableApiFn<OpenAI>>({
    apiFn: params => {
      // 添加排序参数
      const requestParams = {
        ...params,
        sortProp: sortParams.value.prop,
        sortOrder: convertSortOrder(sortParams.value.order)
      };
      return fetchOpenaiPage(requestParams);
    },
    columns: () => [
      { type: 'selection', width: 48 },
      { type: 'index', label: '序号', align: 'center', width: 60 },
      { prop: 'carID', label: '车号', align: 'center', minWidth: 100 },
      { prop: 'email', label: '邮箱', align: 'center', minWidth: 300, showOverflowTooltip: true },
      {
        prop: 'isPlus',
        label: '账号类型',
        align: 'center',
        sortable: true,
        width: 100,
        formatter: ((row: any) => {
          if (row.isPlus === 0) return <ElTag type="primary">Free</ElTag>;
          if (row.isPlus === 1) return <ElTag type="success">Plus</ElTag>;
          if (row.isPlus === 2) return <ElTag type="warning">Team</ElTag>;
          if (row.isPlus === 3) return <ElTag type="danger">Pro</ElTag>;
          return <ElTag type="info">Unknown</ElTag>;
        }) as TableFormatter
      },
      {
        prop: 'status',
        label: '状态',
        align: 'center',
        sortable: true,
        width: 100,
        formatter: (row: OpenAI) => (
          <ElSwitch v-model={row.status} active-value={1} inactive-value={0} onChange={() => changeSwitch(row)} />
        )
      },
      { prop: 'officialSession', label: '官方session', align: 'center', width: 300, showOverflowTooltip: true },
      { prop: 'count', label: '日请求量', align: 'center', width: 100 },
      { prop: 'remark', label: '备注', align: 'center', width: 200, showOverflowTooltip: true },
      {
        prop: 'bindStatus',
        label: '绑定状态',
        align: 'center',
        width: 100,
        formatter: ((row: any) => {
          const bindStatus = getBindStatus(row);
          return <ElTag type={bindStatus === '已绑定' ? 'success' : 'info'}>{bindStatus}</ElTag>;
        }) as TableFormatter
      },
      { prop: 'userToken', label: '独享人', align: 'center', width: 180, showOverflowTooltip: true },
      { prop: 'exclusiveExpireTime', label: '独享过期时间', align: 'center', width: 180 },

      { prop: 'createTime', label: '创建时间', align: 'center', sortable: true, width: 180 },
      { prop: 'updateTime', label: '更新时间', align: 'center', sortable: true, width: 180 },
      {
        prop: 'operate',
        label: '操作',
        align: 'center',
        fixed: 'right',
        width: 200,
        formatter: (row: any) => (
          <div class="flex-center">
            <ElButton type="success" plain size="small" onClick={() => handleUnbind(row)}>
              解绑
            </ElButton>
            <ElButton type="primary" plain size="small" onClick={() => handleEdit(row)}>
              {$t('common.edit')}
            </ElButton>
            <ElPopconfirm title={$t('common.confirmDelete')} onConfirm={() => handleDelClick(row.id)}>
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small">
                    {$t('common.delete')}
                  </ElButton>
                )
              }}
            </ElPopconfirm>
          </div>
        )
      }
    ]
  });

const { checkedRowKeys, onBatchDeleted, onDeleted } = useTableOperate(data as any, getData);

type TableFormatter = (row: any, column: TableColumnCtx<any>, cellValue: any, index: number) => any;

const wrapperRef = ref<HTMLElement | null>(null);
const operateType = ref<OperateType>('create');
const { bool: visible, setTrue: openModal } = useBoolean();
const { bool: batchVisible, setTrue: openBatchModal, setFalse: closeBatchModal } = useBoolean();
const currentData = ref<any>(null);

const isMobile = computed(() => window.innerWidth < 768);

async function changeSwitch(row: OpenAI) {
  try {
    await updateStatus({ id: row.id, status: row.status });
    ElMessage.success('更新成功');
    getData();
  } catch {
    ElMessage.error('更新失败');
  }
}

function handleEdit(row: OpenAI) {
  operateType.value = 'edit';
  currentData.value = data.value.find(item => item.id === row.id);
  openModal();
}

async function handleDelClick(id: string) {
  await removeOpenaiBatchByIds([id]);
  onDeleted();
}

// 添加处理排序变化的函数
function handleSortChange({ prop, order }: { prop: string; order: string }) {
  sortParams.value.prop = prop;
  sortParams.value.order = order;
  getData();
}

// 获取绑定状态
function getBindStatus(row: OpenAI) {
  if (!row.exclusiveExpireTime) return '未绑定'; // 如果没有过期时间，视为未绑定
  const expireDate = new Date(row.exclusiveExpireTime);
  const currentDate = new Date();
  return expireDate > currentDate ? '已绑定' : '已过期';
}

// 处理解绑操作
async function handleUnbind(row: OpenAI) {
  try {
    const expireDate = new Date(row.exclusiveExpireTime || '');
    const currentDate = new Date();

    if (!row.exclusiveExpireTime || expireDate <= currentDate) {
      ElMessage({
        message: '该账号已过期或未绑定',
        type: 'warning',
        plain: true
      });
      return;
    }

    await ElMessageBox.confirm('是否要解绑该账号， 继续?', '注意', {
      confirmButtonText: '确 定',
      cancelButtonText: '取 消',
      type: 'warning'
    });

    await unbindOpenaiSession(row.id); // 调用解绑API

    ElMessage({
      message: '解绑成功',
      type: 'success',
      plain: true
    });

    getData(); // 刷新数据
  } catch {
    ElMessage({
      message: '解绑失败',
      type: 'error',
      plain: true
    });
  }
}

async function handleBatchDelete() {
  await removeOpenaiBatchByIds(checkedRowKeys.value);
  onBatchDeleted();
}

function handleAdd() {
  operateType.value = 'create';
  currentData.value = null;
  openModal();
}

function handleBatchAdd() {
  openBatchModal();
}

// 处理导出
async function handleExport() {
  try {
    const response = await exportOpenaiData();
    // 创建blob URL并触发下载
    const blob = new Blob([response.data || ''], { type: 'application/octet-stream' });
    const downloadElement = document.createElement('a');
    const href = window.URL.createObjectURL(blob);
    downloadElement.href = href;
    downloadElement.download = 'OpenAI账号数据.txt';
    document.body.appendChild(downloadElement);
    downloadElement.click();
    document.body.removeChild(downloadElement);
    window.URL.revokeObjectURL(href);
    ElMessage.success('导出成功');
  } catch {
    ElMessage.error('导出失败，请稍后重试');
  }
}

const accounts = ref('');
const accountType = ref(2);

const saveBatch = async () => {
  if (accounts.value === '') {
    ElMessage({
      message: '请先输入账号信息',
      type: 'warning',
      plain: true
    });
    return;
  }

  try {
    const payload = {
      accounts: accounts.value,
      optType: accountType.value
    };

    await saveOpenaiBatch(payload);

    ElMessage({
      message: '导入成功，请稍后刷新查看',
      type: 'success',
      plain: true
    });

    closeBatchModal();
    await getData();
  } catch {
    ElMessage.error('批量导入失败');
  }
};
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <SubtypeSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <div class="flex items-center">
            <TableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              @add="handleAdd"
              @batch-add="handleBatchAdd"
              @delete="handleBatchDelete"
              @refresh="getData"
              @export="handleExport"
            />
          </div>
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="(selection: OpenAI[]) => checkedRowKeys = selection.map(item => item.id)"
          @sort-change="handleSortChange"
        >
          <ElTableColumn
            v-for="col in columns"
            :key="col.prop"
            v-bind="col"
            :show-overflow-tooltip="col.showOverflowTooltip"
          />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
      <OperateModel v-model:visible="visible" :operate-type="operateType" :data="currentData" @submitted="getData" />
    </ElCard>

    <!-- 批量新增弹窗 -->
    <ElDialog
      v-model="batchVisible"
      title="批量新增账号"
      :width="isMobile ? '95%' : '40%'"
      :close-on-click-modal="false"
    >
      <ElRadioGroup v-model="accountType">
        <ElRadio :label="2" disabled>RefreshToken</ElRadio>
      </ElRadioGroup>
      <ElInput
        v-if="accountType === 2"
        v-model="accounts"
        type="textarea"
        :rows="10"
        placeholder="复制账号密码到这里,每行一个,格式: 【车号,邮箱,密码,refreshToken】"
      />
      <div class="mt-4 text-center">
        <ElButton @click="closeBatchModal">取 消</ElButton>
        <ElButton type="primary" @click="saveBatch">保 存</ElButton>
      </div>
    </ElDialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>

<template>
  <div class="loader-container" v-if="loading">
    <div class="loader">Loading...</div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: true,
    };
  },
  mounted() {
    // 模拟加载过程，实际使用时应替换为真实的加载逻辑
    window.addEventListener('load', this.finishLoading);
  },
  methods: {
    finishLoading() {
      // 在页面加载完成后，2秒后关闭loading
      setTimeout(() => {
        this.loading = false;
      }, 1000);
    },
  },
  beforeUnmount() {
    window.removeEventListener('load', this.finishLoading);
  },
};
</script>

<style scoped>
@keyframes animate8345 {
  0%, 100% {
    filter: hue-rotate(0deg);
  }
  50% {
    filter: hue-rotate(360deg);
  }
}

.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white; /* 明亮模式的背景颜色 */
  z-index: 9999;
}

.loader {
  color: rgb(0, 0, 0);
  background: linear-gradient(to right, #2d60ec, #3ccfda);
  font-size: 30px;
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  animation: animate8345 9s linear infinite;
  font-weight: bold;
}
</style>

package org.seven.share.common.exception;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serial;

/**
 * @ClassName: ServiceException
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/30
 */
@Slf4j
@Getter
public class ServiceException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 1L;

    private final int statusCode;

    public ServiceException(int statusCode, String message){
        super(message);
        this.statusCode = statusCode;
        log.error(message);
    }
}

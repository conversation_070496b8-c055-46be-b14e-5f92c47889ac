package org.seven.share.schedule;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.mapper.ChatGptUserMapper;
import org.seven.share.service.ChatGptConfigService;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * @ClassName: ResetUserLimitScheduler
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/8
 */
@Slf4j
@Component
public class ResetUserLimitScheduler {
    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private ChatGptUserMapper chatGptUserMapper;

    @Scheduled(cron = "0 0 * * * ?")  // 每小时执行一次
    @Transactional(rollbackFor = Exception.class)
    public void resetUserLimit() {
        log.info("开始执行重置用户速率");
        String modelLimits = chatGptConfigService.getValueByKey("modelLimits");
        if (StrUtil.isBlank(modelLimits)) {
            return;
        }
        int count = chatGptUserMapper.updateByCondition(modelLimits);
        log.info("执行{}条重置过期用户速率完成", count);
    }
}

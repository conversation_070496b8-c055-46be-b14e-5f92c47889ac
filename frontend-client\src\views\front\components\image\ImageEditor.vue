<template>
  <div class="space-y-6">
    <!-- 上传区+设置区的网格布局 -->
    <div class="grid md:grid-cols-2 gap-6">
      <!-- 左侧 - 上传和预览 -->
      <div class="space-y-4">
        <label class="flex items-center text-lg font-medium text-slate-800 dark:text-slate-200">
          <Upload class="w-5 h-5 mr-2 text-black dark:text-white" />
          {{ t('draw.editor.uploadImage') }}
        </label>

        <input
          ref="fileInputRef"
          id="image-upload"
          type="file"
          accept="image/*"
          class="sr-only"
          @change="handleImageUpload"
          multiple
        />

        <div
          ref="dropZoneRef"
          :class="[
            'flex flex-col items-center justify-center w-full h-40 border-2 border-dashed rounded-xl p-4',
            'transition-all duration-300 cursor-pointer',
            'bg-slate-50 dark:bg-slate-900 border-slate-200 dark:border-slate-700',
            'hover:border-black dark:hover:border-white hover:bg-gray-50 dark:hover:bg-gray-800',
          ]"
          @click="triggerFileInput"
          @paste="handlePaste"
          @dragover.prevent="handleDragOver"
          @dragleave.prevent="handleDragLeave"
          @drop.prevent="handleDrop"
          tabindex="0"
          @keydown="handleKeyDown"
        >
          <div class="w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-3">
            <ImageIcon class="h-6 w-6 text-black dark:text-white" />
          </div>
          <div class="text-center">
            <p class="text-sm font-medium text-slate-800 dark:text-slate-200 mb-1">{{ t('draw.editor.dragDropUpload') }}</p>
            <p class="text-xs text-slate-500 dark:text-slate-400">{{ t('draw.editor.supportedFormats') }}</p>
          </div>
          <div v-if="images.length > 0" class="mt-2 text-xs text-gray-700 dark:text-gray-200 font-medium">
            {{ t('draw.editor.uploadedCount', { count: images.length }) }}
          </div>
        </div>

        <!-- 缩略图区域 -->
        <div v-if="images.length > 0" class="grid grid-cols-5 sm:grid-cols-6 gap-2">
          <div
            v-for="(image, index) in images"
            :key="index"
            :class="[
              'relative aspect-square rounded-lg overflow-hidden cursor-pointer border-2 transition-all duration-200',
              currentImageIndex === index
                ? 'border-black dark:border-white shadow-md scale-105'
                : 'border-transparent hover:border-gray-400 dark:hover:border-gray-500',
            ]"
            @click="selectImage(index)"
          >
            <img
              :src="image.preview"
              alt="Thumbnail"
              class="w-full h-full object-cover"
            />
            <button
              class="absolute top-1 right-1 w-5 h-5 bg-black/70 text-white rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"
              @click.stop="removeImage(index)"
            >
              <X class="w-3 h-3" />
            </button>
          </div>
        </div>

        <!-- 主图片和遮罩编辑 -->
        <div v-if="currentImage" class="space-y-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <ImageIcon class="w-5 h-5 mr-2 text-gray-700 dark:text-gray-200" />
              <span class="text-base font-medium text-slate-700 dark:text-slate-300">{{ t('draw.editor.imagePreview') }}</span>
            </div>

            <button
              v-if="currentImage"
              @click="startMaskMode(currentImageIndex)"
              class="px-3 py-1.5 text-sm rounded-md bg-gray-100 dark:bg-gray-900/30 hover:bg-gray-200 text-gray-700 dark:text-gray-300 font-medium transition-all flex items-center"
            >
              <Brush class="h-4 w-4 mr-1.5" />
              {{ t('draw.editor.localRedraw') }}
            </button>
          </div>
          
          <div class="grid md:grid-cols-2 gap-4">
            <!-- 主图片 -->
            <div class="relative aspect-square rounded-lg overflow-hidden border border-slate-200 dark:border-slate-700">
              <img
                :src="currentImage.preview"
                alt="Main Image"
                class="w-full h-full object-cover"
              />
              <div class="absolute bottom-2 left-2 right-2 bg-black/50 text-white text-xs p-1 rounded text-center">
                {{ t('draw.editor.originalImage') }}
              </div>
            </div>

            <!-- 遮罩区域 -->
            <div class="relative aspect-square rounded-lg overflow-hidden border border-slate-200 dark:border-slate-700">
              <template v-if="!currentImage.mask">
                <div 
                  class="absolute inset-0 flex flex-col items-center justify-center bg-slate-50 dark:bg-slate-900 cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors"
                  @click="startMaskMode(currentImageIndex)"
                >
                  <Brush class="w-8 h-8 text-slate-400 mb-2" />
                  <span class="text-sm text-slate-500 dark:text-slate-400">{{ t('draw.editor.clickToAddRedraw') }}</span>
                </div>
              </template>
              <template v-else>
                <img
                  :src="currentImage.preview"
                  class="absolute inset-0 w-full h-full object-cover"
                />
                <div class="absolute inset-0 w-full h-full" style="background-color: rgba(200, 200, 200, 0.3);"></div>
                <img
                  :src="currentImage.uiMask || currentImage.mask"
                  class="absolute inset-0 w-full h-full object-cover"
                  style="mix-blend-mode: lighten; filter: brightness(1.5) saturate(2);"
                />
                <div class="absolute inset-0 w-full h-full border-2 border-dashed border-black dark:border-white pointer-events-none"></div>
                <div class="absolute bottom-2 left-2 right-2 bg-black/50 text-white text-xs p-1 rounded text-center">
                  {{ t('draw.editor.markedRedrawArea') }}
                </div>
              </template>
            </div>
          </div>
          
          <div v-if="currentImage.mask" class="flex justify-center">
            <button
              @click="startMaskMode(currentImageIndex)"
              class="px-3 py-1 text-xs rounded-md bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 transition-all flex items-center"
            >
              <Pencil class="h-3 w-3 mr-1" />
              {{ t('draw.editor.modifyRedrawArea') }}
            </button>
          </div>
        </div>

        <!-- 遮罩编辑模态框 -->
        <div 
          v-if="isMaskMode"
          class="fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4"
          @click="finishMask"
        >
          <div 
            class="relative max-w-4xl max-h-[90vh]"
            @click.stop
          >
            <canvas
              ref="maskCanvas"
              class="absolute inset-0 w-full h-full cursor-crosshair"
              @mousedown="startDrawing"
              @mousemove="draw"
              @mouseup="stopDrawing"
              @mouseleave="stopDrawing"
            ></canvas>
            <img
              :src="currentImage.preview"
              class="w-full h-auto max-h-[80vh] object-contain rounded-lg opacity-50"
              style="pointer-events: none;"
            />
            <div class="absolute top-4 left-4 right-4 flex justify-between items-center">
              <div class="flex items-center">
                <Brush class="w-4 h-4 mr-2 text-white" />
                <span class="text-white font-medium">{{ t('draw.editor.localRedraw') }}</span>
              </div>
              <div class="flex space-x-2 items-center">
                <div class="flex items-center mr-4">
                  <span class="text-white text-xs mr-2">{{ t('draw.editor.brushSize') }}:</span>
                  <input 
                    type="range" 
                    min="5" 
                    max="50" 
                    step="5"
                    :value="brushSize"
                    @input="updateBrushSize($event.target.value)"
                    class="w-24"
                  />
                  <span class="text-white text-xs ml-2">{{ brushSize }}px</span>
                </div>
                <button
                  @click.stop="clearMask"
                  class="px-3 py-1 text-sm rounded-md bg-white/20 hover:bg-white/30 text-white transition-all duration-200"
                >
                  {{ t('draw.editor.clear') }}
                </button>
                <button
                  @click.stop="finishMask"
                  class="px-3 py-1 text-sm rounded-md bg-gray-700 dark:bg-gray-200 hover:bg-gray-600  text-white transition-all duration-200"
                >
                  {{ t('draw.editor.finish') }}
                </button>
              </div>
            </div>
            <div class="absolute bottom-4 left-4 right-4">
              <div class="bg-black/70 backdrop-blur-sm p-3 rounded-lg">
                <p class="text-white text-sm mb-2">
                  <span class="bg-blue-500 px-1 py-0.5 rounded text-xs mr-1">{{ t('common.tip') }}</span>
                  {{ t('draw.editor.maskTip') }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速风格选择 -->
        <!-- <div class="space-y-2 mt-3">
          <p class="text-sm font-medium text-slate-600 dark:text-slate-400 flex items-center">
            <Sparkles class="h-3.5 w-3.5 mr-1 text-purple-500" />
            快速风格选择
          </p>
          <div class="flex flex-wrap gap-2">
            <button
              v-for="(preset, index) in STYLE_PRESETS"
              :key="index"
              @click="applyPresetStyle(preset.name)"
              class="px-3 py-1 text-xs rounded-full bg-slate-50 dark:bg-slate-900 hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:text-purple-600 dark:hover:text-purple-400 border border-slate-200 dark:border-slate-700 transition-colors flex items-center"
            >
              <span class="mr-1">{{ preset.icon }}</span>
              <span>{{ preset.name }}</span>
            </button>
          </div>
        </div> -->

        <!-- 编辑提示词 -->
        <div class="space-y-2">
          <div class="flex items-center">
            <MessageSquare class="w-5 h-5 mr-2 text-black dark:text-white" />
            <span class="text-base font-medium text-slate-700 dark:text-slate-300">{{ t('draw.editor.editPrompt') }}</span>
          </div>
          <textarea
            id="edit-prompt"
            v-model="editPrompt"
            :placeholder="t('draw.editor.promptPlaceholder')"
            class="min-h-[80px] text-base resize-y bg-slate-50 dark:bg-slate-900 border-slate-200 dark:border-slate-700 focus:border-black dark:focus:border-white focus:ring-black dark:focus:ring-white w-full rounded-md p-2"
          ></textarea>
        </div>

        <!-- 设置区和操作按钮 -->
        <div class="bg-slate-50 dark:bg-slate-900 rounded-xl p-4 space-y-4">
          <div class="flex items-center mb-3">
            <Settings class="w-5 h-5 mr-2 text-black dark:text-white" />
            <span class="text-base font-medium text-slate-700 dark:text-slate-300">{{ t('draw.editor.imageSettings') }}</span>
          </div>
          
          <div class="grid grid-cols-3 gap-3 items-end">
            <div class="space-y-1 col-span-1">
              <label for="edit-quality" class="text-sm font-medium text-slate-600 dark:text-slate-400">
                {{ t('draw.editor.imageQuality') }}
              </label>
              <select
                id="edit-quality"
                v-model="imageQuality"
                class="w-full h-9 bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700 rounded-md px-2 text-sm"
              >
                <option value="low">{{ t('draw.editor.quality.low') }}</option>
                <option value="medium">{{ t('draw.editor.quality.medium') }}</option>
                <option value="high">{{ t('draw.editor.quality.high') }}</option>
              </select>
            </div>

            <div class="space-y-1 col-span-1">
              <label for="edit-size" class="text-sm font-medium text-slate-600 dark:text-slate-400">
                {{ t('draw.editor.imageSize') }}
              </label>
              <select
                id="edit-size"
                v-model="imageSize"
                class="w-full h-9 bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700 rounded-md px-2 text-sm"
              >
                <option value="1024x1024">{{ t('draw.editor.size.square') }}</option>
                <option value="1024x1536">{{ t('draw.editor.size.portrait') }}</option>
                <option value="1536x1024">{{ t('draw.editor.size.landscape') }}</option>
              </select>
            </div>

            <div v-if="drawCount > 1" class="space-y-1 col-span-1">
              <label for="edit-count" class="text-sm font-medium text-slate-600 dark:text-slate-400">
                {{ t('draw.editor.generateCount') }}
              </label>
              <select
                id="edit-count"
                v-model="imageCount"
                class="w-full h-9 bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700 rounded-md px-2 text-sm"
              >
                <option v-for="n in drawCount" :key="n" :value="n">{{ n }}{{ t('common.unit.piece') }}</option>
              </select>
            </div>
          </div>

          <div class="w-full">
            <button
              @click="handleEditImage"
              :disabled="isEditing || !currentImage || !editPrompt"
              class="w-full h-10 text-sm font-medium bg-black dark:bg-white text-white dark:text-black rounded-lg flex items-center justify-center hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <template v-if="isEditing">
                <Loader2 class="w-4 h-4 mr-2 animate-spin" />
                {{ t('draw.editor.editing') }}
              </template>
              <template v-else>
                <Pencil class="w-4 h-4 mr-2" />
                {{ t('draw.editor.startEdit') }}
              </template>
            </button>
          </div>

          <div 
            v-if="error"
            class="bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 p-3 rounded-lg text-sm break-words whitespace-pre-wrap"
          >
            <div class="flex items-start">
              <div class="w-4 h-4 mr-2 flex-shrink-0">⚠️</div>
              <div class="flex-1 min-w-0">{{ error }}</div>
            </div>
          </div>

          <div 
            v-if="statusMessage"
            class="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200 p-3 rounded-lg text-sm"
          >
            <div class="flex items-center">
              <Loader2 v-if="isEditing" class="w-4 h-4 mr-2 animate-spin text-blue-500" />
              <div v-else class="w-4 h-4 mr-2">✓</div>
              {{ statusMessage }}
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧 - 只显示编辑结果 -->
      <div class="space-y-4 h-full flex flex-col">
        <!-- 默认图像占位区 - 当没有上传图片或没有编辑结果时显示 -->
        <div v-if="!editedImages.length > 0" class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 flex flex-col items-center justify-center p-8 text-center flex-grow">
          <div class="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
            <ImageIcon class="h-10 w-10 text-black dark:text-white opacity-50" />
          </div>
          <h3 class="text-lg font-medium text-slate-800 dark:text-slate-200 mb-2">{{ t('draw.editor.imageWillShowHere') }}</h3>
          <p class="text-sm text-slate-500 dark:text-slate-400 mb-4">{{ t('draw.editor.uploadAndEdit') }}</p>
        </div>
        
        <!-- 添加提醒和提示信息 - 当没有编辑结果时显示，减少空白并加入示例图片 -->
        <div v-if="!editedImages.length > 0" class="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 flex flex-col">
          <div class="flex items-start mb-3">
            <Info class="w-5 h-5 text-gray-600 dark:text-gray-400 mr-3 flex-shrink-0 mt-0.5" />
            <p class="text-base text-gray-800 dark:text-gray-200 font-medium">{{ t('draw.editor.editTips') }}</p>
          </div>

          <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1.5 list-disc pl-6 mb-3">
            <li>{{ t('draw.editor.tips.0') }}</li>
            <li>{{ t('draw.editor.tips.1') }}</li>
            <li>{{ t('draw.editor.tips.2') }}</li>
            <li>{{ t('draw.editor.tips.3') }}</li>
            <li>{{ t('draw.editor.tips.4') }}</li>
          </ul>
        </div>

        <!-- 编辑结果区域 -->
        <div v-if="editedImages.length > 0" class="animate-fadeIn">
          <div class="mb-2 flex items-center justify-between">
            <div class="flex items-center">
              <Sparkles class="w-4 h-4 mr-2 text-black dark:text-white" />
              <span class="text-lg font-medium text-slate-700 dark:text-slate-300">{{ t('draw.editor.editResult') }}</span>
            </div>
            <div class="flex space-x-2">
              <button
                @click="viewOriginalImage"
                class="h-7 px-2 rounded-md bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 transition-all duration-200 flex items-center text-xs"
                :title="t('draw.editor.viewOriginal')"
              >
                <Search class="h-3 w-3 mr-1" />
                {{ t('draw.editor.viewOriginal') }}
              </button>
              <button
                @click="downloadImage"
                class="h-7 px-2 rounded-md bg-black dark:bg-white text-white dark:text-black hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 flex items-center text-xs"
                :title="t('draw.editor.download')"
              >
                <Download class="h-3 w-3 mr-1" />
                {{ t('draw.editor.download') }}
              </button>
            </div>
          </div>
          
          <div class="space-y-4">
            <!-- 主图片展示区 -->
            <div class="relative rounded-lg overflow-hidden bg-slate-100 dark:bg-slate-800">
              <img
                :src="editedImages[currentEditedImageIndex]"
                :alt="'Edited ' + (currentEditedImageIndex + 1)"
                class="w-full h-auto max-h-[400px] object-contain cursor-pointer overflow-hidden"
                @click="enlargeImage(currentEditedImageIndex)"
              />
              
              <!-- 导航按钮 -->
              <div v-if="editedImages.length > 1" class="absolute inset-0 flex items-center justify-between pointer-events-none">
                <button
                  @click.stop="prevEditedImage"
                  :disabled="currentEditedImageIndex === 0"
                  class="pointer-events-auto h-8 w-8 md:h-10 md:w-10 rounded-full bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm shadow-lg hover:scale-105 transition-all duration-200 flex items-center justify-center ml-2"
                  :class="{ 'opacity-50 cursor-not-allowed': currentEditedImageIndex === 0 }"
                >
                  <ChevronLeft class="h-5 w-5" />
                </button>
                <button
                  @click.stop="nextEditedImage"
                  :disabled="currentEditedImageIndex === editedImages.length - 1"
                  class="pointer-events-auto h-8 w-8 md:h-10 md:w-10 rounded-full bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm shadow-lg hover:scale-105 transition-all duration-200 flex items-center justify-center mr-2"
                  :class="{ 'opacity-50 cursor-not-allowed': currentEditedImageIndex === editedImages.length - 1 }"
                >
                  <ChevronRight class="h-5 w-5" />
                </button>
              </div>

              <!-- 图片计数器 -->
              <div v-if="editedImages.length > 1" class="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded-md">
                {{ t('draw.editor.imageCount', { current: currentEditedImageIndex + 1, total: editedImages.length }) }}
              </div>
            </div>

            <!-- 缩略图预览 -->
            <div v-if="editedImages.length > 1" class="flex justify-center gap-2">
              <button
                v-for="(image, index) in editedImages"
                :key="index"
                @click="currentEditedImageIndex = index"
                class="w-12 h-12 rounded-md overflow-hidden border-2 transition-all duration-200"
                :class="[
                  currentEditedImageIndex === index
                    ? 'border-black dark:border-white scale-110'
                    : 'border-transparent hover:border-black dark:hover:border-white'
                ]"
              >
                <img
                  :src="image"
                  :alt="'Thumbnail ' + (index + 1)"
                  class="w-full h-full object-cover"
                />
              </button>
            </div>

            <!-- 提示词和操作建议 -->
            <div class="flex flex-col justify-between h-full">
              <div 
                v-if="editPrompt"
                class="bg-slate-50 dark:bg-slate-900 border-l-4 border-black dark:border-white rounded-lg p-3 shadow-sm flex flex-col"
              >
                <div class="flex items-start mb-2">
                  <MessageSquare class="w-4 h-4 mr-2 text-black dark:text-white mt-0.5 flex-shrink-0" />
                  <p class="text-slate-700 dark:text-slate-300 text-xs font-medium">{{ t('draw.editor.prompt') }}</p>
                </div>
                <p class="text-slate-600 dark:text-slate-400 text-xs leading-relaxed pl-6">{{ editPrompt }}</p>
              </div>
              
              <!-- 添加指导和操作建议 -->
              <div class="mt-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3 text-xs">
                <div class="flex items-start">
                  <Info class="w-4 h-4 mr-2 text-gray-600 dark:text-gray-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <p class="text-gray-800 dark:text-gray-200 font-medium mb-1">{{ t('draw.editor.usageTips') }}</p>
                    <ul class="text-gray-600 dark:text-gray-400 space-y-1 pl-2">
                      <li>• {{ t('draw.editor.usageTipsList.0') }}</li>
                      <li>• {{ t('draw.editor.usageTipsList.1') }}</li>
                      <li>• {{ t('draw.editor.usageTipsList.2') }}</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片放大模态框 -->
    <div 
      v-if="showImageModal"
      class="fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4"
      @click="closeModal"
    >
      <div class="relative max-w-4xl max-h-[90vh]">
        <img
          :src="editedImages[0]"
          alt="Enlarged"
          class="max-w-full max-h-[90vh] object-contain rounded-lg"
          @click.stop
        />
        <button
          @click="closeModal"
          class="absolute -top-4 -right-4 h-10 w-10 rounded-full bg-white text-slate-900 hover:bg-slate-100 flex items-center justify-center"
        >
          <X class="h-5 w-5" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted, onMounted, watchEffect, nextTick } from 'vue';
import { Loader2, Pencil, Search, Download, MessageSquare, X, ImageIcon, Upload, Sparkles, Info, ChevronLeft, ChevronRight, Brush, CheckCircle, Settings } from 'lucide-vue-next';
import { editImage, fetchImageStatus } from '@/api/image';
import { useSiteStore } from '@/store/modules/site';
import { useI18n } from 'vue-i18n';
const props = defineProps({
  imageUrl: {
    type: String,
    required: false
  }
});

const emit = defineEmits(['update:imageUrl']);

const { t } = useI18n();

// 状态
const images = ref([]);
const currentImageIndex = ref(-1);
const editPrompt = ref('');
const imageQuality = ref('medium');
const imageSize = ref('1024x1024');
const imageCount = ref(1);
const editedImages = ref([]);
const currentEditedImageIndex = ref(0);
const isEditing = ref(false);
const error = ref('');
const statusMessage = ref('');
const showImageModal = ref(false);
const taskId = ref(null);
const fileInputRef = ref(null);
const dropZoneRef = ref(null);
let pollingInterval = null;
let timeout = null;
const siteStore = useSiteStore();

const drawCount = computed(() => {
  return siteStore.drawCount ? Number(siteStore.drawCount) : 1;
});

const drawModel = computed(() => {
  return siteStore.drawModel && siteStore.drawModel !== '' ? siteStore.drawModel : 'gpt-image-1';
});

// 遮罩相关状态
const isMaskMode = ref(false);
const maskCanvas = ref(null);
const isDrawing = ref(false);
const lastX = ref(null);
const lastY = ref(null);
const ctx = ref(null);
const brushSize = ref(20);

// 示例提示词，首次加载时随机显示一个
const EXAMPLE_PROMPTS = [
  "将背景改为海滩场景，保持人物不变",
  "将图片转换为吉卜力风格的动画效果",
  "将图片变成水彩画风格，增加艺术感",
  "添加一个明亮的阳光效果和温暖的色调",
  "将图片改为四格漫画风格，增加可爱的表情"
];

// 生命周期钩子 - 组件挂载时
onMounted(() => {
  // 随机选择一个示例提示词
  if (!editPrompt.value) {
    const randomIndex = Math.floor(Math.random() * EXAMPLE_PROMPTS.length);
    editPrompt.value = EXAMPLE_PROMPTS[randomIndex];
  }
});

// 计算属性
const currentImage = computed(() => {
  return images.value.length > 0 && currentImageIndex.value >= 0 
    ? images.value[currentImageIndex.value] 
    : null;
});

// 风格预设
const STYLE_PRESETS = [
  { name: "吉卜力风格", icon: "🏞️", description: "宫崎骏动画风格" },
  { name: "3D渲染风格", icon: "🧊", description: "逼真的3D效果" },
  { name: "Q版公仔风格", icon: "🧸", description: "可爱卡通形象" },
  { name: "赛博朋克风格", icon: "🌃", description: "未来科技感" },
  { name: "日式动漫风格", icon: "🎭", description: "二次元插画风格" },
  { name: "水彩画风格", icon: "🎨", description: "柔和水彩效果" },
  { name: "日本小人风格", icon: "👺", description: "日本传统小人物风格" },
  { name: "Q版表情贴纸", icon: "😊", description: "可爱表情贴纸风格" },
  { name: "四宫格漫画", icon: "🗣️", description: "四格分镜漫画样式" },
  { name: "史努比风格", icon: "🐶", description: "经典卡通狗风格" },
  { name: "二次元风格", icon: "💫", description: "日本动漫风格" },
  { name: "像素风格", icon: "👾", description: "复古像素艺术" },
  { name: "迪士尼风格", icon: "🏰", description: "迪士尼动画风格" },
  { name: "皮克斯风格", icon: "✨", description: "皮克斯3D动画风格" },
  { name: "写实风格", icon: "🖼️", description: "逼真写实艺术风格" },
];

// 方法
const handleImageUpload = (e) => {
  const files = e.target.files;
  if (files && files.length > 0) {
    addImages(files);
  }
};

const addImages = (files) => {
  Array.from(files).forEach((file) => {
    if (file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onloadend = () => {
        // 获取图片尺寸
        const img = new Image();
        img.onload = function() {
          images.value.push({
            file: file,
            preview: reader.result,
            width: img.naturalWidth,
            height: img.naturalHeight
          });

          if (currentImageIndex.value === -1) {
            currentImageIndex.value = 0;
          }
        };
        img.src = reader.result;
      };
      reader.readAsDataURL(file);
    }
  });
};

const handlePaste = (e) => {
  const items = e.clipboardData.items;
  let hasImage = false;

  for (const item of items) {
    if (item.type.indexOf("image") === 0) {
      hasImage = true;
      const blob = item.getAsFile();
      if (blob) {
        addImages([blob]);
      }
    }
  }

  if (hasImage) {
    e.preventDefault();
  }
};

const handleDragOver = (e) => {
  if (dropZoneRef.value) {
    dropZoneRef.value.classList.add("border-black", "dark:border-white", "bg-gray-50", "dark:bg-gray-800");
  }
};

const handleDragLeave = (e) => {
  if (dropZoneRef.value) {
    dropZoneRef.value.classList.remove("border-black", "dark:border-white", "bg-gray-50", "dark:bg-gray-800");
  }
};

const handleDrop = (e) => {
  if (dropZoneRef.value) {
    dropZoneRef.value.classList.remove("border-black", "dark:border-white", "bg-gray-50", "dark:bg-gray-800");
  }

  if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
    addImages(e.dataTransfer.files);
  }
};

const handleKeyDown = (e) => {
  if (e.key === "Enter" || e.key === " ") {
    triggerFileInput();
  }
};

const selectImage = (index) => {
  if (index >= 0 && index < images.value.length) {
    currentImageIndex.value = index;
  }
};

const removeImage = (index) => {
  images.value = images.value.filter((_, i) => i !== index);
  if (images.value.length === 0) {
    currentImageIndex.value = -1;
  } else if (currentImageIndex.value >= index) {
    currentImageIndex.value = Math.max(0, currentImageIndex.value - 1);
  }
};

const handleEditImage = async () => {
  if (!currentImage.value || !editPrompt.value) {
    error.value = t('draw.editor.errors.uploadImageAndPrompt');
    return;
  }

  error.value = "";
  statusMessage.value = t('draw.editor.status.preparing');
  isEditing.value = true;

  try {
    const formData = new FormData();
    
    if (currentImage.value && currentImage.value.file) {
      formData.append('image', currentImage.value.file);
    } else {
      throw new Error(t('draw.editor.errors.invalidImageFile'));
    }
    
    // 添加遮罩
    if (currentImage.value.mask) {
      const maskBlob = await fetch(currentImage.value.mask).then(r => r.blob());
      formData.append('mask', maskBlob);
      
      // 打印mask信息用于调试
      console.log("Mask添加成功，大小:", maskBlob.size, "类型:", maskBlob.type);
      
      // 检查mask尺寸
      const img = new Image();
      img.src = currentImage.value.mask;
      img.onload = () => {
        console.log("Mask尺寸:", img.width, "x", img.height);
        console.log("原图尺寸:", currentImage.value.width, "x", currentImage.value.height);
      };
    }
  console.log(siteStore.drawModel);
    
    formData.append('prompt', editPrompt.value);
    formData.append('size', imageSize.value);
    formData.append('quality', imageQuality.value);
    formData.append('model', drawModel.value);
    formData.append('n', imageCount.value.toString());
    formData.append('response_format', 'url');
    
    console.log("发送编辑请求，文件大小:", currentImage.value.file.size);
    
    // 发送请求获取任务ID - 使用正确的方式调用API
    const id = await editImage(formData);
    
    console.log("获取任务ID:", id);

    if (id) {
      taskId.value = id;
      await startPollingStatus();
    } else {
      throw new Error("未获取到任务ID，请重试");
    }
  } catch (err) {
    console.error("编辑图片错误:", err);
    
    if (err.response) {
      console.error("错误响应详情:", err.response.data);
      error.value = err.response.data?.error?.message || `请求失败 (${err.response.status})`;
    } else if (err.request) {
      console.error("无响应详情:", err.request);
      error.value = "服务器没有响应，请检查网络连接或服务器状态";
    } else {
      error.value = err.message || "图片编辑失败";
    }
    
    statusMessage.value = "";
    emit('error', error.value);
    isEditing.value = false;
  }
};

const startPollingStatus = async () => {
  if (pollingInterval) {
    clearInterval(pollingInterval);
    pollingInterval = null;
  }

  statusMessage.value = t('draw.editor.status.processing');
  
  
  // 设置轮询间隔，每5秒检查一次
  pollingInterval = setInterval(async () => {
    await checkImageStatus();
  }, 5000);
  
  // 设置最长轮询时间为5分钟
  timeout = setTimeout(() => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      pollingInterval = null;
      
      if (isEditing.value) {
        isEditing.value = false;
        error.value = t('draw.editor.errors.editTimeout');
        statusMessage.value = "";
        emit('error', t('draw.editor.errors.editTimeout'));
      }
    }
  }, 300000);
};

const checkImageStatus = async () => {
  if (!taskId.value) {
    console.warn("没有任务ID，无法检查状态");
    return;
  }
  
  try {
    console.log("开始检查任务状态, ID:", taskId.value);
    const response = await fetchImageStatus(taskId.value);
    console.log("状态检查响应:", response);
    
    if (!response) {
      console.error("状态检查没有返回数据");
      throw new Error("状态检查失败：没有返回数据");
    }
    
    const status = response.status;
    const result = response.result;
    console.log("任务状态:", status, "结果:", result);
    
    if (status === "COMPLETED") {
      if (result && result.data && Array.isArray(result.data)) {
        // 获取所有图片URL
        const imageUrls = result.data.map(item => item.url).filter(url => url);
        console.log("获取到图片URLs:", imageUrls);
        
        if (imageUrls.length > 0) {
          // 更新编辑后的图片数组
          editedImages.value = imageUrls;
          statusMessage.value = t('draw.editor.status.successGenerated', { count: imageUrls.length });
          
          // 发出图片编辑成功事件
          emit('image-edited', {
            urls: imageUrls,
            prompt: editPrompt.value,
            timestamp: new Date(),
            type: 'edited',
            size: imageSize.value,
            quality: imageQuality.value
          });
        } else {
          console.error("任务完成但没有有效的图片URL:", result);
          statusMessage.value = t('draw.editor.status.completedInvalid');
        }
      } else {
        console.error("任务完成但数据格式无效:", result);
        statusMessage.value = t('draw.editor.status.completedInvalid');
      }
    
      // 清除轮询
      if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
        console.log("清除状态轮询");
      }
      
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
        console.log("清除超时计时器");
      }
      
      isEditing.value = false;
      taskId.value = null;
      
    } else if (status === "FAILED") {
      // 任务失败
      error.value = response?.errorMessage || "图片编辑失败";
      statusMessage.value = "";
      emit('error', error.value);
      
      // 清除轮询
      if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
      }
      
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      
      isEditing.value = false;
      taskId.value = null;
    } else {
      // 仍在处理中
      console.log("任务仍在处理中");
      statusMessage.value = t('draw.editor.status.waitingResult');
    }
  } catch (err) {
    console.error("检查状态错误详情:", err);
    // 出错时不停止轮询，让超时机制处理
    statusMessage.value = t('draw.editor.status.checkingStatus');
  }
};

const viewOriginalImage = () => {
  if (editedImages.value.length > 0) {
    window.open(editedImages.value[0], "_blank");
  }
};

const changeSchema = (url) => {
  if (url.startsWith('http://')) {
    return url.replace('http://', 'https://');
  }
  return url;
};

const downloadImage = async () => {
  if (editedImages.value.length > 0) {
    try {
      const response = await fetch(changeSchema(editedImages.value[currentEditedImageIndex.value]));
      if (!response.ok) {
        throw new Error('Failed to fetch image');
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `ai-edited-image-${new Date().getTime()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
      error.value = t('draw.editor.errors.downloadFailed');
    }
  }
};

const applyPresetStyle = (style) => {
  editPrompt.value = style;
};

const enlargeImage = (index) => {
  if (editedImages.value[index]) {
    showImageModal.value = true;
    // 防止滚动
    document.body.style.overflow = "hidden";
  }
};

const closeModal = () => {
  showImageModal.value = false;
  // 恢复滚动
  document.body.style.overflow = "auto";
};

const triggerFileInput = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
};

const nextEditedImage = () => {
  if (currentEditedImageIndex.value < editedImages.value.length - 1) {
    currentEditedImageIndex.value++;
  }
};

const prevEditedImage = () => {
  if (currentEditedImageIndex.value > 0) {
    currentEditedImageIndex.value--;
  }
};

// 遮罩相关方法
const startMaskMode = async (index) => {
  isMaskMode.value = true;
  currentImageIndex.value = index;
  await nextTick();
  initMaskCanvas();
};

const initMaskCanvas = () => {
  if (!maskCanvas.value || !currentImage.value) return;
  
  const canvas = maskCanvas.value;
  const img = new Image();
  img.src = currentImage.value.preview;
  
  img.onload = () => {
    // 设置画布尺寸与图片实际尺寸一致
    canvas.width = img.naturalWidth;
    canvas.height = img.naturalHeight;
    
    // 获取上下文
    ctx.value = canvas.getContext('2d', { willReadFrequently: true });
    
    // 设置绘制样式
    ctx.value.strokeStyle = 'rgba(77, 0, 255, 1)'; // 紫蓝色描边
    ctx.value.fillStyle = 'rgba(77, 0, 255, 0.5)'; // 紫蓝色填充
    ctx.value.lineWidth = brushSize.value; // 使用画笔尺寸变量
    ctx.value.lineCap = 'round';
    ctx.value.lineJoin = 'round';
    
    // 移除虚线样式
    ctx.value.setLineDash([]);
    
    // 清除画布
    ctx.value.clearRect(0, 0, canvas.width, canvas.height);
    
    // 如果已有遮罩，加载它（优先使用原始绘制数据）
    if (currentImage.value.mask) {
      const maskImg = new Image();
      // 加载原图作为参考
      maskImg.src = currentImage.value.preview;
      maskImg.onload = () => {
        // 先绘制一个淡色背景
        ctx.value.fillStyle = 'rgba(240, 240, 240, 0.2)';
        ctx.value.fillRect(0, 0, canvas.width, canvas.height);
        
        // 加载原图作为参考
        ctx.value.globalAlpha = 0.2;
        ctx.value.drawImage(maskImg, 0, 0);
        ctx.value.globalAlpha = 1.0;

        // 如果有之前的遮罩数据，通过绘制原始遮罩来恢复
        if (currentImage.value.uiMask) {
          // 如果有UI遮罩，直接加载它
          const uiMask = new Image();
          uiMask.src = currentImage.value.uiMask;
          uiMask.onload = () => {
            ctx.value.globalCompositeOperation = 'source-over';
            ctx.value.drawImage(uiMask, 0, 0);
          };
        } else {
          // 否则，从API遮罩创建适合UI显示的内容
          const originalMask = new Image();
          originalMask.src = currentImage.value.mask;
          originalMask.onload = () => {
            // 创建临时画布提取遮罩信息
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = canvas.width;
            tempCanvas.height = canvas.height;
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.drawImage(originalMask, 0, 0);
            
            // 获取像素数据
            const imageData = tempCtx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;
            
            // 创建一个新画布，用于绘制结果
            const resultCanvas = document.createElement('canvas');
            resultCanvas.width = canvas.width;
            resultCanvas.height = canvas.height;
            const resultCtx = resultCanvas.getContext('2d');
            
            // 设置紫蓝色填充
            resultCtx.fillStyle = 'rgba(77, 0, 255, 0.5)';
            
            // 采样步长，避免逐像素处理
            const sampleStep = Math.max(1, Math.floor(brushSize.value / 4));
            
            // 遍历像素，采样方式寻找透明区域（这些是被选中的区域）
            for (let y = 0; y < tempCanvas.height; y += sampleStep) {
              for (let x = 0; x < tempCanvas.width; x += sampleStep) {
                const i = (y * tempCanvas.width + x) * 4;
                // 如果是完全透明的像素（alpha = 0），这是被选中的区域
                if (data[i+3] === 0) {
                  // 画一个圆点
                  resultCtx.beginPath();
                  resultCtx.arc(x, y, brushSize.value / 3, 0, Math.PI * 2);
                  resultCtx.fill();
                }
              }
            }
            
            // 将结果绘制到主画布
            ctx.value.drawImage(resultCanvas, 0, 0);
          };
        }
      };
    }
  };
};

const startDrawing = (e) => {
  if (!ctx.value) return;
  isDrawing.value = true;
  
  const rect = maskCanvas.value.getBoundingClientRect();
  const scaleX = maskCanvas.value.width / rect.width;
  const scaleY = maskCanvas.value.height / rect.height;
  
  // 记录起始点
  lastX.value = (e.clientX - rect.left) * scaleX;
  lastY.value = (e.clientY - rect.top) * scaleY;
};

const draw = (e) => {
  if (!isDrawing.value || !ctx.value || lastX.value === null || lastY.value === null) return;
  
  const rect = maskCanvas.value.getBoundingClientRect();
  const scaleX = maskCanvas.value.width / rect.width;
  const scaleY = maskCanvas.value.height / rect.height;
  
  // 计算当前点
  const currentX = (e.clientX - rect.left) * scaleX;
  const currentY = (e.clientY - rect.top) * scaleY;
  
  // 开始绘制线条
  ctx.value.beginPath();
  ctx.value.moveTo(lastX.value, lastY.value);
  ctx.value.lineTo(currentX, currentY);
  ctx.value.stroke();
  
  // 更新最后的位置
  lastX.value = currentX;
  lastY.value = currentY;
};

const stopDrawing = () => {
  if (!isDrawing.value) return;
  
  isDrawing.value = false;
  lastX.value = null;
  lastY.value = null;
};

const clearMask = () => {
  if (!ctx.value) return;
  ctx.value.clearRect(0, 0, maskCanvas.value.width, maskCanvas.value.height);
};

const finishMask = () => {
  if (!maskCanvas.value || !ctx.value) return;
  
  const canvas = maskCanvas.value;
  const imageData = ctx.value.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;
  
  // 创建两个新画布：一个用于API的遮罩，一个用于UI显示
  const apiCanvas = document.createElement('canvas');
  apiCanvas.width = canvas.width;
  apiCanvas.height = canvas.height;
  const apiCtx = apiCanvas.getContext('2d');
  
  const uiCanvas = document.createElement('canvas');
  uiCanvas.width = canvas.width;
  uiCanvas.height = canvas.height;
  const uiCtx = uiCanvas.getContext('2d');
  
  // 创建一个全黑不透明的背景 (用于API遮罩)
  apiCtx.fillStyle = 'black';
  apiCtx.fillRect(0, 0, canvas.width, canvas.height);
  
  // 创建视觉效果更明显的UI遮罩
  uiCtx.fillStyle = 'rgba(0, 0, 0, 0)';
  uiCtx.fillRect(0, 0, canvas.width, canvas.height);
  
  // 获取API遮罩的图像数据
  const apiImageData = apiCtx.getImageData(0, 0, canvas.width, canvas.height);
  const apiData = apiImageData.data;
  
  // 获取UI遮罩的图像数据
  const uiImageData = uiCtx.getImageData(0, 0, canvas.width, canvas.height);
  const uiData = uiImageData.data;
  
  // 遍历原始画布数据
  for (let i = 0; i < data.length; i += 4) {
    // 如果像素有任何不透明度（被选中的区域）
    if (data[i + 3] > 0) {
      // 将API遮罩这些区域设为完全透明 (将被编辑)
      apiData[i] = 0;      // R = 0
      apiData[i + 1] = 0;  // G = 0
      apiData[i + 2] = 0;  // B = 0
      apiData[i + 3] = 0;  // A = 0 (完全透明)
      
      // 将UI遮罩这些区域设为鲜艳的颜色
      uiData[i] = 77;      // R (紫色调)
      uiData[i + 1] = 0;   // G
      uiData[i + 2] = 255; // B (蓝色调)
      uiData[i + 3] = 180; // A (半透明)
    }
    // 其他区域保持不变
  }
  
  // 将数据放回画布
  apiCtx.putImageData(apiImageData, 0, 0);
  uiCtx.putImageData(uiImageData, 0, 0);
  
  // 将API遮罩保存为PNG (用于API调用)
  const apiMaskDataUrl = apiCanvas.toDataURL('image/png');
  
  // 将UI遮罩保存为PNG (用于界面显示)
  const uiMaskDataUrl = uiCanvas.toDataURL('image/png');
  
  // 更新当前图片的遮罩
  if (currentImage.value) {
    const updatedImage = { ...currentImage.value };
    updatedImage.mask = apiMaskDataUrl;     // 用于API的遮罩
    updatedImage.uiMask = uiMaskDataUrl;    // 用于UI显示的遮罩
    images.value[currentImageIndex.value] = updatedImage;
  }
  
  // 重置绘制状态
  isDrawing.value = false;
  isMaskMode.value = false;
};

// 更新画笔尺寸的方法
const updateBrushSize = (size) => {
  brushSize.value = size;
  if (ctx.value) {
    ctx.value.lineWidth = size;
  }
};

// 组件卸载时清理
onUnmounted(() => {
  if (pollingInterval) {
    clearInterval(pollingInterval);
  }
  if (timeout) {
    clearTimeout(timeout);
  }
});
</script>

<style>
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.4s ease-out forwards;
}

canvas {
  cursor: crosshair;
}
</style>
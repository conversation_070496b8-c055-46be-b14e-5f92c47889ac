package org.seven.share.service;



import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.api.Result;
import org.seven.share.common.pojo.entity.SysResource;
import org.seven.share.common.pojo.vo.SysMenuTreeVO;
import org.seven.share.common.pojo.vo.SysMenuVO;
import org.seven.share.common.pojo.vo.SysResourceVO;
import org.seven.share.common.pojo.vo.SysRoutesVO;

import java.util.List;
import java.util.Map;

public interface SysResourceService extends IService<SysResource> {

    Result<Map<String, Object>> getUserRoutes(String authorizationHeader);

    Result<IPage<SysMenuVO>> getMenuList(Map<String, Object> params);

    Result<List<SysRoutesVO>> getConstantRoutes();

    List<String> getUserPermissions(Long id);

    Result<List<String>> getAllPages();

    Result<List<SysMenuTreeVO>> getMenuTree();

    Result<String> addResource(SysResourceVO sysResourceVO);

    Result<String> updateResource(SysResourceVO sysResourceVO);

    void deleteResourceBatch(List<Long> sourceIds);
}

package org.seven.share.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.domain.AlipayTradePrecreateModel;
import com.alipay.api.domain.AlipayTradeWapPayModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradePrecreateRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.alipay.api.response.AlipayTradePrecreateResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.response.AlipayTradeWapPayResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.Amount;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayRequest;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse;
import com.wechat.pay.java.service.payments.nativepay.model.QueryOrderByOutTradeNoRequest;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

import org.seven.share.common.enums.PaymentMethod;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.pojo.dto.ExtraData;
import org.seven.share.common.pojo.entity.ChatGptEPayLogsEntity;
import org.seven.share.common.pojo.entity.ChatGptPayConfigEntity;
import org.seven.share.common.pojo.entity.ChatGptSubTypeEntity;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.common.util.DateTimeUtil;
import org.seven.share.common.util.OrderUtils;
import org.seven.share.mapper.ChatGptPayConfigMapper;
import org.seven.share.mapper.ChatGptSubTypeMapper;
import org.seven.share.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.seven.share.common.enums.PaymentMethod.LAN_TU_PAY_WECHAT;
import static org.seven.share.common.enums.PaymentMethod.NATIVE_WECHAT;
import static org.seven.share.common.util.ConstantUtil.*;
import static org.seven.share.common.util.SignUtil.*;


/**
 * @ClassName: PayServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/4
 */
@Slf4j
@Service
public class PayServiceImpl implements PayService {
    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ChatGptPayConfigMapper chatGptPayConfigDao;

    @Resource
    private ChatGptSubTypeService chatGptSubTypeService;

    @Resource
    private ChatGptPayLogsService chatGptPayLogsService;

    @Resource
    private ChatGptUserService chatGptUserService;

    @Resource
    private EmailService emailService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Autowired
    @Qualifier("commonAsyncExecutor")
    private Executor commonAsyncExecutor;

    @Resource
    private ChatGptSubTypeMapper chatGptSubTypeMapper;

    private final Lock lock = new ReentrantLock();

    // 状态映射表
    private static final Map<String, String> STATUS_MAPPING = new HashMap<>();
    static {
        STATUS_MAPPING.put("1", "SUCCESS");
        STATUS_MAPPING.put("0", "UNPAID");
    }

    private static final Map<Integer, String> PAYMENTS_TYPE = new HashMap<>();
    static {
        PAYMENTS_TYPE.put(1, "alipay");
        PAYMENTS_TYPE.put(2, "wxpay");
        PAYMENTS_TYPE.put(6, "usdt");
    }

    // 1. 定义必填参数列表
    private static final Set<String> REQUIRED_PARAMS = new HashSet<>(Arrays.asList(
            "code", "timestamp", "mch_id", "order_no", "out_trade_no",
            "pay_no", "total_fee"
    ));

    @Override
    public String getOrderStatus(String tradeNo) {
        // 从数据库中读取易支付配置，1和2都有易支付信息
        ChatGptPayConfigEntity payConfig = getPayConfigEntity(PaymentMethod.YI_PAY_ALIPAY.getCode());
        String url = payConfig.getExtraData().getPayGatewayUrl() + "/api.php?act=order&pid={appId}&key={appKey}&out_trade_no={tradeNo}";
        ResponseEntity<String> result = restTemplate.getForEntity(url, String.class, payConfig.getAppId(), payConfig.getAppKey(), tradeNo);
        if (result.getStatusCode().is2xxSuccessful()) {
            log.info("查询订单信息成功,响应结果：{}", result.getBody());
            String body = result.getBody();
            try {
                JsonNode rootNode = objectMapper.readTree(body);
                String status = rootNode.path("status").asText();  // 获取 "status" 字段的值
                log.info("订单状态：{}", status);
                return STATUS_MAPPING.get(status);
            } catch (Exception e) {
                log.error("解析JSON时出错", e);
                return null;
            }
        }
        log.info("查询订单信息失败，订单号：{}", tradeNo);
        return null;
    }

    /**
     * 根据支付类型获取支付配置信息
     * @return 配置信息
     */
    private ChatGptPayConfigEntity getPayConfigEntity(Integer type) {
        ChatGptPayConfigEntity payConfig = getPayConfig(type);
        // 当易支付支付方式没找到，则找易支付微信的配置信息
        if (type == PaymentMethod.YI_PAY_ALIPAY.getCode() && ObjectUtil.isEmpty(payConfig)) {
            payConfig = getPayConfig(PaymentMethod.YI_PAY_WECHAT.getCode());
        }
        if (ObjectUtil.isEmpty(payConfig)) {
            throw new CustomException("该支付方式配置未找到，请先确认是否配置");
        }
        if (StrUtil.isEmpty(payConfig.getAppId()) || StrUtil.isEmpty(payConfig.getAppKey())) {
            throw new CustomException("appid为空或者appKey为空");
        }
        // 校验一下回调地址和网关信息是否为空
        if (type != 7 && (StrUtil.isEmpty(payConfig.getExtraData().getNotifyUrl()) || StrUtil.isEmpty(payConfig.getExtraData().getPayGatewayUrl()))) {
            throw new CustomException("支付配置错误：回调或网关地址未配置");
        }
        return payConfig;
    }

    private ChatGptPayConfigEntity getPayConfig(Integer type) {
        LambdaQueryWrapper<ChatGptPayConfigEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatGptPayConfigEntity::getPaymentsType, type); // 支付类型
        return chatGptPayConfigDao.selectOne(wrapper);
    }

    @Override
    public Map<String, Object> getQrCode(String typeId,
                                         Integer payType,
                                         String userToken,
                                         String coupon,
                                         boolean isMobile) {
        lock.lock();
        try{
            // 根据id获取套餐信息
            ChatGptSubTypeEntity chatGptSubType = chatGptSubTypeMapper.getSubtypeByIdWithoutTenant(typeId);
            if (ObjectUtil.isEmpty(chatGptSubType)) {
                log.error("套餐未找到，套餐id为：{}", typeId);
                throw new CustomException("套餐未找到");
            }
            // 处理折扣,如果有优惠卷，先判断优惠卷过期没有，如果没有过期则计算折扣后应支付的金额
            if (StrUtil.isNotEmpty(coupon)) {
                Double finalDiscountMoney = chatGptSubTypeService.calcOfferAmount(chatGptSubType.getId(), coupon, chatGptSubType.getMoney(), true);
                chatGptSubType.setMoney(finalDiscountMoney);
            }
            // 检查是否自定义商品名称了
            String siteShopName = chatGptConfigService.getValueByKey("siteShopName");
            if (StrUtil.isNotEmpty(siteShopName)) {
                chatGptSubType.setName(siteShopName);
            }

            // 生成订单号
            String tradeNo = OrderUtils.generateOrderNumber();
            log.info("订单号：{}", tradeNo);
            // 获取支付配置
            ChatGptPayConfigEntity payConfig = getPayConfigEntity(payType);
            // 是否启用h5
            boolean enableH5 = payType == 6 || isMobile && payConfig.getExtraData().isEnableH5();
            Map<String, Object> map = new HashMap<>();
            // 获取易支付的支付链接
            switch (payType) {
                case 1:
                case 2:
                case 6:
                    map = getEPayUrl(payType, tradeNo, chatGptSubType, payConfig, enableH5);
                    break;
                case 3:
                    map = createF2FPayUrl(tradeNo, chatGptSubType, payConfig);
                    break;
                case 4:
                case 8:
                    map = getXunHuPayUrl(tradeNo, chatGptSubType, payConfig);
                    break;
                case 5:
                    map = createLanTuPayUrl(tradeNo, chatGptSubType, payConfig, enableH5);
                    break;
                case 7:
                    map = createWxNativePayUrl(tradeNo, chatGptSubType, payConfig);
                    break;
                case 9:
                    map = createAlipayPage(tradeNo, chatGptSubType, payConfig, enableH5);
                    break;
                default:
                    log.error("支付方式错误，支付方式为：{}", payType);
            }
            if (map != null) {
                // 保存支付记录
                log.info("开始插入待支付记录...");
                saveEPayLogs(chatGptSubType, tradeNo, String.valueOf(chatGptSubType.getId()), userToken);
                log.info("插入待支付记录成功...");
            }
            return map;
        }finally {
            lock.unlock();
        }
    }

    /**
     * 支付宝网站支付
     * @param tradeNo
     * @param chatGptSubType
     * @param payConfig
     * @return
     */
    private Map<String, Object> createAlipayPage(String tradeNo,
                                                 ChatGptSubTypeEntity chatGptSubType,
                                                 ChatGptPayConfigEntity payConfig,
                                                 boolean isH5) {
        try {
            AlipayClient alipayClient = getAlipayClient(payConfig);
            String notifyUrl = payConfig.getExtraData().getNotifyUrl();
            String returnUrl = notifyUrl.replace("/callBack/aliPayWapNotify", "/callBack/payReturn");
            if (isH5) {
                // 手机端支付
                AlipayTradeWapPayRequest request = new AlipayTradeWapPayRequest();
                AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
                model.setOutTradeNo(tradeNo);
                model.setTotalAmount(String.valueOf(chatGptSubType.getMoney()));
                model.setSubject(chatGptSubType.getName());
                model.setProductCode("QUICK_WAP_WAY");
                request.setBizModel(model);
                request.setNotifyUrl(notifyUrl);
                request.setReturnUrl(returnUrl);
                AlipayTradeWapPayResponse alipayTradeWapPayResponse = alipayClient.pageExecute(request, "POST");
                String pageRedirectionData = alipayTradeWapPayResponse.getBody();
                if (alipayTradeWapPayResponse.isSuccess()) {
                    log.info("支付宝网站支付下单成功:{}", alipayTradeWapPayResponse.isSuccess());
                    //输出
                    return Map.of("tradeNo", tradeNo, "html", pageRedirectionData);
                } else {
                    log.info("支付宝网站支付下单失败：{}", alipayTradeWapPayResponse);
                    throw new CustomException("支付宝网站支付下单失败，请联系管理员");
                }
            } else {
                // pc端支付
                AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
                AlipayTradePagePayModel model = new AlipayTradePagePayModel();
                model.setOutTradeNo(tradeNo);
                model.setTotalAmount(String.valueOf(chatGptSubType.getMoney()));
                model.setSubject(chatGptSubType.getName());
                model.setProductCode("FAST_INSTANT_TRADE_PAY");
                request.setBizModel(model);
                request.setNotifyUrl(notifyUrl);
                request.setReturnUrl(returnUrl);
                AlipayTradePagePayResponse alipayResponse = alipayClient.pageExecute(request, "POST");
                String pageRedirectionData = alipayResponse.getBody();
                if (alipayResponse.isSuccess()) {
                    log.info("支付宝网站支付下单成功:{}", alipayResponse.isSuccess());
                    //输出
                    return Map.of("tradeNo", tradeNo, "html", pageRedirectionData);
                } else {
                    log.info("支付宝网站支付下单失败：{}", alipayResponse);
                    throw new CustomException("支付宝网站支付下单失败，请联系管理员");
                }
            }
        } catch (Exception e) {
            log.error("支付宝网站支付下单失败", e);
            throw new CustomException("支付宝网站支付下单失败，请联系管理员");
        }
    }

    private Map<String, Object> createWxNativePayUrl(String tradeNo, ChatGptSubTypeEntity chatGptSubType, ChatGptPayConfigEntity payConfig) {
        // 使用自动更新平台证书的RSA配置
        // 一个商户号只能初始化一个配置，否则会因为重复的下载任务报错
        NativePayService service = initNativeService(payConfig);
        PrepayRequest request = new PrepayRequest();
        Amount amount = new Amount();
        int totalFeeInCents = (int) Math.round(chatGptSubType.getMoney() * 100);  // 将元转换为分
        amount.setTotal(totalFeeInCents);
        request.setAmount(amount);
        request.setAppid(payConfig.getAppId());
        request.setMchid(payConfig.getExtraData().getMerchantId());
        request.setDescription(chatGptSubType.getName());
        request.setNotifyUrl(payConfig.getExtraData().getNotifyUrl());
        request.setOutTradeNo(tradeNo);
        // 调用下单方法，得到应答
        PrepayResponse response = service.prepay(request);
        String codeUrl = response.getCodeUrl();
        if (StrUtil.isNotEmpty(codeUrl)) {
            log.info("获取微信native二维码成功：{}", codeUrl);
            return Map.of("tradeNo", tradeNo, "qrcode", codeUrl);
        }
        return null;
    }
    private static NativePayService initNativeService(ChatGptPayConfigEntity payConfig) {
        Config config = new RSAAutoCertificateConfig.Builder()
                .merchantId(payConfig.getExtraData().getMerchantId())
                .privateKey(payConfig.getExtraData().getPrivateKeyPem())
                .merchantSerialNumber(payConfig.getExtraData().getSerialNumber())
                .apiV3Key(payConfig.getAppKey())
                .build();
        // 构建service
        return new NativePayService.Builder().config(config).build();
    }

    /**
     * 生成蓝兔支付地址
     * @param tradeNo
     * @param payConfig
     * @return
     */
    private Map<String, Object> createLanTuPayUrl(String tradeNo,
                                                  ChatGptSubTypeEntity chatGptSubType,
                                                  ChatGptPayConfigEntity payConfig,
                                                  boolean enableH5) {
        // 使用 MultiValueMap 来存储请求参数
        MultiValueMap<String, String> requestParam = prepareLanTuRequestParams(tradeNo, chatGptSubType, payConfig);

        // 如果启用了H5支付，则添加return_url
        if (enableH5) {
            requestParam.add("return_url", payConfig.getExtraData().getNotifyUrl().replace("/lanTuPayNotify", "/payReturn"));
        }

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // 创建 HttpEntity 包含头部信息和请求参数
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(requestParam, headers);

        // 选择支付网关URL
        String url = enableH5 ? LANG_TU_WX_H5_URL : payConfig.getExtraData().getPayGatewayUrl();

        // 发起请求
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        // 处理响应
        if (HttpStatus.OK.equals(response.getStatusCode())) {
            try {
                String body = response.getBody();
                log.info("蓝兔支付响应内容：{}", body);
                JsonNode jsonNode = objectMapper.readTree(body);
                int code = jsonNode.get("code").asInt();
                if (code == 0) {
                    log.info("蓝兔支付获取二维码成功");
                    JsonNode dataNode = jsonNode.get("data");
                    Map<String, Object> map = new HashMap<>();
                    if(enableH5) {
                        map.put("payUrl", dataNode.asText());
                        return map;
                    } else {
                        String codeUrl = dataNode.get("code_url").asText();
                        map.put("tradeNo", tradeNo);
                        map.put("qrcode", codeUrl);
                        return map;
                    }
                } else {
                    log.error("蓝兔支付获取二维码失败，body:{}", body);
                    throw new CustomException("获取支付二维码失败");
                }
            } catch (JsonProcessingException e) {
                log.error("蓝兔支付解析响应报错：", e);
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    private static MultiValueMap<String, String> prepareLanTuRequestParams(String tradeNo, ChatGptSubTypeEntity chatGptSubType, ChatGptPayConfigEntity payConfig) {
        MultiValueMap<String, String> requestParam = new LinkedMultiValueMap<>();
        requestParam.add("mch_id", payConfig.getAppId());
        requestParam.add("out_trade_no", tradeNo);
        requestParam.add("total_fee", String.valueOf(chatGptSubType.getMoney()));
        requestParam.add("body", chatGptSubType.getName());
        requestParam.add("notify_url", payConfig.getExtraData().getNotifyUrl());
        requestParam.add("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
        requestParam.add("sign", createLanTuSign(requestParam.toSingleValueMap(), payConfig.getAppKey())); // 注意：签名时需要将 MultiValueMap 转换成普通 Map
        return requestParam;
    }


    private Map<String, Object> getXunHuPayUrl(String tradeNo,
                                               ChatGptSubTypeEntity chatGptSubType,
                                               ChatGptPayConfigEntity payConfig) {
        Map<String, Object> map;
        String appid = payConfig.getAppId();
        String appKey = payConfig.getAppKey();
        ExtraData extraData = payConfig.getExtraData();
        String notifyUrl = extraData.getNotifyUrl();
        String gatewayUrl = extraData.getPayGatewayUrl();
        Map<String, Object> sortParams = new HashMap<>();
        sortParams.put("version", "1.1");
        sortParams.put("appid", appid);
        sortParams.put("trade_order_id", tradeNo);
        sortParams.put("total_fee", convert(chatGptSubType.getMoney()));
        sortParams.put("title", chatGptSubType.getName());
        sortParams.put("time", getSecondTimestamp(new Date()));

        //必填。用户支付成功后，我们服务器会主动发送一个post消息到这个网址(注意：当前接口内，SESSION内容无效)
        sortParams.put("notify_url", notifyUrl);

        //可选。用户支付成功后，我们会让用户浏览器自动跳转到这个网址
        sortParams.put("return_url", notifyUrl.replace("/xunHuPayNotify", "/payReturn"));

        sortParams.put("nonce_str", RandomUtil.randomNumbers(10));

        sortParams.put("hash", createXunHuSign(sortParams, appKey));

        log.info("开始调三方虎皮椒接口...");
        int retryCount = 0;
        boolean success = false;
        ResponseEntity<String> response = null;
        while (retryCount < 3 && !success) {
            try {
                log.info("虎皮椒请求参数：{}", sortParams);
                response= restTemplate.postForEntity(gatewayUrl, sortParams, String.class);
                success = true;
            } catch (Exception e) {
                retryCount++;
                log.warn("请求虎皮椒失败，第{}次重试，失败原因：{}", retryCount, e.getMessage());
                if (retryCount == 3) {
                    log.error("重试三次均失败");
                    throw new CustomException("获取支付二维码失败，请联系管理员排查。");
                }
            }
        }

        log.info("调三方虎皮椒接口结束,响应：{}", response);
        if (response != null && response.getStatusCode() == HttpStatus.OK) {
            String body = response.getBody();
            try {
                map = objectMapper.readValue(body, Map.class);
                map.put("tradeNo", tradeNo);
                return map;
            } catch (JsonProcessingException e) {
                log.error("解析虎皮椒返回数据失败，返回数据为：{}", body);
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    private static String convert(double value) {
        if (value % 1 == 0) {
            return String.valueOf((int) value);
        } else {
            return String.valueOf(value);
        }
    }

    /**
     * 获取精确到秒的时间戳   原理 获取毫秒时间戳，因为 1秒 = 100毫秒 去除后三位 就是秒的时间戳
     * @return
     */
    public static int getSecondTimestamp(Date date){
        if (null == date) {
            return 0;
        }
        String timestamp = String.valueOf(date.getTime());
        int length = timestamp.length();
        if (length > 3) {
            return Integer.parseInt(timestamp.substring(0,length-3));
        } else {
            return 0;
        }
    }

    /**
     * 当面付支付链接
     * @param tradeNo 订单号
     * @param chatGptSubType 订阅信息
     * @param payConfig 支付配置
     */
    private Map<String, Object> createF2FPayUrl( String tradeNo,
                                                 ChatGptSubTypeEntity chatGptSubType,
                                                 ChatGptPayConfigEntity payConfig) {
        try {
            AlipayClient alipayClient = getAlipayClient(payConfig);

            AlipayTradePrecreateModel model = new AlipayTradePrecreateModel();
            model.setOutTradeNo(tradeNo);
            model.setTotalAmount(String.valueOf(chatGptSubType.getMoney()));
            model.setSubject(chatGptSubType.getName());
            model.setQrCodeTimeoutExpress("5m");
            AlipayTradePrecreateRequest request = new AlipayTradePrecreateRequest();
            request.setBizModel(model);
            request.setNotifyUrl(payConfig.getExtraData().getNotifyUrl());

            AlipayTradePrecreateResponse alipayResponse = alipayClient.execute(request);
            if (!alipayResponse.isSuccess() || StrUtil.isEmpty(alipayResponse.getQrCode())) {
                throw new CustomException("当面付生成支付二维码失败");
            }
            log.info("当面付生成支付二维码成功：{}", alipayResponse.getQrCode());
            Map<String, Object> map = new HashMap<>();
            map.put("tradeNo", tradeNo);
            map.put("qrcode", alipayResponse.getQrCode());
            return map;
        } catch (AlipayApiException e) {
            log.error("当面付生成支付二维码失败", e);
            throw new CustomException("当面付生成支付二维码失败");
        }
    }

    /**
     * 获取易支付的支付信息
     * @param payType 支付了下
     * @param tradeNo 订单号
     * @param chatGptSubType 套餐信息
     * @param payConfig 支付信息
     * @return 支付信息
     */
    private Map<String, Object> getEPayUrl(Integer payType,
                                           String tradeNo,
                                           ChatGptSubTypeEntity chatGptSubType,
                                           ChatGptPayConfigEntity payConfig,
                                           boolean enableH5) {
        String notifyUrl = payConfig.getExtraData().getNotifyUrl();
        String gatewayUrl = payConfig.getExtraData().getPayGatewayUrl();
        String appid = payConfig.getAppId();
        // 生成易支付请求参数
        Map<String, String> params = getRequestParamsMap(appid, payType, tradeNo, chatGptSubType, notifyUrl, enableH5);
        // 生成签名信息
        String sign = map2Sign(params, payConfig.getAppKey());
        // 获取生成支付地址响应
        ResponseEntity<String> response = getEPayUrlResponse(params, sign, gatewayUrl, enableH5);
        log.info("易支付请求响应结果：{}", response);

        if (HttpStatus.OK.equals(response.getStatusCode()) && response.hasBody()) {
            String responseBody = response.getBody();
            log.info("响应体内容：{}", responseBody);

            // 手机端并且启用h5的支付链接
            if (enableH5) {
                return extractMobilePayUrl(responseBody, gatewayUrl, payType, tradeNo);
            } else{
                // 电脑端的支付链接
                return extractedPcPayUrl(responseBody, tradeNo);
            }
        }
        return null;
    }

    /**
     * 获取电脑端支付链接
     * @param responseBody 响应体
     * @return 支付链接
     */
    private Map<String, Object> extractedPcPayUrl(String responseBody, String tradeNo) {
        log.info("解析pc端返回的易支付信息...");
        try {
            // 解析 JSON 字符串
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            // 获取字段数据
            int code = jsonNode.get("code").asInt();
            if (code != 1) {
                String msg = jsonNode.has("msg") ? jsonNode.get("msg").asText() : "未知错误";
                log.error("获取pc端支付地址失败，code为：{}, msg:{}", code, msg);
                throw new CustomException(msg);
            }
            log.info("获取pc端支付地址响应成功");
            Map<String, Object> map = new HashMap<>();
            map.put("tradeNo", tradeNo);
            // 优先尝试获取二维码
            String qrcode = jsonNode.has("qrcode") ? jsonNode.get("qrcode").asText() : null;
            if (StrUtil.isNotEmpty(qrcode)) {
                log.info("获取易支付二维码成功：{}", qrcode);
                map.put("qrcode", qrcode);
                return map;
            }
            // 尝试获取支付链接
            String payUrl = jsonNode.has("payurl") ? jsonNode.get("payurl").asText() : null;
            if (StrUtil.isNotEmpty(payUrl)) {
                map.put("payUrl", payUrl);
                return map;
            }
            // 如果两者都为空，抛出异常
            log.error("易支付pc端返回的二维码和支付链接均为空：{}", jsonNode);
            throw new CustomException("获取二维码或支付链接失败，请联系管理员");
        } catch (JsonProcessingException e) {
            log.error("解析易支付返回的JSON响应体失败，失败原因：", e);
            throw new CustomException("解析响应失败，请联系管理员");
        } catch (Exception e) {
            log.error("处理易支付信息时出现异常，失败原因：", e);
            throw new CustomException("获取支付信息失败，请联系管理员");
        }
    }

    private ResponseEntity<String> getEPayUrlResponse(Map<String, String> params, String sign, String gatewayUrl, boolean isMobile) {
        params.put("sign", sign);
        params.put("sign_type", "MD5");
        // 将参数转换为表单格式
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.setAll(params);
        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/x-www-form-urlencoded");
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);
        if (isMobile) {
            gatewayUrl = gatewayUrl + "/submit.php";
        }else {
            gatewayUrl = gatewayUrl + "/mapi.php";
        }
        return restTemplate.exchange( gatewayUrl, HttpMethod.POST, requestEntity, String.class);
    }

    @Override
    public void ePayNotify(HttpServletRequest request) {
        // md5校验
        validMD5(request);
        // 判断支付状态
        if ("TRADE_SUCCESS".equals(request.getParameter("trade_status"))){
            String tradeNo = request.getParameter("out_trade_no");
            doPaySuccess(tradeNo);
        }
    }

    /**
     * 支付成功后的业务处理
     * @param tradeNo 订单号
     */
    private void doPaySuccess(String tradeNo) {
        log.info("支付成功后[{}]的业务处理...", tradeNo);
        if (StrUtil.isEmpty(tradeNo)) {
            throw new CustomException("订单号为空");
        }
        // 根据订单号查询订单信息
        ChatGptEPayLogsEntity payLog = getPayLogByTradeNo(tradeNo);

        String subTypeId = payLog.getSubTypeId();
        // 根据订阅id查询订阅信息
        ChatGptSubTypeEntity subType = chatGptSubTypeMapper.getSubtypeByIdWithoutTenant(subTypeId);
        if (ObjectUtils.isEmpty(subType)) {
            throw new CustomException("订阅信息为空");
        }
        // 查询用户信息表
        String userToken = payLog.getUserToken();
        ChatGptUserEntity user = chatGptUserService.getUserInfoByUsername(userToken);
        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("用户信息为空");
        }
        // 如果套餐是独享类型的
        chatGptUserService.dealUserAndGptSessionRelation(subType, user);

        // 更新用户信息（模型速率）、返现信息、订单状态、租户佣金
        chatGptUserService.updateUserInfoAndPayStatus(subType, user, payLog);

        // 发送邮件
        CompletableFuture.runAsync(() -> {
            try {
                sendPaySuccessNotify(userToken, subType.getName(), payLog.getMoney());
            } catch (Exception e) {
                log.error("发送邮件失败", e);
            }
        }, commonAsyncExecutor);
        log.info("支付成功后[{}]的业务处理完成", tradeNo);
    }


    /**
     * 根据订单号获取支付订单信息
     * @param tradeNo 订单号
     * @return 支付订单信息
     */
    private ChatGptEPayLogsEntity getPayLogByTradeNo(String tradeNo ) {
        log.info("开始获取订单信息...");
        // 查询当前订单的信息，获取当前订单的订阅类型、有效天数、支付的用户名
        ChatGptEPayLogsEntity payLog = chatGptPayLogsService.getOne(new QueryWrapper<ChatGptEPayLogsEntity>()
                .eq("tradeNo", tradeNo)
                .isNull("deleted_at"));
        if (ObjectUtil.isEmpty(payLog)) {
            log.error("获取支付订单失败，订单号为:{}", tradeNo);
            throw new CustomException("获取支付订单失败");
        }
        // 校验订单状态，避免重复回调
        if ("success".equals(payLog.getStatus())) {
            log.info("订单已支付，订单号为:{}", tradeNo);
            throw new CustomException("订单已支付");
        }
        return payLog;
    }

    @Override
    public String aliPayNotify(HttpServletRequest request, int payMethod) {
        log.info("支付宝支付异步通知开始");
        Map<String, String> params = getAliPayRequestParams(request);
        boolean signVerified = false;
        try {
            ChatGptPayConfigEntity payConfig = getPayConfigEntity(payMethod);
            signVerified = AlipaySignature.rsaCheckV1(params, payConfig.getAppKey(), ALIPAY_CHARSET, ALIPAY_SIGN_TYPE);
        } catch (AlipayApiException e) {
            log.error("支付宝回调签名验证失败:", e);
        }
        if (signVerified) {
            String outTradeNo = params.get("out_trade_no");
            String tradeStatus = params.get("trade_status");
            // 支付成功
            if ("TRADE_SUCCESS".equals(tradeStatus)) {
                doPaySuccess(outTradeNo);
                return "success";
            }else {
                log.info("当面付回调的支付失败：{}",tradeStatus);
                return "failure";
            }
        }
        return "failure";
    }

    @Override
    public String queryF2FOrderStatus(String outTradeNo) {
        log.info("查询当面付订单状态，订单号：{}",outTradeNo);
        // 从数据库中查询当面付的配置信息
        ChatGptPayConfigEntity payConfig = getPayConfigEntity(PaymentMethod.F2F_PAY.getCode());
        // 创建AlipayClient实例
        AlipayClient alipayClient = getAlipayClient(payConfig);

        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        request.setBizContent("{" + "\"out_trade_no\":\"" + outTradeNo + "\"}");

        try {
            AlipayTradeQueryResponse response = alipayClient.execute(request);
            if (response.isSuccess() && Objects.equals(response.getTradeStatus(), "TRADE_SUCCESS")) {
                log.info("查询当面付订单状态成功：用户支付成功");
                return response.getTradeStatus();
            } else {
                // 用户还未扫码
                log.warn("查询当面付订单状态失败：订单不存在，用户还未开始扫码");
                return "FAILURE";
            }
        } catch (AlipayApiException e) {
            throw new RuntimeException(e);
        }
    }

    private static AlipayClient getAlipayClient(ChatGptPayConfigEntity payConfig) {
        ExtraData extraData = payConfig.getExtraData();
        return new DefaultAlipayClient(
                extraData.getPayGatewayUrl(),
                payConfig.getAppId(),
                extraData.getPrivateKeyPem(),
                ALIPAY_FORMAT,
                ALIPAY_CHARSET,
                payConfig.getAppKey(), // 公钥
                ALIPAY_SIGN_TYPE);
    }

    @Override
    public String xunHuPayNotify(HttpServletRequest request, int payMethod) {
        log.info("开始处理虎皮椒回调请求..");
        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, Object> resultMap = new HashMap<>();
        parameterMap.keySet().forEach(e -> {
            if (!"hash".equals(e)){
                resultMap.put(e, parameterMap.get(e)[0]);
            }
        });
        ChatGptPayConfigEntity payConfig = getPayConfig(payMethod);
        String sign = createXunHuSign(resultMap, payConfig.getAppKey());
        if (StrUtil.isNotBlank(sign) && sign.equals(request.getParameter("hash"))) {
            log.info("虎皮椒签名认证成功");
            // OD(支付成功)，WP(待支付),CD(已取消)
            if ("OD".equals(request.getParameter("status"))){
                doPaySuccess(request.getParameter("trade_order_id"));
                return "success";
            }
        }else {
            log.error("虎皮椒签名认证失败");
        }
        return "failure";
    }

    @Override
    public String queryHpPayStatus(String tradeNo, int payType) {
        ChatGptPayConfigEntity payConfig = getPayConfigEntity(payType);
        String appid = payConfig.getAppId();
        String appKey = payConfig.getAppKey();
        Map<String, Object> sortParams = new HashMap<>();
        sortParams.put("appid", appid);
        sortParams.put("out_trade_order", tradeNo);
        sortParams.put("nonce_str", RandomUtil.randomNumbers(10));
        sortParams.put("time", System.currentTimeMillis() / 1000);
        sortParams.put("hash", createXunHuSign(sortParams, appKey));

        log.info("开始调三方虎皮椒查询订单接口...");
        String url;
        if (OLD_XUN_HU_PAY_GATEWAY_URL.equals(payConfig.getExtraData().getPayGatewayUrl())) {
            url = OLD_XUN_HU_PAY_STATUS_QUERY_URL;
        } else {
            url = XUN_HU_PAY_STATUS_QUERY_URL;
        }
        ResponseEntity<String> response = restTemplate.postForEntity(url,
                sortParams,
                String.class);
        log.info("调三方虎皮椒查询订单接口结束,响应：{}", response);
        if (response.getStatusCode() == HttpStatus.OK) {
            String body = response.getBody();
            try {
                JsonNode jsonNode = objectMapper.readTree(body);
                JsonNode dataNode  = jsonNode.get("data");
                String status = dataNode.get("status").asText();
                if ("OD".equals(status)) {
                    log.info("虎皮椒查询订单状态完成，用户已支付。");
                    return "SUCCESS";
                }
            } catch (JsonProcessingException e) {
                log.error("虎皮椒查询订单状态数据解析失败:",e);
                throw new RuntimeException(e);
            }
        }
        return "UNPAID";
    }

    /**
     * 查询蓝兔订单支付状态
     * @param tradeNo 订单号
     * @return 支付状态
     */
    @Override
    public String queryLanTuPayStatus(String tradeNo) {
        ChatGptPayConfigEntity payConfig = getPayConfigEntity(LAN_TU_PAY_WECHAT.getCode());
        // 使用 MultiValueMap 来存储请求参数
        MultiValueMap<String, String> requestParam = new LinkedMultiValueMap<>();
        requestParam.add("mch_id", payConfig.getAppId());
        requestParam.add("out_trade_no", tradeNo);
        requestParam.add("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
        requestParam.add("sign", createLanTuSign(requestParam.toSingleValueMap(), payConfig.getAppKey())); // 注意：签名时需要将 MultiValueMap 转换成普通 Map
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // 设置请求参数
        // 创建 HttpEntity 包含头部信息和请求参数
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(requestParam, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(LANG_TU_WX_PAY_STATUS_QUERY_URL,
                requestEntity, String.class);
        // 处理响应
        if (HttpStatus.OK.equals(response.getStatusCode())) {
            log.info("查询蓝兔支付响应成功，响应内容:{}", response.getBody());
            // 解析 JSON 响应
            try {
                JsonNode jsonResponse = objectMapper.readTree(response.getBody());
                int code = jsonResponse.get("code").asInt();
                if (code == 0) {  // 判断 code 是否为 0，表示成功
                    JsonNode dataNode = jsonResponse.get("data");
                    int payStatus = dataNode.get("pay_status").asInt();  // 提取 pay_status

                    // 根据 pay_status 判断状态
                    if (payStatus == 1) {
                        log.info("支付成功，订单号:{}", dataNode.get("out_trade_no").asText());
                        return "SUCCESS";
                    } else {
                        log.info("支付未成功，支付状态:{}", payStatus);
                        return "UNPAID";
                    }
                } else {
                    log.error("查询支付状态失败，错误信息: {}", jsonResponse.get("msg").asText());
                    return "FAILURE";
                }
            } catch (Exception e) {
                log.error("解析支付状态响应时发生错误", e);
                return "FAILURE";
            }
        } else {
            log.error("查询蓝兔支付响应失败，状态码: {}", response.getStatusCode());
            return "FAILURE";
        }
    }

    /**
     * 蓝兔异步回调通知
     * @return 回调结果
     */
    @Override
    public ResponseEntity<?> lanTuPayNotify(Map<String, String> paramMap) {
        if (validLanTuMd5(paramMap)) {
            String code = paramMap.get("code");
            if (StrUtil.isNotEmpty(code) && Integer.parseInt(code) == 0) {
                doPaySuccess((paramMap.get("out_trade_no")));
                log.info("蓝兔异步回调处理成功");
                return ResponseEntity.ok("SUCCESS");
            }
        }
        return ResponseEntity.ok("FAIL");
    }

    @Override
    public String wxNativeNotify(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Gson gson = new Gson();
        log.info("收到微信支付native异步回调");

        // 获取请求体原内容（此时获取的数据是加密的）
        BufferedReader reader = request.getReader();
        StringBuilder requestBody = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            requestBody.append(line);
        }

        // 获取请求携带的数据，构造参数
        RequestParam requestParam = new RequestParam.Builder()
                .serialNumber(request.getHeader("Wechatpay-Serial")) // 微信支付平台证书的序列号
                .nonce(request.getHeader("Wechatpay-Nonce")) // 签名中的随机数
                .signature(request.getHeader("Wechatpay-Signature"))  // 应答的微信支付签名
                .timestamp(request.getHeader("Wechatpay-Timestamp")) // 签名中的时间戳
                .body(requestBody.toString()) // 请求体内容（原始内容，不要解析）
                .build();

        // 初始化RSAAutoCertificateConfig
        ChatGptPayConfigEntity payConfig = getPayConfigEntity(NATIVE_WECHAT.getCode());
        NotificationConfig config = new RSAAutoCertificateConfig.Builder()
                .merchantId(payConfig.getExtraData().getMerchantId()) // 商户号
                .privateKey(payConfig.getExtraData().getPrivateKeyPem())
                .merchantSerialNumber(payConfig.getExtraData().getSerialNumber()) // 证书序列号
                .apiV3Key(payConfig.getAppKey()) // APIV3密匙
                .build();

        // 初始化 NotificationParser
        NotificationParser parser = new NotificationParser(config);
        Map<String, String> map = new HashMap<>();

        try {
            // 解析为Transaction对象（解密数据）
            Transaction transaction = parser.parse(requestParam, Transaction.class);
            log.info("验签成功，解密后的信息：{}", transaction);
            log.info("微信支付单号：{}", transaction.getTransactionId());

            // 处理支付结果
            if ("SUCCESS".equals(transaction.getTradeState().toString())) {
                log.info("订单支付成功，处理业务逻辑中...");
                doPaySuccess(transaction.getOutTradeNo()); // 处理业务逻辑，例如更新订单状态
                map.put("code", "SUCCESS");
                map.put("message", "成功");
                response.setStatus(HttpServletResponse.SC_OK); // 设置 HTTP 状态码为 200，表示成功
                return gson.toJson(map);
            } else {
                // 处理支付失败或其他非成功状态
                log.warn("支付状态非SUCCESS: {}", transaction.getTradeState());
                map.put("code", "FAIL");
                map.put("message", "支付未成功");
                response.setStatus(HttpServletResponse.SC_OK); // 设置为200, 但告知微信处理失败
                return gson.toJson(map);
            }
        } catch (Exception e) {
            log.error("微信native异步回调处理失败：", e);
            map.put("code", "FAIL");
            map.put("message", "验签或处理业务失败");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // 设置 HTTP 状态码为 500，表示服务器错误
            return gson.toJson(map);
        }
    }


    @Override
    public String queryNativeStatus(String tradeNo) {
        ChatGptPayConfigEntity payConfig = getPayConfigEntity(NATIVE_WECHAT.getCode());
        QueryOrderByOutTradeNoRequest queryRequest = new QueryOrderByOutTradeNoRequest();
        queryRequest.setMchid(payConfig.getExtraData().getMerchantId());
        queryRequest.setOutTradeNo(tradeNo);
        try {
            NativePayService service = initNativeService(payConfig);
            Transaction result = service.queryOrderByOutTradeNo(queryRequest);
            if ("SUCCESS".equals(String.valueOf(result.getTradeState()))) {
                log.info("微信native根据订单号:{}查询订单状态成功：{}", tradeNo, result.getTradeState());
                return "SUCCESS";
            }
        } catch (ServiceException e) {
            // API返回失败, 例如ORDER_NOT_EXISTS
            log.error("微信native根据订单号查询订单状态失败，code:{}, message:{}", e.getErrorCode(), e.getErrorMessage());
        }
        return "NOTPAY";
    }

    /**
     * 蓝兔md5校验
     * @param paramMap 回调参数
     */
    private boolean validLanTuMd5(Map<String, String> paramMap) {
        log.info("开始校验蓝兔md5...");
        log.info("回调参数：{}", paramMap);
        ChatGptPayConfigEntity payConfig = getPayConfigEntity(LAN_TU_PAY_WECHAT.getCode());
        String compareSign = paramMap.get("sign");
        Map<String, String> newParamMap = new HashMap<>();
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            if (REQUIRED_PARAMS.contains(entry.getKey())) {
                newParamMap.put(entry.getKey(), entry.getValue());
            }
        }
        String sign = createLanTuSign(newParamMap, payConfig.getAppKey());
        if (!Objects.equals(sign, compareSign)){
            log.error("蓝兔md5校验失败");
            return false;
        }
        log.info("蓝兔md5校验成功");
        return true;
    }

    private void sendPaySuccessNotify(String userToken, String productName, double money) {
        String enablePaySuccessNotice = chatGptConfigService.getValueByKey("enablePaySuccessNotice");
        if (!"true".equals(enablePaySuccessNotice)) {
            return;
        }
        log.info("开始发送支付成功邮件通知...");
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("username", userToken);
        paramMap.put("productName", productName);
        paramMap.put("amount", String.valueOf(money));
        paramMap.put("purchaseTime", DateTimeUtil.getCurrentDateTime());
        emailService.sendPaySuccessEmail(EMAIL_PAY_SUCCESS_NOTIFY_TEMPLATE, EMAIL_PAY_SUBJECT, paramMap);
        log.info("发送支付成功邮件通知完成...");
    }

    private void validMD5(HttpServletRequest request) {
        log.info("开始校验md5...");
        ChatGptPayConfigEntity payConfig = getPayConfigEntity(PaymentMethod.YI_PAY_ALIPAY.getCode());
        Map<String, String[]> parameterMap = request.getParameterMap();
        // 使用 TreeMap 对参数按键名进行排序
        TreeMap<String, String[]> sortedParams = new TreeMap<>(parameterMap);
        StringBuilder concatenatedParams = new StringBuilder();
        for (Map.Entry<String, String[]> entry : sortedParams.entrySet()) {
            String key = entry.getKey();
            // 跳过 sign 和 sign_type 字段
            if ("sign".equals(key) || "sign_type".equals(key)) {
                continue;
            }
            String[] values = entry.getValue();
            for (String value : values) {
                if (Objects.equals(value, "")) {
                    continue;
                }
                concatenatedParams.append(key).append("=").append(value).append("&");
            }
        }
        // 移除最后一个多余的 '&'
        if (!concatenatedParams.isEmpty()) {
            concatenatedParams.setLength(concatenatedParams.length() - 1);
        }
        concatenatedParams.append(payConfig.getAppKey());

        String compareMd5 = SecureUtil.md5(concatenatedParams.toString());
        if (compareMd5.equals(request.getParameter("sign"))) {
            log.info("MD5校验成功");
        } else {
            log.error("MD5校验失败");
            throw new CustomException("支付失败");
        }
    }

    private void updatePayStatus(String tradeNo) {
        log.info("更新支付状态");
        UpdateWrapper<ChatGptEPayLogsEntity> wrapper = new UpdateWrapper<>();
        wrapper.eq("tradeNo", tradeNo);
        wrapper.set("status", "success");
        wrapper.set("updateTime", LocalDateTime.now());
        chatGptPayLogsService.update(wrapper);
    }

    /**
     * 解析易支付返回的手机端响应体，获取支付地址
     * @param responseBody 响应体
     * @return 支付信息
     */
    private Map<String, Object> extractMobilePayUrl(String responseBody, String gatewayUrl, Integer payType, String tradeNo) {
        log.info("开始解析手机端返回的支付地址信息...");
        Document doc = Jsoup.parse(responseBody);
        // 获取 <script> 标签的支付地址
        Element scriptElement = doc.selectFirst("script");
        String scriptContent = scriptElement != null ? scriptElement.html() : "";
        // 正则表达式匹配 URL
        String urlPattern = "window\\.location\\.replace\\(['\"](.*?)['\"]\\)";
        Pattern pattern = Pattern.compile(urlPattern);
        Matcher matcher = pattern.matcher(scriptContent);

        String extractedUrl;
        // 没找到，直接报错返回。
        if (!matcher.find()) {
            // 如果没找到，则获取 <center> 标签的报错内容
            Element centerElement = doc.selectFirst("center");
            String centerText = centerElement != null ? centerElement.text() : "No <center> element found";
            log.error("获取支付二维码失败，错误信息：" + centerText);
            throw new CustomException("获取支付二维码失败");
        }
        log.info("易支付手机端支付地址响应成功");
        extractedUrl = matcher.group(1); // 获取第一个捕获组，即URL
        Map<String, Object> map = new HashMap<>();
        map.put("tradeNo", tradeNo);
        if (payType == 6) {
            map.put("payUrl", extractedUrl);
            return map;
        }
        map.put("payUrl", gatewayUrl + extractedUrl);
        return map;
    }

    /**
     * 保存用户支付记录
     * @param chatGptSubType 订阅信息
     * @param tradeNo 订单号
     * @param typeId 订阅id
     * @param userToken 用户名
     */
    private void saveEPayLogs(ChatGptSubTypeEntity chatGptSubType, String tradeNo, String typeId, String userToken) {
        log.info("开始新增用户支付信息...用户名：{}，订单号：{}", userToken, tradeNo);
        ChatGptEPayLogsEntity logsEntity = new ChatGptEPayLogsEntity();
        logsEntity.setMoney(chatGptSubType.getMoney());
        logsEntity.setTradeNo(tradeNo);
        logsEntity.setDays(chatGptSubType.getValidDays());
        logsEntity.setSubTypeId(typeId);
        logsEntity.setUserToken(userToken);
        logsEntity.setStatus("pending");
        logsEntity.setRemark("【" + userToken + "】购买了" + chatGptSubType.getName()
                + "套餐，有效期：" + chatGptSubType.getValidDays() + "天");
        chatGptPayLogsService.save(logsEntity);
        log.info("新增用户支付信息完成...");
    }

    private Map<String, String> getRequestParamsMap(String appid,
                                                    Integer type,
                                                    String tradeNo,
                                                    ChatGptSubTypeEntity chatGptSubType,
                                                    String notifyUrl,
                                                    boolean enableH5) {
        Map<String, String> params = new TreeMap<>();
        params.put("pid", appid);
        params.put("type", PAYMENTS_TYPE.get(type)); // 根据需要选择支付方式
        params.put("out_trade_no", tradeNo);
        params.put("return_url", notifyUrl.replace("ePayNotify","payReturn")); // 回调地址
        params.put("notify_url", notifyUrl); // 回调地址
        params.put("name", chatGptSubType.getName());
        params.put("money", String.valueOf(chatGptSubType.getMoney()));
        params.put("param", ""); // 业务扩展参数，可以留空
        if (!enableH5) {
            params.put("clientip", "*************"); // 如果是码支付，需要设置这个参数
            params.put("device", "pc"); // 如果是码支付，需要设置这个参数
        }
        return params;
    }
}

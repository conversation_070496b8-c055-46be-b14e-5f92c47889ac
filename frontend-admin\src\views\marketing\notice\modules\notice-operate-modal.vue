<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useForm } from '@/hooks/common/form';
import { addNotice, fetchNoticePage, updateNotice } from '@/service/api';
import HtmlEditor from '@/components/html-editor/HtmlEditor.vue';

defineOptions({ name: 'NoticeOperateModal' });

export type OperateType = 'create' | 'edit';

interface Props {
  /** the type of operation */
  operateType: OperateType;
  /** the notice id for editing */
  noticeId?: string;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useForm();
const htmlEditorRef = ref();
const currentEditType = ref('notice');

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    create: '新增公告',
    edit: '编辑公告'
  };
  return titles[props.operateType];
});

interface FormData {
  content: string;
  type: string;
}

const noticeTypeOptions = [
  { label: 'GPT站内公告', value: 'gptSite' },
  { label: 'Claude站内公告', value: 'claudeSite' },
  { label: 'Grok站内公告', value: 'grokSite' },
  { label: 'Draw站内公告', value: 'drawSite' },
  { label: '系统通知', value: 'system' },
  { label: '前台脚本', value: 'script' },
];

const model = ref<FormData>(createDefaultModel());

function createDefaultModel(): FormData {
  return {
    content: '',
    type: 'gptSite'
  };
}

const rules = {
  content: [{ required: true, message: '请输入公告内容', trigger: 'blur' }],
  type: [{ required: true, message: '请选择公告类型', trigger: 'change' }]
};

const openEditor = (type: string, initialContent: string) => {
  htmlEditorRef.value?.openDialog(initialContent);
  currentEditType.value = type;
};

const handleContentUpdate = (content: string) => {
  if (currentEditType.value === 'notice') {
    model.value.content = content;
  }
};

async function handleInitModel() {
  if (props.operateType === 'edit' && props.noticeId) {
    try {
      const res = await fetchNoticePage({ id: props.noticeId });
      if (res.data.records?.[0]) {
        const notice = res.data.records[0];
        model.value = {
          content: notice.content,
          type: notice.type || 'gptSite'
        };
      }
    } catch {
      ElMessage.error('获取公告详情失败');
    }
  }
}

function closeDrawer() {
  visible.value = false;
  model.value = createDefaultModel();
}

async function handleSubmit() {
  try {
    await validate();

    if (model.value.content === '') {
      ElMessage({
        message: '请输入公告内容',
        type: 'warning'
      });
      return;
    }
    if (!model.value.type) {
      ElMessage({
        message: '请选择公告类型',
        type: 'warning'
      });
      return;
    }
    if (props.operateType === 'create') {
      await addNotice({ ...model.value });
      ElMessage.success('创建公告成功');
    } else {
      await updateNotice({
        id: props.noticeId,
        ...model.value
      });
      ElMessage.success('更新公告成功');
    }
    closeDrawer();
    emit('submitted');
  } catch {
    ElMessage.error('操作失败');
  }
}

watch(visible, newVal => {
  if (newVal) {
    handleInitModel();
    restoreValidation();
    if (props.operateType === 'create') {
      model.value = createDefaultModel();
    }
  }
});
</script>

<template>
  <ElDialog v-model="visible" :title="title" preset="card" align-center width="800px">
    <HtmlEditor ref="htmlEditorRef" @update-content="handleContentUpdate" />
    <ElForm ref="formRef" :model="model" :rules="rules" :label-width="100">
      <ElRow>
        <ElCol :span="24">
          <ElFormItem label="公告类型" prop="type">
            <ElSelect v-model="model.type" placeholder="请选择公告类型">
              <ElOption v-for="item in noticeTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow>
        <ElCol :span="24">
          <ElFormItem label="公告内容" prop="content">
            <ElInput v-model="model.content" type="textarea" :rows="15" @click="openEditor('notice', model.content)" />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElSpace :size="16" class="float-right">
        <ElButton @click="closeDrawer">取 消</ElButton>
        <ElButton type="primary" @click="handleSubmit">保 存</ElButton>
      </ElSpace>
    </template>
  </ElDialog>
</template>

<style scoped>
:deep(.el-dialog) {
  margin-top: 8vh !important;
}
</style>

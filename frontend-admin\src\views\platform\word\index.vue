<script setup lang="tsx">
import { ref } from 'vue';
import { <PERSON><PERSON><PERSON>on, ElPopconfirm, ElSwitch } from 'element-plus';
import WordSearch from './modules/word-search.vue'
import WordDrawer from './modules/word-drawer.vue'
import WordTableHeaderOperation from './modules/word-table-header-operation.vue'
import {
  fetchSensitivePage,
  updateSensitiveWord,
  removeSensitiveBatchByIds
} from '@/service/api';

import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';

// Define the proper type for your table data
interface SensitiveWords {
  id: string;
  word: string;
  status: number;
  remark: string;
  createTime: string;
  updateTime: string;
}

const wrapperRef = ref<HTMLElement | null>(null);

const {
  columns,
  columnChecks,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  resetSearchParams,
  getDataByPage
} = useTable<SensitiveWords>({
  apiFn: fetchSensitivePage,
  columns: () => [
    { type: 'selection', width: 48 },
    { prop: 'index', label: $t('common.index'), width: 64 },
    { prop: 'word', label: '敏感词', minWidth: 200, showOverflowTooltip: true },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      align: 'center',
      formatter: (row: SensitiveWords) => (
        <ElSwitch
          v-model={row.status}
          active-value={1}
          inactive-value={0}
          onChange={() => handleStatusChange(row)}
        />
      )
    },
    { prop: 'remark', label: '备注', minWidth: 150, showOverflowTooltip: true},
    { prop: 'createTime', label: '创建时间', width: 180 },
    { prop: 'updateTime', label: '更新时间', width: 180 },
    {
      prop: 'operate',
      label: $t('common.operate'),
      align: 'center',
      formatter: (row: SensitiveWords) => (
        <div class="flex-center">
          <ElButton type="primary" plain size="small" onClick={() => handleEdit(row.id)}>
            {$t('common.edit')}
          </ElButton>
          <ElPopconfirm title={$t('common.confirmDelete')} onConfirm={() => handleDelete(row.id)}>
            {{
              reference: () => (
                <ElButton type="danger" plain size="small">
                  {$t('common.delete')}
                </ElButton>
              )
            }}
          </ElPopconfirm>
        </div>
      )
    }
  ]
});

const {
  checkedRowKeys,
  onBatchDeleted,
  handleAdd: handleAddBase,
  onDeleted,
  handleEdit: handleEditBase
} = useTableOperate(data, getData);

const drawerVisible = ref(false);
const drawerType = ref<'add' | 'edit' | 'batch'>('add');
const currentData = ref<any>(null);

async function handleStatusChange(row: SensitiveWords) {
  try {
    await updateSensitiveWord(row);
    window.$message?.success('状态修改成功');
    await getData();
  } catch (error) {
    window.$message?.error('状态修改失败');
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1;
  }
}

function handleAdd() {
  drawerType.value = 'add';
  currentData.value = null;
  drawerVisible.value = true;
}

function handleBatchAdd() {
  drawerType.value = 'batch';
  currentData.value = null;
  drawerVisible.value = true;
}

function handleEdit(id: string) {
  drawerType.value = 'edit';
  currentData.value = data.value.find(item => item.id === id);
  drawerVisible.value = true;
}

function handleDrawerSuccess() {
  getData();
}

async function handleBatchDelete() {
  await removeSensitiveBatchByIds(checkedRowKeys.value);
  onBatchDeleted();
}

async function handleDelete(id: string) {
  await removeSensitiveBatchByIds([id]);
  onDeleted();
}
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <WordSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <div class="flex items-center gap-2">
            <!-- <ElButton type="success" plain @click="handleBatchAdd">批量新增</ElButton> -->
            <WordTableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              @delete="handleBatchDelete"
              @refresh="getData"
              @batch-add="handleBatchAdd"
              @add="handleAdd"
            />
          </div>
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="(selection: SensitiveWords[]) => checkedRowKeys = selection.map(item => item.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" :show-overflow-tooltip="col.showOverflowTooltip" />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
    
    <WordDrawer
      v-model:visible="drawerVisible"
      :type="drawerType"
      :data="currentData"
      @success="handleDrawerSuccess"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
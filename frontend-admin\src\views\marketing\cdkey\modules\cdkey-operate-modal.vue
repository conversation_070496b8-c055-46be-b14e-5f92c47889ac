<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useForm } from '@/hooks/common/form';
import { exportCdkey, fetchSubTypeList, generateCodes } from '@/service/api';

defineOptions({ name: 'PayOperateModal' });

export type OperateType = 'export' | 'create';

interface Props {
  /** the type of operation */
  operateType: OperateType;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useForm();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    export: '导出激活码',
    create: '生成激活码'
  };
  return titles[props.operateType];
});

interface FormData {
  subTypeId: number | null;
  count: number;
}

interface OptionItem {
  value: number;
  label: string;
}

const model = ref<FormData>(createDefaultModel());

function createDefaultModel(): FormData {
  return {
    subTypeId: null,
    count: 10
  };
}

const rules = {
  subTypeId: [{ required: true, message: '请选择套餐', trigger: 'blur' }],
  count: [
    { required: true, message: '请输入生成个数', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ]
};

const options = ref<OptionItem[]>([]);

async function handleInitModel() {
  try {
    const res = await fetchSubTypeList();
    options.value = res.data.map((item: any) => ({
      value: item.id,
      label: item.name
    }));
  } catch {
    options.value = [];
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  try {
    await validate();

    if (model.value.subTypeId === null) {
      ElMessage.error('请选择套餐');
      return;
    }

    if (props.operateType === 'create') {
      const keys = await generateCodes(model.value.subTypeId, model.value.count);
      const keysString = keys.data.join('\n');
      // 使用Clipboard API将格式化后的keys复制到粘贴板
      navigator.clipboard
        .writeText(keysString)
        .then(() => {
          // 复制成功后显示成功提示
          ElMessage({
            message: '生成成功，并已复制到粘贴板',
            type: 'success',
            plain: true
          });
        })
        .catch(() => {
          // 复制失败的处理
          ElMessage({
            message: '复制失败，请手动复制',
            type: 'error',
            plain: true
          });
        });
      emit('submitted');
    } else {
      try {
        const response = await exportCdkey(model.value.subTypeId);
        // 创建blob URL并触发下载
        const blob = new Blob([response.data || ''], { type: 'text/plain;charset=UTF-8' });
        const downloadElement = document.createElement('a');
        const href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = 'card-codes.txt';
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
        ElMessage.success('导出激活码成功');
      } catch {
        ElMessage.error('导出失败，请稍后重试');
        return;
      }
    }

    closeDrawer();
  } catch {
    ElMessage.error('操作失败');
  }
}

watch(visible, newVal => {
  if (newVal) {
    handleInitModel();
    restoreValidation();
    model.value = createDefaultModel();
  }
});
</script>

<template>
  <ElDialog v-model="visible" :title="title" preset="card" align-center width="500px">
    <ElForm ref="formRef" :model="model" :rules="rules" :label-width="100">
      <ElRow>
        <ElCol :span="24">
          <ElFormItem label="选择套餐" prop="subTypeId">
            <ElSelect v-model="model.subTypeId" placeholder="请选择套餐" clearable>
              <ElOption v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol v-if="operateType === 'create'" :span="24">
          <ElFormItem label="激活码个数" prop="count">
            <ElInputNumber v-model="model.count" :min="1" :max="1000" placeholder="请输入生成个数" />
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <ElSpace :size="16" class="float-right">
        <ElButton @click="closeDrawer">取 消</ElButton>
        <ElButton type="primary" @click="handleSubmit">保 存</ElButton>
      </ElSpace>
    </template>
  </ElDialog>
</template>

<style scoped></style>

<template>
    <div class="instructions-container" v-if="sidebarOpenType === '_self' || !sidebarOpenType">
      <h2 class="text-2xl md:text-3xl font-bold mb-6">{{ customerSidebarName }}</h2>
      <div class="rounded shadow-lg">
        <iframe
          :src="customerSidebarUrl" 
          frameborder="0"
          class="w-full h-screen overflow-y-auto"
          :title="customerSidebarName"
        ></iframe>
      </div>
    </div>
  </template>
  
  <style scoped>
  .instructions-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .iframe-wrapper {
    flex: 1;
    min-height: 0;
  }
  
  
  @media (max-width: 768px) {
    .instructions-container {
      height: 100%;
    }
    
  }
  </style>
  
  <script setup>
  import { computed } from 'vue';
  import { useSiteStore } from '@/store/modules/site';
  
  const siteStore = useSiteStore();
  const customerSidebarUrl = computed(() => siteStore.customerSidebarUrl);
  const customerSidebarName = computed(() => siteStore.customerSidebarName);
  const sidebarOpenType = computed(() => siteStore.sidebarOpenType);
</script>
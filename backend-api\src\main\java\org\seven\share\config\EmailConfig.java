package org.seven.share.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

/**
 * @ClassName: MailConfig
 * @Description: 邮件配置
 * @Author: Seven
 * @Date: 2024/8/13
 */
@Scope("prototype")
@Configuration
public class EmailConfig {
    @Bean
    public JavaMailSender javaMailSender() {
//        List<ChatGptConfigEntity> entityList = chatGptConfigDao.selectList(new LambdaQueryWrapper<ChatGptConfigEntity>()
//                .like(ChatGptConfigEntity::getKey, "email"));
//        Map<String, String> map = entityList.stream().collect(Collectors.toMap(ChatGptConfigEntity::getKey, ChatGptConfigEntity::getValue));
//        JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
//        javaMailSender.setHost(map.get("emailHost"));
//        javaMailSender.setPort(Integer.parseInt(map.getOrDefault("emailPort", "0")));
//        javaMailSender.setUsername(map.get("email"));
//        javaMailSender.setPassword(map.get("emailPassword"));
//        javaMailSender.setDefaultEncoding("UTF-8");
//        Properties props = javaMailSender.getJavaMailProperties();
//        props.put("mail.transport.protocol", "smtps");
//        props.put("mail.smtp.auth", "true"); // 开启认证
//        if ("587".equals(map.get("emailPort"))) {
//            props.put("mail.smtp.starttls.enable", "true"); // // 对于587端口，需要开启STARTTLS
//        }
//        props.put("mail.debug", "false"); // 开启debug模式
//        javaMailSender.setJavaMailProperties(props);
        return new JavaMailSenderImpl();
    }
}

# 数据源配置
spring:
  datasource:
    url: *********************************************************************************************************************************************************************************************************
    username: root
    password: Seven@2025
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource # JDBC 连接池类型：HikariCP
    hikari:
      connection-timeout: 100000     # 等待连接池分配链接的最大时长（毫秒），超过这个时长还没有可用的连接则发生 SQLException，默认：30 秒
      minimum-idle: 5               # 最小连接数
      maximum-pool-size: 15         # 最大连接数
      auto-commit: true             # 自动提交
      idle-timeout: 300000          # 连接超时的最大时长（毫秒），超时则被释放（retired），默认：10 分钟
      pool-name: DataSourceHikariCP # 连接池名称
      max-lifetime: 1800000         # 连接的生命时长（毫秒），超时而且没被使用则被释放（retired），默认： 30 分钟
      keepalive-time: 60000        # 添加keepalive，维持连接活性
      validation-timeout: 5000     # 添加验证超时配置
      connection-test-query: SELECT 1  # 添加连接测试
  # redis配置
  redis:
    host: localhost
    port: 6379
    database: 0
    password:
# mybatis plus配置
mybatis-plus:
  configuration:
    #在映射实体或者属性时，将数据库中表名和字段名中的下划线去掉，按照驼峰命名法映射
    #    map-underscore-to-camel-case: true
    # 把SQL的查询的过程输出到控制台
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: AUTO
    banner: false
  mapper-locations: classpath*:mapper/*.xml

api-auth: xxx

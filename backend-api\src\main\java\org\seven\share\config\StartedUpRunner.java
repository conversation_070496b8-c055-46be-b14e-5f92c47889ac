package org.seven.share.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.pojo.entity.ChatGptConfigEntity;
import org.seven.share.mapper.ChatGptUserMapper;
import org.seven.share.mapper.CreateTableMapper;
import org.seven.share.service.ChatGptConfigService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.seven.share.common.util.ConstantUtil.*;
import static org.seven.share.constant.CacheConstant.CONFIG_KEY_PREFIX;


/**
 * @ClassName: StartedUpRunner
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/15
 */

@Slf4j
@Component
public class StartedUpRunner implements ApplicationRunner {

    @Resource
    private CreateTableMapper createTableMapper;

    @Resource
    private ChatGptUserMapper chatGptUserMapper;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private ObjectMapper objectMapper;

    private final long startTime;


    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Value("${spring.datasource.url}")
    private String datasourceUrl;


    public StartedUpRunner(){
        this.startTime = System.currentTimeMillis();
    }
    @Override
    public void run(ApplicationArguments args) throws JsonProcessingException {
        // 初始化建表操作
        createTable();

        if (!isVersionFileExists()) {
            log.info("首次运行，开始初始化...");

            // 初始化系统配置信息
            initSystemConfig();

            // 初始化用户注册速率
            dealSysModelLimits();
        } else {
            log.info("已初始化，跳过检测...");
        }
        // 生成并存储版本号
        CompletableFuture.runAsync(() -> {
            try {
                generateAndStoreVersion();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });

        // 新增用户表字段
        addUserColumns();

        addColumnSafely("index_userToken", () -> createTableMapper.addUserTokenIndex());
        addColumnSafely("user_id", () -> createTableMapper.addGptSessionColumn());
        addColumnSafely("tenant_id", () -> createTableMapper.addTenantIdColumn());
        addColumnSafely("draw_quota", () -> createTableMapper.addDrawQuotaColumn());
        addColumnSafely("isSuper", () -> createTableMapper.addIsSuperColumn());
        addColumnSafely("tenant_id", () -> createTableMapper.addSysNoticeColumn());
        addColumnSafely("sort", () -> createTableMapper.addClaudeSessionColumn());
        addColumnSafely("idx_config_key", () -> createTableMapper.addConfigKeyUnique());

        long endTime = System.currentTimeMillis();
        log.info("expander 服务启动成功，耗时：{} ms", endTime - startTime );
    }

    private void addColumnSafely(String columnName, Runnable addAction) {
        try {
            addAction.run();
            log.info("成功添加 {} 字段", columnName);
        } catch (Exception e) {
            if (e.getMessage().contains("Duplicate column name")) {
                log.warn("{} 字段已存在，跳过添加", columnName);
            } else if (e.getMessage().contains("Duplicate key")) {
                log.warn("{} 主键约束已存在，跳过添加", columnName);
            } else {
                log.error("添加 {} 字段失败", columnName, e);
            }
        }
    }

    /**
     * 更新套餐中的模型速率数据
     * 1、删除o1-mini模型
     * 2、修改o3系列模型名称
     */

    private void initSystemConfig() {
        List<ChatGptConfigEntity> list = new ArrayList<>();
        Map<String, String> configMap = Map.of("freeNodeName", "ChatGPT Free",
                "normalNodeName", "ChatGPT Base",
                "plusNodeName", "ChatGPT Plus",
                "claudeNodeName", "Claude",
                "grokNodeName", "Grok",
                "virtualProNo", "20");
        configMap.forEach((key, value) -> {
            ChatGptConfigEntity config = new ChatGptConfigEntity();
            config.setKey(key);
            config.setValue(value);
            config.setRemark("系统自动生成");
            updateRedis(key, value);
            list.add(config);
        });
        chatGptConfigService.saveBatch(list);
    }

    private void dealSysModelLimits() throws JsonProcessingException {
        // 查询配置
        ChatGptConfigEntity config = chatGptConfigService.getOne(new LambdaQueryWrapper<ChatGptConfigEntity>()
                .eq(ChatGptConfigEntity::getKey, "modelLimits"));

        if (config == null) {
            // 配置不存在，创建默认配置
            String limits = createDefaultModelLimits();
            config = new ChatGptConfigEntity();
            config.setKey("modelLimits");
            config.setValue(limits);
            chatGptConfigService.save(config);
            updateRedis("modelLimits", limits);
            log.info("Created default modelLimits: {}", limits);
        }
    }

    private String createDefaultModelLimits() throws JsonProcessingException {
        Map<String, String> map = chatGptConfigService.getKeyValueMapByKeys(List.of("usageCount", "usagePeriod"));
        int defaultLimit = Optional.ofNullable(map.get("usageCount"))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .map(Integer::parseInt)
                .orElse(20);
        String defaultPer = Optional.ofNullable(map.get("usagePeriod"))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .orElse("1h");

        ObjectNode jsonNode = objectMapper.createObjectNode();
        jsonNode.set("claude-3.5", createLimitNode(20, "1h"));
        jsonNode.set("gpt-4o", createLimitNode(defaultLimit, defaultPer));
        jsonNode.set("o3", createLimitNode(defaultLimit, defaultPer));
        jsonNode.set("o1-pro", createLimitNode(defaultLimit, defaultPer));
        jsonNode.set("o4-mini", createLimitNode(defaultLimit, defaultPer));
        jsonNode.set("o4-mini-high", createLimitNode(defaultLimit, defaultPer));
        jsonNode.set("gpt-4-5", createLimitNode(50, "1w"));
        jsonNode.set("grok-3", createLimitNode(50, "1d"));
        jsonNode.set("research", createLimitNode(50, "1d"));
        jsonNode.set("third-models", createLimitNode(50, "1h"));

        return objectMapper.writeValueAsString(jsonNode);
    }

    private ObjectNode createLimitNode(int limit, String per) {
        ObjectNode node = objectMapper.createObjectNode();
        node.put("limit", limit);
        node.put("per", per);
        return node;
    }

    private void updateRedis(String key, String value) {
        String redisKey = CONFIG_KEY_PREFIX + key;
        String currentValue = stringRedisTemplate.opsForValue().get(redisKey);
        if (!Objects.equals(currentValue, value)) {
            stringRedisTemplate.opsForValue().set(redisKey, value, 30, TimeUnit.DAYS);
            log.info("更新 Redis 配置: {} = {}", redisKey, value);
        }
    }

    private String getSchema() {
        String[] parts = datasourceUrl.split("/");
        for (String part : parts) {
            if (part.contains("?")) {
                return part.substring(0, part.indexOf("?"));
            }
        }
        return parts[parts.length - 1]; // 返回 cool
    }

    private void addUserColumns() {
        log.info("开始校验字段...");
        // 已存在的字段
        List<String> existingColumns  = chatGptUserMapper.getTableColumns("chatgpt_user", getSchema());
        // 定义期望的表结构
        Map<String, String> desiredColumns = new LinkedHashMap<>();
        // 定义期望的表结构字段及其属性
        desiredColumns.put("id", "bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT");
        desiredColumns.put("createTime", "datetime(3) NOT NULL COMMENT '创建时间'");
        desiredColumns.put("updateTime", "datetime(3) NOT NULL COMMENT '更新时间'");
        desiredColumns.put("deleted_at", "datetime(3) DEFAULT NULL");
        desiredColumns.put("userToken", "varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'UserToken'");
        desiredColumns.put("password", "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码'");
        desiredColumns.put("expireTime", "datetime(3) DEFAULT NULL COMMENT '过期时间'");
        desiredColumns.put("isPlus", "tinyint(1) DEFAULT 0 COMMENT 'PLUS'");
        desiredColumns.put("isAdmin", "tinyint(1) DEFAULT 0 COMMENT '是否为管理员'");
        desiredColumns.put("remark", "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '备注'");
        desiredColumns.put("sessionId", "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'sessionId'");
        desiredColumns.put("email", "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'email'");
        desiredColumns.put("carids", "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'chatgpt车队'");
        desiredColumns.put("`limit`", "bigint(20) DEFAULT 20 COMMENT '限制'");
        desiredColumns.put("per", "varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '1h' COMMENT '时间段'");
        desiredColumns.put("subTypeId", "bigint(20) DEFAULT 0 COMMENT '用户订阅类型id'");
        desiredColumns.put("affCode", "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '推广码'");
        desiredColumns.put("affQuota", "varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '可提现金额'");
        desiredColumns.put("affHistoryQuota", "varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '推广已提现金额'");
        desiredColumns.put("affTotalQuota", "varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '推广总金额'");
        desiredColumns.put("affCount", "bigint(20) DEFAULT 0 COMMENT '推广人数'");
        desiredColumns.put("receiptFile", "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '收款码'");
        desiredColumns.put("status", "bigint(20) DEFAULT 1 COMMENT '状态'");
        desiredColumns.put("inviterId", "bigint(20) DEFAULT NULL COMMENT '邀请人的Id'");
        desiredColumns.put("isBackupData", "tinyint(1) DEFAULT 1 COMMENT '聊天数据是否备份'");
        desiredColumns.put("lastActiveTime", "datetime(3) DEFAULT NULL COMMENT '最后登陆时间'");
        desiredColumns.put("affRate", "double DEFAULT NULL COMMENT '返佣比例'");
        desiredColumns.put("userType", "bigint(20) DEFAULT 1 COMMENT '账号类型'");
        desiredColumns.put("gptsIds", "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'gptsIds'");
        desiredColumns.put("claudeExpireTime", "datetime(3) DEFAULT NULL COMMENT 'claude过期时间'");
        desiredColumns.put("claudeProExpireTime", "datetime(3) DEFAULT NULL COMMENT 'claude pro过期时间'");
        desiredColumns.put("isPro", "tinyint(1) DEFAULT 0 COMMENT 'PRO'");
        desiredColumns.put("claudeCarids", "longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'claude车队'");
        desiredColumns.put("claudeLimit", "bigint(20) DEFAULT 20 COMMENT '限制'");
        desiredColumns.put("claudePer", "varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '1h' COMMENT '时间段'");
        desiredColumns.put("claudeSubTypeId", "bigint(20) DEFAULT 0 COMMENT 'claude订阅类型id'");
        desiredColumns.put("plusExpireTime", "datetime(3) DEFAULT NULL COMMENT 'plus过期时间'");
        desiredColumns.put("dailyConversationCount", "int DEFAULT 0 COMMENT '对话次数'");
        desiredColumns.put("clientIp", "varchar(100) DEFAULT NULL COMMENT '客户端ip'");
        desiredColumns.put("dailyClaudeConversationCount", "int DEFAULT 0 COMMENT 'claude对话次数'");
        desiredColumns.put("deviceId", "varchar(50) DEFAULT NULL COMMENT '设备id'");
        desiredColumns.put("loginType", "tinyint(1) DEFAULT 1 COMMENT '登录方式'");
        desiredColumns.put("grokExpireTime", "datetime(3) DEFAULT NULL COMMENT 'grok过期时间'");
        desiredColumns.put("grokSuperExpireTime", "datetime(3) DEFAULT NULL COMMENT 'grok super过期时间'");
        desiredColumns.put("model_limits", "json COMMENT '模型限制'");
        desiredColumns.put("loginToken", "varchar(50) COMMENT '登陆凭证'");
        desiredColumns.put("tenant_id", "VARCHAR(20) NOT NULL DEFAULT '000000' COMMENT '租户编号'");

        StringBuilder alterSql = new StringBuilder("ALTER TABLE chatgpt_user ");
        boolean needAlter = false;
        for (String columnName : desiredColumns.keySet()) {
            String columnType = desiredColumns.get(columnName);
            boolean columnExists = existingColumns.stream()
                    .anyMatch(column -> column.equalsIgnoreCase(columnName.replace("`", "")));
            if (!columnExists) {
                log.info("需要新增字段,字段名称：{}", columnName);
                if (needAlter) {
                    alterSql.append(", ");
                }
                alterSql.append("ADD COLUMN ").append(columnName).append(" ").append(columnType);
                needAlter = true;
            }
        }
        if (needAlter) {
            chatGptUserMapper.executeSql(alterSql.toString());
            List<String> updatedColumns = chatGptUserMapper.getTableColumns("chatgpt_user", getSchema());
            if (updatedColumns.stream().anyMatch(col -> col.equalsIgnoreCase("loginToken"))) {
                // 添加索引
                try {
                    chatGptUserMapper.executeSql("CREATE INDEX idx_login_token ON chatgpt_user(loginToken)");
                    log.info("已为 loginToken 字段创建索引");
                } catch (Exception e) {
                    log.warn("创建 loginToken 索引失败，可能已存在: {}", e.getMessage());
                }
            }
        }
        log.info("字段校验成功...");
    }

    private void createTable() {
        log.info("开始建表...");
        createTableMapper.createCouponTable();

        createTableMapper.createClaudeSessionTable();

        createTableMapper.createSubTypeTable();

        createTableMapper.createPayConfigTable();

        createTableMapper.createPayLogsTable();

        createTableMapper.createSysNoticeTable();

        createTableMapper.createSensitiveWordTable();

        createTableMapper.createChatGptRedemptionTable();

        createTableMapper.createWithdrawalsTable();

        createTableMapper.createSignInRecordsTable();

        createTableMapper.createChatGptAffRecordTable();

        createTableMapper.createRiskControlRecordTable();

        createTableMapper.createSysTenantTable();

        createTableMapper.createDrawRecord();

        createTableMapper.createQuotaChangeRecord();

        createTableMapper.crateUserDrawQuota();

        createTableMapper.createGrokSessionTable();

        createTableMapper.createGrokConversationsTable();

        createTableMapper.createClaudeConversationsTable();

        createTableMapper.createSysOperatorLogTable();

        log.info("建表成功...");
    }

    private boolean isVersionFileExists() {
        File versionFile = new File(VERSION_FILE);
        return versionFile.exists();
    }

    private void generateAndStoreVersion() throws IOException {
        String version = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        Files.write(Paths.get(VERSION_FILE), version.getBytes());  // 将版本号写入文件
    }
}

<template>
  <div class="flex justify-center items-center min-h-screen">
    <el-card class="w-full max-w-96">
      <h1 class="text-2xl text-center font-bold mb-4">{{ t('register.title') }}</h1>
      <el-form ref="registerRef" :model="registerForm" :rules="registerRules">
        <el-form-item prop="username">
          <el-input v-model.trim="registerForm.username" type="text" size="large" auto-complete="off"
            :placeholder="t('register.username')">
            <template #prefix>
              <el-icon>
                <User />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="email">
          <el-input v-model="registerForm.email" type="email" size="large" auto-complete="off"
            :placeholder="t('register.email')">
            <template #prefix>
              <el-icon>
                <Message />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="code" v-if="enableEmailCheck">
          <div class="flex">
            <el-input v-model="registerForm.code" type="text" size="large" auto-complete="off"
              :placeholder="t('register.code')" class="flex-grow mr-4">
              <template #prefix>
                <el-icon>
                  <Key />
                </el-icon>
              </template>
            </el-input>
            <el-button :disabled="isGettingCode" size="large" @click="getVerificationCode">
              {{ isGettingCode ? `${countdown}s` : t('register.getVerificationCode') }}
            </el-button>
          </div>
        </el-form-item>

        <el-form-item prop="password">
          <el-input v-model="registerForm.password" type="password" size="large" auto-complete="off"
            :placeholder="t('register.password')" show-password>
            <template #prefix><el-icon>
                <Lock />
              </el-icon></template>
          </el-input>
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input v-model="registerForm.confirmPassword" type="password" size="large" auto-complete="off"
            :placeholder="t('register.confirmPassword')" show-password>
            <template #prefix><el-icon>
                <Lock />
              </el-icon></template>
          </el-input>
        </el-form-item>

        <!--邀请码功能-->
        <el-form-item prop="inviter">
          <el-input v-model="registerForm.inviter" type="text" size="large" auto-complete="off" :disabled="disableInviter"
            :placeholder="t('register.inviter')">
            <template #prefix>
              <el-icon>
                <Promotion />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <button :disabled="loading" type="submit" class="w-full text-lg bg-black dark:bg-white text-white dark:text-black rounded-lg font-medium hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 disabled:opacity-50 py-2" @click.prevent="handleRegister">
            <span v-if="!loading">{{ t('register.register') }}</span>
            <span v-else>{{ t('register.registering') }}</span>
          </button>
        </el-form-item>
      </el-form>

      <div class="mt-4 text-center text-sm text-blue-500 hover:underline">
        <router-link to="/login">{{ t('register.alreadyHaveAccount') }} {{ t('register.login') }}</router-link>
      </div>
    </el-card>
  </div>
</template>
  
<script setup>
import { ref, onMounted, computed  } from 'vue'
import { User, Message, Lock, Key, Promotion } from '@element-plus/icons-vue'
import { ElNotification } from 'element-plus'
import * as api from '@/api/user.js'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import Cookies from 'js-cookie'
import { useSiteStore } from '@/store/modules/site.js';
import { encrypt } from '@/utils/encrypt'

const siteStore = useSiteStore();
const disableInviter = ref(false)
const { t } = useI18n()
const enableEmailCheck = computed(() => siteStore.enableEmailCheck === 'true' || siteStore.enableEmailCheck === '');
const enableInviteCode = computed(() => siteStore.enableInviteCode === 'true');
const router = useRouter();  // 使用vue-router
const route = useRoute();

const registerForm = ref({
  username: "",
  email: "",
  code: "",
  password: "",
  confirmPassword: "",
  inviter: ''
});
const registerRef = ref()
const registerRules = {
  username: [
    { required: true, message: t('register.errors.usernameRequired'), trigger: "blur" },
    { min: 3, max: 20, message: t('register.errors.usernameLength'), trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (/[\u4e00-\u9fa5]/.test(value)) {
          return callback(new Error('用户名不能包含中文字符'));
        }
        if (/\s/.test(value)) {
        return callback(new Error('用户名不能包含空格'));
      }
        return callback();
      },
      trigger: "blur"
    }
  ],
  email: [
    { required: true, message: t('register.errors.emailRequired'), trigger: "blur" },
    { type: "email", message: t('register.errors.emailInvalid'), trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        const regex = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,}$/;
        if (!regex.test(value)) {
          callback(new Error('请输入有效的邮箱地址'));
        } else {
          callback();
        }
    }, trigger: "blur" }
  ],
  code: [
    { required: true, message: t('register.errors.codeRequired'), trigger: "blur" },
    { len: 6, message: t('register.errors.codeLength'), trigger: "blur" }
  ],
  password: [
    { required: true, message: t('register.errors.passwordRequired'), trigger: "blur" },
    { min: 6, message: t('register.errors.passwordMinLength'), trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: t('register.errors.confirmPasswordRequired'), trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.value.password) {
          callback(new Error(t('register.errors.passwordMismatch')));
        } else {
          callback();
        }
      },
      trigger: "blur"
    },
  ],
  inviter: [{ required: enableInviteCode.value, message: t('register.errors.inviterRequired'), trigger: "blur" }]
};

const loading = ref(false);
const isGettingCode = ref(false);
const countdown = ref(60);

function handleRegister () {
  // 实现注册逻辑
  registerRef.value.validate(async valid => {
    if (valid) {
      loading.value = true;
      try {
        const res = await api.register(registerForm.value)
        ElNotification({
          message: t('register.errors.registrationSuccess'),
          type: 'success',
        })
        // 将用户名和密码存到cookie中
        Cookies.set("username", registerForm.value.username, { expires: 30 });
        Cookies.set('password', encrypt(registerForm.password), { expires: 30, secure: true });
        router.push('/login');
      } finally {
        loading.value = false;
      }
    }
  });
}

const getVerificationCode = async () => {
  if (isGettingCode.value) return;

  // 验证邮箱
  if (!registerForm.value.email) {
    ElNotification.error(t('register.errors.emailMissing'));
    return;
  }

  // 设置倒计时的持续时间（秒）
  const countdownDuration = 60;

  // 记录倒计时的结束时间
  const endTime = Date.now() + countdownDuration * 1000;
  localStorage.setItem('countdownEndTime', endTime);

  isGettingCode.value = true;

  // 开始倒计时
  const updateCountdown = () => {
    const remainingTime = Math.max(0, Math.floor((endTime - Date.now()) / 1000));
    countdown.value = remainingTime;

    if (remainingTime <= 0) {
      clearInterval(timer);
      isGettingCode.value = false;
      localStorage.removeItem('countdownEndTime'); // 清除存储的倒计时
    }
  };

  const timer = setInterval(updateCountdown, 1000);

  // 初始化时更新一次倒计时
  updateCountdown();

  // 在这里添加发送验证码的逻辑
  const param = {
    username: registerForm.value.username,
    email: registerForm.value.email
  };
  try {
    await api.getRegisterCode(param);
    ElNotification.success(t('register.errors.codeSent'));
  } catch (error) {
    clearInterval(timer);
    isGettingCode.value = false;
    localStorage.removeItem('countdownEndTime');
  }
};

// 页面加载时检查是否有未完成的倒计时
const savedEndTime = localStorage.getItem('countdownEndTime');
if (savedEndTime) {
  const remainingTime = Math.max(0, Math.floor((savedEndTime - Date.now()) / 1000));
  if (remainingTime > 0) {
    isGettingCode.value = true;
    countdown.value = remainingTime;

    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
        isGettingCode.value = false;
        localStorage.removeItem('countdownEndTime');
      }
    }, 1000);
  } else {
    localStorage.removeItem('countdownEndTime');
  }
}

onMounted(() => {
  const inviterParam = route.query.inviter
  if (inviterParam && inviterParam != 'null') {
    registerForm.value.inviter = inviterParam; // 将参数值填入响应式变量
    disableInviter.value = true
  }
})

</script>

import request from "../utils/request";
// 获取用户信息
export function page (params) {
  return request({
    url: '/page',
    method: 'get',
    params
  });
}

export function createUser (data) {
  return request({
    url: '/create',
    method: 'post',
    data
  });
}

export function removeBatchByIds (data) {
  return request({
    url: '/del',
    method: 'delete',
    data
  });
}

export function updateUser (data) {
  return request({
    url: '/update',
    method: 'put',
    data
  });
}

export function exportListToTxt (params) {
  return request({
    url: '/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

export function exportTxtByIds (data) {
  return request({
    url: '/export-select',
    method: 'post',
    data,
    responseType: 'blob'
  });
}

export function changeSwitch (params) {
  return request({
    url: '/change',
    method: 'put',
    params
  });
}

export function statistic () {
  return request({
    url: '/statistic',
    method: 'get',
  });
}

export function getRegisterCode (params) {
  return request({
    url: '/register-code',
    params,
    method: 'get',
  });
}

export function getResetPasswordCode (params) {
  return request({
    url: '/password-rest-code',
    params,
    method: 'get',
  });
}

export function register (data) {
  return request({
    url: '/register',
    data,
    method: 'post',
  });
}

export function login (data) {
  return request({
    url: '/login',
    data,
    method: 'post',
  });
}

export function resetPassword (data) {
  return request({
    url: '/reset',
    data,
    method: 'post',
  });
}

export function authUser (data) {
  return request({
    url: '/openai/auth',
    data,
    method: 'post',
  });
}

export function getUser (params) {
  return request({
    url: '/getme',
    params,
    method: 'get',
  });
}

export function getCarList (params) {
  return request({
    url: '/openai/carpage',
    method: 'get',
    params
  });
}

export function getPeriodTime (params) {
  return request({
    url: '/calc',
    params,
    method: 'get',
  });
}

export function changePassword (data) {
  return request({
    url: '/change-password',
    data,
    method: 'post',
  });
}
export function getInviterPage (params) {
  return request({
    url: '/inviterPage',
    params,
    method: 'get',
  });
}

export function fetchAccessToken () {
  return request({
    url: '/getAccessToken ',
    method: 'get',
  })
}

export function bindUserEmail (params) {
  return request({
    url: '/updateUserEmail',
    method: 'get',
    params
  })
}

export function checkAccess (params) {
  return request({
    url: '/checkAccess',
    method: 'get',
    params
  })
}
export function getPaymentHistory(userToken) {
  return request({
    url: '/payment/history',
    method: 'get',
    params: { userToken }
  })
}
export function logout(params) {
  return request({
    url: '/logout',
    method: 'get',
    params
  })
}

export function signIn(params) {
  return request({
    url: '/sign-in',
    method: 'post',
    params
  })
}

export function getSignInInfo(params) {
  return request({
    url: '/sign-in/info',
    method: 'get',
    params
  })
}

export function getUserLimits(params) {
  return request({
    url: '/limits',
    method: 'get',
    params
  })
}

export function fetchLoginWithAuthCode(params) {
  return request({
    url: '/auth-code',
    method: 'get',
    params
  })
}

export function fetchSassLoginUrl () { 
  return request({
    url:'/sass/logintoken'
  })
}

export function fetchGrokLoginUrl (isSuper) { 
  return request({
    url:'/grok/loginToken',
    params: { isSuper }
  })
}


export function fetchGrokList () {
  return request({
      url: '/grok/carpage',
      method: 'get',
  });
}

export function fetchSassList () {
  return request({
      url: '/sass/carpage',
      method: 'get',
  });
}

export function fetchLoginToken () {
  return request({
      url: '/getLoginToken',
      method: 'get',
  });
}
package org.seven.share.common.util;

import java.util.regex.Pattern;

/**
 * @ClassName: EmailUtil
 * @Description:
 * @Author: Seven
 * @Date: 2024/10/19
 */
public class EmailUtil {
    final static Pattern partern = Pattern.compile("[a-zA-Z0-9]+[\\.]{0,1}[a-zA-Z0-9]+@[a-zA-Z0-9]+\\.[a-zA-Z]+");
    /**
     * 验证输入的邮箱格式是否符合
     * @param email
     * @return 是否合法
     */
    public static boolean emailFormat(String email){
        boolean isMatch = partern.matcher(email).matches();
        return isMatch;
    }
}

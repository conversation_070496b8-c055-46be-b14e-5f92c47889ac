package org.seven.share.common.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * @ClassName: OrderUtils
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/4
 */
public class OrderUtils {
    public static String generateOrderNumber() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());

        // 生成随机数
        Random random = new Random();
        int randomNum = random.nextInt(10000);

        // 组合时间戳和随机数生成订单号
        return timestamp + String.format("%04d", randomNum);
    }
}

package org.seven.share.security.filter;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.util.SysUserDetail;
import org.seven.share.config.ApiSecurityConfig;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.filter.OncePerRequestFilter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class RoleBasedApiFilter extends OncePerRequestFilter {

    private final ApiSecurityConfig apiSecurityConfig;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    public RoleBasedApiFilter(ApiSecurityConfig apiSecurityConfig) {
        this.apiSecurityConfig = apiSecurityConfig;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        try {
            if (authentication != null && authentication.isAuthenticated()) {
                boolean isExcluded = apiSecurityConfig.getExcludeApis().stream()
                        .anyMatch(pattern -> pathMatcher.match(pattern, request.getRequestURI()));
                if (isExcluded) {
                    filterChain.doFilter(request, response);
                    return;
                }

                Object principal = authentication.getPrincipal();
                if (principal instanceof SysUserDetail) {

                    SysUserDetail sysUserDetail = (SysUserDetail) principal;
                    List<String> roleUser = sysUserDetail.getRoles();
                    List<String> permissions = sysUserDetail.getPermissions();
                    String requestUri = request.getRequestURI();

                    boolean isSuperAdmin = roleUser.contains("SUPER_ADMIN");
                    // 超级管理员直接放行adminAllowedApis
                    if (isSuperAdmin) {
                        filterChain.doFilter(request, response);
                        return;
                    } else {
                        boolean hasAccess = permissions.stream()
                                .anyMatch(pattern -> pathMatcher.match(pattern, requestUri));
                        if (!hasAccess) {
                            log.info("用户角色: {},没有{}的访问权限",roleUser, requestUri);
                            throw new AccessDeniedException("权限不足");
                        }
                    }
                }
            }
        }catch (AccessDeniedException e) {
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, e.getMessage());
            return;
        }
        filterChain.doFilter(request, response);
    }
}

package org.seven.share.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.exception.CustomException;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Host machine identification utility
 */
@Slf4j
public class LicenseUtil {

    private static final String PRODUCT_UUID_PATH = "/host/sys/class/dmi/id/product_uuid";
    private static final String MACHINE_ID_PATH = "/host/etc/machine-id";

    /**
     * Generate a unique authorization value based on host machine identifiers
     * @return unique authorization string
     */
    public static String generateServerHash() {
        try {
            String productUuid = readFile(PRODUCT_UUID_PATH);
            String machineId = readFile(MACHINE_ID_PATH);
            if (productUuid == null || machineId == null) {
                throw new CustomException("无法读取主机标识信息，请先确认docker-compose.yml中是否正确挂载");
            }

            // Combine the identifiers and generate a hash
            String combined = productUuid.trim() + machineId.trim();
            return generateSHA256(combined);
        } catch (Exception e) {
            log.error("生成主机授权值失败", e);
            throw new CustomException(e.getMessage());
        }
    }

    /**
     * Read content from a file
     * @param filePath path to the file
     * @return file content as string
     */
    private static String readFile(String filePath) {
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            return reader.readLine();
        } catch (IOException e) {
            log.error("读取文件失败: {}", filePath, e);
            return null;
        }
    }

    /**
     * Generate SHA-256 hash of the input string
     * @param input input string to hash
     * @return SHA-256 hash as hex string
     */
    private static String generateSHA256(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));

            // Convert byte array to hex string
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("SHA-256算法不可用", e);
            throw new CustomException("SHA-256算法不可用");
        }
    }
}

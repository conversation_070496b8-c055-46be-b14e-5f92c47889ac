package org.seven.share.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.api.R;
import org.seven.share.common.pojo.entity.ChatGptSysNoticeEntity;
import org.seven.share.service.ChatGptSysNoticeService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ChatGptSysNoticeContorller
 * @Description: 系统公告控制器
 * @Author: Seven
 * @Date: 2024/8/1
 */

@RestController
@RequestMapping("/expander-api/notice")
public class ChatGptSysNoticeController {

    @Resource
    private ChatGptSysNoticeService chatGptSysNoticeService;

    @GetMapping("/page")
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer current,
                  @RequestParam(value = "size", defaultValue = "10") Integer size,
                  Long id){
        Page<ChatGptSysNoticeEntity> pageInfo = chatGptSysNoticeService.selectNoticePage(new Page<>(current, size), id);
        return R.ok(pageInfo);
    }


    @PostMapping("/create")
    @CacheEvict(cacheNames = "notice", allEntries = true)
    public R create(@RequestBody @Validated ChatGptSysNoticeEntity notice) {
        notice.setPublishTime(LocalDateTime.now());
        return R.ok(chatGptSysNoticeService.save(notice));
    }

    @PostMapping("/update")
    @CacheEvict(cacheNames = "notice", allEntries = true)
    public R update(@RequestBody @Validated ChatGptSysNoticeEntity notice) {
        return R.ok(chatGptSysNoticeService.updateById(notice));
    }

    @DeleteMapping("/delete")
    @CacheEvict(cacheNames = "notice", allEntries = true)
    public R delete(@RequestBody List<String> ids){
        chatGptSysNoticeService.removeBatchByIds(ids);
        return R.ok();
    }

    @GetMapping("/list")
    public R list(){
        LambdaQueryWrapper<ChatGptSysNoticeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(ChatGptSysNoticeEntity::getUpdateTime).last("limit 10");
        List<ChatGptSysNoticeEntity> list = chatGptSysNoticeService.list(wrapper);
        return R.ok(list);
    }
}

package org.seven.share.common.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: AuthorizationResult
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/10
 */
@Data
public class AuthorizationResult implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String ip;

    private boolean authorized;

    private boolean temporaryAuthorized; //临时授权

    private int isPlus;

    private int isClaude;

    private int grok;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireTime;  // 授权的有效截止时间

    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expireTime);  // 判断当前时间是否超过过期时间
    }
}

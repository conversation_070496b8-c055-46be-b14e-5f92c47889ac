package org.seven.share.common.pojo.vo;

import lombok.Data;
import org.seven.share.common.pojo.dto.CarStatus;

import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: CarInfoVo
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/5
 */
@Data
public class CarInfoVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String carID;

    private Integer isPlus;

    private Long count;

    private String status;

    private String detail;

    private CarStatus carStatus;

}

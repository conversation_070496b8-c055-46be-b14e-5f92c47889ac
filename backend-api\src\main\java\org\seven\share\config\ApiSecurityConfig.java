package org.seven.share.config;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import java.util.ArrayList;
import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "api.security")
@Data
public class ApiSecurityConfig {
    private List<String> normalNotAllowedApis = new ArrayList<>();

    private List<String> excludeApis = new ArrayList<>();
}

import router from './router'
import { getToken } from '@/utils/auth'
import { ElNotification } from 'element-plus'
import NProgress from 'nprogress' // 导入 nprogress
import 'nprogress/nprogress.css'
import { useSiteStore } from '@/store/modules/site';
import { useUserStore } from '@/store/modules/user';

// 配置 NProgress
NProgress.configure({ showSpinner: false }) // 禁用加载螺旋

const whiteList = ['/login', '/401', '/403', '/404',"/",'/register','/home', '/forgot-password'];
router.beforeEach(async (to, from, next) => {
  /* 路由发生变化修改页面title */
  if (to.meta.title) {
    document.title = to.meta.title;
  }
  const siteStore = useSiteStore(); 
  const userStore = useUserStore(); 
  const enableRegister = siteStore.enableRegister
  const token = getToken();
 
  const userRole = userStore.isAdmin == '1' ? 'admin' : 'user'; // 正确获取 userStore 的 isAdmin 值
  if (enableRegister=='false' && to.path === '/register') { 
    ElNotification({
      message: '管理员未开放注册',
      type: 'error',
    });
    next("/login")
    NProgress.done(); // 结束进度条
    return; // 防止继续执行，避免进入注册页面
  }
  if (token) {
    if (to.meta.requireAuth && userRole === 'admin') {
      // 如果页面需要认证且用户角色为admin，则放行
      next();
    } else if (to.meta.requireAuth && userRole !== 'admin') {
      // 如果页面需要认证但用户角色不是admin，提示无权限
      ElNotification({
        message: '您没有权限',
        type: 'error',
      });
      next({path:'/403'}); // 停止当前导航
    } else {
      next();
    }
  } else {
    // 如果没有token
    if (whiteList.includes(to.path)) {
      // 在免登录白名单，直接进入
      next();
    } else {
      // 不在白名单，重定向到登录页
      ElNotification({
        message: '登录信息过期，请重新登录',
        type: 'error',
      });
      next({ path: '/login' });
    }
  }
})


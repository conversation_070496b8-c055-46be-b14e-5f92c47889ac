<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.seven.share.mapper.ChatGptPayLogsMapper">
    <select id="getOverallPayStats" resultType="java.util.Map">
        SELECT
            -- 今日统计
            SUM(CASE WHEN DATE(updateTime) = CURDATE() AND status = 'success' THEN money ELSE 0 END) AS todayTotalMoney,
            SUM(CASE WHEN DATE(updateTime) = CURDATE() AND status = 'success' THEN 1 ELSE 0 END) AS todayPayNum,
            SUM(CASE WHEN DATE(updateTime) = CURDATE() AND status = 'pending' THEN 1 ELSE 0 END) AS todayUnPayNum,

            -- 月总统计
            SUM(CASE WHEN MONTH(updateTime) = MONTH(CURDATE()) AND YEAR(updateTime) = YEAR(CURDATE()) AND status = 'success' THEN money ELSE 0 END) AS monthTotalMoney
        FROM
            chatgpt_epaylogs
    </select>
</mapper>

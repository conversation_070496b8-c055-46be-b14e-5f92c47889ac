<script setup lang="ts">
import * as echarts from 'echarts';
import { computed, onMounted, onUnmounted, ref, nextTick } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { getLast12MonthsIncome, getLast15DaysIncome, getYearlyIncome, statistic } from '@/service/api/statistic';
import HeaderBanner from './modules/header-banner.vue';
import CardData from './modules/card-data.vue';
import PieChart from './modules/pie-chart.vue';
import ProjectNews from './modules/project-news.vue';
import CreativityBanner from './modules/creativity-banner.vue';
import { useAuthStore } from '@/store/modules/auth';
const authStore = useAuthStore();
const roles = authStore.userInfo?.roles

interface StatisticData {
  totalNum: number;
  addNum: number;
  payNum: number;
  unPayNum: number;
  totalMoney: number;
  topics: number;
  normalUsage: number;
  advanceUsage: number;
  monthTotalMoney: number;
  onlineNums: number;
  visitorUsage?: number;
  claudeUsage?: number;
}

interface ChartData {
  xAxisData: string[];
  yAxisData: number[];
}

interface EchartsData {
  day: ChartData;
  month: ChartData;
  year: ChartData;
}

type ViewType = 'day' | 'month' | 'year';

const appStore = useAppStore();
const gap = computed(() => (appStore.isMobile ? 0 : 16));

const statisticData = ref<StatisticData>({
  totalNum: 0,
  addNum: 0,
  payNum: 0,
  unPayNum: 0,
  totalMoney: 0.0,
  topics: 0,
  normalUsage: 0,
  advanceUsage: 0,
  monthTotalMoney: 0,
  onlineNums: 0
});

const currentView = ref<ViewType>('day');
const echartsData = ref<EchartsData>({
  day: { xAxisData: [], yAxisData: [] },
  month: { xAxisData: [], yAxisData: [] },
  year: { xAxisData: [], yAxisData: [] }
});

let myChart: echarts.ECharts | null = null;

// 获取视图标题
const getViewTitle = (view: ViewType): string => {
  switch (view) {
    case 'day':
      return '日收入趋势';
    case 'month':
      return '月收入趋势';
    case 'year':
      return '年收入趋势';
    default:
      return '收入趋势';
  }
};

// 更新图表
const updateChart = (view: ViewType) => {
  if (!myChart) return;
  const data = echartsData.value[view];
  myChart.setOption({
    xAxis: {
      data: data.xAxisData
    },
    series: [
      {
        data: data.yAxisData
      }
    ],
    title: {
      text: getViewTitle(view)
    }
  });
};

// 提取 API 响应中的数据
const processEchartsData = (data: Array<{ xAxis: string; yAxis: number }>, type: ViewType) => {
  const xAxisData: string[] = [];
  const yAxisData: number[] = [];

  data.forEach(item => {
    xAxisData.push(item.xAxis);
    yAxisData.push(item.yAxis);
  });

  echartsData.value[type] = {
    xAxisData,
    yAxisData
  };
};

// API 请求数据
const getStatistic = async () => {
  try {
    const { data } = await statistic();
    if (data) {
      statisticData.value = data as StatisticData;
    }
  } catch (error) {
    console.error('Failed to fetch statistics:', error);
  }
};

// 获取所有时间维度的数据
const fetchAllData = async () => {
  try {
    const [dayResponse, monthResponse, yearResponse] = await Promise.all([
      getLast15DaysIncome(),
      getLast12MonthsIncome(),
      getYearlyIncome()
    ]);

    if (dayResponse.data) {
      processEchartsData(dayResponse.data as Array<{ xAxis: string; yAxis: number }>, 'day');
    }
    if (monthResponse.data) {
      processEchartsData(monthResponse.data as Array<{ xAxis: string; yAxis: number }>, 'month');
    }
    if (yearResponse.data) {
      processEchartsData(yearResponse.data as Array<{ xAxis: string; yAxis: number }>, 'year');
    }
  } catch (error) {
    console.error('Failed to fetch chart data:', error);
  }
};

// 视图切换处理
const handleViewChange = (value: string | number | boolean | undefined) => {
  if (typeof value === 'string' && (value === 'day' || value === 'month' || value === 'year')) {
    updateChart(value);
  }
};

onMounted(async () => {
  await getStatistic();
  await fetchAllData();

  // 使用 nextTick 确保 DOM 已经渲染
  await nextTick();
  
  const chartDom = document.getElementById('main');
  if (!chartDom) return;

  // 确保容器有高度
  chartDom.style.height = '400px';
  
  myChart = echarts.init(chartDom);

  const option = {
    title: {
      text: '收入统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter(params: any) {
        const data = params[0];
        return `时间：${data.name}<br/>收入：${data.value}`;
      }
    },
    grid: {
      top: '15%',
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      axisTick: {
        alignWithLabel: true
      },
      data: echartsData.value.day.xAxisData
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: echartsData.value.day.yAxisData,
        type: 'bar',
        barWidth: '60%',
        name: '收入'
      }
    ]
  };

  myChart.setOption(option);

  // 监听窗口大小变化
  const handleResize = () => {
    myChart?.resize();
  };
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  if (myChart) {
    myChart.dispose();
  }
});
</script>

<template>
  <ElSpace direction="vertical" fill class="pb-0" :size="0">
    <HeaderBanner class="mb-16px" />
    <CardData class="mb-16px" />
    <ElRow :gutter="gap" v-if="!roles.includes('TENANT')">
      <ElCol :lg="14" :sm="24" class="mb-16px">
        <ElCard class="card-wrapper">
          <div class="chart-controls">
            <ElRadioGroup v-model="currentView" @change="handleViewChange">
              <ElRadioButton value="day">日视图</ElRadioButton>
              <ElRadioButton value="month">月视图</ElRadioButton>
              <ElRadioButton value="year">年视图</ElRadioButton>
            </ElRadioGroup>
          </div>
          <div id="main"></div>
        </ElCard>
      </ElCol>
      <ElCol :lg="10" :sm="24" class="mb-16px">
        <ElCard class="card-wrapper">
          <PieChart />
        </ElCard>
      </ElCol>
    </ElRow>
    <ElRow :gutter="gap">
      <ElCol :lg="14" :sm="24" class="mb-16px">
        <ProjectNews />
      </ElCol>
      <ElCol :lg="10" :sm="24" class="mb-16px">
        <CreativityBanner />
      </ElCol>
    </ElRow>
  </ElSpace>
</template>

<style scoped>
.chart-controls {
  margin-bottom: 20px;
  text-align: center;
}

.card-wrapper {
  height: 100%;
}
</style>

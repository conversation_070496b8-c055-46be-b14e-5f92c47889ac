package org.seven.share.common.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: ApplyWithdrawalDto
 * @Description:
 * @Author: Seven
 * @Date: 2024/10/16
 */
@Data
public class ApplyWithdrawalDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotBlank
    private long userId;

    @NotBlank
    private String password;

    @NotBlank
    private Double money;

    @NotBlank
    private String contact;
}

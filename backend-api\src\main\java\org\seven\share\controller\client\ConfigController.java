package org.seven.share.controller.client;

import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.R;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.entity.ChatGptSubTypeEntity;
import org.seven.share.common.pojo.entity.ChatGptSysNoticeEntity;
import org.seven.share.common.pojo.vo.ChatGptPayConfigVo;
import org.seven.share.service.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

import static org.seven.share.common.util.ServletUtils.getHost;

@RequestMapping("/client-api")
@RestController
@RequiredArgsConstructor
public class ConfigController {

    private final ChatGptSysNoticeService chatGptSysNoticeService;

    private final ChatGptSubTypeService chatGptSubTypeService;

    private final ChatGptConfigService chatGptConfigService;

    private final ChatGptPayConfigService chatGptPayConfigService;

    @GetMapping("/pay/list")
    public R list() {
        List<ChatGptPayConfigVo> voList = chatGptPayConfigService.listPayments();
        return R.ok(voList);
    }

    /**
     * 查询站点配置信息
     * @return
     */
    @GetMapping("/site/config")
    public R getSiteData() {
        Map<String, String> map = chatGptConfigService.getSiteData();
        return R.ok(map);
    }

    @GetMapping("/getLatestNotice")
    public R getLatestNotice() {
        String domain = getHost();
        ChatGptSysNoticeEntity notice = chatGptSysNoticeService.getDomainNotice(domain);
        return R.ok(notice);
    }

    @GetMapping("/subtype/list")
    public R getSubTypeList() {
        List<ChatGptSubTypeEntity> list = chatGptSubTypeService.listSubTypeByDomain();
        return R.ok(list);
    }

    @GetMapping("/site/script")
    public R getSiteScript() {
        return R.ok(chatGptConfigService.getSiteScript());
    }
}

package org.seven.share.config;

import lombok.extern.slf4j.Slf4j;
import org.seven.share.interceptor.AuthInterceptor;
import org.seven.share.security.filter.JwtInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.*;

import javax.annotation.Resource;
import java.io.File;

/**
 * @ClassName: WebConfig
 * @Description:
 * @Author: Seven
 * @Date: 2024/6/23
 */
@Configuration
@Slf4j
public class WebConfig implements WebMvcConfigurer  {

    @Resource
    private AuthInterceptor authInterceptor;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)  // 允许跨域请求携带认证信息
                .maxAge(3600); // 预检请求的有效期，单位秒
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authInterceptor)
                .addPathPatterns("/**")  // 拦截所有请求
                .excludePathPatterns(    // 排除不需要拦截的路径
                        "/",
                        "/index.html",
                        "/webjars/**",
                        "/doc.html",
                        "/swagger-resources/**",
                        "/v3/api-docs/**",
                        "/swagger-ui/**",
                        "/swagger-resources",
                        "/swagger-ui.html",
                        "/expander/**",
                        "/app/**",
                        "/expander-api/auth/login",
                        "/expander-api/route/getConstantRoutes",
                        "/expander-api/auth/code",
                        "/expander-api/auth/getUserInfo",
                        "/expander-api/route/getUserRoutes",
                        "/expander-api/sys/getForm",
                        "/expander-api/sys/saveForm",
                        "/expander-api/sys/licenseInfo",
                        "/client-api/license"
                );
        registry.addInterceptor(new JwtInterceptor()).addPathPatterns("/client-api/**");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 将"/doc.html"路径映射到"classpath:/META-INF/resources/"下的静态资源文件
        registry.addResourceHandler("doc.html")
                .addResourceLocations("classpath:/META-INF/resources/");

        // 将"/webjars/**"路径映射到"classpath:/META-INF/resources/webjars/"下的静态资源文件，通常用于前端依赖库（如jQuery、Bootstrap等）
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");

        // 获取项目根目录下的图片保存目录
        String projectRoot = System.getProperty("user.dir");
        String imagePath = projectRoot + File.separator + "data" + File.separator + "images";

        // 将图片目录映射到/expander-api/images/**路径下
        registry.addResourceHandler("/expander-api/images/**")
                .addResourceLocations("file:" + imagePath + File.separator);
        // 将图片目录映射到/api/images/**路径下
        registry.addResourceHandler("/api/images/**")
                .addResourceLocations("file:" + imagePath + File.separator);

        log.info("Static resource mapping configured: {} -> {}", "/api/images/**", imagePath);
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // 根路径重定向到client
        registry.addViewController("/").setViewName("forward:/app/index.html");
        // sassadmin路径重定向到sassadmin/index.html
        registry.addViewController("/expander").setViewName("forward:/expander/index.html");
        registry.addViewController("/expander/{path:[^\\.]*}").setViewName("forward:/expander/index.html");
        // app路径重定向到app/index.html
        registry.addViewController("/app").setViewName("forward:/app/index.html");
        registry.addViewController("/app/{path:[^\\.]*}").setViewName("forward:/app/index.html");
    }

}

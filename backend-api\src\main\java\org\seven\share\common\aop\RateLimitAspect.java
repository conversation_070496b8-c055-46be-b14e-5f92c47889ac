package org.seven.share.common.aop;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.seven.share.common.annotation.RateLimit;
import org.seven.share.common.exception.ServiceException;
import org.seven.share.common.util.IpUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
@Slf4j
public class RateLimitAspect {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private HttpServletRequest request;

    @Around("@annotation(rateLimit)")
    public Object limitRate(ProceedingJoinPoint pjp, RateLimit rateLimit) throws Throwable {
        String key = rateLimit.key();
        if (StrUtil.isEmpty(key)) {
            key = IpUtils.getClientIp(request);
        }
        String rateLimitKey = "rate_limit:" + key;

        // 获取当前访问次数
        Long count = stringRedisTemplate.opsForValue().increment(rateLimitKey);

        // 如果是第一次访问，设置过期时间
        if (count != null && count == 1) {
            stringRedisTemplate.expire(rateLimitKey, rateLimit.time(), TimeUnit.SECONDS);
        }

        // 如果超过限制，抛出异常
        if (count != null && count > rateLimit.value()) {
            log.warn("key: {} 访问频率超限", key);
            throw new ServiceException(429, "请求太频繁，请稍后重试");
        }

        return pjp.proceed();
    }
}

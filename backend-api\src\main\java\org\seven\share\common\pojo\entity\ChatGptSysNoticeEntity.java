package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: ChatGptSysNoticeEntity
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/1
 */
@Data
@TableName("chatgpt_sys_notice")
public class ChatGptSysNoticeEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @NotBlank(message = "公告内容不能为空")
    private String content;

    @TableField("publishTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime publishTime;

    @TableField(value = "createTime",  fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "updateTime",  fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    private String remark;

    @Schema(description = "租户编号")
    @TableField(value = "tenant_id", fill = FieldFill.INSERT_UPDATE)
    private String tenantId;

    @Schema(description = "公告类型：site:站内公告，system：系统通知，login:登录公告")
    private String type;

    @Schema(description = "租户名称")
    @TableField(value = "tenant_name", exist = false)
    private String tenantName;
}

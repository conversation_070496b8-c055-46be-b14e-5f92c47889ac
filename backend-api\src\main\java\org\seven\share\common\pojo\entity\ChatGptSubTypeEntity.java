package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.seven.share.common.pojo.dto.ModelLimitDto;

import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ChatGptSubType
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/11
 */
@Data
@TableName(value = "chatgpt_subtype", autoResultMap = true)
public class ChatGptSubTypeEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @NotBlank(message = "订阅名称不能为空")
    private String name;

    @NotBlank(message = "金额不能为空")
    private Double money;

    @NotBlank(message = "有效天数不能为空")
    @TableField("validDays")
    private Integer validDays;

    @TableField("isPlus")
    private Integer isPlus;

    @TableField("deleted_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deletedAt;

    @TableField(value = "createTime",  fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "updateTime",  fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    private Integer sort;

    private String remark;

    @TableField(exist = false)
    private List<String> features;

    @TableField("`limit`")
    private Long limit;

    private String per;

    /**
     * 0：启用，1：禁用
     */
    @TableField("isNotValued")
    private Integer isNotValued;

    /**
     * 套餐类型
     * 1：gpt
     * 2：claude
     * 3：gpt +claude
     */
    @TableField("subType")
    private String subType;

    @TableField("isPro")
    private Integer isPro;

    @TableField(value = "model_limits", typeHandler = JacksonTypeHandler.class)
    private Map<String, ModelLimitDto> modelLimits;

    /**
     * 0-否，1-是
     */
    private Integer exclusive;

    /**
     * 0-FREE，1-PLUS, 2-TEAM,3-PRO
     */
    @TableField("exclusiveType")
    private Integer exclusiveType;

    /**
     * 0-否，1-是
     */
    @TableField("isHotSale")
    private Integer isHotSale;

    @TableField("isSuper")
    private Integer isSuper;

    private Long drawQuota;

    @Schema(description = "租户编号")
    @TableField(value = "tenant_id", fill = FieldFill.INSERT_UPDATE)
    private String tenantId;

    @Schema(description = "租户名称")
    @TableField(value = "tenant_name", exist = false)
    private String tenantName;

}

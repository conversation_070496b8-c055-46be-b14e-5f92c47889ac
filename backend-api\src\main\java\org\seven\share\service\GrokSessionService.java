package org.seven.share.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.seven.share.common.pojo.dto.ChatGptSessionDto;
import org.seven.share.common.pojo.entity.ChatGptSessionEntity;
import org.seven.share.common.pojo.entity.GrokSessionEntity;
import org.seven.share.common.pojo.vo.CarInfoVo;
import org.seven.share.common.pojo.vo.ChatGptSessionVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @InterfaceName: GrokSessionService
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/14
 */
public interface GrokSessionService extends IService<GrokSessionEntity> {

    Page<ChatGptSessionDto> getPage(Integer current, Integer size, String query, String sortProp, String sortOrder);

    void updateAccessToken(GrokSessionEntity grokSession);

    void removeGrokSessionBatch(List<String> ids);

    void insertAccessToken(GrokSessionEntity grokSession);
}

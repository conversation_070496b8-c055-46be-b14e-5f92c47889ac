package org.seven.share.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.seven.share.common.pojo.dto.ChatGptSessionDto;
import org.seven.share.common.pojo.entity.ChatGptSessionEntity;
import org.seven.share.common.pojo.vo.CarInfoVo;
import org.seven.share.common.pojo.vo.ChatGptSessionVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @InterfaceName: ChatGptSessionService
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/14
 */
public interface ChatGptSessionService extends IService<ChatGptSessionEntity> {
    void saveAccessTokenBatch(String accounts, int optType);

    void updateAccessToken(ChatGptSessionEntity chatGptSession);

    void removeCarInfoBatch(List<String> ids);

    void insertAccessToken(ChatGptSessionEntity chatGptSession);

    List<ChatGptSessionVo> listNoBindCarInfo();

    void exportGptSession(HttpServletResponse response);

    Map<String, String> getIdleCar(String username);

    Map<String, String> getIdleCarByUserExpire(String userId);

    List<CarInfoVo> fetchAllCarList();

    String getOaiLoginToken(String carId, String userToken) throws JsonProcessingException;

    void updateGptSessionStatus(Long id, Integer status);

    void updateGptStatus(String carId);

    Page<ChatGptSessionDto> getPage(Integer page, Integer size, String query, String sortProp, String sortOrder);

    void unbindSession(Long id);
}

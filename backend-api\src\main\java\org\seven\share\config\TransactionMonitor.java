package org.seven.share.config;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * @ClassName: TransactionMonitor
 * @Description: 事务监控切面
 * @Author: Seven
 * @Date: 2025/1/19
 */
@Slf4j
@Aspect
@Component
public class TransactionMonitor {
    @Around("@annotation(org.springframework.transaction.annotation.Transactional)")
    public Object monitorTransaction(ProceedingJoinPoint pjp) throws Throwable {
        long start = System.currentTimeMillis();
        try {
            return pjp.proceed();
        } finally {
            long duration = System.currentTimeMillis() - start;
            if (duration > 5000) { // 5秒
                // 发送告警
                log.error("事务执行时间过长: " + pjp.getSignature() + "总耗时：" + duration / 1000 + "秒");
            }
        }
    }
}

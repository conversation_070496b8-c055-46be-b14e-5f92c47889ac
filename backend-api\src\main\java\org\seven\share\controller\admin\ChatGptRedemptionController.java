package org.seven.share.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.R;
import org.seven.share.common.api.Result;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.dto.ChatGptRedemptionDto;
import org.seven.share.common.pojo.entity.ChatGptRedemptionEntity;
import org.seven.share.service.ChatGptRedemptionService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @ClassName: ChatGptRedemptionController
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/28
 */
@RestController
@RequestMapping("/expander-api/codes")
public class ChatGptRedemptionController {

    @Resource
    private ChatGptRedemptionService chatGptRedemptionService;

    @GetMapping("/page")
    @SysLogInterface(title = "分页查询激活码", businessType = BusinessType.QUERY)
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer current,
                  @RequestParam(value = "size", defaultValue = "10") Integer size,
                  String key,
                  String userToken,
                  Integer status){
        // 创建分页对象
        Page<ChatGptRedemptionDto> pageParams = new Page<>(current, size);
        // 执行分页查询
        IPage<ChatGptRedemptionDto> pageInfo = chatGptRedemptionService.selectPage(pageParams, key, userToken, status);
        // 返回分页查询结果
        return R.ok(pageInfo);
    }

    @GetMapping("/generate")
    @SysLogInterface(title = "生成激活码", businessType = BusinessType.OTHER)
    public R generate(@RequestParam Long subTypeId, @RequestParam Integer count){
        List<String> keys = chatGptRedemptionService.generateCodes(subTypeId, count);
        return R.ok(keys);
    }

    @DeleteMapping("/delete")
    @SysLogInterface(title = "批量删除激活码", businessType = BusinessType.DELETE)
    public R delete(@RequestBody List<String> ids){
        chatGptRedemptionService.removeBatchByIds(ids);
        return R.ok();
    }


    @GetMapping("/export-key")
    @SysLogInterface(title = "导出激活码记录", businessType = BusinessType.EXPORT)
    public void exportGptSession(HttpServletResponse response, @RequestParam Long subTypeId) {
        chatGptRedemptionService.exportCardCodes(response, subTypeId);
    }

    @PostMapping("/recycle")
    @SysLogInterface(title = "回收激活码", businessType = BusinessType.OTHER)
    public Result<?> recycleKey(@RequestBody ChatGptRedemptionEntity entity) {
        chatGptRedemptionService.recycleKey(entity);
        return Result.success();
    }
}

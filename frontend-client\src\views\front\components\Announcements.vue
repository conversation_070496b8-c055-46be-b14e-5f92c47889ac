
<template>
  <div class="instructions-container">
    <h2 class="text-2xl md:text-3xl font-bold mb-6">{{ $t('announcement.title') }}</h2>
        <p class="mb-1">{{ $t('announcement.description') }}</p>
    <div class="rounded overflow-hidden shadow-lg iframe-wrapper">
      <iframe
        :srcdoc="siteAnnouncement" 
        frameborder="0"
        class="w-full h-full"
        :title="$t('announcement.iframeTitle')"
      ></iframe>
    </div>
  </div>
</template>
<script setup>
import { computed } from 'vue';
import { useSiteStore } from '@/store/modules/site';

const siteStore = useSiteStore();
const siteAnnouncement = computed(() => siteStore.siteAnnouncement);

</script>

<style scoped>
.instructions-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .iframe-wrapper {
    flex: 1;
    min-height: 0;
  }
  
  
  @media (max-width: 768px) {
    .instructions-container {
      height: 100%;
    }
    
  }
</style>
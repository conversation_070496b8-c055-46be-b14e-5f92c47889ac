<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.seven.share.mapper.ChatGptUserMapper">

    <!-- 结果映射 -->
    <resultMap id="userResultMap" type="org.seven.share.common.pojo.entity.ChatGptUserEntity">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userToken" column="userToken" jdbcType="VARCHAR"/>
        <result property="expireTime" column="expireTime" jdbcType="TIMESTAMP"/>
        <result property="plusExpireTime" column="plusExpireTime" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
        <result property="deletedAt" column="deleted_at" jdbcType="TIMESTAMP"/>
        <result property="isPlus" column="isPlus" jdbcType="INTEGER"/>
        <result property="isAdmin" column="isAdmin" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="subTypeId" column="subTypeId" jdbcType="INTEGER"/>
        <result property="affCode" column="affCode" jdbcType="VARCHAR"/>
        <result property="inviterId" column="inviterId" jdbcType="BIGINT"/>
        <result property="limit" column="`limit`" jdbcType="BIGINT"/>
        <result property="per" column="per" jdbcType="VARCHAR"/>
        <result property="carids" column="carids" jdbcType="VARCHAR"/>
        <result property="userType" column="userType" jdbcType="INTEGER"/>
        <result property="dailyConversationCount" column="dailyConversationCount" jdbcType="INTEGER"/>
        <result property="dailyClaudeConversationCount" column="dailyClaudeConversationCount" jdbcType="INTEGER"/>
        <result property="lastActiveTime" column="lastActiveTime" jdbcType="TIMESTAMP"/>
        <result property="affQuota" column="affQuota" jdbcType="VARCHAR"/>
        <result property="affHistoryQuota" column="affHistoryQuota" jdbcType="VARCHAR"/>
        <result property="affTotalQuota" column="affTotalQuota" jdbcType="VARCHAR"/>
        <result property="affCount" column="affCount" jdbcType="INTEGER"/>
        <result property="receiptFile" column="receiptFile" jdbcType="VARCHAR"/>
        <result property="affRate" column="affRate" jdbcType="DOUBLE"/>
        <result property="claudeExpireTime" column="claudeExpireTime" jdbcType="TIMESTAMP"/>
        <result property="claudeProExpireTime" column="claudeProExpireTime" jdbcType="TIMESTAMP"/>
        <result property="claudeLimit" column="claudeLimit" jdbcType="BIGINT"/>
        <result property="claudePer" column="claudePer" jdbcType="VARCHAR"/>
        <result property="isPro" column="isPro" jdbcType="INTEGER"/>
        <result property="clientIp" column="clientIp" jdbcType="VARCHAR"/>
        <result property="deviceId" column="deviceId" jdbcType="VARCHAR"/>
        <result property="loginType" column="loginType" jdbcType="INTEGER"/>
        <result property="grokExpireTime" column="grokExpireTime" jdbcType="TIMESTAMP"/>
        <result property="grokSuperExpireTime" column="grokSuperExpireTime" jdbcType="TIMESTAMP"/>
        <result property="modelLimits" column="model_limits" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="loginToken" column="loginToken" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="tenantName" column="tenant_name" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 直接使用 CREATE TABLE IF NOT EXISTS -->
    <select id="getTableColumns" resultType="string">
       SELECT
           COLUMN_NAME
       FROM
           information_schema.columns
       WHERE
           table_name = #{tableName}
       AND
           TABLE_SCHEMA = #{schema}
    </select>

    <update id="executeSql">
        ${sql}
    </update>
    <resultMap id="ChatGptUserResultMap" type="org.seven.share.common.pojo.dto.UserDto">
        <result property="modelLimits" column="model_limits" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>

    <select id="selectUserWithInviterPage" resultMap="ChatGptUserResultMap">
        select u1.*, u2.userToken as invitor, s.exclusive_expire_time as exclusiveExpireTime,
            case u1.tenant_id when '000000' then '主站'
            else t.tenant_name
            end as tenant_name
        from chatgpt_user u1
            left join chatgpt_user u2 on u1.inviterId = u2.id and u2.deleted_at is null
            left join (
            SELECT user_id, MAX(exclusive_expire_time) AS exclusive_expire_time
             FROM chatgpt_session
             WHERE user_id IS NOT NULL and  exclusive_expire_time > now()
            GROUP BY user_id) s on u1.id = s.user_id
            left join t_sys_tenant t
            on u1.tenant_id = t.id
        <where>
            u1.deleted_at is null
            <if test="query != null and query != ''">
                and (
                u1.userToken LIKE CONCAT('%', #{query}, '%')
                or u1.email LIKE CONCAT('%', #{query}, '%')
                or u1.remark LIKE CONCAT('%', #{query}, '%')
                or u1.loginToken like CONCAT('%', #{query}, '%')
                )
            </if>
        </where>
        <if test="sortProp != null and sortProp !=''">
            order by ${sortProp} ${sortOrder}
        </if>
        <if test="sortProp == null or sortProp == ''">
            order by u1.createTime desc
        </if>
    </select>

    <update id="resetDailyConversations">
        update chatgpt_user set dailyConversationCount = 0, dailyClaudeConversationCount = 0 where deleted_at is null;
    </update>

    <select id="listPaymentHistory">
        select
            id, updateTime, money, tradeNo, status
        from chatgpt_epaylogs
        where userToken = #{userToken}
        order by updateTime desc limit 10;
    </select>

    <update id="updateIsDeletedByIds">
        update chatgpt_user set deleted_at = now() where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </update>
    <!-- 计算当日新增用户、总用户数、一小时前的在线人数 -->
    <select id="calculateUserStats" resultType="java.util.Map">
        select
            count(id) as newUsers,
            (select count(id) from chatgpt_user where deleted_at is null) as totalUsers,
            (select count(id) from chatgpt_user where lastActiveTime > #{oneHourAgo}) as onlineUsers
        from chatgpt_user
        where createTime > current_date();
    </select>

    <update id="updatePlusExpireTime">
        UPDATE chatgpt_user AS u1
           JOIN (SELECT id, expireTime FROM chatgpt_user
                                      WHERE deleted_at IS NULL AND isPlus = 1 AND expireTime IS NOT NULL
           AND expireTime > '1970-01-01 00:00:00' ) AS u2 ON u1.id = u2.id
           SET u1.plusExpireTime = u2.expireTime
           WHERE u1.deleted_at IS NULL
    </update>

    <update id="updateBatchUserExpireTime">
        UPDATE chatgpt_user AS u1
            JOIN (SELECT id, plusExpireTime FROM chatgpt_user
                  WHERE deleted_at IS NULL AND plusExpireTime IS NOT NULL
                    AND plusExpireTime > now() ) AS u2 ON u1.id = u2.id
        SET u1.claudeExpireTime = u2.plusExpireTime,
            u1.grokExpireTime = u2.plusExpireTime
        WHERE u1.deleted_at IS NULL and (u1.claudeExpireTime is null or u1.claudeExpireTime &lt; now() or u1.grokExpireTime is null)
    </update>

    <update id="updateUserType">
        update chatgpt_user
        set userType = 3
        where plusExpireTime > now()
           or claudeExpireTime > now()
           or claudeProExpireTime > now();
    </update>
    <select id="getInviteDetails" resultType="org.seven.share.common.pojo.vo.InviteDetailsVo">
        select
            u.userToken,
            r.affMoney,
            r.orderType,
            r.status,
            r.createTime
        from chatgpt_aff_record r
        left JOIN chatgpt_user u on r.inviteeId = u.id
        where r.inviterId = #{id}
    </select>

    <update id="updateByCondition">
        UPDATE chatgpt_user
        SET userType = 1,
        model_limits = #{keyValueMap}
        WHERE (expireTime &lt; NOW())
        AND (plusExpireTime IS NULL OR plusExpireTime &lt; NOW())
        AND (claudeExpireTime IS NULL OR claudeExpireTime &lt; NOW())
        AND (claudeProExpireTime IS NULL OR claudeProExpireTime &lt; NOW())
        AND (grokExpireTime IS NULL OR grokExpireTime &lt; NOW())
        AND (grokSuperExpireTime IS NULL OR grokSuperExpireTime &lt; NOW())
        AND userType != 1;
    </update>

    <select id="getUserByUserToken" resultMap="userResultMap">
        select * from chatgpt_user
                 where deleted_at is null
                   and (userToken = #{userToken} or email = #{userToken} or loginToken = #{userToken})
    </select>

    <select id="getByIdWithoutTenant" resultMap="userResultMap">
        select * from chatgpt_user
        where deleted_at is null
          and id = #{userId}
    </select>

    <select id="getUserByLoginToken" resultMap="userResultMap">
        select * from chatgpt_user where loginToken = #{loginToken} and deleted_at is null
    </select>

    <select id="selectOneByNameAndLoginType" resultMap="userResultMap">
        select * from chatgpt_user
        where deleted_at is null
          and (userToken = #{username} or email = #{username}) and loginType = #{type};
    </select>

    <!-- 忽略多租户的更新方法 -->
    <update id="updateByIdWithoutTenant" parameterType="org.seven.share.common.pojo.entity.ChatGptUserEntity">
        UPDATE chatgpt_user
        <set>
            <if test="entity.userToken != null">userToken = #{entity.userToken},</if>
            <if test="entity.expireTime != null">expireTime = #{entity.expireTime},</if>
            <if test="entity.plusExpireTime != null">plusExpireTime = #{entity.plusExpireTime},</if>
            <if test="entity.createTime != null">createTime = #{entity.createTime},</if>
            <if test="entity.updateTime != null">updateTime = #{entity.updateTime},</if>
            <if test="entity.deletedAt != null">deleted_at = #{entity.deletedAt},</if>
            <if test="entity.isPlus != null">isPlus = #{entity.isPlus},</if>
            <if test="entity.isAdmin != null">isAdmin = #{entity.isAdmin},</if>
            <if test="entity.remark != null">remark = #{entity.remark},</if>
            <if test="entity.email != null">email = #{entity.email},</if>
            <if test="entity.password != null and entity.password != ''">password = #{entity.password},</if>
            <if test="entity.status != null">status = #{entity.status},</if>
            <if test="entity.subTypeId != null">subTypeId = #{entity.subTypeId},</if>
            <if test="entity.affCode != null">affCode = #{entity.affCode},</if>
            <if test="entity.inviterId != null">inviterId = #{entity.inviterId},</if>
            <if test="entity.limit != null">`limit` = #{entity.limit},</if>
            <if test="entity.per != null">per = #{entity.per},</if>
            <if test="entity.carids != null">carids = #{entity.carids},</if>
            <if test="entity.userType != null">userType = #{entity.userType},</if>
            <if test="entity.dailyConversationCount != null">dailyConversationCount = #{entity.dailyConversationCount},</if>
            <if test="entity.dailyClaudeConversationCount != null">dailyClaudeConversationCount = #{entity.dailyClaudeConversationCount},</if>
            <if test="entity.lastActiveTime != null">lastActiveTime = #{entity.lastActiveTime},</if>
            <if test="entity.affQuota != null">affQuota = #{entity.affQuota},</if>
            <if test="entity.affHistoryQuota != null">affHistoryQuota = #{entity.affHistoryQuota},</if>
            <if test="entity.affTotalQuota != null">affTotalQuota = #{entity.affTotalQuota},</if>
            <if test="entity.affCount != null">affCount = #{entity.affCount},</if>
            <if test="entity.receiptFile != null">receiptFile = #{entity.receiptFile},</if>
            <if test="entity.affRate != null">affRate = #{entity.affRate},</if>
            <if test="entity.claudeExpireTime != null">claudeExpireTime = #{entity.claudeExpireTime},</if>
            <if test="entity.claudeProExpireTime != null">claudeProExpireTime = #{entity.claudeProExpireTime},</if>
            <if test="entity.claudeLimit != null">claudeLimit = #{entity.claudeLimit},</if>
            <if test="entity.claudePer != null">claudePer = #{entity.claudePer},</if>
            <if test="entity.isPro != null">isPro = #{entity.isPro},</if>
            <if test="entity.clientIp != null">clientIp = #{entity.clientIp},</if>
            <if test="entity.deviceId != null">deviceId = #{entity.deviceId},</if>
            <if test="entity.loginType != null">loginType = #{entity.loginType},</if>
            <if test="entity.grokExpireTime != null">grokExpireTime = #{entity.grokExpireTime},</if>
            <if test="entity.grokSuperExpireTime != null">grokSuperExpireTime = #{entity.grokSuperExpireTime},</if>
            <if test="entity.modelLimits != null">model_limits = #{entity.modelLimits, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},</if>
            <if test="entity.loginToken != null">loginToken = #{entity.loginToken},</if>
        </set>
        WHERE id = #{entity.id}
    </update>

    <select id="getLoginTokenById">
        select loginToken from chatgpt_user where id = #{uid}
    </select>
</mapper>

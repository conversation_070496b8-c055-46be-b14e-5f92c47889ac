declare namespace Api {
  namespace Auth {
    interface LoginToken {
      token: string;
      refreshToken: string;
    }

    interface UserInfo {
      userId: string;
      userName: string;
      roles: string[];
      buttons: string[];
      permissions: string[];
    }
  }

  namespace TenantManage {
    interface Tenant {
      id: string;
      tenantId: string;
      tenantName: string;
      contactUserName: string;
      contactPhone: string;
      commissionRatio: number;
      commissionBalance: number;
      remark: string;
      domain: string;
      expireTime: string;
      status: string;
      createTime: string;
      updateTime: string;
      createBy: string;
      updateBy: string;
      siteName: string;
      siteLogo: string;
    }
  }
}

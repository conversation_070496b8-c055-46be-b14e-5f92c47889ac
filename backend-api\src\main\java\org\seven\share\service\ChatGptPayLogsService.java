package org.seven.share.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.pojo.entity.ChatGptEPayLogsEntity;

import java.util.List;
import java.util.Map;

/**
 * @InterfaceName: ChatGptPayLogsService
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/5
 */
public interface ChatGptPayLogsService extends IService<ChatGptEPayLogsEntity> {
    boolean changeStatus(String id);

    List<Map<String, Object>> getLast15DaysIncome();

    List<Map<String, Object>> getLast12MonthsIncome();

    List<Map<String, Object>> getYearlyIncome();
}

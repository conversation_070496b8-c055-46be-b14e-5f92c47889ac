import { ElNotification } from 'element-plus';
import FingerprintJS from '@fingerprintjs/fingerprintjs'; // 引入 FingerprintJS

export const goToChat = async (carId, nodeType, username, router) => {
    console.log("登录信息：", carId, nodeType, username)
    if (!carId || !nodeType || !username) { 
        console.log("存在为空的参数，返回首页")
        router.push('/home');
        return;
    }
    const loginData = {
        usertoken: username,
        nodeType: nodeType,
        carid: carId,
        planType: '',
    };

    const response = await fetch(`/auth/login?carid=${carId}`, {
        method: 'post',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginData),
    });

    if (response.redirected) {
        window.location.href = '/';
    } else {
        ElNotification.error('跳转失败，回到选车页面');
        router.push('/home');
        return;
    }
};

// 生成设备唯一 ID（基于 FingerprintJS）
export const getDeviceId = async () => {
    let deviceId = localStorage.getItem('device_id');
    if (!deviceId) {
      const fp = await FingerprintJS.load(); // 加载 FingerprintJS
      const result = await fp.get(); // 获取指纹
      deviceId = result.visitorId; // 使用 visitorId 作为唯一标识符
      localStorage.setItem('device_id', deviceId);
    }
    return deviceId;
  };

package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: CluadeSessionEntity
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/12
 */
@Data
@TableName("claude_session")
public class ClaudeSessionEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @TableField(value = "createTime", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "updateTime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField("deleted_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deletedAt;

    @TableField("email")
    private String email;

    /**
     * 0 可用，1不可用
     */
    private Integer status;

    @TableField("isPro")
    private Integer isPro;

    @TableField("carID")
    private String carID;

    @TableField("officialSession")
    private String officialSession;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remark;

    /**
     * 剩余次数
     */
    @TableField(value = "remaining", updateStrategy = FieldStrategy.ALWAYS)
    private String remaining;

    /**
     * 重置时间
     */
    @TableField(value = "resetsAt", updateStrategy = FieldStrategy.ALWAYS)
    private String resetsAt;

    private String password;

    private int count;

    private int sort;
}

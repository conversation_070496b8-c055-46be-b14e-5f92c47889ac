package org.seven.share.config;

import com.fasterxml.jackson.databind.module.SimpleModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JacksonConfig {

    @Bean
    public SimpleModule smartLongModule() {
        SimpleModule module = new SimpleModule();
        // 处理包装类型Long和基本类型long
        module.addSerializer(Long.class, new SmartLongSerializer());
        module.addSerializer(long.class, new SmartLongSerializer());
        return module;
    }
}

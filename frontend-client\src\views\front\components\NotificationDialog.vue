<template>
    <el-dialog 
    :title="$t('notification.title')"
      v-model="dialogVisible" 
      :width="dialogWidth" 
      :before-close="handleClose"
      align-center
    >
      <div class="overflow-y-auto" style="max-height: calc(80vh);">
        <div class="notice-content">
          <div v-html="fetchedNoticeContent"></div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <button
            class="confirm-btn"
            @click="handleClose"
          >{{ $t('notification.read') }}</button>
        </span>
      </template>
    </el-dialog>
  </template>
    
  <script setup>
  import { ref, watch, computed, onMounted, onUnmounted } from 'vue'
  import { getLatestNotice } from '@/api/notice.js'
  
  const fetchedNoticeContent = ref('')
  const props = defineProps({
    visible: Boolean
  })
  
  const emit = defineEmits(['update:visible'])
  
  const dialogVisible = ref(props.visible)
  const windowWidth = ref(window.innerWidth)
  
  const dialogWidth = computed(() => {
    return windowWidth.value < 768 ? '90%' : '50%'
  })
  
  const handleClose = () => {
    emit('update:visible', false)
    dialogVisible.value = false
    closeNotice()
  }
  
  const getNoticeData = async () => {
    const data = await getLatestNotice()
    fetchedNoticeContent.value = data?.content
  }
  
  const handleResize = () => {
    windowWidth.value = window.innerWidth
  }
  
  onMounted(async() => {
    window.addEventListener('resize', handleResize)
    await getNoticeData()
    checkNotice()
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })
  
  watch(() => props.visible, (newValue) => {
    dialogVisible.value = newValue
  })
  
  const checkNotice = () => {
    const storedNoticeContent = localStorage.getItem('noticeContent')
    const currentNoticeContent = fetchedNoticeContent.value

    if (!currentNoticeContent || currentNoticeContent.trim() === '') {
      return
    }

    if (storedNoticeContent === null) {
      dialogVisible.value = true
      return
    }

    if (storedNoticeContent !== currentNoticeContent) {
      dialogVisible.value = true
      return
    }
  }
  
  const closeNotice = () => {
    localStorage.setItem('noticeContent', fetchedNoticeContent.value)
  }
  </script>
  
  <style scoped>

  .notice-content {
    line-height: 1.6;
  }
  
  .confirm-btn {
    background-color: #000000;
    border: none;
    color: white;
    padding: 8px 24px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .confirm-btn:hover {
    background-color: #333333;
    transform: translateY(-1px);
  }

  /* 暗黑模式下的按钮样式 */
  :deep(.dark) .confirm-btn {
    background-color: #ffffff;
    color: #000000;
  }

  :deep(.dark) .confirm-btn:hover {
    background-color: #e5e5e5;
  }
  
  </style>
package org.seven.share.config;

import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.security.cert.X509Certificate;

/**
 * @ClassName: RestTemplateConfig
 * @Description: http请求配置
 * @Author: Seven
 * @Date: 2024/8/4
 */
@Configuration
public class RestTemplateConfig {
    /**
     * 配置并返回一个不验证SSL证书的RestTemplate实例
     * 这个方法通过创建一个信任所有SSL证书的SSLContext，并使用这个SSLContext创建一个HttpClient，
     * 然后基于这个HttpClient创建一个HttpComponentsClientHttpRequestFactory，
     * 最后返回一个使用这个Factory的RestTemplate实例
     *
     * @return 配置好的RestTemplate实例，用于HTTP请求，不验证SSL证书
     * @throws Exception 如果在创建SSLContext或HttpClient过程中发生错误，则会抛出异常
     */
    @Bean
    public RestTemplate restTemplate() throws Exception {
        // 定义一个信任策略，这里配置为信任所有证书
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

        // 使用上述信任策略创建一个SSLContext
        SSLContext sslContext = SSLContextBuilder.create()
                .setProtocol("TLS")
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();

        // 使用SSLContext创建一个HttpClient，该HttpClient将信任所有SSL证书
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLContext(sslContext)
//                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE) // 设置hostname验证器为不验证 生产环境需要关闭
                .build();

        // 使用HttpClient创建一个HttpComponentsClientHttpRequestFactory
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
        factory.setConnectTimeout(300000);    // 连接超时，单位为毫秒
        factory.setReadTimeout(300000);     // 300秒读取超时，单位为毫秒
        // 返回一个使用上述Factory的RestTemplate实例
        return new RestTemplate(factory);
    }
}

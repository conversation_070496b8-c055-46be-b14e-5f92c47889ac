package org.seven.share.common.util;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * @ClassName: AESUtil
 * @Description:
 * @Author: Seven
 * @Date: 2025/5/12
 */
public class AESUtil {
    private static final String ALGORITHM = "AES";

    // 生成 16 字节密钥（AES-128），不足补 0
    private static SecretKeySpec getKey(String myKey) {
        byte[] keyBytes = new byte[16];
        byte[] paramBytes = myKey.getBytes();
        System.arraycopy(paramBytes, 0, keyBytes, 0, Math.min(paramBytes.length, keyBytes.length));
        return new SecretKeySpec(keyBytes, ALGORITHM);
    }

    /**
     * 加密hash
     * @param strToEncrypt 原数据
     * @param secret 加密秘钥
     * @return
     * @throws Exception
     */
    public static String encrypt(String strToEncrypt, String secret) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, getKey(secret));
        byte[] encrypted = cipher.doFinal(strToEncrypt.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);
    }

    /**
     * 解密hash
     * @param strToDecrypt 待解密数据
     * @param secret 秘钥
     * @return 解密后的数据
     * @throws Exception
     */
    public static String decrypt(String strToDecrypt, String secret) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, getKey(secret));
        byte[] decoded = Base64.getDecoder().decode(strToDecrypt);
        byte[] decrypted = cipher.doFinal(decoded);
        return new String(decrypted, StandardCharsets.UTF_8);
    }
}

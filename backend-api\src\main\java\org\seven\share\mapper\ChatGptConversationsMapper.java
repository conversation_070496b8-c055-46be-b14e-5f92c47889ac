package org.seven.share.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;
import org.seven.share.common.pojo.entity.ChatGptConversationEntity;

import java.time.LocalDateTime;

/**
 * @InterfaceName: ChatGptConversationsDao
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/11
 */
@Mapper
public interface ChatGptConversationsMapper extends BaseMapper<ChatGptConversationEntity> {

    @Update("update chatgpt_conversations set is_archived = 1, deleted_at = now() where createTime <= #{cutoffDate}")
    int achiveData(LocalDateTime cutoffDate);
}

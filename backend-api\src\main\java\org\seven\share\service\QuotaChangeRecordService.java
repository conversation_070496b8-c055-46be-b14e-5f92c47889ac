package org.seven.share.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.pojo.entity.QuotaChangeRecordEntity;
import org.seven.share.mapper.QuotaChangeRecordMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: QuotaChangeRecordService
 * @Description:
 * @Author: Seven
 * @Date: 2025/4/26
 */
@Service
public class QuotaChangeRecordService {

    @Resource
    private QuotaChangeRecordMapper quotaChangeRecordMapper;

    /**
     * 套餐购买、用户画图
     */
    public void addQuotaChangeRecord(Long uid,
                                     int changeType,
                                     Long changeAmount,
                                     String remark) {
        QuotaChangeRecordEntity changeRecordEntity = new QuotaChangeRecordEntity();
        changeRecordEntity.setChangeType(changeType);
        changeRecordEntity.setUserId(uid);
        changeRecordEntity.setChangeAmount(changeAmount);
        changeRecordEntity.setRemark(remark);
        quotaChangeRecordMapper.insert(changeRecordEntity);
    }

    public Page<QuotaChangeRecordEntity> pageQuotaChangeByUId(int page, int size, String userId) {
        if (StrUtil.isEmpty(userId)) {
            return null;
        }
           LambdaQueryWrapper<QuotaChangeRecordEntity> queryWrapper = new LambdaQueryWrapper<QuotaChangeRecordEntity>()
                    .eq(StrUtil.isNotEmpty(userId), QuotaChangeRecordEntity::getUserId, userId)
                   .orderByDesc(QuotaChangeRecordEntity::getCreatedAt);
        return quotaChangeRecordMapper.selectPage(new Page<>(page, size), queryWrapper);
    }

    public void removeQuotaChangeByIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }

        quotaChangeRecordMapper.deleteByIds(ids);
    }

    public IPage<QuotaChangeRecordEntity> pageQuotaChange(int page, int size, String username) {
        LambdaQueryWrapper<QuotaChangeRecordEntity> queryWrapper = new LambdaQueryWrapper<QuotaChangeRecordEntity>()
                .and(StrUtil.isNotEmpty(username), w -> w.apply("u.userToken LIKE {0}", "%" + username + "%"))
                .orderByDesc(QuotaChangeRecordEntity::getCreatedAt);
        return quotaChangeRecordMapper.selectChangeRecordPage(new Page<>(page, size), queryWrapper);
    }
}

package org.seven.share.controller.admin;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.R;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.entity.ChatGptCouponEntity;
import org.seven.share.service.ChatGptCouponService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName: ChatGptCouponController
 * @Description: 优惠卷
 * @Author: Seven
 * @Date: 2024/8/13
 */

@RestController
@RequestMapping("/expander-api/coupon")
public class ChatGptCouponController {
    @Resource
    private ChatGptCouponService chatGptCouponService;

    @GetMapping("/page")
    @SysLogInterface(title = "分页查询优惠券记录", businessType = BusinessType.QUERY)
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer current,
                  @RequestParam(value = "size", defaultValue = "10") Integer size,
                  String coupon){
        LambdaQueryWrapper<ChatGptCouponEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(ChatGptCouponEntity::getUpdateTime);
        queryWrapper.like(StrUtil.isNotBlank(coupon), ChatGptCouponEntity::getCoupon, coupon);
        Page<ChatGptCouponEntity> pageInfo = chatGptCouponService.page(new Page<>(current,size), queryWrapper);
        List<ChatGptCouponEntity> records = pageInfo.getRecords();
        List<ChatGptCouponEntity> collect = records.stream()
                .peek(e -> {
                    if (StrUtil.isNotEmpty(e.getSubTypeIds())) {
                        e.setIds(Arrays.asList(e.getSubTypeIds().split(",")));
                    }
                }).collect(Collectors.toList());
        return R.ok(pageInfo.setRecords(collect));
    }

    @PostMapping("/create")
    @SysLogInterface(title = "创建优惠券记录", businessType = BusinessType.INSERT)
    public R save(@RequestBody @Validated ChatGptCouponEntity coupon){
        chatGptCouponService.saveCoupon(coupon);
        return R.ok();
    }

    @PutMapping("/update")
    @SysLogInterface(title = "修改优惠券", businessType = BusinessType.UPDATE)
    public R update(@RequestBody @Validated ChatGptCouponEntity coupon){
        List<String> ids = coupon.getIds();
        if (!CollectionUtils.isEmpty(ids)) {
            String collect = String.join(",", ids);
            coupon.setSubTypeIds(collect);
        }
        chatGptCouponService.updateById(coupon);
        return R.ok();
    }


    @DeleteMapping("/delete")
    @SysLogInterface(title = "删除优惠券记录", businessType = BusinessType.DELETE)
    public R del(@RequestBody List<String> ids){
        return chatGptCouponService.removeBatchByIds(ids) ? R.ok() : R.error();
    }


    @GetMapping("/generate")
    @SysLogInterface(title = "生成优惠券", businessType = BusinessType.OTHER)
    public R generateCoupon(){
        String couponCode = chatGptCouponService.generateCouponCode();
        return R.ok(Map.of("couponCode", couponCode));
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.seven.share.mapper.SysResourceMapper">

    <!-- resultMap映射 -->
    <resultMap id="BaseResultMap" type="org.seven.share.common.pojo.entity.SysResource">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="parent_id" property="parentId" jdbcType="INTEGER"/>
        <result column="ui_path" property="uiPath" jdbcType="VARCHAR"/>
        <result column="menu_type" property="menuType" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="menu_name" property="menuName" jdbcType="VARCHAR"/>
        <result column="route_name" property="routeName" jdbcType="VARCHAR"/>
        <result column="route_path" property="routePath" jdbcType="VARCHAR"/>
        <result column="component" property="component" jdbcType="VARCHAR"/>
        <result column="meta" property="meta" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="INTEGER"/>
        <result column="create_id" property="createId" jdbcType="BIGINT"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_id" property="updateId" jdbcType="BIGINT"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
        <result column="delete_time" property="deleteTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- SQL片段，列出表的所有基础字段 -->
    <sql id="baseColumns">
        t.id,
        t.parent_id,
        t.ui_path,
        t.menu_type,
        t.status,
        t.menu_name,
        t.route_name,
        t.route_path,
        t.component,
        t.meta,
        t.weight,
        t.create_id,
        t.create_by,
        t.update_id,
        t.update_by,
        t.create_time,
        t.update_time,
        t.is_deleted,
        t.delete_time
    </sql>

    <select id="getUserRoutes" resultType="org.seven.share.common.pojo.entity.SysResource"
            parameterType="java.lang.String">
        SELECT
            m.id,
            m.parent_id,
            m.menu_type,
            m.menu_name,
            m.route_name,
            m.route_path,
            m.component,
            m.meta,
            m.weight,
            m.status
        FROM
            t_sys_resource m
        WHERE
            m.status = 1 AND m.is_deleted = 0 AND m.menu_type not in ('3','4')
          AND m.id IN (
            SELECT DISTINCT
                ( rmb.resource_id )
            FROM
                t_sys_user_role p
                    JOIN t_sys_role r ON p.role_Id = r.id
                    AND p.user_id = #{id} AND p.is_deleted = 0
                    AND r.STATUS = 1 AND r.is_deleted = 0
                    JOIN t_sys_role_resource rmb ON rmb.role_id = r.id AND rmb.is_deleted = 0 UNION
            SELECT
                r.id
            FROM
                t_sys_resource r
                    JOIN t_sys_role_resource rr ON r.id = rr.resource_id
                    JOIN t_sys_role ro ON rr.role_id = ro.id
                    AND ro.status = 1 AND ro.role_code LIKE 'COMMON%' AND ro.is_deleted = 0 AND rr.is_deleted = 0
                    AND r.menu_type not in ('3','4')
        )
        ORDER BY
            m.parent_id ASC,
            m.weight ASC
    </select>

    <select id="getUserPermissions" resultType="java.lang.String"
            parameterType="java.lang.Long">
        SELECT
        concat('/expander-api' , m.route_path) route_path
        FROM
        t_sys_resource m
        WHERE
        m.status = 1 AND m.is_deleted = 0
        AND m.id IN (
        SELECT DISTINCT
        ( rmb.resource_id )
        FROM
        t_sys_user_role p
        JOIN t_sys_role r ON p.role_Id = r.id
        AND p.user_id = #{id} AND p.is_deleted = 0
        AND r.STATUS = 1 AND r.is_deleted = 0
        JOIN t_sys_role_resource rmb ON rmb.role_id = r.id AND rmb.is_deleted = 0 UNION
        SELECT
        r.id
        FROM
        t_sys_resource r
        JOIN t_sys_role_resource rr ON r.id = rr.resource_id
        JOIN t_sys_role ro ON rr.role_id = ro.id
        AND ro.status = 1 AND ro.role_code LIKE 'COMMON%' AND ro.is_deleted = 0 AND rr.is_deleted = 0
        AND r.menu_type = '3'
        )
        ORDER BY
        m.parent_id ASC,
        m.weight ASC
    </select>

</mapper>

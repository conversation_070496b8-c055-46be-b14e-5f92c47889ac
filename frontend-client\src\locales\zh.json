{"resetPassword": {"title": "重置密码", "username": "用户名", "email": "电子邮箱", "code": "验证码", "newPassword": "新密码", "confirmPassword": "确认新密码", "getVerificationCode": "获取验证码", "reset": "重置密码", "resetting": "重置中...", "backToLogin": "返回登录", "errors": {"usernameRequired": "请输入您的用户名", "emailRequired": "请输入电子邮箱", "emailInvalid": "请输入正确的邮箱地址", "codeRequired": "请输入验证码", "codeLength": "验证码长度应为 6 位", "newPasswordRequired": "请输入新密码", "newPasswordMinLength": "密码长度不能小于 6 个字符", "confirmPasswordRequired": "请确认新密码", "passwordMismatch": "两次输入密码不一致", "emailMissing": "请先输入邮箱地址", "codeSent": "验证码发送成功", "passwordResetSuccess": "密码重置成功"}}, "register": {"title": "创建新账户", "username": "用户名", "email": "电子邮箱", "code": "验证码", "password": "密码", "confirmPassword": "确认密码", "getVerificationCode": "获取验证码", "register": "注册", "registering": "注册中...", "inviter": "邀请码，没有可不填", "alreadyHaveAccount": "已有账号？", "login": "立即登录", "errors": {"usernameRequired": "请输入用户名", "usernameLength": "长度在 3 到 20 个字符", "emailRequired": "请输入电子邮箱", "emailInvalid": "请输入正确的邮箱地址", "codeRequired": "请输入验证码", "codeLength": "验证码长度应为 6 位", "passwordRequired": "请输入密码", "passwordMinLength": "密码长度不能小于 6 个字符", "confirmPasswordRequired": "请确认密码", "passwordMismatch": "两次输入密码不一致", "emailMissing": "请先输入邮箱地址", "codeSent": "发送成功，请前往邮箱或者垃圾箱中查看验证码", "codeFailed": "验证码发送失败", "registrationSuccess": "注册成功", "inviterRequired": "请输入邀请码"}}, "login": {"title": "登录您的账号", "accountLogin": "账号密码", "authCodeLogin": "授权码", "enterAuthCode": "请输入授权码", "username": "用户名", "code": "验证码", "password": "密码", "rememberMe": "记住密码", "dologin": "登录", "loging": "登录中...", "registerNewAccount": "没有账号？去注册", "forgot_password": "忘记密码", "loginSuccess": "登录成功", "otherLoginMethods": "其他登录方式", "errors": {"usernameRequired": "请输入用户名或邮箱", "codeRequired": "请输入验证码", "passwordRequired": "请输入密码", "codeError": "验证码输入错误", "authCodeRequired": "请输入授权码"}}, "sidebar": {"dashboard": "开始使用", "shop": "在线商店", "buy": "卡密购买", "redeem": "卡密兑换", "promotion": "推广信息", "announcements": "站内公告", "instructions": "使用说明", "adminPanel": "管理后台"}, "changePassword": {"success": "密码修改成功", "email": "邮箱", "userName": "用户名", "expirationDateExpired": "ChatGPT 有效期", "plusExpirationDateExpired": "ChatGPT plus 有效期", "claudeExpirationDateExpired": "<PERSON> 有效期", "claudeProExpirationDateExpired": "Claude Pro 有效期", "affCode": "邀请码", "membershipPlan": "会员套餐", "currentPassword": "当前密码", "newPassword": "新密码", "confirmPassword": "确认新密码", "perLimit": "ChatGPT 限制", "claudePerLimit": "<PERSON> 限制"}, "auth": {"loginPrompt": "功能不可用，前往登录?", "notLoggedIn": "暂未登录", "registerPrompt": "功能不可用，前往注册?"}, "announcements": {"claude": {"title": "<PERSON> 服务公告"}, "grok": {"title": "Grok 服务公告"}, "draw": {"title": "AI 绘图服务公告"}, "gpt": {"title": "GPT 服务公告"}}, "common": {"announcement": "公告", "login": "登录", "register": "注册", "confirm": "确认", "cancel": "取消", "close": "关闭", "save": "保存", "edit": "编辑", "delete": "删除", "download": "下载", "upload": "上传", "loading": "加载中...", "noData": "暂无数据", "success": "成功", "error": "错误", "warning": "警告", "tip": "提示", "personalCenter": "个人中心", "noValidNodes": "暂无可用节点，请联系管理员", "officialStatus": "官网状态", "draw": "绘图", "welcome": "欢迎", "unit": {"piece": "张", "times": "次"}}, "mobile": {"dailyCheckIn": "每日签到", "notifications": "通知消息", "lightMode": "浅色模式", "darkMode": "深色模式", "language": "语言", "more": "更多"}, "userGuide": {"title": "欢迎使用AI服务平台", "subtitle": "如果您是第一次使用，请按照以下步骤开始使用我们的AI服务", "steps": {"selectModel": {"title": "选择产品", "description": "点击左侧下拉菜单选择您需要的AI产品"}, "checkStatus": {"title": "查看状态", "description": "中间显示官方服务状态，确保服务正常运行"}, "selectNode": {"title": "选择节点", "description": "从下方节点列表中选择合适的服务节点"}}, "announcementTip": "点击右侧公告按钮查看最新通知和使用说明"}, "modelSelector": {"title": "选择AI产品", "subtitle": "选择您要使用的AI产品", "loading": "正在加载模型...", "noModels": "没有找到有效的模型配置", "loadingModels": "正在加载可用模型..."}, "userProfile": {"validUntil": "有效期至", "noPlan": "还未购买套餐", "newPassword": "新密码", "enterNewPassword": "请输入新密码", "newPasswordRequired": "请输入新密码", "passwordLengthError": "密码长度不能小于6位", "confirmPasswordRequired": "请确认新密码", "passwordMismatch": "两次输入的密码不一致", "benefits": "权益", "changePassword": "修改密码", "currentPassword": "当前密码", "verified": "已验证", "logout": "退出登录", "cancel": "取消", "save": "保存", "unVerified": "未认证", "bindEmail": "绑定邮箱", "noEmail": "未绑定邮箱", "saving": "保存中...", "enterCurrentPassword": "请输入当前密码", "enterConfirmPassword": "请再次输入新密码", "confirmPassword": "确认新密码", "paymentHistory": "近期支付记录", "noPaymentHistory": "暂无支付记录", "paymentDate": "交易日期", "productName": "商品名称", "amount": "金额", "status": "状态", "success": "支付成功", "pending": "处理中", "failed": "支付失败", "tradeNo": "交易单号", "limits": "模型限制", "perSecond": "每秒", "perMinute": "每分", "perHour": "每时", "perThreeHour": "每三时", "perFiveHour": "每五时", "perDay": "每天", "perWeek": "每周", "perMonth": "每月", "perYear": "每年", "noLimit": "不限制", "useBan": "禁止使用"}, "shop": {"title": "在线商店", "loading": "正在加载商品信息...", "days": "天", "dailyPrice": "每天仅需", "perImagePrice": "每张仅需", "moreFeatures": "更多功能", "showLess": "收起", "hotSale": "热销", "recommended": "推荐", "exclusive": "独享", "basicFeatures": "基础功能包含", "categories": {"all": "全部"}, "noProducts": {"title": "暂无商品", "description": "管理员还未上架任何商品，请稍后再来查看或联系客服了解详情。", "refresh": "刷新页面", "clearFilters": "清除筛选"}, "error": {"title": "加载失败", "fetchFailed": "获取商品信息失败，请稍后重试"}, "defaultFeatures": {"chatgpt": "ChatGPT 4.0 访问", "claude": "Claude 3.5 访问", "grok": "Grok 3.0 访问", "chatgptClaude": "ChatGPT + Claude 双模型", "chatgptGrok": "ChatGPT + Grok 组合", "claudeGrok": "<PERSON> + <PERSON><PERSON> 组合", "allModels": "全部AI模型访问", "aiDrawing": "AI绘图功能", "webAccess": "网页端访问", "mobileApp": "移动端应用", "realTimeInfo": "实时信息获取", "apiAccess": "API 接口访问", "multipleStyles": "多种艺术风格", "hdGeneration": "高清图片生成", "commercialLicense": "商用授权", "basicSupport": "基础客服支持", "prioritySupport": "优先客服支持", "premiumSupport": "专属客服支持", "basicService": "基础服务功能"}, "packages": {"basic": {"name": "基础版", "description": "适合个人用户的基础AI服务", "features": {"chatgpt": "ChatGPT 4.0 访问", "basicSupport": "基础客服支持", "webAccess": "网页端访问", "mobileApp": "移动端应用"}}, "professional": {"name": "专业版", "description": "适合专业用户的全面AI解决方案", "features": {"allModels": "ChatGPT + Claude 双模型", "prioritySupport": "优先客服支持", "advancedFeatures": "高级功能访问", "apiAccess": "API 接口访问", "teamCollaboration": "团队协作功能"}}, "enterprise": {"name": "旗舰版", "description": "企业级全功能AI服务套餐", "features": {"allModels": "全部AI模型访问", "unlimitedUsage": "无限制使用", "premiumSupport": "专属客服支持", "customIntegration": "定制化集成", "advancedAnalytics": "高级数据分析", "aiDrawing": "AI绘图功能"}}}, "duration": {"week": "7天", "month": "30天", "quarter": "90天", "year": "365天"}, "discount": {"save15": "省15%", "save30": "省30%"}, "payment": {"title": "订单确认", "orderInfo": "订单信息", "package": "套餐名称", "amount": "支付金额", "originalPrice": "原价", "discountPrice": "优惠价", "validDays": "有效天数", "usageLimit": "使用限制", "days": "天", "scanToPay": "请打开{payMethod}扫码支付", "scanTip": "打开对应APP扫描二维码完成支付", "selectPayment": "选择支付方式", "processing": "处理中...", "confirmPay": "确认支付", "close": "关闭", "coupon": "优惠券", "cancel": "取消", "confirm": "确定", "confirmClose": {"title": "确认关闭", "message": "订单已创建，关闭后订单仍会保留。确定要关闭支付页面吗？", "confirm": "确定关闭", "cancel": "继续支付"}, "methods": {"alipay": "支付宝", "usdt": "USTD", "wechat": "微信"}}, "coupon": {"title": "优惠券", "inputLabel": "优惠券代码", "placeholder": "请输入优惠券（选填）", "confirm": "确定", "cancel": "取消", "validating": "验证中...", "applied": "优惠券已应用", "remove": "移除", "code": "优惠券", "discountAmount": "优惠金额: ¥{amount}", "saved": "已优惠 ¥{amount}", "errors": {"empty": "请输入优惠券代码", "invalid": "优惠券无效或已过期", "noDiscount": "此优惠券不适用于当前商品", "networkError": "网络错误，请稍后重试"}, "success": {"title": "优惠券应用成功", "message": "已为您节省 ¥{discount}"}, "removed": {"title": "优惠券已移除", "message": "价格已恢复原价"}}, "subscribe": "立即订阅", "errors": {"noPayment": "管理员还未配置支付方式", "fetchError": "获取数据失败"}, "login": {"title": "暂未登录", "message": "功能不可用，前往登录?", "confirm": "登录", "cancel": "取消"}}, "renewNow": "立即续费", "promotion": {"title": "推广信息", "description": "这里记录您的邀请记录和佣金信息"}, "confirm": "确定", "cancel": "取消", "notification": {"title": "通知", "read": "已读"}, "card_redeem_title": "卡密兑换", "card_redeem_subtitle": "在这里您可以兑换您的卡密", "redeem_card_code": "兑换卡密", "redeem_button_text": "立即兑换", "redeem_in_progress": "兑换中...", "redeem_success": "兑换成功！", "redeem_warning": "请输入卡密", "instructions_title": "使用说明：", "instructions_list_first": "输入您获得的卡密兑换码", "instructions_list_second": "点击'立即兑换'按钮进行兑换", "instructions_list_third": "兑换成功后系统会自动为您的账户充值相应权益", "instructions_list_fourth": "如遇问题请联系客服处理", "redeem_time": "兑换时间：", "redeem_loading": "加载中...", "redemption_history": "最新兑换记录", "no_redemption_history": "暂无兑换记录", "keyCodePurchase": {"title": "卡密购买", "description": "这里您可以购买新的卡密", "instructions": "使用说明"}, "instructions": {"title": "使用说明", "description": "这里提供了详细的使用说明和帮助文档"}, "promotionQRCode": "推广二维码", "inviteLinkText": "通过分享二维码或者复制邀请链接邀请好友，好友购买后您将获得订单金额", "withdrawCash": "的推广现金", "withdrawThreshold": "满 {amount} 元可立即提现。", "cashbackForPaidUsers": "管理员开启了付费用户返现，只有付费用户邀请的人才会计算返现金额。", "totalOrders": "推广总订单数", "totalAmount": "推广总金额", "withdrawableAmount": "可提现金额", "withdrawnAmount": "已提现总金额", "copyInviteLink": "复制邀请链接", "withdrawNow": "立即提现", "inviteRecord": "邀请记录", "withdrawRecord": "提现记录", "table": {"index": "序号", "noData": "暂无数据", "username": "用户名", "email": "邮箱", "memberType": "会员类型", "inviteTime": "邀请时间", "withdrawAmount": "提现金额", "withdrawTime": "提现时间", "approvalStatus": "审批状态", "approvalResult": "审批结果", "types": {"plus": "高级会员", "normal": "普通会员"}, "statuses": {"approved": "已审批", "pending": "待审批", "rejected": "已驳回", "unknown": "未知状态"}}, "pagination": {"prev": "上一页", "next": "下一页"}, "announcement": {"title": "站内公告", "description": "您可以在这里获取到最新的公告信息", "iframeTitle": "站内公告"}, "status": {"idle": "空闲", "busy": "繁忙", "offline": "停运", "unknown": "未知", "operational": "正常运行", "degraded_performance": "性能下降", "partial_outage": "部分中断", "major_outage": "严重中断", "maintenance": "维护中"}, "recommendations": {"recommended": "推荐", "available": "可用"}, "loading": {"enteringChat": "正在进入对话...", "loadingData": "数据加载中...", "switchingModel": "正在切换模型...", "connectingService": "正在连接服务...", "loadingInstances": "正在获取实例信息...", "connectingToService": "正在连接到 {model} 服务...", "loadingModelInstances": "正在获取 {model} 实例信息..."}, "errors": {"getCarListFailed": "获取车队列表失败", "selectCarFailed": "选择车辆失败", "nodeError": "该节点异常，请更换节点。", "noBackupAccess": "您没有使用备用站点的权限，请充值后重试", "switchModelFailed": "切换模型失败", "switchModelError": "切换模型时发生错误，请重试", "initializationFailed": "初始化失败", "loadDataFailed": "加载数据失败", "parseBackupSitesFailed": "解析备用站点失败", "parseNodeOrderFailed": "解析节点排序失败", "requestTimeout": "请求超时", "fetchOpenAIStatusFailed": "获取OpenAI状态失败", "fetchClaudeStatusFailed": "获取Claude状态失败", "asyncFetchOpenAIStatusFailed": "异步获取OpenAI状态失败", "asyncFetchClaudeStatusFailed": "异步获取Claude状态失败", "timedFetchOpenAIStatusFailed": "定时获取OpenAI状态失败", "timedFetchClaudeStatusFailed": "定时获取Claude状态失败", "cannotGetOpenAIStatus": "无法获取OpenAI状态", "cannotGetClaudeStatus": "无法获取Claude状态", "claudeComponentNotFound": "未找到claude.ai组件"}, "login-warn": {"notLoggedIn": "暂未登录", "functionUnavailable": "功能不可用，前往登录?", "loginButton": "登录", "registerButton": "注册"}, "withdrawDialog": {"title": "申请提现", "amount": "提现金额", "amountPlaceholder": "请输入提现金额", "password": "登录密码", "passwordPlaceholder": "输入登录密码", "contact": "联系方式", "contactPlaceholder": "输入联系方式", "qrcode": "收款码", "qrcodePlaceholder": "上传收款码", "uploadError": "请上传收款码", "cancel": "取消", "confirm": "确定", "fileError": "文件上传失败: {error}", "fileLimitError": "最多只能上传1个文件", "extractAll": "提取全部"}, "tooltip": {"profile": "用户中心", "signIn": "签到", "notification": "通知", "language": "切换语言", "darkMode": "切换暗黑模式"}, "noValidNodes": "没有有效的节点，请联系管理员", "paymentSuccess": "支付成功", "signIn": {"title": "每日签到", "signIn": "签到", "signed": "已签到", "success": "签到成功！", "failed": "签到失败", "alreadySignedIn": "您今天已经签到过了", "continuousDays": "连续签到: {days} 天", "signInNow": "立即签到", "signingIn": "签到中..."}, "all": "全部", "request": {"configError": "请求配置错误", "loginExpired": "登录信息过期，请重新登录", "operationFailed": "操作失败", "systemError": "系统异常", "authExpired": "授权信息已过期，请联系站长", "noPermission": "您没有权限访问该资源", "serverError": "服务器内部错误", "connectionError": "连接错误{status}", "networkError": "网络异常，请检查您的网络连接", "timeout": "请求超时，请稍后重试"}, "instances": {"title": "AI 模型实例", "selectModel": "选择产品", "noInstances": "暂无可用实例", "instanceInfo": "实例信息", "accountType": "账户类型", "carId": "车辆ID", "usage": "使用率", "enterChat": "进入对话", "models": {"chatgpt": "ChatGPT", "claude": "<PERSON>", "grok": "Grok", "grokSuper": "Super", "sass": "SASS", "draw": "AI绘图"}, "types": {"free": "Free", "4o": "4o", "plus": "Plus", "team": "Team", "pro": "Pro", "standard": "标准版", "max": "Max", "claude37": "4.0"}, "drawPanel": {"title": "AI 绘图", "close": "关闭"}, "descriptions": {"chatgpt": "智能对话助手，支持多轮对话和复杂任务处理", "claude": "高质量AI助手，擅长分析和创作任务", "grok": "实时信息AI，具备最新数据访问能力", "gemini": "Google AI模型，多模态理解能力强", "ai绘图": "专业AI图像生成工具，支持多种艺术风格", "default": "强大的AI助手，为您提供智能服务"}}, "draw": {"title": "AI 绘图", "announcement": "绘图公告", "quota": {"title": "绘图额度", "used": "已用", "remaining": "剩余", "total": "总计", "resetTime": "重置时间", "notSet": "未设置", "today": "今天", "tomorrow": "明天", "daysLater": "{days}天后", "dateFormat": "{year}年{month}月{day}日", "fetchError": "获取绘图额度失败"}, "tabs": {"generate": "生成图像", "edit": "编辑图像", "history": "历史绘图", "record": "消费记录"}, "editor": {"uploadImage": "上传图片", "dragDropUpload": "拖放或点击上传图片", "supportedFormats": "支持 JPG, PNG, WEBP, JPEG 格式, 大小不超过10MB。", "uploadedCount": "已上传 {count} 张图片", "imagePreview": "图片预览", "localRedraw": "局部重绘", "originalImage": "原始图片", "clickToAddRedraw": "点击添加局部重绘", "markedRedrawArea": "已标记重绘区域", "modifyRedrawArea": "修改重绘区域", "editPrompt": "编辑提示词", "promptPlaceholder": "描述您想要对图片进行的编辑...", "imageSettings": "图片设置", "imageQuality": "画面质量", "imageSize": "图片尺寸", "generateCount": "生成数量", "startEdit": "开始编辑", "editing": "编辑中...", "imageWillShowHere": "您的图像将在这里显示", "uploadAndEdit": "上传图片并输入提示词，点击\"开始编辑\"按钮开始创作", "editTips": "图片编辑提示", "editResult": "编辑结果", "viewOriginal": "查看原图", "download": "下载", "imageCount": "图片 {current} / {total}", "prompt": "提示词", "usageTips": "使用提示", "brushSize": "画笔大小", "clear": "清除", "finish": "完成", "maskTip": "涂抹的区域将会被重绘，未涂抹的区域将保持不变", "quality": {"low": "低质量", "medium": "中等质量", "high": "高质量"}, "size": {"square": "正方形 (1:1)", "portrait": "竖向 (2:3)", "landscape": "横向 (3:2)"}, "tips": {"0": "明确描述您想要的具体变化，如'将背景改为海滩场景'", "1": "高质量选项会生成更精细的图片，但需要更长处理时间", "2": "生成数量越多，额度消耗越大", "3": "重绘仅对第一张图片生效", "4": "处理时间约为1-3分钟，请耐心等待"}, "usageTipsList": {"0": "尝试不同的风格搭配获得更好效果", "1": "可以多次编辑优化图片细节", "2": "使用更详细的描述获得更精准的结果"}, "errors": {"uploadImageAndPrompt": "请上传图片并输入编辑提示词", "invalidImageFile": "图片文件无效", "editTimeout": "图片编辑超时，请重试", "downloadFailed": "图片下载失败，请稍后重试"}, "status": {"preparing": "正在准备编辑...", "processing": "开始处理图片...", "waitingResult": "图片处理中，请耐心等待1-5分钟，也可以在绘图历史中查看...", "checkingStatus": "正在检查处理状态...", "successGenerated": "成功生成 {count} 张图片！", "completedInvalid": "图片编辑完成但结果无效，请修改后重试!"}}, "generator": {"title": "AI 图像生成", "prompt": "描述提示词", "promptPlaceholder": "描述您想要生成的图像...", "negativePrompt": "负面提示词", "negativePromptPlaceholder": "描述您不想要的元素...", "style": "艺术风格", "model": "生成模型", "imageSize": "图片尺寸", "generateCount": "生成数量", "generateButton": "开始生成", "generating": "生成中...", "generationTips": "生成提示", "tips": {"0": "详细描述能获得更精准的结果", "1": "使用艺术风格关键词如'油画'、'水彩'等", "2": "负面提示词可以排除不想要的元素", "3": "高分辨率图片需要更多时间生成"}, "styles": {"realistic": "写实风格", "anime": "动漫风格", "cartoon": "卡通风格", "oil": "油画风格", "watercolor": "水彩风格", "sketch": "素描风格", "digital": "数字艺术"}, "models": {"stable": "Stable Diffusion", "midjourney": "Midjourney", "dalle": "DALL-E"}, "suggestions": {"cat": "一只可爱的小猫咪坐在窗台上，阳光照射，背景是蓝天", "cyberpunk": "未来城市的天际线，霓虹灯光，赛博朋克风格", "forest": "宫崎骏风格的童话森林，小精灵在飞舞", "landscape": "水彩画风格的山水风景，薄雾缭绕", "portrait": "复古波普艺术风格的肖像，鲜艳的色彩"}, "errors": {"promptRequired": "请输入提示词", "generateFailed": "图像生成失败", "downloadFailed": "下载图片失败，请稍后重试", "noTaskId": "未获取到任务ID，请重试"}, "status": {"connecting": "正在连接服务器...", "generating": "开始生成图像...", "processing": "开始生成图片中...", "checking": "正在检查生成状态...", "success": "图片生成成功！", "failed": "图片生成失败，请修改后重试!", "timeout": "图片生成超时，请重试", "waiting": "图片处理中，请耐心等待1-5分钟，也可以在绘图历史中查看..."}}, "history": {"title": "绘图历史", "description": "查看您之前生成和编辑后的所有图像（每5分钟同步一次状态）", "noHistory": "暂无历史记录", "startCreating": "当您生成或编辑图像后，它们将显示在这里。您可以随时回顾和重新使用之前的作品。", "loadMore": "加载更多", "loading": "加载中...", "createdAt": "创建时间", "prompt": "提示词", "model": "模型", "size": "尺寸", "status": "状态", "download": "下载", "regenerate": "重新生成", "delete": "删除", "filters": {"all": "全部图像", "generated": "生成的图像", "edited": "编辑的图像"}, "types": {"generated": "生成", "edited": "编辑", "generatedImage": "生成的图像", "editedImage": "编辑的图像"}, "statuses": {"pending": "等待中", "processing": "处理中", "completed": "已完成", "failed": "处理失败"}, "errors": {"fetchFailed": "获取历史记录失败，请稍后重试"}, "deleteImage": "删除图片", "deleting": "删除中...", "confirmDelete": "确定要删除这张图片吗？删除后无法恢复。", "deleteFailed": "删除失败，请稍后重试", "deleteSuccess": "删除成功", "copyPrompt": "复制提示词", "copySuccess": "提示词复制成功", "copyFailed": "复制提示词失败"}, "record": {"title": "消费记录", "noRecord": "暂无消费记录", "pleaseLogin": "请先登录", "index": "序号", "amount": "消费数量", "type": "变化类型", "createdAt": "消费时间", "remark": "备注", "noRemark": "无备注", "date": "日期", "cost": "消耗", "description": "描述", "types": {"purchase": "套餐购买", "consumption": "绘图消耗", "refund": "失败返还", "adminAdjust": "后台修改", "other": "其他", "generate": "图像生成", "edit": "图像编辑", "upscale": "图像放大"}, "errors": {"fetchFailed": "获取消费记录失败"}}}}
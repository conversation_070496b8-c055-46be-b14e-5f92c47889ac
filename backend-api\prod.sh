#!/bin/bash

# 生成时间戳版本号
TAG=$(date +%Y%m%d%H%M%S)
echo "Building for prod with version: $TAG"
export JAVA_HOME=/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home
export PATH=$JAVA_HOME/bin:$PATH
# 1. 构建前台
cd ../frontend-client

echo "start frontend-client build"
pnpm build
if [ $? -ne 0 ]; then
    echo "frontend-client build failed"
    exit 1
fi

echo "frontend-client build success"

# 1. 构建后台
cd ../frontend-admin
echo "strat frontend-admin build"
pnpm build
if [ $? -ne 0 ]; then
    echo "frontend-admin build failed"
    exit 1
fi

echo "frontend-admin build success"


# 2. 将版本号追加到文件
echo "$TAG" >> /Users/<USER>/project/expander-sass/backend-api/src/main/resources/versions.txt

# 3. 打包后端 JAR
echo "start Backend-api build"
cd ../backend-api
mvn clean package -Dmaven.test.skip=true
if [ $? -ne 0 ]; then
    echo "Backend api build failed"
    exit 1
fi

echo "backend api build success"

# 4. 确保启用了 buildx 并创建多架构构建器
docker buildx create --use --name multiarch-builder || true


# 6. 使用 buildx 构建并推送多架构镜像
docker buildx build --platform linux/amd64,linux/arm64 \
  --tag seven222/chatgpt-share-server-expander-sass:$TAG \
  --tag seven222/chatgpt-share-server-expander-sass:latest \
  --push \
  -f Dockerfile .
if [ $? -ne 0 ]; then
    echo "Docker build and push failed"
    exit 1
fi

# 7. 验证多架构支持
echo "正在验证镜像架构支持..."
docker buildx imagetools inspect seven222/chatgpt-share-server-expander-sass:latest

echo "Prod build and push completed. Version: $TAG"
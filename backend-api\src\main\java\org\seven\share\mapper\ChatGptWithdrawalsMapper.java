package org.seven.share.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.seven.share.common.pojo.entity.ChatGptWithdrawalsEntity;

import java.util.Map;

/**
*@ClassName: ChatGptWithdrawalsDao
*@Description:
*@Author: Seven
*@Date: 2024/10/15
*/
@Mapper
public interface ChatGptWithdrawalsMapper extends BaseMapper<ChatGptWithdrawalsEntity> {
    @Select("SELECT " +
            "SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) AS completed, " +
            "SUM(CASE WHEN status = '0' THEN 1 ELSE 0 END) AS pending " +
            "FROM chatgpt_withdrawals")
    Map<String, Integer> getToDoData();
}

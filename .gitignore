# Dependencies
node_modules/
/.pnp
.pnp.js
package-lock.json
yarn.lock
data
# Testing
/coverage
.nyc_output

# Production
/build
/dist
/out
/.next

/backend-api/src/main/resources/static/expander/*
/backend-api/src/main/resources/static/admin/*
/backend-api/src/main/resources/static/client/*
/backend-api/src/main/resources/static/app/*

# Environment files
# .env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local
# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IDE - WebStorm/IntelliJ
.idea/
*.iml
*.iws
.idea_modules/

# IDE - Eclipse
.project
.classpath
.settings/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# TypeScript
*.tsbuildinfo

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Java
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar
hs_err_pid*
target/
.gradle/
# build/

# Database
*.sqlite
*.sqlite3
*.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.swp
*.swo
*~ 
const SOYBEANJS =
  '2020205f5f5f5f5f202020205f5f5f5f2020205f5f20202020205f5f20205f5f5f5f202020205f5f5f5f5f5f20202020202020202020202020205f2020205f20202020202020205f202020205f5f5f5f5f200a20202f205f5f5f5f7c20202f205f5f205c20205c205c2020202f202f207c20205f205c20207c20205f5f5f5f7c20202020202f5c20202020207c205c207c207c2020202020207c207c20202f205f5f5f5f7c0a207c20285f5f5f2020207c207c20207c207c20205c205c5f2f202f20207c207c5f29207c207c207c5f5f202020202020202f20205c202020207c20205c7c207c2020202020207c207c207c20285f5f5f20200a20205c5f5f5f205c20207c207c20207c207c2020205c2020202f2020207c20205f203c20207c20205f5f7c20202020202f202f5c205c2020207c202e2060207c20205f2020207c207c20205c5f5f5f205c200a20205f5f5f5f29207c207c207c5f5f7c207c202020207c207c202020207c207c5f29207c207c207c5f5f5f5f2020202f205f5f5f5f205c20207c207c5c20207c207c207c5f5f7c207c20205f5f5f5f29207c0a207c5f5f5f5f5f2f2020205c5f5f5f5f2f20202020207c5f7c202020207c5f5f5f5f2f20207c5f5f5f5f5f5f7c202f5f2f202020205c5f5c207c5f7c205c5f7c20205c5f5f5f5f2f20207c5f5f5f5f5f2f20';

function decode16(str: string): string {
  const arr: string[] = [];
  for (let i = 0; i < str.length; i += 2) {
    const asciiCode = Number.parseInt(str.slice(i, i + 2), 16);
    const charValue = String.fromCharCode(asciiCode);
    arr.push(charValue);
  }
  return arr.join('');
}

export async function printSoybean() {
  console.log(decode16(SOYBEANJS));
}

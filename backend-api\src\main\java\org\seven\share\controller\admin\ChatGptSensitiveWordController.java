package org.seven.share.controller.admin;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.R;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.entity.ChatGptSensitiveWordEntity;
import org.seven.share.service.ChatGptSensitiveWordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: ChatGptSensitiveWordController
 * @Description:
 * @Author: Seven
 * @Date: 2024/10/9
 */
@RestController
@RequestMapping("/expander-api/word")
public class ChatGptSensitiveWordController {
    @Resource
    private ChatGptSensitiveWordService chatGptSensitiveWordService;


    @GetMapping("/page")
    @SysLogInterface(title = "分页查询敏感词", businessType = BusinessType.QUERY)
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer current,
                  @RequestParam(value = "size", defaultValue = "10") Integer size,
                  @RequestParam(required = false) String word,
                  @RequestParam(required = false) Integer status){
        LambdaQueryWrapper<ChatGptSensitiveWordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotEmpty(word), ChatGptSensitiveWordEntity::getWord, word);
        queryWrapper.eq(status != null, ChatGptSensitiveWordEntity::getStatus, status);
        queryWrapper.orderByDesc(ChatGptSensitiveWordEntity::getUpdateTime);
        Page<ChatGptSensitiveWordEntity> pageInfo = chatGptSensitiveWordService.page(new Page<>(current, size), queryWrapper);
        return R.ok(pageInfo);
    }


    @PostMapping("/add")
    @SysLogInterface(title = "新增敏感词", businessType = BusinessType.INSERT)
    public R saveWord(@RequestBody ChatGptSensitiveWordEntity entity){
        chatGptSensitiveWordService.saveWord(entity);
        return R.ok();
    }

    @PostMapping("/update")
    @SysLogInterface(title = "更新敏感词", businessType = BusinessType.UPDATE)
    public R updateWord(@RequestBody ChatGptSensitiveWordEntity entity){
        chatGptSensitiveWordService.updateWord(entity);
        return R.ok();
    }


    @DeleteMapping("/delete")
    @SysLogInterface(title = "批量删除敏感词信息", businessType = BusinessType.DELETE)
    public R delete(@RequestBody List<String> ids){
        chatGptSensitiveWordService.deleteBatchWordByIds(ids);
        return R.ok();
    }


    @PostMapping("/batch")
    @SysLogInterface(title = "批量保存敏感词信息", businessType = BusinessType.INSERT)
    public R saveBatchWords(@RequestBody List<String> words){
        chatGptSensitiveWordService.saveBatchWords(words);
        return R.ok();
    }

}

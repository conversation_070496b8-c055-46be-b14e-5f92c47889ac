package org.seven.share.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.pojo.entity.ChatGptSysNoticeEntity;
import org.seven.share.common.pojo.entity.SysTenant;
import org.seven.share.mapper.ChatGptConfigMapper;
import org.seven.share.mapper.CreateTableMapper;
import org.seven.share.common.pojo.entity.ChatGptConfigEntity;
import org.seven.share.service.*;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.seven.share.common.util.ConstantUtil.SYS_TENANT_ID;
import static org.seven.share.common.util.ServletUtils.getHost;
import static org.seven.share.constant.CacheConstant.CONFIG_KEY_PREFIX;

/**
 * @ClassName: ChatGptConfigServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/1
 */
@Service
@Slf4j
public class ChatGptConfigServiceImpl extends ServiceImpl<ChatGptConfigMapper, ChatGptConfigEntity> implements ChatGptConfigService {

    @Resource
    private ChatGptConfigMapper chatGptConfigMapper;

    @Resource
    private EmailService emailService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private CreateTableMapper createTableMapper;

    @Resource
    private LicenseValidator licenseValidator;

    @Resource
    private ChatGptSysNoticeService chatGptSysNoticeService;

    @Resource
    private SysTenantService sysTenantService;

    @Resource
    private ChatGptConfigService self;

    @PostConstruct
    public void init() {
        log.info("初始化ChatGptConfigServiceImpl");
        // 初始化redis数据
        createTableMapper.createChatGptConfigTable();
//        getConfigMap();
        log.info("初始化ChatGptConfigServiceImpl完成");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = "config", allEntries = true)
    public void saveForm(Map<String, Object> map) {
        log.info("保存配置信息");
        if (ObjectUtil.isEmpty(map)) {
            return;
        }
        // 获取数据库中已有的键值对
        Map<String, ChatGptConfigEntity> existingEntities = this.lambdaQuery()
                .in(ChatGptConfigEntity::getKey, map.keySet())
                .list()
                .stream()
                .collect(Collectors.toMap(ChatGptConfigEntity::getKey, entity -> entity));
        // 区分新增和更新数据
        List<ChatGptConfigEntity> toInsert = new ArrayList<>();
        List<ChatGptConfigEntity> toUpdate = new ArrayList<>();
        for (String key : map.keySet()) {
            ChatGptConfigEntity entity = existingEntities.get(key);
            String newValue = String.valueOf(map.get(key));
            if (entity == null) {
                entity = new ChatGptConfigEntity();
                entity.setKey(key);
                entity.setValue(newValue);
                toInsert.add(entity);
            } else if (!newValue.equals(entity.getValue())) {
                entity.setValue(newValue);
                toUpdate.add(entity);
            }
        }
        // 批量执行
        if (!toInsert.isEmpty()) {
            saveBatch(toInsert);
        }
        if (!toUpdate.isEmpty()) {
            updateBatchById(toUpdate);
        }

        log.info("保存配置信息完成");
    }

    @Override
    public Map<String, String> getConfigMap() {

        List<ChatGptConfigEntity> entityList = chatGptConfigMapper.selectList(null);
        // 更新缓存
        Map<String, String> map = entityList.stream()
                .filter(entity -> entity.getKey() != null && entity.getValue() != null) // 过滤掉键和值为null的元素
                .collect(Collectors.toMap(ChatGptConfigEntity::getKey, ChatGptConfigEntity::getValue));
        return map;
    }

    @Override
    public void testEmail(String email) {
        log.info("开始发送测试邮件...");
        CompletableFuture.runAsync(() -> {
            emailService.sendEmailNotTemplate(email, "测试邮件", "这是一封测试邮件，请忽略。");
        });
        log.info("发送测试邮件完成");
    }

    @Override
    public String getValueByKey(String key) {
        Map<String, String> map = self.getKeyValueMapByKeys(Collections.singletonList(key));
        return map != null ? map.get(key) : "";
    }

    @Override
    public Map<String, String> getSiteData() {
        List<String> keys = List.of("enableRegister", "siteName", "userGuideUrl", "fkAddress",
                "freeNodes", "enableClaudeNode", "openWechat", "enableSiteShop", "freeNodeName",
                "normalNodeName", "plusNodeName", "claudeNodeName", "customerSidebarUrl", "enableEmailCheck",
                "enablePromotion", "loginAndChat", "threshold", "showVersion", "enableBackNode",
                "backupNode", "backupUrl", "backupOpenType", "enableNoLogin", "themeName", "enableExpirationReminder",
                "useBackNode", "customerSidebarName", "sidebarOpenType", "showPaymentHistory", "enableInvite",
                "enableShowRemaining", "enableNoSelectCar", "enableInviteCode", "enableSignIn", "signInAnnouncement",
                "fkAddressOpenType", "userGuideUrlOpenType", "enableCashbackForPaidUsers", "collapseSidebar",
                "closeCardExchange", "showModelRate", "closeSubTypeShow", "enableSxClaude", "sxClaudeUrl", "claudeUrl",
                "backupSites","soruxGptSideBarOpenType","soruxGptSideBarUrl","soruxGptSideBarName", "grokUrl", "grokNodeName",
                "grokOpenType", "showNodeName", "enableDraw", "drawNodeName", "nodeOrder", "drawCount",
                "drawModel", "logoUrl", "enableBackupSitesForSubSite", "enableSidebarForSubSite", "enableUserGuideForSubSite");
        Map<String, String> mapByKeys = getKeyValueMapByKeys(keys);
        mapByKeys.put("version", getVersion());

        Map<String, String> noticeMap = getLatestNotices(Arrays.asList("gptSite", "claudeSite", "grokSite", "drawSite"));
        mapByKeys.put("gptAnnouncement", noticeMap.get("gptSite"));  // gpt站内公告
        mapByKeys.put("claudeAnnouncement", noticeMap.get("claudeSite"));  // gpt站内公告
        mapByKeys.put("grokAnnouncement", noticeMap.get("grokSite"));  // gpt站内公告
        mapByKeys.put("drawAnnouncement", noticeMap.get("drawSite"));  // gpt站内公告
        mapByKeys.put("enableDraw", Boolean.toString(licenseValidator.haveAccess("draw")
                && "true".equals(mapByKeys.get("enableDraw"))));
        genTenantConfig(mapByKeys);
        return mapByKeys;
    }

    private void genTenantConfig(Map<String, String> map) {
        // 根据host 查找到用户的租户id
        SysTenant tenant = sysTenantService.getTenantInfoByHost(getHost());
        if (ObjectUtil.isEmpty(tenant)) {
            return;
        }
        map.put("logoUrl", tenant.getSiteLogo());
        map.put("siteName", tenant.getSiteName());
    }

    private Map<String, String> getLatestNotices(List<String> types) {
        List<ChatGptSysNoticeEntity> notices = chatGptSysNoticeService.list(new LambdaQueryWrapper<ChatGptSysNoticeEntity>()
                .orderByDesc(ChatGptSysNoticeEntity::getUpdateTime)
                .in(ChatGptSysNoticeEntity::getType, types));
        // 按 type 分组，取每组最新的记录的 content
        return  notices.stream()
                .collect(Collectors.toMap(
                        ChatGptSysNoticeEntity::getType, // 按 type 作为 key
                        ChatGptSysNoticeEntity::getContent, // content 作为 value
                        (existing, replacement) -> existing // 保留第一条（最新的）
                ));
    }

    private String getVersion() {
        try {
            ClassPathResource resource = new ClassPathResource("versions.txt");
            BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()));
            String line;
            String lastLine = "";
            while ((line = reader.readLine()) != null) {
                lastLine = line.trim(); // 始终返回最后一行
            }
            reader.close();
            return lastLine;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据键查询值
     * @param keys
     * @return
     */
    @Override
    public Map<String, String> getKeyValueMapByKeys(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return null;
        }
        Map<String, String> allConfigs = self.getAllConfigs();
        return keys.stream()
                .filter(allConfigs::containsKey)
                .collect(Collectors.toMap(
                        Function.identity(),
                        allConfigs::get
                ));
    }

    @Override
    @Cacheable(cacheNames = "config", key = "'all_config_map'")
    public Map<String, String> getAllConfigs() {
        log.info("从数据库加载所有配置到缓存");
        List<ChatGptConfigEntity> list = this.list();
        return list.stream()
                .filter(e -> e.getKey() != null && e.getValue() != null)
                .collect(Collectors.toMap(
                        ChatGptConfigEntity::getKey,
                        ChatGptConfigEntity::getValue,
                        (existing, replacement) -> replacement
                ));
    }


    @Override
    public Map<String, String> getSiteScript() {
        return getLatestNotices(Collections.singletonList("script"));
    }
}

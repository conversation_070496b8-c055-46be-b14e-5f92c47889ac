<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { $t } from '@/locales';
// import { loginModuleRecord } from '@/constants/app';
import { useRouterPush } from '@/hooks/common/router';
import { useForm, useFormRules } from '@/hooks/common/form';
import { useAuthStore } from '@/store/modules/auth';
import { fetchLoginCode } from '@/service/api';

defineOptions({ name: 'PwdLogin' });

const authStore = useAuthStore();
const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useForm();

interface FormModel {
  userName: string;
  password: string;
  code: string;
  uuid: string;
}

const model = ref<FormModel>({
  userName: '',
  password: '',
  code: '',
  uuid: ''
});

const captchaCode = ref('1234');
const loading = ref(false);

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  const { formRules } = useFormRules();

  return {
    userName: formRules.userName,
    password: formRules.pwd,
    code: [
      { required: true, message: $t('page.login.common.codePlaceholder'), trigger: 'blur' },
      { min: 4, max: 4, message: $t('page.login.common.codeLengthError'), trigger: 'blur' }
    ]
  };
});

async function refreshCode() {
  loading.value = true;
  try {
    const { data } = await fetchLoginCode();
    if (data) {
      captchaCode.value = data.code;
      model.value.uuid = data.uuid;
    }
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  refreshCode();
});

async function handleSubmit() {
  await validate();
  await authStore.login(model.value.userName, model.value.password, model.value.code, model.value.uuid);
  refreshCode();
}

// type AccountKey = 'super' | 'admin' | 'user';

// interface Account {
//   key: AccountKey;
//   label: string;
//   userName: string;
//   password: string;
// }

// const accounts = computed<Account[]>(() => [
//   {
//     key: 'super',
//     label: $t('page.login.pwdLogin.superAdmin'),
//     userName: 'Super',
//     password: '123456'
//   },
//   {
//     key: 'admin',
//     label: $t('page.login.pwdLogin.admin'),
//     userName: 'Admin',
//     password: '123456'
//   },
//   {
//     key: 'user',
//     label: $t('page.login.pwdLogin.user'),
//     userName: 'User',
//     password: '123456'
//   }
// ]);

// async function handleAccountLogin(account: Account) {
//   await authStore.login(account.userName, account.password);
// }
</script>

<template>
  <ElForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="handleSubmit">
    <ElFormItem prop="userName">
      <ElInput v-model="model.userName" :placeholder="$t('page.login.common.userNamePlaceholder')" />
    </ElFormItem>
    <ElFormItem prop="password">
      <ElInput
        v-model="model.password"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.passwordPlaceholder')"
      />
    </ElFormItem>
    <ElFormItem prop="code">
      <div class="w-full flex gap-2">
        <ElInput v-model="model.code" :placeholder="$t('page.login.common.codePlaceholder')" class="flex-1" />
        <div class="captcha-container">
          <div class="captcha-code" :class="{ 'is-loading': loading }" @click="refreshCode" v-html="captchaCode" />
          <ElIcon v-if="loading" class="loading-icon" :size="20"><Loading /></ElIcon>
        </div>
      </div>
    </ElFormItem>
    <ElSpace direction="vertical" :size="24" class="w-full" fill>
      <div class="flex-y-center justify-between">
        <ElCheckbox>{{ $t('page.login.pwdLogin.rememberMe') }}</ElCheckbox>
        <ElButton text @click="toggleLoginModule('reset-pwd')">
          {{ $t('page.login.pwdLogin.forgetPassword') }}
        </ElButton>
      </div>
      <ElButton type="primary" size="large" round block :loading="authStore.loginLoading" @click="handleSubmit">
        {{ $t('common.confirm') }}
      </ElButton>

      <!--
 <ElDivider class="text-14px text-#666 !m-0">{{ $t('page.login.pwdLogin.otherAccountLogin') }}</ElDivider>
      <div class="flex-center gap-12px">
        <ElButton
          v-for="item in accounts"
          :key="item.key"
          size="default"
          type="primary"
          @click="handleAccountLogin(item)"
        >
          {{ item.label }}
        </ElButton>
      </div> 
-->
    </ElSpace>
  </ElForm>
</template>

<style scoped>
.captcha-container {
  position: relative;
  flex-shrink: 0;
  width: auto;
}

.captcha-code {
  height: 40px;
  width: auto;
  min-width: 90px;
  padding: 0 12px;
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.captcha-code:hover {
  background: #e6e8eb;
  border-color: #c0c4cc;
}

.captcha-code:active {
  background: #dcdfe6;
}

.captcha-code.is-loading {
  cursor: not-allowed;
  opacity: 0.7;
}

.loading-icon {
  position: absolute;
  color: #409eff;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>

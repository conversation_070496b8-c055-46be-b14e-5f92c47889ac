<script setup lang="tsx">
import { ref } from 'vue';
import { cleanOperatorLog, fetchOperatorLogPage } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import TableHeaderOperation from './modules/table-header-operation.vue';

const businessTypeMap: Record<number, string> = {
  0: '其它',
  1: '新增',
  2: '修改',
  3: '删除',
  4: '授权',
  5: '导出',
  6: '导入',
  7: '强退',
  8: '生成代码',
  9: '清空数据',
  10: '查询'
};

const wrapperRef = ref<HTMLElement | null>(null);

const { columns, columnChecks, data, loading, pagination, getData } = useTable({
  apiFn: fetchOperatorLogPage,
  apiParams: {
    current: 1,
    size: 10
  },
  columns: () => [
    { type: 'selection', width: 48 },
    { prop: 'index', label: $t('common.index'), width: 64 },
    {
      prop: 'businessType',
      label: '操作类型',
      width: 120,
      formatter: (row: any) => businessTypeMap[row.businessType] || '未知'
    },
    { prop: 'method', label: '方法名称', minWidth: 150, showOverflowTooltip: true },
    { prop: 'requestMethod', label: '请求方式', width: 100 },
    { prop: 'description', label: '操作描述', minWidth: 150, showOverflowTooltip: true },
    { prop: 'reqIp', label: '请求IP', width: 120 },
    { prop: 'operName', label: '操作人', width: 120 },
    { prop: 'operLocation', label: '操作地点', width: 120 },
    { prop: 'reqParam', label: '请求参数', minWidth: 150, showOverflowTooltip: true },
    { prop: 'resp', label: '响应信息', minWidth: 150, showOverflowTooltip: true },
    { prop: 'errorMsg', label: '错误信息', minWidth: 150, showOverflowTooltip: true },
    { prop: 'status', label: '状态', width: 100 },
    { prop: 'createTime', label: '创建时间', width: 180 },
    { prop: 'updateTime', label: '更新时间', width: 180 }
  ]
});

async function handleClean() {
  await cleanOperatorLog();
  window.$message?.success($t('common.cleanSuccess'));
  getData();
}
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :loading="loading"
            @clean="handleClean"
            @refresh="getData"
          />
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable v-loading="loading" height="100%" border class="sm:h-full" :data="data" row-key="id">
          <ElTableColumn
            v-for="col in columns"
            :key="col.prop"
            v-bind="col"
            :show-overflow-tooltip="col.showOverflowTooltip"
            :fixed="col.fixed"
          />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>

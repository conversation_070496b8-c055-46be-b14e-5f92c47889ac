package org.seven.share.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * @ClassName: SignUtil
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/4
 */
public class SignUtil {
    public SignUtil() {
    }

    public static String map2Sign(Map<String, String> params, String appKey) {
        // 将参数按照参数名ASCII码从小到大排序，并拼接商户密钥生成签名
        Map<String, String> sortedParams = new TreeMap<>(params);
        String paramString = sortedParams.entrySet().stream()
                .filter(entry -> entry.getValue() != null && !entry.getValue().isEmpty())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        paramString += appKey;
        return SecureUtil.md5(paramString);
    }


    /**
     * 创建虎皮椒签名信息 待签名信息：key1=value2&key2=value2+appKey
     * @param params 请求参数
     * @param appKey 商户密钥
     * @return 签名后的信息
     */
    public static String createXunHuSign(Map<String, Object> params, String appKey) {
        StringBuilder sb = new StringBuilder();
        // 将HashMap 进行 键 的 Ascll 从小到大排序 并 将每个 hashmap元素 以 & 拼接起来
        params.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEach(a ->{
            sb.append(a).append("&");});
        System.out.println("排序后的参数:" + params);
        // 去除 最后一位的 &
        sb.deleteCharAt(sb.length()-1);
        // 拼接上密钥
        sb.append(appKey);
        // 拼接上appKey
        return SecureUtil.md5(sb.toString());
    }

    /**
     * 获取支付宝的请求参数,用,连接参数
     * @param request
     * @return
     */
    public static Map<String, String> getAliPayRequestParams(HttpServletRequest request) {
        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (String key : requestParams.keySet()) {
            String[] values = requestParams.get(key);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                        : valueStr + values[i] + ",";
            }
            params.put(key, valueStr);
        }
        return params;
    }

    /**
     * 蓝兔支付参数处理，包含排序和key=value&的形式拼接
     * @param params
     * @return
     */
    public static String packageSign(Map<String, String> params) {
        // 先将参数以其参数名的字典序升序进行排序
        TreeMap<String, String> sortedParams = new TreeMap<>(params);
        // 遍历排序后的字典，将所有参数按"key=value"格式拼接在一起
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            if (StrUtil.isNotEmpty(entry.getValue())) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }
        return sb.deleteCharAt(sb.length() - 1).toString();
    }

    /**
     * 蓝兔支付生成签名
     * @param params
     * @param appKey
     * @return
     */
    public static String createLanTuSign(Map<String, String> params, String appKey) {
        params.remove("sign");
        String stringA = packageSign(params);
        String stringSignTemp = stringA + "&key=" + appKey;
        System.out.println("加密前的数据：" + stringSignTemp);
        return SecureUtil.md5(stringSignTemp).toUpperCase();
    }
}

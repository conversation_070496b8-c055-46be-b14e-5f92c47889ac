package org.seven.share.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.pojo.entity.SysOperationLog;
import org.seven.share.common.pojo.entity.SysRole;
import org.seven.share.mapper.SysOperationLogMapper;
import org.seven.share.service.SysOperationLogService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class SysOperationLogServiceImpl extends ServiceImpl<SysOperationLogMapper, SysOperationLog>
        implements SysOperationLogService {

    @Override
    public int clean() {
        return this.baseMapper.clean();
    }

    @Override
    public List<SysOperationLog> getExportList(SysOperationLog sysOperationLog) {
        return null;
    }

    @Override
    public IPage<SysOperationLog> getPage(Map<String, Object> params) {
        int pageSize = Integer.parseInt(String.valueOf(params.get("size")));
        int pageNum = Integer.parseInt(String.valueOf(params.get("current")));
        return page(new Page<>(pageNum, pageSize));
    }
}

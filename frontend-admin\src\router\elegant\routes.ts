/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'about',
    path: '/about',
    component: 'layout.base$view.about',
    meta: {
      title: 'about',
      i18nKey: 'route.about',
      icon: 'fluent:book-information-24-regular',
      order: 10
    }
  },
  {
    name: 'account',
    path: '/account',
    component: 'layout.base',
    meta: {
      title: 'account',
      i18nKey: 'route.account',
      icon: 'mdi-light:plus-box',
      order: 4
    },
    children: [
      {
        name: 'account_claude',
        path: '/account/claude',
        component: 'view.account_claude',
        meta: {
          title: 'account_claude',
          i18nKey: 'route.account_claude'
        }
      },
      {
        name: 'account_grok',
        path: '/account/grok',
        component: 'view.account_grok',
        meta: {
          title: 'account_grok',
          i18nKey: 'route.account_grok'
        }
      },
      {
        name: 'account_openai',
        path: '/account/openai',
        component: 'view.account_openai',
        meta: {
          title: 'account_openai',
          i18nKey: 'route.account_openai'
        }
      }
    ]
  },
  {
    name: 'draw',
    path: '/draw',
    component: 'layout.base',
    meta: {
      title: 'draw',
      i18nKey: 'route.draw'
    },
    children: [
      {
        name: 'draw_quota-change',
        path: '/draw/quota-change',
        component: 'view.draw_quota-change',
        meta: {
          title: 'draw_quota-change',
          i18nKey: 'route.draw_quota-change'
        }
      },
      {
        name: 'draw_quota-manage',
        path: '/draw/quota-manage',
        component: 'view.draw_quota-manage',
        meta: {
          title: 'draw_quota-manage',
          i18nKey: 'route.draw_quota-manage'
        }
      },
      {
        name: 'draw_record',
        path: '/draw/record',
        component: 'view.draw_record',
        meta: {
          title: 'draw_record',
          i18nKey: 'route.draw_record'
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'manage',
    path: '/manage',
    component: 'layout.base',
    meta: {
      title: 'manage',
      i18nKey: 'route.manage',
      icon: 'carbon:cloud-service-management',
      order: 9,
      roles: ['SUPBER_ADMIN']
    },
    children: [
      {
        name: 'manage_log',
        path: '/manage/log',
        component: 'view.manage_log',
        meta: {
          title: 'manage_log',
          i18nKey: 'route.manage_log'
        }
      },
      {
        name: 'manage_menu',
        path: '/manage/menu',
        component: 'view.manage_menu',
        meta: {
          title: 'manage_menu',
          i18nKey: 'route.manage_menu',
          icon: 'material-symbols:route',
          order: 3,
          roles: ['SUPBER_ADMIN'],
          keepAlive: true
        }
      },
      {
        name: 'manage_role',
        path: '/manage/role',
        component: 'view.manage_role',
        meta: {
          title: 'manage_role',
          i18nKey: 'route.manage_role',
          icon: 'carbon:user-role',
          order: 2,
          roles: ['SUPBER_ADMIN']
        }
      },
      {
        name: 'manage_user',
        path: '/manage/user',
        component: 'view.manage_user',
        meta: {
          title: 'manage_user',
          i18nKey: 'route.manage_user',
          icon: 'ic:round-manage-accounts',
          order: 1,
          roles: ['SUPBER_ADMIN']
        }
      },
      {
        name: 'manage_user-detail',
        path: '/manage/user-detail/:id',
        component: 'view.manage_user-detail',
        props: true,
        meta: {
          title: 'manage_user-detail',
          i18nKey: 'route.manage_user-detail',
          hideInMenu: true,
          roles: ['SUPBER_ADMIN'],
          activeMenu: 'manage_user'
        }
      }
    ]
  },
  {
    name: 'marketing',
    path: '/marketing',
    component: 'layout.base',
    meta: {
      title: 'marketing',
      i18nKey: 'route.marketing',
      icon: 'hugeicons:marketing',
      order: 5
    },
    children: [
      {
        name: 'marketing_cdkey',
        path: '/marketing/cdkey',
        component: 'view.marketing_cdkey',
        meta: {
          title: 'marketing_cdkey',
          i18nKey: 'route.marketing_cdkey'
        }
      },
      {
        name: 'marketing_coupon',
        path: '/marketing/coupon',
        component: 'view.marketing_coupon',
        meta: {
          title: 'marketing_coupon',
          i18nKey: 'route.marketing_coupon'
        }
      },
      {
        name: 'marketing_notice',
        path: '/marketing/notice',
        component: 'view.marketing_notice',
        meta: {
          title: 'marketing_notice',
          i18nKey: 'route.marketing_notice'
        }
      },
      {
        name: 'marketing_withdrawals',
        path: '/marketing/withdrawals',
        component: 'view.marketing_withdrawals',
        meta: {
          title: 'marketing_withdrawals',
          i18nKey: 'route.marketing_withdrawals'
        }
      }
    ]
  },
  {
    name: 'order',
    path: '/order',
    component: 'layout.base',
    meta: {
      title: 'order',
      i18nKey: 'route.order',
      icon: 'mage:arrowlist',
      order: 6
    },
    children: [
      {
        name: 'order_info',
        path: '/order/info',
        component: 'view.order_info',
        meta: {
          title: 'order_info',
          i18nKey: 'route.order_info'
        }
      },
      {
        name: 'order_payconfig',
        path: '/order/payconfig',
        component: 'view.order_payconfig',
        meta: {
          title: 'order_payconfig',
          i18nKey: 'route.order_payconfig'
        }
      }
    ]
  },
  {
    name: 'platform',
    path: '/platform',
    component: 'layout.base',
    meta: {
      title: 'platform',
      i18nKey: 'route.platform',
      icon: 'ep:platform',
      order: 7
    },
    children: [
      {
        name: 'platform_claude-topic',
        path: '/platform/claude-topic',
        component: 'view.platform_claude-topic',
        meta: {
          title: 'platform_claude-topic',
          i18nKey: 'route.platform_claude-topic'
        }
      },
      {
        name: 'platform_grok-topic',
        path: '/platform/grok-topic',
        component: 'view.platform_grok-topic',
        meta: {
          title: 'platform_grok-topic',
          i18nKey: 'route.platform_grok-topic'
        }
      },
      {
        name: 'platform_riskcontrol',
        path: '/platform/riskcontrol',
        component: 'view.platform_riskcontrol',
        meta: {
          title: 'platform_riskcontrol',
          i18nKey: 'route.platform_riskcontrol'
        }
      },
      {
        name: 'platform_sysconfig',
        path: '/platform/sysconfig',
        component: 'view.platform_sysconfig',
        meta: {
          title: 'platform_sysconfig',
          i18nKey: 'route.platform_sysconfig'
        }
      },
      {
        name: 'platform_topic',
        path: '/platform/topic',
        component: 'view.platform_topic',
        meta: {
          title: 'platform_topic',
          i18nKey: 'route.platform_topic',
          icon: 'material-symbols-light:chat-outline'
        }
      },
      {
        name: 'platform_word',
        path: '/platform/word',
        component: 'view.platform_word',
        meta: {
          title: 'platform_word',
          i18nKey: 'route.platform_word'
        }
      }
    ]
  },
  {
    name: 'subtype',
    path: '/subtype',
    component: 'layout.base$view.subtype',
    meta: {
      title: 'subtype',
      i18nKey: 'route.subtype',
      order: 3,
      icon: 'mdi:picture'
    }
  },
  {
    name: 'tenant',
    path: '/tenant',
    component: 'layout.base$view.tenant',
    meta: {
      title: 'tenant',
      i18nKey: 'route.tenant',
      order: 4,
      icon: 'mdi:account-group'
    }
  },
  {
    name: 'user',
    path: '/user',
    component: 'layout.base$view.user',
    meta: {
      title: 'user',
      i18nKey: 'route.user',
      order: 2,
      icon: 'mdi:user'
    }
  },
  {
    name: 'user-center',
    path: '/user-center',
    component: 'layout.base$view.user-center',
    meta: {
      title: 'user-center',
      i18nKey: 'route.user-center',
      hideInMenu: true
    }
  }
];

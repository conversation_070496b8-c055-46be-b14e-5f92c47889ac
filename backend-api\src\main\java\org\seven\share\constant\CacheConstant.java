package org.seven.share.constant;

/**
 * @ClassName: CacheConstant
 * @Description:
 * @Author: Seven
 * @Date: 2025/1/20
 */
public class CacheConstant {
    public static final String VERIFICATION_CODE_PREFIX = "verification_code:";

    public static final String USER_LIMIT_PREFIX = "user:rate-limit:";

    public static final String USER_CLAUDE_LIMIT_PREFIX = "user:claude-rate-limit:";

    public static final String USER_GROK_LIMIT_PREFIX = "user:grok-rate-limit:";

    public static final String USER_SESSION_PREFIX = "user_sessions:";

    public static final String USER_GPT_USAGE = "user:gpt-usage:";

    public static final String REDIS_SENSITIVE_WORD_KEY = "sensitive_words";

    public static final String TODAY_NORMAL_USAGE = "user:today-normal";

    public static final String TODAY_ADVANCE_USAGE = "user:today-advance";

    public static final String TODAY_CLAUDE_USAGE = "user:today-claude";

    public static final String TODAY_VISITOR_USAGE = "user:today-visitor";

    public static final String LOGIN_ATTEMPTS = "login_attempts:";

    public static final String LOGIN_LOCK = "login_lock:";

    public static final String USER_SESSIONS = "user:sessions:";

    public static final String REDEMPTION_CODE_LOCK = "redemption_code_lock:";

    public static final String CONFIG_KEY_PREFIX = "system:config:";

    public static final String TASK_PREFIX = "task:";

    public static final String TASK_KEY_PATTERN = "task:*";

    public static final String CAPTCHA_CODE_KEY = "captcha_code:";
}

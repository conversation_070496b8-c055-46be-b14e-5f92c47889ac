package org.seven.share.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.R;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.entity.ChatGptPayConfigEntity;
import org.seven.share.service.ChatGptPayConfigService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: ChatGptPayConfigController
 * @Description: 支付管理控制器
 * @Author: Seven
 * @Date: 2024/7/12
 */
@RestController
@RequestMapping("/expander-api/pay")
public class ChatGptPayConfigController {

    @Resource
    private ChatGptPayConfigService chatGptPayConfigService;

    @GetMapping("/page")
    @SysLogInterface(title = "分页查询支付配置", businessType = BusinessType.QUERY)
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer current,
                  @RequestParam(value = "size", defaultValue = "10") Integer size){
        LambdaQueryWrapper<ChatGptPayConfigEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(ChatGptPayConfigEntity::getUpdateTime);
        Page<ChatGptPayConfigEntity> pageInfo = chatGptPayConfigService.page(new Page<>(current,size), queryWrapper);
        return R.ok(pageInfo);
    }

    @PostMapping("/add")
    @SysLogInterface(title = "新增支付配置", businessType = BusinessType.INSERT)
    public R save(@RequestBody ChatGptPayConfigEntity config){
        chatGptPayConfigService.savePayConfig(config);
        return R.ok();
    }

    @PostMapping("/update")
    @SysLogInterface(title = "修改支付配置", businessType = BusinessType.UPDATE)
    @CacheEvict(cacheNames = "pay-config", allEntries = true)
    public R update(@RequestBody ChatGptPayConfigEntity config){
        chatGptPayConfigService.updateById(config);
        return R.ok();
    }

    @DeleteMapping("/delete")
    @CacheEvict(cacheNames = "pay-config", allEntries = true)
    @SysLogInterface(title = "删除支付配置", businessType = BusinessType.DELETE)
    public R delete(@RequestBody List<String> ids){
        return chatGptPayConfigService.removeBatchByIds(ids) ? R.ok() : R.error();
    }
}

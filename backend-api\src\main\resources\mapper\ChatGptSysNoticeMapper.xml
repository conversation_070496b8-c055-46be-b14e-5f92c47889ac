<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.seven.share.mapper.ChatGptSysNoticeMapper">
    <select id="selectNoticePage" resultType="org.seven.share.common.pojo.entity.ChatGptSysNoticeEntity">
        select n.*,
               case n.tenant_id when '000000' then '主站'
               else t.tenant_name
               end as tenant_name from chatgpt_sys_notice n
        left join t_sys_tenant t
        on n.tenant_id = t.id
        <where>
            <if test="id != null and id != ''">
                n.id = #{id}
            </if>
        </where>
        order by updateTime desc;
    </select>
</mapper>

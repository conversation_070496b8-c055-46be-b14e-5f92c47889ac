<template>
  <div style="border: 1px solid #ccc">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <Editor
      style="height: 500px; overflow-y: hidden;"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="mode"
      @onCreated="handleCreated"
    />
  </div>
</template>

<script>
import '@wangeditor/editor/dist/css/style.css'
import { onBeforeUnmount, ref, shallowRef, onMounted, watch } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

export default {
  props: {
    modelValue: {
      type: String,
      default: ''
    }
  },
  components: { Editor, Toolbar },
  setup(props, { emit }) {
    const editorRef = shallowRef()
    const valueHtml = ref(props.modelValue) // 初始化时使用外部传递的值

    const toolbarConfig = {}
    const editorConfig = { placeholder: '请输入内容...' }

    onBeforeUnmount(() => {
      const editor = editorRef.value
      if (editor == null) return
      editor.destroy()
    })

    const handleCreated = (editor) => {
      editorRef.value = editor
    }

    // 监听 valueHtml 变化，并将值同步到父组件
    watch(valueHtml, (newValue) => {
      emit('update:modelValue', newValue) // 同步到父组件
    })

    // 监听 props 变化，以便从父组件接收更新
    watch(() => props.modelValue, (newVal) => {
      valueHtml.value = newVal
    })

    return {
      editorRef,
      valueHtml,
      mode: 'default',
      toolbarConfig,
      editorConfig,
      handleCreated
    }
  }
}
</script>

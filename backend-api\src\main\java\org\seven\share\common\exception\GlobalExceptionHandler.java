package org.seven.share.common.exception;

import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.api.R;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @ClassName: GloableExceptionHandler
 * @Description:
 * @Author: Seven
 * @Date: 2024/6/22
 */
@Slf4j
@ControllerAdvice
@ResponseBody
public class GlobalExceptionHandler {
    /***
     * 全局处理自定义业务异常类
     * @param e
     * @return
     */
    @ExceptionHandler({CustomException.class})
    public R handleCustomException(CustomException e){
        log.error("业务系统异常：",e);
        return R.error(500, e.getMessage());
    }

    @ExceptionHandler(ServiceException.class)
    public R handleServiceException(ServiceException e) {
        log.error("服务异常", e);
        return R.error(e.getStatusCode(), e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public R handleGeneralException(Exception e){
        log.error("系统异常：",e);
        return R.error(500, "系统繁忙，请稍后重试");
    }

    /**
     * 拦截validation框架抛出的异常处理
     * @param e
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        StringBuilder stringBuilder = new StringBuilder();
        e.getBindingResult().getAllErrors().forEach(error -> {
            String errorMessage = error.getDefaultMessage();
            stringBuilder.append(errorMessage);
        });
        return R.error(500, stringBuilder.toString());
    }

}

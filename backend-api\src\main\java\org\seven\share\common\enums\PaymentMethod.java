package org.seven.share.common.enums;

import lombok.Getter;

/**
 * @ClassName: PaymentMethod
 * @Description: 支付方式枚举
 * @Author: Seven
 * @Date: 2024/9/5
 */
@Getter
public enum PaymentMethod {
    YI_PAY_ALIPAY(1, "易支付支付"),
    YI_PAY_WECHAT(2, "易支付微信"),
    F2F_PAY(3, "当面付"),
    HU_PI_JIAO_WECHAT(4, "虎皮椒微信"),
    HU_PI_JIAO_ALIPAY(8, "虎皮椒支付宝"),
    LAN_TU_PAY_WECHAT(5, "蓝兔微信支付"),
    ALIPAY_WAP_PAY(9, "支付宝网站支付"),
    NATIVE_WECHAT(7, "微信native");

    // 获取编号
    private final int code;
    // 获取描述
    private final String description;

    // 构造方法
    PaymentMethod(int code, String description) {
        this.code = code;
        this.description = description;
    }

    // 根据编号获取支付方式
    public static PaymentMethod fromCode(int code) {
        for (PaymentMethod method : PaymentMethod.values()) {
            if (method.getCode() == code) {
                return method;
            }
        }
        throw new IllegalArgumentException("Invalid payment method code: " + code);
    }

    @Override
    public String toString() {
        return code + ": " + description;
    }
}

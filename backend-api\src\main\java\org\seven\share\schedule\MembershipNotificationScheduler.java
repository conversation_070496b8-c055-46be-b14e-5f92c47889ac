package org.seven.share.schedule;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.common.util.DateTimeUtil;
import org.seven.share.service.ChatGptConfigService;
import org.seven.share.service.ChatGptUserService;
import org.seven.share.service.EmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static org.seven.share.common.util.ConstantUtil.MEMBERSHIP_EXPIRATION;

/**
 * @ClassName: MembershipNotificationScheduler
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/13
 */
@Slf4j
@Component
public class MembershipNotificationScheduler {

    @Resource
    private ChatGptUserService chatGptUserService;

    @Resource
    private EmailService emailService;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Autowired
    @Qualifier("commonAsyncExecutor")
    private Executor commonAsyncExecutor;

    /**
     * 每天9点发送会员3天后到期的邮件提醒
     * 邮箱不能为空,未删除的用户才通知
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void notifyMembershipExpiration() {
        LocalDate localDate = LocalDate.now().plusDays(3L);
        List<ChatGptUserEntity> list = chatGptUserService.list(new QueryWrapper<ChatGptUserEntity>()
                .apply("DATE(plusExpireTime) = {0}", localDate)
                .isNotNull("email")
                .isNull("deleted_at"));
        if (CollectionUtil.isNotEmpty(list)) {
            String siteName = chatGptConfigService.getValueByKey("siteName");
            list.forEach(e -> {
                log.info("用户{}（邮箱：{}）的会员即将到期", e.getUserToken(), e.getEmail());
                Map<String, String> contentMap = new HashMap<>();
                contentMap.put("userName", e.getUserToken());
                contentMap.put("expirationDate", DateTimeUtil.formatDateTime(e.getPlusExpireTime()));
                contentMap.put("siteName", siteName);
                // 异步发送邮件
                CompletableFuture.runAsync(() -> {
                    try{
                        emailService.sendEmail(e.getEmail(), MEMBERSHIP_EXPIRATION, "会员即将到期", contentMap);
                    }
                    catch (Exception ex){
                        log.error("发送邮件失败", ex);
                    }
                }, commonAsyncExecutor);
            });
        }
    }
}

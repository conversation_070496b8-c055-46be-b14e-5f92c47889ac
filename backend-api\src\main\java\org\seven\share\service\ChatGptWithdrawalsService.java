package org.seven.share.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.pojo.dto.ApplyWithdrawalDto;
import org.seven.share.common.pojo.entity.ChatGptWithdrawalsEntity;
import org.seven.share.common.pojo.vo.InviteDetailsVo;

import java.util.List;
import java.util.Map;

/**
 * @InterfaceName: ChatGptWithdrawalsService
 * @Description:
 * @Author: Seven
 * @Date: 2024/10/15
 */
public interface ChatGptWithdrawalsService extends IService<ChatGptWithdrawalsEntity> {
    void applyWithdrawal(ApplyWithdrawalDto dto);

    void approvalWithdrawal(String userId);

    void rejectWithdrawal(String id, String reason);

    List<InviteDetailsVo> getInviteDetails(String id);

    Map<String, Integer> getToDoData();
}

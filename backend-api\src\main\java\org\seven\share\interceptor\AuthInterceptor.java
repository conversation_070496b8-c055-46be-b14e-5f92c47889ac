package org.seven.share.interceptor;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.util.AESUtil;
import org.seven.share.common.util.LicenseUtil;
import org.seven.share.service.ChatGptConfigService;
import org.seven.share.service.LicenseValidator;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * @ClassName: AuthInterceptor
 * @Description: 授权拦截器
 * @Author: Seven
 * @Date: 2025/5/12
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthInterceptor implements HandlerInterceptor {

    final LicenseValidator licenseValidator;
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            // 验证授权
            licenseValidator.isValid();
            return true;
        } catch (Exception e) {
            log.error("License validation failed: {}", e.getMessage());
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            response.setContentType("application/json;charset=utf-8");
            response.getWriter().write("{\"error\":\"" + e.getMessage() + "\"}");
            return false;
        }
    }
}

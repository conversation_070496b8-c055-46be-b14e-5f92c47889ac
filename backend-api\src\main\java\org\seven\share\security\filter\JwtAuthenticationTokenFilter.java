package org.seven.share.security.filter;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.enums.CacheConstants;
import org.seven.share.common.exception.ServiceException;
import org.seven.share.common.pojo.entity.SysUser;
import org.seven.share.common.util.IpUtil;
import org.seven.share.common.util.JwtTokenUtil;
import org.seven.share.common.util.SecurityUtil;
import org.seven.share.common.util.SysUserDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;

/**
 * Jwt 过滤器
 */
@Slf4j
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        long startTime, endTime;
        // 如果是前台需要认证token的路由，则只校验token是否过期，不需要获取权限数据
        String requestURI = request.getRequestURI();
        if (requestURI.startsWith("/client-api/") || requestURI.startsWith("/api")) {
            // 如果是前台请求，则交给其他拦截器处理
            filterChain.doFilter(request, response);
            return;
        }
        String authHeader = request.getHeader(jwtTokenUtil.getTokenHeader());
        String username = null;

        if (authHeader != null && authHeader.startsWith(jwtTokenUtil.getTokenHead())) {
            // The part after "Bearer "
            String authToken = authHeader.substring(jwtTokenUtil.getTokenHead().length());

            // 页面上传过来的token 解析出来的username
            username = jwtTokenUtil.getUserNameFromToken(authToken);


            if (username != null
                    && SecurityContextHolder.getContext().getAuthentication() == null) {
                // 获取用户信息
                UserDetails userDetails = this.userDetailsService.loadUserByUsername(username);
                if (jwtTokenUtil.validateToken(authToken, userDetails)) {

                    UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                            userDetails, null, userDetails.getAuthorities());

                    authentication.setDetails(
                            new WebAuthenticationDetailsSource().buildDetails(request));
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                } else {
                    throw new ServiceException(401, "token已过期");
                }
            }
        }

        startTime = System.currentTimeMillis();

        filterChain.doFilter(request, response);

        endTime = System.currentTimeMillis();

        String fullUrl = request.getRequestURL().toString();
        String requestType = request.getMethod();
        formMapKey(username, fullUrl, requestType, IpUtil.getIp(request), authHeader,
                endTime, startTime);

    }

    /**
     * @param methodName  请求接口
     * @param requestType 请求类型
     * @param ip          请求IP
     * @param token       请求token
     */
    private void formMapKey(String username, String methodName, String requestType,
                            String ip, String token, long endTime, long startTime) {
        log.info(
                "接口信息： url:{} | type:{} | ip:{} | cost:{}ms\n",
                methodName, requestType, ip,
                (endTime - startTime));
    }

}

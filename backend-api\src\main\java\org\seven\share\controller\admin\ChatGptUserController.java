package org.seven.share.controller.admin;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.seven.share.common.annotation.RateLimit;
import org.seven.share.common.api.R;
import org.seven.share.common.api.Result;
import org.seven.share.common.pojo.dto.*;

import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.common.pojo.vo.CarInfoVo;
import org.seven.share.common.pojo.vo.StatisticVo;
import org.seven.share.service.ChatGptUserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: UserTokenController
 * @Description: 用户管理控制器
 * @Author: Seven
 * @Date: 2024/6/22
 */
@RestController
@RequestMapping("/expander-api/user")
public class ChatGptUserController {

    @Resource
    private ChatGptUserService chatGptUserService;


    /**
     * 后台新增用户
     * @param userDto 用户名
     * @return 结果
     */
    @PostMapping("/create")
    public R create(@RequestBody @Validated UserDto userDto) {
        return R.ok(chatGptUserService.createUser(userDto));
    }

    /**
     * 分页查询用户
     * 通过分页和查询条件来获取用户列表
     *
     * @param current  当前页码，默认为1
     * @param size  每页显示数量，默认为10
     * @param query 查询关键字，可以是用户令牌或邮箱
     * @return 分页数据，包括总页数、总记录数和当前页数据
     */
    @GetMapping("/page")
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer current,
                  @RequestParam(value = "size", defaultValue = "10") Integer size,
                  String query,
                  String sortProp,
                  String sortOrder){
       IPage<UserDto> pageInfo = chatGptUserService.selectPage(current, size, query, sortOrder, sortProp);
        return R.ok(pageInfo);
    }

    /**
     * 批量删除
     * @param ids id集合
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    public R delete(@RequestBody List<Long> ids){
        chatGptUserService.removeBatchByIds(ids);
        return R.ok();
    }

    /**
     * 修改用户信息
     * @param userDto 用户
     * @return 修改结果
     */
    @PutMapping("/update")
    public R update(@RequestBody @Validated UserDto userDto){
        chatGptUserService.updateUser(userDto);
        return R.ok();
    }

    @PutMapping("/status")
    public R changeStatus(@RequestParam("id") Long id, @RequestParam("isPlus") Integer isPlus){
        chatGptUserService.updateStatusById(id, isPlus);
        return R.ok();
    }

    @PutMapping("/changeStatus")
    public Result<?> changeSwitch(@RequestParam("id") Long id, @RequestParam("status") Integer status){
        UpdateWrapper<ChatGptUserEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.set("status", status);
        chatGptUserService.update(updateWrapper);
        return Result.success();
    }

    @GetMapping("/statistic")
    public R statistic () {
        StatisticVo statisticData = chatGptUserService.getStatisticData();
        return R.ok(statisticData);
    }

    @GetMapping("/dataSync")
    public R dataSync(){
        chatGptUserService.updateExpireTimeForPlusUsers();
        return R.ok();
    }


    /**
     * 补偿用户时长
     * @param type 补偿类型
     * @param minutes 补偿分钟数
     * @return 结果
     */
    @PostMapping("/compensate")
    public R compensateTime(@RequestParam String type, @RequestParam Long minutes){
        chatGptUserService.compensateTime(type, minutes);
        return R.ok();
    }

    @PostMapping("/batchCreate")
    public R batchCreateUser(@RequestBody BatchUserDto dto){
        List<ChatGptUserEntity> list= chatGptUserService.batchCreateUser(dto);
        List<String> users = list.stream().map(ChatGptUserEntity::getUserToken).toList();
        return R.ok(users);
    }

    @GetMapping("/claudeDataSync")
    public R claudeDataSync(){
        chatGptUserService.updateBatchUserExpireTimeTimeForPlusUsers();
        return R.ok();
    }

    @PostMapping("/reset-pwd")
    public R resetPwd(@RequestParam("id") Long id){
        chatGptUserService.resetPwd(id);
        return R.ok();
    }

}

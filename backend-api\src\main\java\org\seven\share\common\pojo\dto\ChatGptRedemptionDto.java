package org.seven.share.common.pojo.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.seven.share.common.pojo.entity.ChatGptRedemptionEntity;

import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: ChatGptRedemptionDto
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChatGptRedemptionDto extends ChatGptRedemptionEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String userToken;

    private String name;

    private String validDays;

    private int isPro;
}

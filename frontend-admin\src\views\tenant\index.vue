<script setup lang="tsx">
import { ref } from 'vue';
import type { Ref } from 'vue';
import { useBoolean } from '@sa/hooks';
import { ElButton, ElImage, ElPopconfirm, ElTag } from 'element-plus';
import { usePermission } from '@/hooks/business/auth';
import { fetchDeleteTenantByIds, fetchDeleteTenants, fetchGetTenantPage } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import { enableStatusRecord } from '@/constants/business';
import TenantSearch from './modules/tenant-search.vue';
import TenantOperateModal, { type OperateType } from './modules/tenant-operate-modal.vue';
const { bool: visible, setTrue: openModal } = useBoolean();
const operateType = ref<OperateType>('add');
const editingData: Ref<Api.TenantManage.Tenant | null> = ref(null);
const { hasPermission } = usePermission();
const { columns, columnChecks, data, loading, pagination, getData, getDataByPage, searchParams, resetSearchParams } =
  useTable({
    apiFn: fetchGetTenantPage,
    showTotal: true,
    apiParams: {
      current: 1,
      size: 10,
      tenantId: undefined,
      tenantName: undefined,
      domain: undefined
    },
    columns: () => [
      { type: 'selection', width: 48 },
      { prop: 'id', label: 'ID', width: 90 },
      { prop: 'tenantId', label: $t('page.tenant.tenantId'), minWidth: 120 },
      { prop: 'tenantName', label: $t('page.tenant.tenantName'), minWidth: 150 },
      {
        prop: 'domain',
        label: $t('page.tenant.domain'),
        minWidth: 200,
        cellClassName: 'break-word',
        showOverflowTooltip: {
          effect: 'light',
          placement: 'top-start'
        }
      },
      { prop: 'contactUserName', label: $t('page.tenant.contactUserName'), minWidth: 120 },
      { prop: 'commissionBalance', label: $t('page.tenant.commissionBalance'), minWidth: 120 },
      { prop: 'commissionRatio', label: $t('page.tenant.commissionRatio'), minWidth: 120 },
      {
        prop: 'siteLogo',
        label: $t('page.tenant.siteLogo'),
        width: 200,
        align: 'center',
        formatter: (row: any) => (
          <ElImage
            style={{ width: '100px', height: '100px' }}
            src={row.siteLogo}
            preview-src-list={[row.siteLogo]}
            zoom-rate={1.2}
            max-scale={7}
            min-scale={0.2}
            preview-teleported={true}
            fit="cover"
          />
        )
      },
      { prop: 'siteName', label: $t('page.tenant.siteName'), minWidth: 120 },
      {
        prop: 'expireTime',
        label: $t('page.tenant.expireTime'),
        width: 150,
        formatter: row => {
          const date = new Date(row.expireTime);
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          const seconds = String(date.getSeconds()).padStart(2, '0');
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
      },
      {
        prop: 'status',
        label: $t('page.tenant.status'),
        width: 100,
        formatter: row => {
          if (row.status === undefined) {
            return '';
          }

          const tagMap: Record<Api.Common.EnableStatus, UI.ThemeColor> = {
            1: 'success',
            2: 'warning'
          };

          const label = $t(enableStatusRecord[row.status]);

          return <ElTag type={tagMap[row.status]}>{label}</ElTag>;
        }
      },
      {
        prop: 'operate',
        label: $t('common.operate'),
        width: 130,
        align: 'center',
        fixed: 'right',
        formatter: row => (
          <div class="flex-center justify-end pr-10px">
            {hasPermission('tenant/update') && (
              <ElButton type="primary" plain size="small" onClick={() => handleEdit(row)}>
                {$t('common.edit')}
              </ElButton>
            )}
            <ElPopconfirm title={$t('common.confirmDelete')} onConfirm={() => handleDelete(row.id)}>
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small" v-show={hasPermission('tenant/delete')}>
                    {$t('common.delete')}
                  </ElButton>
                )
              }}
            </ElPopconfirm>
          </div>
        )
      }
    ]
  });

const { checkedRowKeys, onDeleted } = useTableOperate(data, getData);

// 操作处理函数
function handleAdd() {
  operateType.value = 'add';
  editingData.value = null;
  openModal();
}

async function handleBatchDelete() {
  const { error } = await fetchDeleteTenantByIds(checkedRowKeys.value);
  if (!error) {
    window.$message?.success($t('common.deleteSuccess'));
    onDeleted();
  } else {
    window.$message?.error($t('common.error'));
  }
}

async function handleDelete(id: number) {
  const { error } = await fetchDeleteTenants(id);
  if (!error) {
    window.$message?.success($t('common.deleteSuccess'));
    onDeleted();
  } else {
    window.$message?.error($t('common.error'));
  }
}

function handleEdit(tenant: Api.TenantManage.Tenant) {
  operateType.value = 'edit';
  editingData.value = { ...tenant };
  openModal();
}
</script>

<template>
  <div class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <TenantSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.tenant.title') }}</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            :show-add="hasPermission('tenant/add')"
            :show-delete="hasPermission('tenant/delete')"
            @add="handleAdd"
            @delete="handleBatchDelete"
            @refresh="getData"
          />
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="(selection: Api.TenantManage.Tenant[]) => checkedRowKeys = selection.map(item => String(item.id))"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            class="mt-20px"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
      <TenantOperateModal
        v-model:visible="visible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>

import { useAuthStore } from '@/store/modules/auth';

/**
 * 站点类型判断工具函数
 */

/**
 * 判断当前用户是否为分站（租户）
 * @returns {boolean} 是否为分站
 */
export function isTenantSite(): boolean {
  const authStore = useAuthStore();
  return authStore.userInfo?.roles?.includes('TENANT') || false;
}

/**
 * 判断当前用户是否为主站管理员
 * @returns {boolean} 是否为主站管理员
 */
export function isMainSite(): boolean {
  const authStore = useAuthStore();
  return authStore.userInfo?.roles?.includes('SUPER_ADMIN') || 
         authStore.userInfo?.roles?.includes('ADMIN') || false;
}

/**
 * 判断当前用户是否为超级管理员
 * @returns {boolean} 是否为超级管理员
 */
export function isSuperAdmin(): boolean {
  const authStore = useAuthStore();
  return authStore.userInfo?.roles?.includes('SUPER_ADMIN') || false;
}

/**
 * 获取当前站点类型
 * @returns {'main' | 'tenant' | 'unknown'} 站点类型
 */
export function getSiteType(): 'main' | 'tenant' | 'unknown' {
  if (isMainSite()) {
    return 'main';
  }
  if (isTenantSite()) {
    return 'tenant';
  }
  return 'unknown';
}

/**
 * 判断功能是否对当前站点可见
 * @param enableForSubSite 是否对分站开启
 * @returns {boolean} 是否可见
 */
export function isFeatureVisible(enableForSubSite: boolean): boolean {
  // 主站始终可见所有功能
  if (isMainSite()) {
    return true;
  }
  
  // 分站根据配置决定是否可见
  if (isTenantSite()) {
    return enableForSubSite;
  }
  
  // 未知角色默认不可见
  return false;
}

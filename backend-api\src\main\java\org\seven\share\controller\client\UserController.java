package org.seven.share.controller.client;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.seven.share.common.annotation.JwtToken;
import org.seven.share.common.annotation.RateLimit;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.R;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.dto.*;
import org.seven.share.common.pojo.entity.*;
import org.seven.share.common.pojo.image.ImageEditRequest;
import org.seven.share.common.pojo.image.ImageGenerationRequest;
import org.seven.share.common.pojo.vo.InviteDetailsVo;
import org.seven.share.common.pojo.vo.RedemptionHistoryVo;
import org.seven.share.common.pojo.vo.UserInfoVo;
import org.seven.share.common.util.JwtUtil;
import org.seven.share.mapper.DrawRecordMapper;
import org.seven.share.service.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.seven.share.common.util.ConstantUtil.AUTH_HEADER;

@RestController
@RequestMapping("/client-api")
@RequiredArgsConstructor
@JwtToken
public class UserController {

    private final ChatGptSubTypeService chatGptSubTypeService;

    private final QuotaChangeRecordService quotaChangeRecordService;

    private final ChatGptWithdrawalsService chatGptWithdrawalsService;

    private final OpenAIImageService openAIService;

    private final UserDrawingQuotaService quotaService;

    private final ChatGptUserService chatGptUserService;

    private final ChatGptRedemptionService chatGptRedemptionService;

    private final DrawRecordMapper drawRecordMapper;


    @GetMapping("/calc-amount")
    public Double calcOfferAmount(@RequestParam("planId") Long planId,
                                  @RequestParam("coupon") String coupon,
                                  @RequestParam("originalMoney") double originalMoney){
        return chatGptSubTypeService.calcOfferAmount(planId, coupon, originalMoney, false);
    }


    /**
     * 提现记录分页
     * @return
     */
    @GetMapping("/getWithdrawPage")
    public R getUserWithdrawalsPage(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                    @RequestParam(value = "size", defaultValue = "10") Integer size,
                                    String userId) {
        LambdaQueryWrapper<ChatGptWithdrawalsEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotEmpty(userId), ChatGptWithdrawalsEntity::getUserId, userId);
        Page<ChatGptWithdrawalsEntity> pageInfo = chatGptWithdrawalsService.page(new Page<>(page, size), wrapper);
        return R.ok(pageInfo);
    }

    @PostMapping("/withdrawals/apply")
    public R apply(@RequestBody ApplyWithdrawalDto dto){
        chatGptWithdrawalsService.applyWithdrawal(dto);
        return R.ok();
    }

    /**
     * 查询用户邀请记录
     * @return
     */
    @GetMapping("/withdrawals/getInviteDetails")
    public R getInviteDetails(@RequestParam String id) {
        List<InviteDetailsVo> list = chatGptWithdrawalsService.getInviteDetails(id);
        return R.ok(list);
    }

    @PostMapping("/images/generate")
    @RateLimit(10)
    public String submitImageGeneration(@RequestBody ImageGenerationRequest request, HttpServletRequest servletRequest) {
        return openAIService.submitImageGenerationTask(request, servletRequest);
    }

    @GetMapping("/images/status/{taskId}")
    public R getTaskStatus(@PathVariable String taskId) {
        return R.ok(openAIService.getTaskStatus(taskId));
    }

    @PostMapping("/images/edit")
    @RateLimit(10)
    public String imageEdit(@RequestPart("image") List<MultipartFile> images,  // 使用单数形式
                            @RequestParam("prompt") String prompt,
                            @RequestParam("model") String model,
                            @RequestParam(value = "n", defaultValue = "1") Integer n,
                            @RequestParam(value = "size", defaultValue = "1024x1024") String size,
                            @RequestParam(value = "quality", required = false) String quality,
                            @RequestParam(value = "responseFormat", defaultValue = "url") String responseFormat,
                            HttpServletRequest servletRequest) {
        // 创建请求对象
        ImageEditRequest request = new ImageEditRequest();
        request.setImages(images);
        request.setPrompt(prompt);
        request.setModel(model);
        request.setN(n);
        request.setSize(size);
        request.setQuality(quality);
        request.setResponseFormat(responseFormat);
        return openAIService.submitImageEditTask(request, servletRequest);
    }

    /**
     * 显示用户绘画历史数据
     * @param request
     * @return
     */
    @GetMapping("/images/record/list")
    public R list(HttpServletRequest request) {
        String header = request.getHeader(AUTH_HEADER);
        Long uid = JwtUtil.getUid(header);
        List<DrawRecordEntity> list = openAIService.listDrawRecordByUid(uid);
        return R.ok(list);
    }

    /**
     * 查询用户绘画额度
     * @param uid
     * @return
     */
    @GetMapping("/images/quota")
    public R queryQuota(Long uid) {
        UserDrawingQuotaEntity quotaEntity = quotaService.queryUserDrawQuotaById(uid);
        return R.ok(quotaEntity);
    }

    /**
     * 分页根据用户id查询用户绘画额度变动明细
     * @param current
     * @param size
     * @param userId
     * @return
     */
    @GetMapping("/images/quota/change/record")
    public R pageQuotaChangeByUId(@RequestParam(value = "current", defaultValue = "1") int current,
                                  @RequestParam(value = "size", defaultValue = "10") int size,
                                  @RequestParam String userId) {
        Page<QuotaChangeRecordEntity> pageInfo = quotaChangeRecordService.pageQuotaChangeByUId(current, size, userId);
        return R.ok(pageInfo);
    }

    /**
     * 用户模型速率信息
     * @param id
     * @return
     */
    @GetMapping("/limits")
    public R getUserLimits(@RequestParam("id") Long id) {
        return R.ok(chatGptUserService.getUserLimits(id));
    }


    @GetMapping("/code/redemption-history")
    @Operation(description = "查询激活码兑换历史")
    public R getRedemptionHistory(@RequestParam Long userId){
        List<RedemptionHistoryVo> list = chatGptRedemptionService.listRedemptionHistory(userId);
        return R.ok(list);
    }

    @GetMapping("/code/redeem")
    @Operation(description = "激活码兑换")
    public R redeem(@RequestParam String key, @RequestParam Long userId){
        chatGptRedemptionService.redeemCodes(key, userId);
        return R.ok();
    }

    /**
     * 签到
     * @param userId
     * @return
     */
    @PostMapping("/sign-in")
    @RateLimit(10)
    public R signIn(Long userId) {
        SignInRecordEntity signInRecord = chatGptUserService.signIn(userId);
        return R.ok(signInRecord);
    }

    /**
     * 签到信息
     * @param userId
     * @return
     */
    @GetMapping("/sign-in/info")
    public R signInInfo(Long userId) {
        SignInInfoDTO records = chatGptUserService.getSignInInfo(userId);
        return R.ok(records);
    }
    /**
     * 用户绑定邮箱
     * @param userId
     * @param email
     * @return
     */
    @GetMapping("/updateUserEmail")
    @RateLimit(10)
    public R updateUserEmail(String userId, String email) {
        chatGptUserService.updateUserEmailByUserId(userId, email);
        return R.ok();
    }

    /**
     * 用户个人信息
     * @param userName
     * @return
     */
    @GetMapping("/getme")
    public R getUserInfo(@RequestParam String userName) {
        UserInfoVo userInfo = chatGptUserService.getUserInfo(userName);
        return R.ok(userInfo);
    }



    /**
     * 修改密码
     * @param changePasswordDto
     * @return
     */
    @PostMapping("/change-password")
    @RateLimit(20)
    public R changePassword(@RequestBody @Validated ChangePasswordDto changePasswordDto) {
        chatGptUserService.changePassword(changePasswordDto);
        return R.ok();
    }

    /**
     * 分页邀请数据
     * @param page
     * @param size
     * @param userId
     * @return
     */
    @GetMapping("/inviterPage")
    public R getInviterPage(@RequestParam(value = "page", defaultValue = "1") Integer page,
                            @RequestParam(value = "size", defaultValue = "10") Integer size,
                            @RequestParam("userId") Long userId){
        // 创建查询条件封装器
        LambdaQueryWrapper<ChatGptUserEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatGptUserEntity::getInviterId, userId);
        // 执行分页查询
        Page<ChatGptUserEntity> pageInfo = chatGptUserService.page(new Page<>(page,size), queryWrapper);
        List<UserInfoVo> collect = pageInfo.getRecords().stream().map(user -> {
            UserInfoVo vo = new UserInfoVo();
            BeanUtil.copyProperties(user, vo);
            return vo;
        }).collect(Collectors.toList());
        Page<UserInfoVo> voPage = new Page<>(pageInfo.getCurrent(), pageInfo.getSize(), pageInfo.getTotal());
        voPage.setRecords(collect);
        // 返回分页查询结果
        return R.ok(voPage);
    }


    /**
     * 检查是否有使用备用节点的权限
     * @param userId
     * @return
     */
    @GetMapping("/checkAccess")
    public R checkBackAccess(@RequestParam("userId") String userId) {
        boolean b = chatGptUserService.checkBackAccess(userId);
        return R.ok(b);
    }

    @GetMapping("/images/record/delete")
    public R deleteRecord(@RequestParam("rid") String rid) {
        if(StringUtils.isBlank(rid)) {
            R.error();
        }
        return R.ok(drawRecordMapper.deleteById(rid));
    }

    @GetMapping("/getLoginToken")
    public R getLoginToken(HttpServletRequest request) {
        Map<String, String> map = chatGptUserService.getLoginToken(request);
        return R.ok(map);
    }
}

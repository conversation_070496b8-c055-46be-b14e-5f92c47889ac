<script setup lang="tsx">
import { ref } from 'vue';
import type { Ref } from 'vue';
import { ElButton, ElMessage, ElPopconfirm, ElSwitch } from 'element-plus';
import CouponSearch from './modules/coupon-search.vue'
import {
  fetchCouponPage,
  updateCoupon,
  removeCouponBatchByIds
} from '@/service/api';
import { useBoolean } from '@sa/hooks';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import NoticeOperateModel , { type OperateType } from './modules/coupon-operate-modal.vue'
import { usePermission } from '@/hooks/business/auth';
const { hasPermission } = usePermission();
// Define the proper type for your table data
interface Coupon {
  id: string;
  coupon: string;
  discount: number;
  discountAmount: string;
  discountCount: number;
  usageDiscountCount: number;
  status: number;
  expireTime: string;
  remark: string;
  createTime: string;
  updateTime: string;
}

const wrapperRef = ref<HTMLElement | null>(null);
const operateType = ref<OperateType>('add');
const { bool: visible, setTrue: openModal } = useBoolean();
const operateModalData = ref<Coupon | null>(null);

const {
  columns,
  columnChecks,
  data,
  loading,
  pagination,
  getData,
  searchParams,
  resetSearchParams,
  getDataByPage
} = useTable<Coupon>({
  apiFn: fetchCouponPage,
  columns: () => [
    { type: 'selection', width: 48 },
    { prop: 'index', label: $t('common.index'), width: 64 },
    { prop: 'coupon', label: '优惠卷', minWidth: 300, showOverflowTooltip: true },
    { prop: 'discount', label: '优惠折扣', minWidth: 180, showOverflowTooltip: true },
    { prop: 'discountAmount', label: '优惠金额', minWidth: 180 },
    { prop: 'discountCount', label: '优惠次数', minWidth: 180 },
    { prop: 'usageDiscountCount', label: '已优惠次数', minWidth: 180 },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      align: 'center',
      formatter: (row: Coupon) => (
        <ElSwitch
          v-model={row.status}
          active-value={0}
          inactive-value={1}
          onChange={() => changeStatus(row)}
        />
      )
    },
    { prop: 'expireTime', label: '过期时间', width: 180},
    { prop: 'remark', label: '备注', minWidth: 200, showOverflowTooltip: true},
    { prop: 'createTime', label: '创建时间', width: 180 },
    { prop: 'updateTime', label: '更新时间', width: 180 },
    {
      prop: 'operate',
      label: $t('common.operate'),
      align: 'center',
      fixed: 'right',
      width: 140,
      formatter: (row: Coupon) => (
        <div class="flex-center">
          {hasPermission('coupon/update') && (
            <ElButton type="primary" plain size="small" onClick={() => handleEdit(row)}>
              {$t('common.edit')}
            </ElButton>
          )}
        <ElPopconfirm title={$t('common.confirmDelete')} onConfirm={() => handleDelete(row.id)}>
          {{
            reference: () => (
              <ElButton type="danger" v-show={hasPermission('coupon/delete')} plain size="small">
                {$t('common.delete')}
              </ElButton>
            )
          }}
        </ElPopconfirm>
        </div>
      )
    }
  ]
});

const {
  checkedRowKeys,
  onBatchDeleted,
  onDeleted
} = useTableOperate(data, getData);


const handleEdit = (row: Coupon) => {
  operateType.value = 'update';
  operateModalData.value = Object.assign({}, row);
  console.log(operateModalData.value)
  openModal()
};

const handleDelete = (id: number) => {
  removeCouponBatchByIds([id]);
  onDeleted()
}

const handleBatchDelete = () => {
  removeCouponBatchByIds(checkedRowKeys.value);
  onBatchDeleted();
};


const changeStatus = async (data: Coupon) => {
  await updateCoupon(data);
  ElMessage({
    message: '状态修改成功',
    type: 'success',
    plain: true
  });
  getData();
};

function handleAdd() {
  operateType.value = 'add';
  openModal();
}

async function handleExport() {
  operateType.value = 'update';
  openModal();
}

</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <CouponSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <div class="flex items-center">
            <TableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              :show-add="hasPermission('coupon/add')"
              :show-delete="hasPermission('coupon/delete')"
              @add="handleAdd"
              @export="handleExport"
              @delete="handleBatchDelete"
              @refresh="getData"
            />
          </div>
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="(selection: Coupon[]) => checkedRowKeys = selection.map(item => item.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" :show-overflow-tooltip="col.showOverflowTooltip" />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
      <NoticeOperateModel
        v-model:visible="visible"
        :operate-type="operateType"
        :row-data = "operateModalData"
        @submitted="getDataByPage"
      />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>

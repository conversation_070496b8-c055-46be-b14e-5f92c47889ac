<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { addSensitiveWord, updateSensitiveWord, saveSenstiveBatch } from '@/service/api';

interface Props {
  visible: boolean;
  type: 'add' | 'edit' | 'batch';
  data?: any;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}>();

const formRef = ref();
const formData = ref({
  word: '',
  status: 1,
  remark: '',
  batchWords: '' // 用于批量新增
});

const rules = {
  word: [{ required: true, message: '请输入敏感词', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  batchWords: [{ required: true, message: '请输入敏感词列表', trigger: 'blur' }]
};

const title = computed(() => {
  switch (props.type) {
    case 'add':
      return '新增敏感词';
    case 'edit':
      return '编辑敏感词';
    case 'batch':
      return '批量新增敏感词';
    default:
      return '';
  }
});

watch(
  () => props.visible,
  (val) => {
    if (val) {
      if (props.type === 'edit' && props.data) {
        formData.value = { ...props.data };
      } else {
        formData.value = {
          word: '',
          status: 1,
          remark: '',
          batchWords: ''
        };
      }
    }
  }
);

async function handleSubmit() {
  if (!formRef.value) return;
  
  await formRef.value.validate();
  
  try {
    if (props.type === 'batch') {
      const wordList = formData.value.batchWords
        .split('\n') // 按换行符切割
        .map(word => word.trim()) // 去除每一行的多余空格
        .filter(word => word.length > 0); // 过滤掉空行
      
      await saveSenstiveBatch(wordList);
      ElMessage.success('批量新增成功');
    } else if (props.type === 'add') {
      await addSensitiveWord(formData.value);
      ElMessage.success('新增成功');
    } else if (props.type === 'edit') {
      await updateSensitiveWord(formData.value);
      ElMessage.success('修改成功');
    }
    
    emit('success');
    handleClose();
  } catch (error) {
    ElMessage.error('操作失败');
  }
}

function handleClose() {
  emit('update:visible', false);
}
</script>

<template>
  <ElDrawer
    :model-value="visible"
    :title="title"
    size="500px"
    @update:model-value="handleClose"
    @close="handleClose"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="80px"
    >
      <template v-if="type !== 'batch'">
        <ElFormItem label="敏感词" prop="word">
          <ElInput v-model="formData.word" placeholder="请输入敏感词" />
        </ElFormItem>
        <ElFormItem label="状态" prop="status">
          <ElSwitch
            v-model="formData.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </ElFormItem>
      </template>
      
      <template v-else>
        <ElFormItem label="敏感词列表" prop="batchWords">
          <ElInput
            v-model="formData.batchWords"
            type="textarea"
            :rows="10"
            placeholder="请输入敏感词列表，每行一个"
          />
        </ElFormItem>
      </template>
      
      <ElFormItem label="备注">
        <ElInput
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </ElFormItem>
    </ElForm>
    
    <template #footer>
      <ElSpace>
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit">确定</ElButton>
      </ElSpace>
    </template>
  </ElDrawer>
</template> 
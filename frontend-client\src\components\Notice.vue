<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('notice')"
    :width="dialogWidth"
    @close="handleClose"
    align-center
  >
    <iframe :srcdoc="noticeContent" style="width: 100%; border: none; min-height: calc(85vh);" ></iframe>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useSiteStore } from '@/store/modules/site';

const siteStore = useSiteStore();
const noticeContent = computed(() => siteStore.siteAnnouncement);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  notice: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible']);

const dialogVisible = ref(props.visible);

const windowWidth = ref(window.innerWidth);
const dialogWidth = computed(() => {
  if (windowWidth.value < 768) return '96%';
  if (windowWidth.value < 1024) return '70%';
  return '80%';
});

const handleClose = () => {
  localStorage.setItem("noticeLastSeen", new Date().toLocaleDateString())
  dialogVisible.value = false
  emit('update:visible', false);
};

watch(() => props.visible, (newValue) => {
  dialogVisible.value = newValue;
});

// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth;
};
const checkNotice = () => { 
  const today = new Date().toLocaleDateString()
  const lastSeen = localStorage.getItem("noticeLastSeen")
  if (today !== lastSeen) { 
    dialogVisible.value = true
  }
}
// 生命周期钩子
onMounted(() => {
  window.addEventListener('resize', handleResize);
  // checkNotice()
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>
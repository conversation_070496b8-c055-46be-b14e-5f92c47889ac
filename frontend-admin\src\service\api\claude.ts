import { request } from '../request';

export function fetchClaudePage(params: any) {
  return request({
    url: '/claude/page',
    method: 'get',
    params
  });
}

export function saveClaude(data: any) {
  return request({
    url: '/claude/create',
    data,
    method: 'post'
  });
}

export function updateCladue(data: any) {
  return request({
    url: '/claude/update',
    data,
    method: 'put'
  });
}

export function removeClaudeBatchByIds(data: any) {
  return request({
    url: '/claude/delete',
    data,
    method: 'delete'
  });
}

export function fetchCarId() {
  return request({
    url: '/claude/getCarID',
    method: 'get'
  });
}

export function removeClaudeConversationsBatch(data: any) {
  return request({
    url: '/claude/conversations/delete',
    method: 'post',
    data
  });
}

export function fetchClaudeConversationsPage(params: any) {
  return request({
    url: '/claude/conversations/page',
    method: 'get',
    params
  });
}

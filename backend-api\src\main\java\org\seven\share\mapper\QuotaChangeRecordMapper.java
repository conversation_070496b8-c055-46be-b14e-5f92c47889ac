package org.seven.share.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.seven.share.common.pojo.entity.QuotaChangeRecordEntity;

/**
 * @ClassName: QuotaChangeRecordDao
 * @Description:
 * @Author: Seven
 * @Date: 2025/4/26
 */
@Mapper
public interface QuotaChangeRecordMapper extends BaseMapper<QuotaChangeRecordEntity> {
    IPage<QuotaChangeRecordEntity> selectChangeRecordPage(IPage<QuotaChangeRecordEntity> page,
                                                          @Param("ew") Wrapper<QuotaChangeRecordEntity> queryWrapper);
}

package org.seven.share.config;


import lombok.RequiredArgsConstructor;
import org.seven.share.security.filter.JwtAuthenticationTokenFilter;
import org.seven.share.security.filter.RoleBasedApiFilter;
import org.seven.share.security.handle.RestAuthenticationEntryPoint;
import org.seven.share.security.handle.RestfulAccessDeniedHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.intercept.AuthorizationFilter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * SecurityConfig
 */
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
@EnableMethodSecurity
public class SecurityConfig {

    /**
     * 自定义用户认证逻辑
     */
    final UserDetailsService userDetailsService;

    final RestfulAccessDeniedHandler restfulAccessDeniedHandler;

    final RestAuthenticationEntryPoint restAuthenticationEntryPoint;

    private final ApiSecurityConfig apiSecurityConfig;

    @Bean
    public RoleBasedApiFilter roleBasedApiFilter(ApiSecurityConfig apiSecurityConfig) {
        return new RoleBasedApiFilter(apiSecurityConfig);
    }

    @Bean
    protected SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
                // 由于使用的是JWT，我们这里不需要csrf
                .csrf(AbstractHttpConfigurer::disable)
                // 基于token，所以不需要session
                .sessionManagement(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests()
                .antMatchers(
                        // 允许对于网站静态资源的无授权访问
                        "/",
                        "/index.html",
                        "/webjars/**",
                        "/doc.html",
                        "/swagger-resources/**",
                        "/v3/api-docs/**",
                        "/swagger-ui/**",
                        "/swagger-resources",
                        "/swagger-ui.html",
                        "/expander/**",
                        "/app/**")
                .permitAll()
                // 允许匿名访问
                .antMatchers("/expander-api/auth/login",
                        "/expander-api/route/getConstantRoutes",
                        "/expander-api/auth/code",
                        "/client-api/**",
                        "/expander-api/images/**",
                        "/api/**"
                        ).permitAll()
                .anyRequest().authenticated();
        // 禁用缓存
        http.headers(headers -> headers
                .cacheControl(HeadersConfigurer.CacheControlConfig::disable)
        );

        // 解决X-frame-Options deny问题，同源允许frame
        http.headers().frameOptions().sameOrigin();

        http.addFilterBefore(jwtAuthenticationTokenFilter(), UsernamePasswordAuthenticationFilter.class);

        // 添加自定义未授权和未登录结果返回
        http.exceptionHandling(exceptionHandling -> exceptionHandling
                .accessDeniedHandler(restfulAccessDeniedHandler)
                .authenticationEntryPoint(restAuthenticationEntryPoint)
        );

        // 校验角色路由权限
        http.addFilterAfter(new RoleBasedApiFilter(apiSecurityConfig), AuthorizationFilter.class);

        // 注入authenticationManager
        http.authenticationManager(authenticationManager());
        return http.build();
    }

    /**
     * 身份认证接口 构造一个AuthenticationManager，使用自定义的userDetailsService和passwordEncoder
     */
    @Bean
    public AuthenticationManager authenticationManager() {
        DaoAuthenticationProvider authenticationProvider = new DaoAuthenticationProvider();
        authenticationProvider.setUserDetailsService(userDetailsService);
        authenticationProvider.setPasswordEncoder(passwordEncoder());

        return new ProviderManager(authenticationProvider);
    }

    /**
     * 强散列哈希加密实现
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter() {
        return new JwtAuthenticationTokenFilter();
    }
}

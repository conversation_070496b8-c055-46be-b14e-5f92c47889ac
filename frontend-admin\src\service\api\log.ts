import { request } from '../request';

export function fetchOperatorLogPage(params: Api.SystemManage.CommonSearchParams) {
  return request({
    url: '/sys/log/page',
    method: 'get',
    params
  });
}

export function cleanOperatorLog() {
  return request({
    url: '/sys/log/clean',
    method: 'post'
  });
}

export function removeOperatorLogBatch(data: any) {
  return request({
    url: '/sys/log/delete',
    method: 'post',
    data
  });
}

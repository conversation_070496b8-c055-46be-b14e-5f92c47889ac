package org.seven.share.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.R;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.entity.ChatGptConversationEntity;
import org.seven.share.service.ChatGptConversationService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: ChatGptConversationController
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/1
 */
@RestController
@RequestMapping("/expander-api/conversation")
public class ChatGptConversationController {

    @Resource
    private ChatGptConversationService chatGptConversationService;

    @GetMapping("/page")
    @SysLogInterface(title = "分页查询对话记录", businessType = BusinessType.QUERY)
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer current,
                  @RequestParam(value = "size", defaultValue = "10") Integer size){
        LambdaQueryWrapper<ChatGptConversationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(ChatGptConversationEntity::getDeletedAt);
        Page<ChatGptConversationEntity> pageInfo = chatGptConversationService.page(new Page<>(current,size), queryWrapper);
        return R.ok(pageInfo);
    }


    @DeleteMapping("/delete")
    @SysLogInterface(title = "删除对话记录", businessType = BusinessType.DELETE)
    public R del(@RequestBody List<String> ids){
        return chatGptConversationService.removeBatchByIds(ids) ? R.ok() : R.error();
    }
}

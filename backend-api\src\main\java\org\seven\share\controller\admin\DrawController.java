package org.seven.share.controller.admin;


import com.baomidou.mybatisplus.core.metadata.IPage;
import org.seven.share.common.api.R;
import org.seven.share.common.pojo.entity.DrawRecordEntity;
import org.seven.share.common.pojo.entity.QuotaChangeRecordEntity;
import org.seven.share.common.pojo.entity.UserDrawingQuotaEntity;
import org.seven.share.service.OpenAIImageService;
import org.seven.share.service.QuotaChangeRecordService;
import org.seven.share.service.UserDrawingQuotaService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@RestController
@RequestMapping("/expander-api/draw")
public class DrawController {

    @Resource
    private OpenAIImageService openAIService;

    @Resource
    private UserDrawingQuotaService quotaService;

    @Resource
    private QuotaChangeRecordService quotaChangeRecordService;

    /**
     * 分页查询绘画记录
     * @param current
     * @param size
     * @param prompt
     * @param username
     * @return
     */
    @GetMapping("/recode/page")
    public R page(@RequestParam(value = "current", defaultValue = "1") int current,
                  @RequestParam(value = "size", defaultValue = "10") int size,
                  String prompt,
                  String username) {
        IPage<DrawRecordEntity> pageInfo = openAIService.pageDrawRecord(current, size, prompt, username);
        return R.ok(pageInfo);
    }

    /**
     * 删除绘画记录
     * @param ids
     * @return
     */
    @PostMapping("/recode/delete")
    public R removeRecord(@RequestBody List<Long> ids) {
        openAIService.removeByIds(ids);
        return R.ok();
    }

    /**
     * 分页根查询用户绘画额度变动明细
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/quota/change/page")
    public R pageQuotaChange(@RequestParam(value = "current", defaultValue = "1") int current,
                             @RequestParam(value = "size", defaultValue = "10") int size,
                             String username) {
        IPage<QuotaChangeRecordEntity> pageInfo = quotaChangeRecordService.pageQuotaChange(current, size, username);
        return R.ok(pageInfo);
    }

    /**
     * 删除用户绘画额度变更记录
     * @param ids
     * @return
     */
    @PostMapping("/quota/change/delete")
    public R removeDrawQuota(@RequestBody List<Long> ids) {
        quotaChangeRecordService.removeQuotaChangeByIds(ids);
        return R.ok();
    }

    /**
     * 分页查询用户额度数据
     * @param current
     * @param size
     * @param username
     * @return
     */
    @GetMapping("/quota/page")
    public R pageUserQuota(@RequestParam(value = "current", defaultValue = "1") int current,
                             @RequestParam(value = "size", defaultValue = "10") int size,
                             String username) {
        IPage<UserDrawingQuotaEntity> pageInfo = quotaService.pageUserQuota(current, size, username);
        return R.ok(pageInfo);
    }

    /**
     * 删除用户绘画额度
     * @param ids
     * @return
     */
    @PostMapping("/quota/delete")
    public R removeUserQuota(@RequestBody List<Long> ids) {
        quotaService.removeUserQuota(ids);
        return R.ok();
    }

    @PostMapping("/quota/update")
    public R updateUserQuota(@RequestBody UserDrawingQuotaEntity quota) {
        quotaService.updateUserQuota(quota);
        return R.ok();
    }
}

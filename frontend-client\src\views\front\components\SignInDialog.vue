<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { signIn, getSignInInfo } from '@/api/user' // You'll need to create these API functions
import dayjs from 'dayjs' // 需要添加 dayjs 依赖
import { useSiteStore } from '@/store/modules/site'
import { useUserStore } from '@/store/modules/user'
const siteStore = useSiteStore()
const userStore = useUserStore()
const isMobile = computed(() => {
  return window.innerWidth < 768
})
const { t } = useI18n()
const visible = ref(false)
const loading = ref(false)
const announcement = siteStore.signInAnnouncement
const signInInfo = ref({
  hasSignedIn: false,
  continuousDays: 0,
  reward: 0,
  signedDates: [], // 新增：已签到日期数组
  currentMonth: [] // 新增：当月签到记录
})

const currentDate = ref(new Date())
const calendar = computed(() => {
  const date = dayjs(currentDate.value)
  const daysInMonth = date.daysInMonth()
  const firstDay = date.startOf('month').day()
  const calendar = []
  
  // 填充日历数组
  let day = 1
  for (let i = 0; i < 6; i++) {
    const week = []
    for (let j = 0; j < 7; j++) {
      if ((i === 0 && j < firstDay) || day > daysInMonth) {
        week.push(null)
      } else {
        week.push(day++)
      }
    }
    calendar.push(week)
  }
  return calendar
})

const openDialog = async () => {
  visible.value = true
  await refreshSignInInfo()
}

const refreshSignInInfo = async () => {
  try {
    const data = await getSignInInfo({userId: userStore.id})
    signInInfo.value = data
  } catch (error) {
    console.error('Failed to get sign-in info:', error)
  }
}

const handleSignIn = async () => {
  if (signInInfo.value.hasSignedIn) {
    ElMessage.warning(t('signIn.alreadySignedIn'))
    return
  }

  loading.value = true
  try {
    const res = await signIn({userId: userStore.id})
    ElMessage.success(t('signIn.success'))
    await refreshSignInInfo()
  } catch (error) {
    console.error('Sign-in failed:', error)
  } finally {
    loading.value = false
  }
}

defineExpose({
  openDialog
})
</script>

<template>
  <el-dialog
    :title="t('signIn.title')"
    v-model="visible"
    align-center
    :width="isMobile ? '90%' : '35%'"
    class="sign-in-dialog"
  >
    <div class="space-y-4">
      <!-- 公告部分 -->
      <div v-if="announcement" class="bg-blue-900/20 p-4 rounded-lg mb-4">
        <div class="text-sm" v-html="announcement"></div>
      </div>

      <!-- 日历签到部分 -->
      <div>
        <div class="grid grid-cols-7 gap-1">
          <!-- 周一到周日的表头 -->
          <div v-for="weekday in ['周一', '周二', '周三', '周四', '周五', '周六', '周日']" 
               :key="weekday"
               class="h-8 flex items-center justify-center text-sm text-gray-400">
            {{ weekday }}
          </div>
          
          <!-- 日历格子 -->
          <template v-for="week in calendar" :key="week">
            <template v-if="week.some(day => day !== null)">
              <div v-for="day in week" 
                   :key="day"
                   class="aspect-square flex items-center justify-center">
                <template v-if="day">
                  <div
                    :class="[
                      'w-full h-full flex flex-col items-center justify-center cursor-pointer relative',
                      signInInfo.signedDates.includes(day) ? 'bg-emerald-500/80 text-white' : 'text-gray-700 dark:text-gray-300',
                      day === new Date().getDate() ? 'border border-emerald-500' : '',
                      'hover:bg-gray-700/50 rounded-sm'
                    ]"
                    @click="handleSignIn"
                  >
                    <span class="text-xs mb-1">{{ day }}</span>
                    <template v-if="signInInfo.signedDates.includes(day)">
                      <el-icon class="text-white"><Check /></el-icon>
                    </template>
                    <template v-else-if="day <= new Date().getDate()">
                      <template v-if="day === new Date().getDate()">
                        <el-icon class="text-yellow-500"><Present /></el-icon>
                      </template>
                      <template v-else>
                        <el-icon class="text-gray-500"><Close /></el-icon>
                      </template>
                    </template>
                  </div>
                </template>
                <template v-else>
                  <div class="w-full h-full flex items-center justify-center text-gray-600">
                    {{ day }}
                  </div>
                </template>
              </div>
            </template>
          </template>
        </div>
      </div>

      <!-- 签到按钮 -->
      <button
        class="mt-4 w-full bg-black dark:bg-white text-white dark:text-black font-medium px-4 py-2 rounded-lg text-sm hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 disabled:opacity-50"
        :disabled="loading"
        @click="handleSignIn"
      >
        {{ loading ? t('signIn.signingIn') : t('signIn.signInNow') }}
      </button>
    </div>
  </el-dialog>
</template>

<style scoped>
.sign-in-dialog :deep(.el-dialog) {
  background: #1a1a1a;
  border-radius: 8px;
  margin: 0 !important;
}

/* 移动端样式优化 */
@media (max-width: 640px) {
  .sign-in-dialog :deep(.el-dialog) {
    border-radius: 0;
  }

  .sign-in-dialog :deep(.el-dialog__header) {
    padding: 16px;
    margin-right: 0;
  }

  .sign-in-dialog :deep(.el-dialog__body) {
    padding: 16px;
  }

  .sign-in-dialog :deep(.el-dialog__headerbtn) {
    top: 16px;
  }
}

.sign-in-dialog :deep(.el-dialog__title) {
  color: white;
}

.sign-in-dialog :deep(.el-dialog__header) {
  border-bottom: 1px solid rgba(255,255,255,0.1);
}
</style>

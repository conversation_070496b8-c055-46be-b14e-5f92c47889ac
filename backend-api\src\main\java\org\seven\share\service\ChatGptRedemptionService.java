package org.seven.share.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.pojo.dto.ChatGptRedemptionDto;
import org.seven.share.common.pojo.entity.ChatGptRedemptionEntity;
import org.seven.share.common.pojo.vo.RedemptionHistoryVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @InterfaceName: ChatGptRedemptionService
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/28
 */
public interface ChatGptRedemptionService extends IService<ChatGptRedemptionEntity> {
    List<String> generateCodes(Long subTypeId, Integer count);

    void redeemCodes(String key, Long userId);

    IPage<ChatGptRedemptionDto> selectPage(Page<ChatGptRedemptionDto> pageInfo,
                                           String key,
                                           String userToken,
                                           Integer status);

    void exportCardCodes(HttpServletResponse response, Long subTypeId);

    List<RedemptionHistoryVo> listRedemptionHistory(Long userId);

    void recycleKey(ChatGptRedemptionEntity entity);
}

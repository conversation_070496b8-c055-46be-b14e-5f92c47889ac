package org.seven.share.schedule;

import lombok.extern.slf4j.Slf4j;
import org.seven.share.mapper.DrawRecordMapper;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @ClassName: DrawFailureSchedule
 * @Description:
 * @Author: Seven
 * @Date: 2025/4/29
 */
@Slf4j
@Component
public class DrawFailureSchedule {
    @Resource
    private DrawRecordMapper drawRecordMapper;

    // 处理超过15分钟的数据
    @Scheduled(fixedRate = 300000) // 单位：毫秒，300000ms = 5分钟
    public void run () {
        drawRecordMapper.updateDrawTimeOut();
    }
}

package org.seven.share.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.pojo.entity.ChatGptConfigEntity;

import java.util.List;
import java.util.Map;

/**
 * @InterfaceName: ChatGptConfigService
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/1
 */
public interface ChatGptConfigService extends IService<ChatGptConfigEntity> {
    void saveForm(Map<String, Object> form);

    Map<String, String> getConfigMap();

    void testEmail(String email);

    String getValueByKey(String key);

    Map<String, String> getSiteData();

    Map<String, String> getKeyValueMapByKeys(List<String> keys);

    Map<String, String> getSiteScript();

    Map<String, String> getAllConfigs();
}

package org.seven.share.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.Result;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.entity.SysRole;
import org.seven.share.common.pojo.vo.SysRoleVO;
import org.seven.share.service.SysRoleService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "SysRoleController", description = "用户角色控制层")
//@RequestMapping("/sys/role")
@RequestMapping("/expander-api/systemManage")
public class SysRoleController {

    private final SysRoleService sysRoleService;

    @Operation(summary = "list 分页列表")
    @Parameters({
            @Parameter(name = "current", description = "当前页", required = true, example = "1"),
            @Parameter(name = "size", description = "每页显示条数", required = true, example = "10"),
            @Parameter(name = "username", description = "用户名称"),
    })
    @GetMapping(value = "/getRoleList")
    @SysLogInterface(title = "查询角色信息", businessType = BusinessType.OTHER)
    public Result<IPage<SysRole>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> params) {
        IPage<SysRole> sysUsers = sysRoleService.getPage(params);
        return Result.success(sysUsers);
    }

    @Operation(summary = "查询角色信息")
    @GetMapping(value = "/getAllRoles")
    public Result<List<SysRoleVO>> getAllRoles(@RequestHeader("Authorization") String authorizationHeader) {
        return sysRoleService.getAllRoles(authorizationHeader);
    }

    @Operation(summary = "新增角色信息")
    @PostMapping(value = "/addRole")
    public Result<Boolean> add(@RequestBody SysRole sysRole) {
        return sysRoleService.add(sysRole);
    }

    @Operation(summary = "查询角色资源ID")
    @PostMapping(value = "/getRoleResourceId")
    public Result<List<Long>> getRoleResourceId(@RequestBody Long roleId) {
        return sysRoleService.getRoleResourceId(roleId);
    }


    @Operation(summary = "删除角色信息")
    @PostMapping(value = "/deleteRoleBatch")
    @SysLogInterface(title = "删除角色信息", businessType = BusinessType.DELETE)
    public Result<String> deleteRole(@RequestBody List<Long> ids) {
        sysRoleService.deleteRoleBatch(ids);
        return Result.success();
    }


    @Operation(summary = "绑定角色资源")
    @PostMapping(value = "/bindRoleResource/{roleId}")
    @SysLogInterface(title = "绑定角色资源", businessType = BusinessType.OTHER)
    public Result<?> bindRoleResource(@PathVariable("roleId") Long roleId ,@RequestBody List<Long> ids) {
        sysRoleService.bindRoleResource(roleId, ids);
        return Result.success();
    }

    @Operation(summary = "根据角色id查找资源id集合")
    @GetMapping(value = "/getRoleResource/{roleId}")
    @SysLogInterface(title = "根据角色id查找资源id集合", businessType = BusinessType.QUERY)
    public Result<?> getRoleResource(@PathVariable("roleId") Long roleId) {
        List<Long> resourceIds = sysRoleService.getRoleResource(roleId);
        return Result.success(resourceIds);
    }

}

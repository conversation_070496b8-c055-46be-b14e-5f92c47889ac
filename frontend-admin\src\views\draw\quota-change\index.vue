<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { ElButton, ElMessage, ElMessageBox, ElPopconfirm, ElTable, ElTableColumn, ElTag } from 'element-plus';
import { fetchQuotaChangeRecord, removeQuotaChangeBatch } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import QuotaChangeSearch from './modules/quota-change-search.vue';

// Define the proper type for your table data
interface QuotaChange {
  id: string;
  username: string;
  userId: number;
  changeType: number;
  changeAmount: string;
  remark: string;
  createdAt: string;
}

const wrapperRef = ref<HTMLElement | null>(null);
const windowWidth = ref(window.innerWidth);
const maxHeight = ref(window.innerHeight - 220);

// Handle window resize
const handleResize = () => {
  windowWidth.value = window.innerWidth;
  maxHeight.value = window.innerHeight - 220;
};

// For display formatting
const getDrawTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    '1': '套餐购买',
    '2': '绘图消耗',
    '3': '失败返还',
    '4': '后台修改'
  };
  return typeMap[type] || '未知';
};

const getDrawTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    '1': 'success',
    '2': 'warning',
    '3': 'info',
    '4': 'danger'
  };
  return tagMap[type] || 'info';
};

const { columns, columnChecks, data, loading, pagination, getData, searchParams, getDataByPage, resetSearchParams } =
  useTable({
    apiFn: fetchQuotaChangeRecord,
    apiParams: {
      current: 1,
      size: 10,
      username: undefined
    },
    columns: () => [
      { type: 'selection', width: 48 },
      { prop: 'index', label: $t('common.index'), width: 64 },
      { prop: 'username', label: '用户名', width: 250, showOverflowTooltip: true },
      {
        prop: 'changeType',
        label: '变更类型',
        width: 100,
        align: 'center',
        formatter: (row: any) => <ElTag type={getDrawTypeTag(row.changeType)}>{getDrawTypeText(row.changeType)}</ElTag>
      },
      { prop: 'changeAmount', label: '变更数量', width: 100, align: 'center' },
      { prop: 'remark', label: '描述', minWidth: 150, align: 'center', showOverflowTooltip: true },
      { prop: 'createdAt', label: '创建时间', width: 180, align: 'center', sortable: true },
      {
        prop: 'operate',
        label: $t('common.operate'),
        align: 'center',
        fixed: 'right',
        width: 80,
        formatter: (row: any) => (
          <div class="flex-center">
            <ElPopconfirm title="确定删除该记录吗？" onConfirm={() => handleDelete(row.id)}>
              {{
                reference: () => (
                  <ElButton size="small" plain type="danger">
                    删除
                  </ElButton>
                )
              }}
            </ElPopconfirm>
          </div>
        )
      }
    ]
  });

const { checkedRowKeys, onBatchDeleted } = useTableOperate(data as any, getData);

// Handle single record deletion
async function handleDelete(id: string) {
  try {
    await removeQuotaChangeBatch([id]);
    ElMessage({
      message: '删除成功',
      type: 'success'
    });
    await getData();
  } catch {
    ElMessage.error('删除失败');
  }
}

// Handle batch deletion
async function handleBatchDelete() {
  if (checkedRowKeys.value.length < 1) {
    ElMessage({
      message: '请先勾选数据',
      type: 'warning'
    });
    return;
  }

  try {
    await ElMessageBox.confirm('确定批量删除选中记录吗？', '注意', {
      confirmButtonText: '确 定',
      cancelButtonText: '取 消',
      type: 'warning'
    });

    await removeQuotaChangeBatch(checkedRowKeys.value);
    onBatchDeleted();

    ElMessage({
      message: '删除成功',
      type: 'success'
    });
  } catch {
    // 取消删除或发生错误
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize);
  getData();
});
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <QuotaChangeSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            @delete="handleBatchDelete"
            @refresh="getData"
          />
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          :max-height="maxHeight"
          @selection-change="(selection: QuotaChange[]) => checkedRowKeys = selection.map(item => String(item.id))"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>

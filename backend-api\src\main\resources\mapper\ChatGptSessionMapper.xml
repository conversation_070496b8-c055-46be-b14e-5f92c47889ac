<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.seven.share.mapper.ChatGptSessionMapper">
    <update id="bindUserGptSession">
        UPDATE chatgpt_session
        SET user_id = #{id},
            exclusive_expire_time = #{expireTime}
        WHERE
            id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")" >
            #{id}
        </foreach>
    </update>

    <update id="cancelUserGptSessionRelation">
        UPDATE chatgpt_session
        SET user_id = null,
            exclusive_expire_time = null
        WHERE
        id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")" >
            #{id}
        </foreach>
    </update>

    <select id="selectPageInfo" resultType="org.seven.share.common.pojo.dto.ChatGptSessionDto">
        select s.* , u.userToken from chatgpt_session s left join chatgpt_user u on s.user_id = u.id
        <where>
            s.deleted_at is null
            <if test="query != null and query != ''">
                and (s.carID LIKE CONCAT('%', #{query}, '%') or s.email LIKE CONCAT('%', #{query}, '%'))
            </if>
            <if test="sortProp != null and sortProp !=''">
                order by ${sortProp} ${sortOrder}
            </if>
            <if test="sortProp == null or sortProp == ''">
                order by createTime desc
            </if>
        </where>
    </select>

</mapper>

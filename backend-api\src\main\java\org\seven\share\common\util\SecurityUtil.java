package org.seven.share.common.util;


import org.seven.share.common.exception.ServiceException;
import org.seven.share.common.pojo.entity.SysUser;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Optional;

import static org.seven.share.common.enums.CacheConstants.TENANT_USER_CACHE_PREFIX;
import static org.seven.share.common.util.ConstantUtil.SYS_TENANT_ID;


/**
 * SecurityUtil
 */
public class SecurityUtil {

    public static SysUser getSysUser() {
        try {
            SecurityContext ctx = SecurityContextHolder.getContext();
            Authentication auth = ctx.getAuthentication();
            SysUserDetail sysUserDetail = (SysUserDetail) auth.getPrincipal();
            return sysUserDetail.getSysUser();
        } catch (Exception e) {
            return null;
        }
    }

    public static UserDetails getUserDetails() {
        UserDetails userDetails;
        try {
            userDetails = (UserDetails) SecurityContextHolder.getContext()
                    .getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new ServiceException(401, "未认证");
        }
        return userDetails;
    }

    /**
     * 获取用户的租户id
     * @return
     */
    public static String getTenantId() {
        try {
            SysUser sysUser = getSysUser();
            if (sysUser != null) {
               return sysUser.getTenantId();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

}

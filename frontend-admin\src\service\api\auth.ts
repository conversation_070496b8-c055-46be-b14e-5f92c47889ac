import { request } from '../request';

/**
 * Login
 *
 * @param userName User name
 * @param password Password
 * @param code Verification code
 */
export function fetchLogin(userName: string, password: string, code: string, uuid: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/login',
    method: 'post',
    data: {
      userName,
      password,
      code,
      uuid
    }
  });
}

/** Get user info */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({ url: '/auth/getUserInfo' });
}

/**
 * Refresh token
 *
 * @param refreshToken Refresh token
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken
    }
  });
}

/**
 * Update password
 *
 * @param oldPassword Old password
 * @param newPassword New password
 */
export function fetchUpdatePassword(oldPassword: string, newPassword: string) {
  return request({
    url: '/auth/updatePassword',
    method: 'post',
    params: {
      oldPassword,
      newPassword
    }
  });
}

/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } });
}

/** Get login verification code */
export function fetchLoginCode() {
  return request<{ code: string; uuid: string }>({
    url: '/auth/code',
    method: 'get'
  });
}

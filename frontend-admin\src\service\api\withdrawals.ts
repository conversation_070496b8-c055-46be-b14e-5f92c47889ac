import { request } from '../request';


// 分页查询提现信息
export function fetchWithdrawPage (params:any) {
    return request({
        url: '/withdrawals/getWithdrawPage',
        method: 'get',
        params
    });
}

// 删除提现记录
export function removeWithdrawBatchByIds (data:any) {
    return request({
        url: '/withdrawals/delete',
        data,
        method: 'delete',
    });
}

// 审批提现申请
export function fetchApproval (params:any) {
    return request({
        url: '/withdrawals/approval',
        method: 'get',
        params
    });
}

// 驳回申请
export function rejectApproval (params:any) {
    return request({
        url: '/withdrawals/reject',
        params,
        method: 'get',
    });
}

// 待审批数量
export function fetchPendingCount () {
    return request({
        url: '/withdrawals/pendingCount',
        method: 'get',
    });
}

// 获取邀请详情
export function getInviteDetails(params: any) {
    return request({
        url: '/withdrawals/getWithdrawPage',
        method: 'get',
        params
    });
}

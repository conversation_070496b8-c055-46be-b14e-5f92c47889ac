<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('redeem.dialogTitle')"
    :width="dialogWidth"
    @close="handleClose"
  >
    <div>
      <!-- 输入卡密的输入框 -->
      <el-input
        v-model="cardCode"
        :placeholder="$t('redeem.placeholderCardCode')"
        clearable
        class="input-card-code"
      ></el-input>
    </div>
    <template #footer>
      <!-- 弹窗底部的按钮 -->
      <el-button @click="handleClose">{{ $t('redeem.cancelButton') }}</el-button>
      <el-button color="#18A058" @click="redeemCard">{{ $t('redeem.redeemButton') }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { redeemCode } from '@/api/redemption.js';
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';  // 引入国际化函数

import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore();

const { t } = useI18n(); // 初始化国际化

const userId = computed(() => userStore.id);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const cardCode = ref('');
const emit = defineEmits(['update:visible']);

const dialogVisible = ref(props.visible);
const noticeContent = ref(props.notice);

const windowWidth = ref(window.innerWidth);
const dialogWidth = computed(() => {
  if (windowWidth.value < 768) return '95%';
  if (windowWidth.value < 1024) return '70%';
  return '40%';
});

const handleClose = () => {
  emit('update:visible', false);
  cardCode.value = ''
};

watch(
  () => props.visible,
  (newValue) => {
    dialogVisible.value = newValue;
  }
);

watch(
  () => props.notice,
  (newValue) => {
    noticeContent.value = newValue || '';
  }
);

// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

// 生命周期钩子
onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

const redeemCard = async () => {
  if (cardCode.value == '') {
    ElMessage({
      message: t('redeem.inputWarning'),  // 使用国际化提示
      type: 'warning',
      plain: true,
    });
    return;
  }
  const params = {
    key: cardCode.value,
    userId: userId.value,
  };
  await redeemCode(params);
  ElMessage({
    message: t('redeem.redeemSuccess'),  // 使用国际化成功消息
    type: 'success',
    plain: true,
  });
  handleClose()
};
</script>

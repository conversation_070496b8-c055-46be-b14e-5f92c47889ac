import { createRouter, createWebHashHistory } from 'vue-router'
import { Menu, Document, CreditCard, Avatar, Histogram, Setting, Platform, WarnTriangleFilled, Money, Comment, DocumentAdd, SetUp, GoodsFilled, Ticket, Key, ChatDotSquare, Position, EditPen,Lock } from '@element-plus/icons-vue'
import { useSiteStore } from '@/store/modules/site'
// 创建一个获取主题类型的函数
const getThemeComponent = (componentName) => {
  return () => {
    const siteStore = useSiteStore()
    let theme = siteStore.themeName === 'basic' ? '-basic' : ''
    return import(`@/views/${componentName}${theme}.vue`)
  }
}
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: '加载中'
    }
  },
  {
    path: '/home',
    name: 'List',
    component: () => import('@/views/front/CarList'),
    meta: {
      title: '快速开始'
    }
  },
  {
    path: '/image',
    name: 'Image',
    component: () => import('@/views/front/components/Image'),
    meta: {
      title: '在线绘图'
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/front/Profile'),
    meta: {
      title: '个人中心'
    }
  },
  {
    path: '/external-profile',
    name: 'external-Profile',
    component: () => import('@/views/front/ExternalProfile'),
    meta: {
      title: '个人中心外部'
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: getThemeComponent('login'),
    hidden: true,
    meta: {
      title: '用户登录'
    }
  },
  {
    path: '/shop',
    name: 'Shop',
    component: () => import('@/views/front/components/Shop.vue'),
    hidden: true,
    meta: {
      title: '商店'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: getThemeComponent('register'),
    hidden: true,
    meta: {
      title: '用户注册'
    }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: getThemeComponent('forgot-password'),
    hidden: true,
    meta: {
      title: '忘记密码'
    }
  },
  {
    path: '/401',
    name: '401',
    component: () => import('@/views/error/401'),
    meta: {
      title: '登录过期'
    }
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/error/403'),
    meta: {
      title: '无权限'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: '404',
    component: () => import('@/views/error/404'),
    meta: {
      title: '页面找不到'
    }
  },
];


const router = createRouter({
  history: createWebHashHistory(),
  routes
});

export default router;
package org.seven.share.service.audit;

import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.BucketConfiguration;
import io.github.bucket4j.ConsumptionProbe;
import io.github.bucket4j.distributed.ExpirationAfterWriteStrategy;
import io.github.bucket4j.distributed.proxy.ProxyManager;
import io.github.bucket4j.distributed.serialization.Mapper;
import io.github.bucket4j.redis.jedis.Bucket4jJedis;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.Protocol;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: RateLimitService2
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/12
 */
@Service
@Slf4j
public class RateLimitPersistService {

    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.port}")
    private int redisPort;

    @Value("${spring.redis.password}")
    private String redisPassword;

    private JedisPool jedisPool;
    private ProxyManager<String> proxyManager;

    @PostConstruct
    public void init(){
        // 初始化 JedisPool（单例）
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(100); // 最大连接数
        poolConfig.setMaxIdle(50);   // 最大空闲连接数
        poolConfig.setMinIdle(10);   // 最小空闲连接数
        poolConfig.setTestOnBorrow(true); // 获取连接时检查有效性
        if (redisPassword != null && !redisPassword.isEmpty()) {
            jedisPool = new JedisPool(poolConfig, redisHost, redisPort, Protocol.DEFAULT_TIMEOUT, redisPassword);
        } else {
            jedisPool = new JedisPool(poolConfig, redisHost, redisPort);
        }

        // 初始化 ProxyManager（单例）
        proxyManager = Bucket4jJedis.casBasedBuilder(jedisPool)
                .expirationAfterWrite(ExpirationAfterWriteStrategy.none())
                .keyMapper(Mapper.STRING)
                .build();
    }

    public Bucket getBucket(long limit, Duration duration, String key){
        Bandwidth bandwidth = Bandwidth.builder()
                .capacity(limit)
                .refillIntervally(limit, duration)
                .build();

        BucketConfiguration newConfiguration = BucketConfiguration.builder()
                .addLimit(bandwidth)
                .build();
        // 检查速率配置是否变更，如果变更，则删除旧的限流配置，使用新的
        Optional<BucketConfiguration> bucketConfiguration = proxyManager.getProxyConfiguration(key);
        Bucket bucket = proxyManager.getProxy(key, () -> newConfiguration);
        if (bucketConfiguration.isPresent()) {
            BucketConfiguration oldConfiguration = bucketConfiguration.get();
            if (!Objects.equals(oldConfiguration, newConfiguration)) {
                log.info("速率周期发生变化，使用新的限流配置...");
                // 删除现有的 Redis 键
                try (Jedis jedis = jedisPool.getResource()) {
                    jedis.del(key.getBytes(StandardCharsets.UTF_8));  // 删除旧的限流配置
                }
                bucket = proxyManager.getProxy(key, () -> newConfiguration);
            }
        }
        return bucket;
    }

    public long getWaitTime(Bucket bucket) {
        // 检查是否可以消费令牌，并获取到下一个令牌补充的时间
        ConsumptionProbe probe = bucket.tryConsumeAndReturnRemaining(1);
        if (!probe.isConsumed()) {
            long nanosToWaitForRefill = probe.getNanosToWaitForRefill();
            long secondsToWaitForRefill = TimeUnit.NANOSECONDS.toSeconds(nanosToWaitForRefill);
            log.info("下一个令牌可用倒计时: {} 秒", secondsToWaitForRefill);
            return secondsToWaitForRefill;
        } else {
            log.info("当前没有速率限制，立即可用");
            return 0;
        }
    }
}

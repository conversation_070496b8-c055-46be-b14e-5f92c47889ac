package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: ChatGptWithdrawalsEntity
 * @Description:
 * @Author: Seven
 * @Date: 2024/10/15
 */
@TableName("chatgpt_withdrawals")
@Data
public class ChatGptWithdrawalsEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @TableField(value = "createTime", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "updateTime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField(value = "withdrawalTime", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime withdrawalTime;

    /**
     * 提现人
     */
    private String username;

    /**
     * 提现人id
     */
    @TableField("userId")
    private Long userId;

    /**
     * 提现金额
     */
    @TableField("withdrawalMoney")
    private Double withdrawalMoney;

    /**
     * 二维码存放地址
     */
    @TableField("withdrawalQrcode")
    private String withdrawalQrcode;

    private Integer status;

    private String remark;

    private String contact;

}

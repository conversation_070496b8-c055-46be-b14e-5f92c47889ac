<script setup>
import { ref } from 'vue';
import { ElDialog } from 'element-plus';
import WangEditor from 'wangeditor';

defineOptions({ name: 'HtmlEditor' });

const dialogVisible = ref(false);
const editor = ref(null);
const editorRef = ref(null);
const currentContent = ref('');

const emit = defineEmits(['updateContent']);

function openDialog(content) {
  currentContent.value = content;
  dialogVisible.value = true;
  // 等待 DOM 更新后初始化编辑器
  setTimeout(() => {
    initEditor();
  }, 0);
}

function initEditor() {
  if (editorRef.value) {
    if (editor.value) {
      editor.value.destroy();
    }
    editor.value = new WangEditor(editorRef.value);
    editor.value.config.height = 500;
    editor.value.config.zIndex = 3000;
    editor.value.config.placeholder = '请输入内容';
    editor.value.config.onchange = (html) => {
      currentContent.value = html;
    };
    editor.value.create();
    editor.value.txt.html(currentContent.value);
  }
}

function handleConfirm() {
  emit('updateContent', currentContent.value);
  handleClose();
}

function handleClose() {
  dialogVisible.value = false;
  if (editor.value) {
    editor.value.destroy();
    editor.value = null;
  }
}

defineExpose({
  openDialog
});
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="编辑内容"
    :before-close="handleClose"
    fullscreen 
    append-to-body
    destroy-on-close
  >
    <div ref="editorRef" class="bg-white"></div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
:deep(.w-e-toolbar) {
  background: inherit !important;
  border-color: var(--el-border-color) !important;
}
:deep(.w-e-text-container) {
  background: inherit;
  border-color: var(--el-border-color) !important;
}
</style> 
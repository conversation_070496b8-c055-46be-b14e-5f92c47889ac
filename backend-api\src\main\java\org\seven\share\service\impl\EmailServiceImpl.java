package org.seven.share.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.pojo.entity.ChatGptConfigEntity;
import org.seven.share.service.ChatGptConfigService;
import org.seven.share.service.EmailService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.seven.share.constant.CacheConstant.VERIFICATION_CODE_PREFIX;


/**
 * @ClassName: EmailServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/29
 */
@Service
@Slf4j
public class EmailServiceImpl implements EmailService {
    @Resource
    private JavaMailSender mailSender;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private TemplateEngine templateEngine;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private String emailFrom;

    /**
     * 从数据库中读取邮件配置信息
     */
    private void initMailSender(String sender) {
        List<ChatGptConfigEntity> entityList = chatGptConfigService.list(new LambdaQueryWrapper<ChatGptConfigEntity>()
                .like(ChatGptConfigEntity::getKey, "email")
                .or()
                .eq(ChatGptConfigEntity::getKey, "siteName"));
        Map<String, String> map = entityList.stream().collect(Collectors.toMap(ChatGptConfigEntity::getKey, ChatGptConfigEntity::getValue));
        String siteName = map.getOrDefault("siteName", "系统邮件");
        String email = map.get("email");
        emailFrom = siteName + "<" + email + ">" ;
        String emailWhitelist = map.get("emailWhitelist");
        if (StrUtil.isNotEmpty(emailWhitelist)) {
            checkEmailWhiteList(sender, emailWhitelist);
        }
        JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
        javaMailSender.setHost(map.get("emailHost"));
        javaMailSender.setPort(Integer.parseInt(map.getOrDefault("emailPort", "0")));
        javaMailSender.setPassword(map.get("emailPassword"));
        javaMailSender.setDefaultEncoding("UTF-8");
        javaMailSender.setUsername(email);
        Properties props = javaMailSender.getJavaMailProperties();
        props.put("mail.smtp.auth", "true"); // 开启认证
        if ("587".equals(map.get("emailPort"))) {
            props.put("mail.smtp.starttls.enable", "true"); // // 对于587端口，需要开启STARTTLS
            props.put("mail.transport.protocol", "smtp");
        } else {
            props.put("mail.transport.protocol", "smtps");
        }
        props.put("mail.debug", "false"); // 开启debug模式
        javaMailSender.setJavaMailProperties(props);
        this.mailSender = javaMailSender;
    }

    private static void checkEmailWhiteList(String sender, String emailWhitelist) {
        // 如果有白名单，则要验证邮件白名单
        List<String> list = Optional.ofNullable(emailWhitelist)
                .map(e -> Arrays.stream(e.split(",")).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        boolean exist = list.stream().anyMatch(s -> sender != null && sender.endsWith(s));
        log.info("是否在白名单中: {}", exist);
        if (!exist) {
            throw new CustomException("系统暂不支持此后缀邮箱"+ Objects.requireNonNull(sender).substring(sender.indexOf("@")));
        }
    }

    /**
     * 发送邮件
     * @param email 接收人
     * @param template 邮件模板名称
     */
    @Override
    public void sendEmail(String email, String template, String subject, Map<String, String> contentMap) {
        initMailSender(email);
        if (ObjectUtil.isEmpty(contentMap) && contentMap.isEmpty()) {
            throw new CustomException("模板信息不能为空");
        }
        // 组装内容
        Context context = new Context();
        Set<String> keys = contentMap.keySet();
        for (String key : keys) {
            context.setVariable(key, contentMap.get(key));
            if ("verificationCode".equals(key)) {
                saveVerificationCode(email, contentMap.get(key));
            }
        }

        // 获取模板
        String body = templateEngine.process(template, context);
        try{
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMessage);
            mimeMessageHelper.setFrom(emailFrom);
            mimeMessageHelper.setTo(email);
            mimeMessageHelper.setSubject(subject);
            mimeMessageHelper.setText(body, true);
            mailSender.send(mimeMessage);
            log.info("{}，邮件发送成功", subject);
        }catch (Exception e) {
            log.error("邮件发送失败！", e);
            if (e.getMessage().contains("550 The recipient may contain a non-existent account")) {
                throw new CustomException("邮箱不存在，请检查邮箱是否填写正确");
            }
            throw new CustomException("邮件发送失败");
        }
    }

    @Override
    public void sendEmailNotTemplate(String email, String subject, String content) {
        try {
            initMailSender(email);
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage);
            helper.setFrom(emailFrom);
            helper.setTo(email);
            helper.setSubject(subject);
            helper.setText(content, true);
            mailSender.send(mimeMessage);
            log.info("无模板邮件发送成功");
        } catch (MessagingException e) {
            log.error("发送邮件失败：",e);
            throw new CustomException("无模板邮件发送失败，请检查配置");
        }
    }

    @Override
    public void sendPaySuccessEmail(String template, String subject, Map<String, String> contentMap) {
        // 获取管理员邮箱
        String email = chatGptConfigService.getValueByKey("email");
        if (StrUtil.isNotEmpty(email)) {
            sendEmail(email, template, subject, contentMap);
        }
    }

    /**
     * 存到redis中，并设置30分钟过期。
     * @param email
     * @param verificationCode
     */
    private void saveVerificationCode(String email, String verificationCode) {
        String key = VERIFICATION_CODE_PREFIX + email;
        stringRedisTemplate.opsForValue().set(key, verificationCode, 30, TimeUnit.MINUTES);
    }
}

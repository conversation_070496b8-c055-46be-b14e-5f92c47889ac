<template>
  <!-- 用户基本信息 -->
  <div class="p-4 rounded-lg max-w-5xl mx-auto">
    <div class="p-4 rounded-lg">
      <div class="flex items-start space-x-4">
        <el-avatar size="large" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" />
        <div class="flex-1 min-w-0">
          <div class="flex items-center justify-between">
            <div class="min-w-0 flex-shrink">
              <h4 class="text-lg font-medium font-semibold mb-1.5 truncate">{{ form.username }}</h4>
              <p v-if="loginType === 1" class="text-sm text-gray-900 dark:text-gray-400 truncate">{{ form.email }}
                <el-button v-if="!form.email" link type="warning" @click="bindEmail">{{ t('userProfile.bindEmail')
                  }}</el-button>
              </p>
            </div>
            <div class="flex items-center space-x-2 flex-shrink-0">
              <el-tag v-if="form.email" type="success">
                {{ t('userProfile.verified') }}
              </el-tag>
              <el-tag v-else type="info">
                {{ t('userProfile.unVerified') }}
              </el-tag>
              <!-- <el-button
                  type="danger"
                  size="small"
                  @click="handleLogout"
                  class="ml-2"
                >
                  {{ t('userProfile.logout') }}
                </el-button> -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 表单区域 -->
    <el-tabs type="border-card">
      <el-tab-pane :label="t('userProfile.benefits')" v-if="showGptExpireTime || showClaudeExpireTime">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
            <!-- ChatGPT 卡片 -->
            <div class="bg-gradient-to-br from-purple-900 to-purple-700 rounded-lg p-3 md:p-4 shadow-lg"
              v-if="showGptExpireTime">
              <div class="flex justify-between items-center mb-2">
                <h3 class="text-base md:text-lg font-semibold text-white">ChatGPT Base</h3>
                <el-icon class="text-purple-200">
                  <Medal />
                </el-icon>
              </div>
              <div class="space-y-2">
                <div class="flex items-center text-purple-200 text-sm">
                  <el-icon class="mr-1.5">
                    <Timer />
                  </el-icon>
                  <span>{{ userData.expireTime ? formatDate(userData.expireTime) : t('userProfile.noPlan') }}</span>
                </div>
              </div>
            </div>

            <!-- ChatGPT Plus 卡片 -->
            <div class="bg-gradient-to-br from-purple-900 to-purple-700 rounded-lg p-3 md:p-4 shadow-lg"
              v-if="showGptExpireTime">
              <div class="flex justify-between items-center mb-2">
                <h3 class="text-base md:text-lg font-semibold text-white">ChatGPT Plus</h3>
                <el-icon class="text-blue-200">
                  <Medal />
                </el-icon>
              </div>
              <div class="space-y-2">
                <div class="flex items-center text-blue-200 text-sm">
                  <el-icon class="mr-1.5">
                    <Timer />
                  </el-icon>
                  <span>{{ userData.plusExpireTime ? formatDate(userData.plusExpireTime) : t('userProfile.noPlan')
                    }}</span>
                </div>
              </div>
            </div>

            <!-- Claude 卡片 -->
            <div class="bg-gradient-to-br from-emerald-900 to-emerald-700 rounded-lg p-3 md:p-4 shadow-lg"
              v-if="showClaudeExpireTime">
              <div class="flex justify-between items-center mb-2">
                <h3 class="text-base md:text-lg font-semibold text-white">Claude Base</h3>
                <el-icon class="text-indigo-200">
                  <Medal />
                </el-icon>
              </div>
              <div class="space-y-2">
                <div class="flex items-center text-indigo-200 text-sm">
                  <el-icon class="mr-1.5">
                    <Timer />
                  </el-icon>
                  <span>{{ userData.claudeExpireTime ? formatDate(userData.claudeExpireTime) : t('userProfile.noPlan')
                    }}</span>
                </div>
              </div>
            </div>

            <!-- Claude Pro 卡片 -->
            <div class="bg-gradient-to-br from-emerald-900 to-emerald-700 rounded-lg p-3 md:p-4 shadow-lg"
              v-if="showClaudeExpireTime">
              <div class="flex justify-between items-center mb-2">
                <h3 class="text-base md:text-lg font-semibold text-white">Claude Pro</h3>
                <el-icon class="text-emerald-200">
                  <Medal />
                </el-icon>
              </div>
              <div class="space-y-2">
                <div class="flex items-center text-emerald-200 text-sm">
                  <el-icon class="mr-1.5">
                    <Timer />
                  </el-icon>
                  <span>{{ userData.claudeProExpireTime ? formatDate(userData.claudeProExpireTime) :
                    t('userProfile.noPlan') }}</span>
                </div>
              </div>
            </div>
            <!-- grok 卡片 -->
            <div class="bg-gradient-to-br from-red-900 to-indigo-700 rounded-lg p-3 md:p-4 shadow-lg"
              v-if="showGrok">
              <div class="flex justify-between items-center mb-2">
                <h3 class="text-base md:text-lg font-semibold text-white">Grok Base</h3>
                <el-icon class="text-indigo-200">
                  <Medal />
                </el-icon>
              </div>
              <div class="space-y-2">
                <div class="flex items-center text-indigo-200 text-sm">
                  <el-icon class="mr-1.5">
                    <Timer />
                  </el-icon>
                  <span>{{ userData.grokExpireTime ? formatDate(userData.grokExpireTime) : t('userProfile.noPlan')
                    }}</span>
                </div>
              </div>
            </div>
            <div class="bg-gradient-to-br from-red-900 to-indigo-700 rounded-lg p-3 md:p-4 shadow-lg"
              v-if="showGrok">
              <div class="flex justify-between items-center mb-2">
                <h3 class="text-base md:text-lg font-semibold text-white">Grok Super</h3>
                <el-icon class="text-indigo-200">
                  <Medal />
                </el-icon>
              </div>
              <div class="space-y-2">
                <div class="flex items-center text-indigo-200 text-sm">
                  <el-icon class="mr-1.5">
                    <Timer />
                  </el-icon>
                  <span>{{ userData.grokSuperExpireTime ? formatDate(userData.grokSuperExpireTime) : t('userProfile.noPlan')
                    }}</span>
                </div>
              </div>
            </div>
        </div>
        <div v-if="siteStore.showModelRate === 'true'">
          <el-divider>{{ t('userProfile.limits') }}</el-divider>

          <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4 p-4">
            <template v-for="(info, model) in userLimits" :key="model">
              <div
                class="relative bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 p-4 border border-gray-100 dark:border-gray-700">
                <!-- 模型名称 -->
                <div class="mb-3">
                  <div class="text-sm font-medium text-gray-700 dark:text-gray-300 truncate" :title="model">
                    {{ model }}
                  </div>
                </div>

                <!-- 限制次数和时间周期 -->
                <div class="flex items-center justify-between">
                  <div :class="info.limit === 0 ? 'text-red-500 font-bold' : 'text-2xl font-bold text-purple-600 dark:text-purple-400'">
                    {{ info.limit === 0 ? t('userProfile.useBan') : info.limit }}
                  </div>
                  <div
                    v-if="info.limit !== 0 && info.per !== null"
                    class="text-xs px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 rounded-full">
                    {{ info.per === null ? t('userProfile.noLimit') : formatPeriod(info.per) }}
                  </div>
                </div>

                <!-- 底部装饰线 -->
                <div
                  class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-b-xl opacity-75">
                </div>
              </div>
            </template>
          </div>
        </div>
      </el-tab-pane>

      <!-- 新增支付记录标签页 -->
      <el-tab-pane :label="t('userProfile.paymentHistory')" v-if="siteStore.showPaymentHistory === 'true'">
        <div class="p-4">
          <el-table :data="paymentHistory" style="width: 100%" :empty-text="t('userProfile.noPaymentHistory')">
            <el-table-column prop="tradeNo" align="center" show-overflow-tooltip
              :label="t('userProfile.tradeNo')"></el-table-column>
            <el-table-column prop="updateTime" align="center" min-width="100" :label="t('userProfile.paymentDate')">
              <template #default="scope">
                {{ formatDate(scope.row.updateTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="money" align="center" :label="t('userProfile.amount')">
              <template #default="scope">
                {{ scope.row.money }}¥
              </template>
            </el-table-column>
            <el-table-column prop="status" align="center" :label="t('userProfile.status')" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'success' ? 'success' : 'info'">
                  {{ t(`userProfile.${scope.row.status}`) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <el-tab-pane :label="t('userProfile.changePassword')" v-if="loginType === 1">
        <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-position="top"
          class="space-y-4">
          <el-form-item :label="t('userProfile.currentPassword')" prop="currentPassword" class="!mb-4">
            <el-input v-model="passwordForm.currentPassword" type="password"
              :placeholder="t('userProfile.enterCurrentPassword')" class="custom-dark-input" show-password />
          </el-form-item>

          <el-form-item :label="t('userProfile.newPassword')" prop="newPassword" class="!mb-4">
            <el-input v-model="passwordForm.newPassword" type="password"
              :placeholder="t('userProfile.enterNewPassword')" class="custom-dark-input" show-password />
          </el-form-item>

          <el-form-item :label="t('userProfile.confirmPassword')" prop="confirmPassword">
            <el-input v-model="passwordForm.confirmPassword" type="password"
              :placeholder="t('userProfile.enterConfirmPassword')" class="custom-dark-input" show-password />
          </el-form-item>
          <!-- 底部按钮 -->
          <div class="flex justify-end space-x-3">
            <el-button type="primary" @click="handleSubmit" :loading="loading"
              class="!bg-purple-600 !border-purple-600 hover:!bg-purple-700">
              {{ t('userProfile.save') }}
            </el-button>
          </div>
        </el-form>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import 'element-plus/es/components/message/style/css'
import 'element-plus/es/components/message-box/style/css'
import { useUserStore } from '@/store/modules/user';
import { removeToken } from '@/utils/auth.js';
import { encrypt } from '@/utils/encrypt';
import { formatDate } from '@/utils/date.js';
import { useSiteStore } from '@/store/modules/site';
import { useRouter } from 'vue-router';
const router = useRouter();
const siteStore = useSiteStore();
import {
  getUser,
  changePassword,
  bindUserEmail,
  getPaymentHistory,
  getUserLimits
} from '@/api/user.js';
import Cookies from 'js-cookie';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const userStore = useUserStore();
const loginType = computed(() => userStore.loginType)

onMounted(() => {
  fetchUserInfo()
  fetchUserLimits()
})

// 表单refs
const passwordFormRef = ref()
const showGptExpireTime = computed(() => {
  return siteStore.freeNodeName || siteStore.normalNodeName || siteStore.plusNodeName || siteStore.soruxGptSideBarName
})
const showClaudeExpireTime = computed(() => {
  return siteStore.claudeNodeName
})
const showGrok = computed(() => { 
  return siteStore.grokNodeName
})
// 弹窗状态
const isOpen = ref(false)
const loading = ref(false)

// 基本信息表单
const form = reactive({
  username: '',
  email: '',
})

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码验证规则
const passwordRules = {
  currentPassword: [
    { required: true, message: t('userProfile.currentPassword'), trigger: 'blur' },
    { min: 6, message: t('userProfile.passwordLengthError'), trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: t('userProfile.newPasswordRequired'), trigger: 'blur' },
    { min: 6, message: t('userProfile.passwordLengthError'), trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: t('userProfile.confirmPasswordRequired'), trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error(t('userProfile.passwordMismatch')))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}
const bindEmail = () => {
  ElMessageBox.prompt('请输入邮箱信息', '提示', {
    confirmButtonText: '绑定',
    cancelButtonText: '取消',
    inputPattern:
      /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
    inputErrorMessage: '邮箱无效',
  })
    .then(({ value }) => {
      const params = {
        userId: userStore.id,
        email: value,
      };
      bindUserEmail(params) // Assume this API call is defined elsewhere in your project
        .then((res) => {
          ElMessage({
            type: 'success',
            message: `邮箱绑定成功`,
          });
          fetchUserInfo(); // Refresh user data
        })
        .catch((err) => {
          console.error(err);
        });
    })
    .catch((error) => {
      console.log('取消', error);
    });
};
const userData = ref({})
const paymentHistory = ref([])

// 打开弹窗
async function fetchUserInfo () {
  const param = {
    userName: userStore.username,
  };

  userData.value = await getUser(param);
  passwordForm.userName = userData.value.userToken;
  form.email = userData.value.email;
  form.username = userData.value.userToken;

  // 获取支付记录
  try {
    const data = await getPaymentHistory(userStore.username);
    paymentHistory.value = data;
  } catch (error) {
    console.error('Failed to fetch payment history:', error);
  }
}
const openDialog = async () => {
  isOpen.value = true
  fetchUserInfo()
  fetchUserLimits()
}

const userLimits = ref({})
const fetchUserLimits = async () => {
  userLimits.value = await getUserLimits({ id: userStore.id });
}

// 关闭弹窗
const closeDialog = () => {
  passwordFormRef.value?.resetFields()
  isOpen.value = false
}

// 提交表单
const handleSubmit = async () => {
  passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      await changePassword(passwordForm);
      // 将用户名和密码更新到cookie中
      Cookies.set('username', passwordForm.userName, { expires: 30 });
      Cookies.set('password', encrypt(passwordForm.newPassword), {
        expires: 30,
        secure: true,
      });
      // 密码修改成功后，删除token，让用户重新登录。
      removeToken();
      ElMessage({
        message: t('changePassword.success'),
        type: 'success',
        plain: true,
      });
      passwordFormRef.value.resetFields();
    } else {
      return false;
    }
  });
};

// 添加退出登录方法
const handleLogout = () => {
  userStore.logOut();
  Cookies.remove('gfsessionid');
  Cookies.set('visitor', true, { expires: 30 });
  router.push('/login');
}

const formatPeriod = (per) => {
  const periods = {
    '1s': t('userProfile.perSecond'),
    '1m': t('userProfile.perMinute'),
    '1h': t('userProfile.perHour'),
    '3h': t('userProfile.perThreeHour'),
    '1d': t('userProfile.perDay'),
    '1w': t('userProfile.perWeek'),
    '1y': t('userProfile.perYear')
  };
  return periods[per] || per;
};

</script>
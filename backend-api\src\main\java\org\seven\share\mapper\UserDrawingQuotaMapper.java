package org.seven.share.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.seven.share.common.pojo.entity.UserDrawingQuotaEntity;

/**
 * @InterfaceName: UserDrawingQuotDao
 * @Description:
 * @Author: Seven
 * @Date: 2025/4/25
 */
@Mapper
public interface UserDrawingQuotaMapper extends BaseMapper<UserDrawingQuotaEntity> {
    @Update("UPDATE user_drawing_quota" +
            "    SET remaining_quota = remaining_quota - #{count}," +
            "        used_quota = used_quota + #{count}," +
            "        updated_at = NOW()" +
            "    WHERE user_id = #{uid} AND remaining_quota >= #{count}")
    int decrementQuota(Long uid, int count);

    @Update("UPDATE user_drawing_quota" +
            "    SET remaining_quota = remaining_quota + #{quota}," +
            "        total_quota = total_quota + #{quota}," +
            "        reset_at = DATE_ADD(COALESCE(reset_at, NOW()), INTERVAL #{days} DAY)," +
            "        updated_at = NOW()" +
            "    WHERE user_id = #{uid}")
    void updateUserQuotaByUid(Long uid, Long quota, int days);

    @Update("UPDATE user_drawing_quota" +
            "    SET remaining_quota = remaining_quota - #{quota}," +
            "        total_quota = used_quota - #{quota}," +
            "        reset_at = DATE_SUB(COALESCE(reset_at, NOW()), INTERVAL #{days} DAY)," +
            "        updated_at = NOW()" +
            "    WHERE user_id = #{uid} and (remaining_quota > 0 or total_quota > 0)" )
    void rollbackDrawQuotaByUid(Long uid, Long quota, Integer days);

    @Select("<script>" +
            "SELECT q.*, u.userToken as username " +
            "FROM user_drawing_quota q " +
            "LEFT JOIN chatgpt_user u " +
            "ON q.user_id = u.id " +
            "<where>" +
            "  <if test='username != null and username != \"\"'>" +
            "    AND u.userToken LIKE CONCAT('%', #{username}, '%')" +
            "  </if>" +
            "</where>" +
            "order by created_at desc"+
            "</script>")
    IPage<UserDrawingQuotaEntity> pageUserQuota(Page<UserDrawingQuotaEntity> page, @Param("username") String username);

    @Update("UPDATE user_drawing_quota" +
            "    SET remaining_quota = remaining_quota + #{quota}," +
            "        used_quota = used_quota - #{quota}," +
            "        updated_at = NOW()" +
            "    WHERE user_id = #{uid}" )
    void refundUserDrawQuota(Long uid, int quota);
}

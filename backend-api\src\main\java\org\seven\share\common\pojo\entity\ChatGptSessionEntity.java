package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: ChatGptSession
 * @Description:
 * @Author: Seven
 * @Date: 2024/6/23
 */
@Data
@TableName("chatgpt_session")
public class ChatGptSessionEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @TableField(value = "createTime", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "updateTime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField("deleted_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deletedAt;

    @TableField("email")
    private String email;

    @TableField("password")
    private String password;

    /**
     * 0 禁用 1 启用
     */
    private Integer status;

    /**
     * 0 普通用户 1 Plus用户
     */
    @TableField("isPlus")
    private Integer isPlus;

    @TableField("carID")
    private String carID;

    @TableField("officialSession")
    private String officialSession;

    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remark;

    private Long sort;

    private Long count;

    @TableField(value = "user_id", updateStrategy = FieldStrategy.ALWAYS)
    private String userId;

    @TableField(value = "exclusive_expire_time", updateStrategy = FieldStrategy.ALWAYS)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime exclusiveExpireTime;
}

package org.seven.share.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.pojo.dto.*;
import org.seven.share.common.pojo.entity.ChatGptEPayLogsEntity;
import org.seven.share.common.pojo.entity.ChatGptSubTypeEntity;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.common.pojo.entity.SignInRecordEntity;
import org.seven.share.common.pojo.vo.CarInfoVo;
import org.seven.share.common.pojo.vo.StatisticVo;
import org.seven.share.common.pojo.vo.UserInfoVo;
import org.seven.share.common.pojo.vo.UserPayLogsVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @InterfaceName: UserTokenService
 * @Description:
 * @Author: Seven
 * @Date: 2024/6/22
 */
public interface ChatGptUserService extends IService<ChatGptUserEntity> {
    ChatGptUserEntity createUser(UserDto userDto);

    String authUserInfo(String userToken, String carId, String type, Integer planType);

    void removeBatchByIds(List<Long> ids);

    void updateStatusById(Long id, Integer isPlus);

    StatisticVo getStatisticData();

    void verifyCodeAndSaveUserInfo(RegisterDto registerDto);

    Map<String, Object> login(LoginDto loginDto);

    void resetPassword(ResetPasswordDto resetPasswordDto);

    Map<String, Object> getCarList(String type, Integer page, Integer size);

    void calculateExpirationTime(ChatGptSubTypeEntity subType, ChatGptUserEntity user);

    ChatGptUserEntity getUserInfoByUsername(String userToken);

    void changePassword(ChangePasswordDto changePasswordDto);

    void getRegisterCode(String username, String email);

    void getPasswordRestCode(String username, String email);

    void updateExpireTimeForPlusUsers();

    List<String> getIdsByUser(String userName);

    void updateUser(UserDto userDto);

    IPage<UserDto> getUserOrdersPage(Page<Object> objectPage, String query, String sortProp, String sortOrder);

    Map<String, Object> getValidityAndUsage(String username);

    void updateDailyConversationCount(String username, boolean updateClaudeFlag);

    void updateUserAffQuota(Double money, ChatGptUserEntity user, int orderType);

    void compensateTime(String type, Long minutes);

    List<ChatGptUserEntity> batchCreateUser(BatchUserDto dto);

    Map<String, String> getAccessTokenAndCarId();

    void updateUserEmailByUserId(String userId, String email);

    boolean checkBackAccess(String userId);

    List<UserPayLogsVo> getPaymentHistory(String userToken);

    void updateBatchUserExpireTimeTimeForPlusUsers();

    void logout(HttpServletRequest request, String username);

    SignInRecordEntity signIn(Long userId);

    SignInInfoDTO getSignInInfo(Long userId);

    UserInfoVo getUserInfo(String userName);

    IPage<UserDto> selectPage(Integer page, Integer size, String query, String sortOrder, String sortProp);

    Map<String, ModelLimitDto> getUserLimits(Long id);

    int updateUserInfoAndPayStatus(ChatGptSubTypeEntity subType, ChatGptUserEntity user, ChatGptEPayLogsEntity payLogsEntity);

    Map<String, Object> authCode(String code);

    void dealUserAndGptSessionRelation(ChatGptSubTypeEntity subType, ChatGptUserEntity user);

    String getSassLoginToken(HttpServletRequest request);

    String getGrokLoginToken(HttpServletRequest request, Integer isSuper);

    Map<String, Object> authGrokAccess(String loginToken, Integer isSuper);

    List<CarInfoVo> getGrokCarPage();

    ChatGptUserEntity getUserByLoginToken(String loginToken);

    List<CarInfoVo> getSassCarPage();

    ChatGptUserEntity getByIdWithoutTenant(long userId);

    void updateByIdWithoutTenant(ChatGptUserEntity user);

    void resetPwd(Long id);

    Map<String, String> getLoginToken(HttpServletRequest request);
}

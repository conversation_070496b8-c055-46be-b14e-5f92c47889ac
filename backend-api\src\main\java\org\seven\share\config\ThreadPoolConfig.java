package org.seven.share.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @ClassName: ThreadPoolConfig
 * @Description:
 * @Author: Seven
 * @Date: 2025/3/2
 */
@Slf4j
@Configuration
public class ThreadPoolConfig {
    @Bean(name = "commonAsyncExecutor")
    public Executor commonAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        int corePoolSize = Runtime.getRuntime().availableProcessors() * 2;
        // 核心线程数 - 保持在CPU核心数或略低
        executor.setCorePoolSize(corePoolSize);
        // 最大线程数 - 不建议超过CPU核心数的2倍
        executor.setMaxPoolSize(corePoolSize * 2);
        // 队列大小 - 适当设置以缓冲请求，但不要过大
        executor.setQueueCapacity(1000);
        // 线程前缀名
        executor.setThreadNamePrefix("Common-Async-");
        // 拒绝策略
        executor.setRejectedExecutionHandler((r, e) -> {
            log.warn("Task rejected, queue size: {}, active threads: {}", e.getQueue().size(), e.getActiveCount());
            throw new RejectedExecutionException("Thread pool exhausted");
        });        // 等待任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(10);

        executor.initialize();
        return executor;
    }
}

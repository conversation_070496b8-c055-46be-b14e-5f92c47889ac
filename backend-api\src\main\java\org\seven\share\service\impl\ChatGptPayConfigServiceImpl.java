package org.seven.share.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.seven.share.common.exception.CustomException;
import org.seven.share.mapper.ChatGptPayConfigMapper;
import org.seven.share.common.pojo.entity.ChatGptPayConfigEntity;
import org.seven.share.common.pojo.vo.ChatGptPayConfigVo;
import org.seven.share.service.ChatGptPayConfigService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static org.seven.share.common.util.ConstantUtil.PAYMENT_STATUS_ENABLE;


/**
 * @ClassName: ChatGptPayConfigServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/12
 */
@Service
public class ChatGptPayConfigServiceImpl extends ServiceImpl<ChatGptPayConfigMapper, ChatGptPayConfigEntity>
        implements ChatGptPayConfigService {

    @Resource
    private ChatGptPayConfigMapper chatGptPayConfigMapper;

    @Override
    @Cacheable(cacheNames = "pay-config", key = "'pay_config_list'")
    public List<ChatGptPayConfigVo> listPayments() {
        LambdaQueryWrapper<ChatGptPayConfigEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatGptPayConfigEntity::getStatus, PAYMENT_STATUS_ENABLE); // 启用
        queryWrapper.orderByAsc(ChatGptPayConfigEntity::getSort);

        List<ChatGptPayConfigEntity> list = chatGptPayConfigMapper.selectList(queryWrapper);
        List<ChatGptPayConfigVo> voList = BeanUtil.copyToList(list, ChatGptPayConfigVo.class);
        // 遍历设置 enableH5 字段
        for (int i = 0; i < list.size(); i++) {
            ChatGptPayConfigEntity entity = list.get(i);
            ChatGptPayConfigVo vo = voList.get(i);
            // 调用 entity 中的计算方法来设置 enableH5
            vo.setEnableH5(entity.getExtraData().isEnableH5());
        }
        return voList;
    }

    @Override
    @CacheEvict(cacheNames = "pay-config", allEntries = true)
    public void savePayConfig(ChatGptPayConfigEntity config) {
        // 每种支付类型只能新增一个
        ChatGptPayConfigEntity one = chatGptPayConfigMapper.selectOne(new LambdaQueryWrapper<ChatGptPayConfigEntity>()
                .eq(ChatGptPayConfigEntity::getPaymentsType, config.getPaymentsType()));
        if (one != null) {
            throw new CustomException("该支付方式已存在，不可新增");
        }
        chatGptPayConfigMapper.insert(config);
    }
}

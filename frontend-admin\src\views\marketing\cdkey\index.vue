<script setup lang="tsx">
import { ref } from 'vue';
import { ElButton, ElMessage, ElPopconfirm, ElTag } from 'element-plus';
import { useBoolean } from '@sa/hooks';
import { fetchCdkeyPage, recycleKey, removeCdkeyBatchByIds } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import { usePermission } from '@/hooks/business/auth';
import CdkeySearch from './modules/cdkey-search.vue';
import CdkeyTableHeaderOperation from './modules/cdkey-table-header-operation.vue';
import CdkeyOperateModel, { type OperateType } from './modules/cdkey-operate-modal.vue';
const { hasPermission } = usePermission();
// Define the proper type for your table data
interface Cdkey {
  id: string;
  key: string;
  redeemedTime: number;
  userToken: string;
  status: number;
  isPlus: number;
  isPro: number;
  name: string;
  validDays: string;
  createTime: string;
  updateTime: string;
}

const wrapperRef = ref<HTMLElement | null>(null);
const operateType = ref<OperateType>('add');
const { bool: visible, setTrue: openModal } = useBoolean();

const { columns, columnChecks, data, loading, pagination, getData, searchParams, resetSearchParams, getDataByPage } =
  useTable<Cdkey>({
    apiFn: fetchCdkeyPage,
    columns: () => [
      { type: 'selection', width: 48 },
      { prop: 'index', label: $t('common.index'), width: 64 },
      { prop: 'key', label: '激活码', minWidth: 300, showOverflowTooltip: true },
      { prop: 'userToken', label: '兑换人', minWidth: 180, showOverflowTooltip: true },
      { prop: 'redeemedTime', label: '兑换时间', minWidth: 180 },
      {
        prop: 'status',
        label: '状态',
        width: 100,
        align: 'center',
        formatter: (row: Cdkey) =>
          row.status === 0 ? <ElTag type="primary">未兑换</ElTag> : <ElTag type="success">已兑换</ElTag>
      },
      {
        prop: 'isPlus',
        label: 'Plus',
        minWidth: 100,
        align: 'center',
        formatter: (row: Cdkey) =>
          row.isPlus === 0 ? <ElTag type="primary">否</ElTag> : <ElTag type="success">是</ElTag>
      },
      {
        prop: 'isPro',
        label: 'Pro',
        minWidth: 100,
        align: 'center',
        formatter: (row: Cdkey) =>
          row.isPro === 0 ? <ElTag type="primary">否</ElTag> : <ElTag type="success">是</ElTag>
      },
      { prop: 'name', label: '套餐名称', minWidth: 250, showOverflowTooltip: true },
      { prop: 'validDays', label: '有效天数', minWidth: 100, showOverflowTooltip: true },
      { prop: 'createTime', label: '创建时间', width: 180 },
      { prop: 'updateTime', label: '更新时间', width: 180 },
      {
        prop: 'operate',
        label: $t('common.operate'),
        align: 'center',
        fixed: 'right',
        formatter: (row: Cdkey) => (
          <div class="flex-center">
            <ElPopconfirm title={$t('common.confirmDelete')} onConfirm={() => handleRecycleClick(row)}>
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small" v-show={hasPermission('codes/recycle')} disabled={row.status === 0}>
                    回收
                  </ElButton>
                )
              }}
            </ElPopconfirm>
          </div>
        )
      }
    ]
  });

const { checkedRowKeys, onBatchDeleted } = useTableOperate(data, getData);

async function handleRecycleClick(row: Cdkey) {
  await recycleKey(row);
  ElMessage({
    message: '回收成功',
    type: 'success',
    plain: true
  });
  getData();
}

async function handleBatchDelete() {
  await removeCdkeyBatchByIds(checkedRowKeys.value);
  onBatchDeleted();
}

function handleAdd() {
  operateType.value = 'create';
  openModal();
}

async function handleExport() {
  operateType.value = 'export';
  openModal();
}
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <CdkeySearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <div class="flex items-center">
            <CdkeyTableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              :show-add="hasPermission('codes/generate')"
              :show-delete="hasPermission('codes/delete')"
              :show-export="hasPermission('codes/export-key')"
              @add="handleAdd"
              @export="handleExport"
              @delete="handleBatchDelete"
              @refresh="getData"
            />
          </div>
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="(selection: Cdkey[]) => checkedRowKeys = selection.map(item => item.id)"
        >
          <ElTableColumn
            v-for="col in columns"
            :key="col.prop"
            v-bind="col"
            :show-overflow-tooltip="col.showOverflowTooltip"
          />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
      <CdkeyOperateModel v-model:visible="visible" :operate-type="operateType" @submitted="getDataByPage" />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>

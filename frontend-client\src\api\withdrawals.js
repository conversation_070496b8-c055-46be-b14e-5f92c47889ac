import request from "../utils/request";

// 分页查询提现信息
export function withdrawPage (params) {
    return request({
        url: '/getWithdrawPage',
        method: 'get',
        params
    });
}


// 提现申请
export function fetchApply (data) {
    return request({
        url: '/withdrawals/apply',
        data,
        method: 'post',
    });
}

// 获取邀请详情
export function getInviteDetails (params) {
    return request({
        url: '/withdrawals/getInviteDetails',
        method: 'get',
        params
    });
}

// 获取待审批提现数量
export function fetchPendingCount () {
    return request({
        url: '/withdrawals/pending/count',
        method: 'get',
    });
}

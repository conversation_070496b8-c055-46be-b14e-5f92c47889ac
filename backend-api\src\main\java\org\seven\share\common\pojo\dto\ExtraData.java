package org.seven.share.common.pojo.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: ExtraData
 * @Description: 支付配置额外数据
 * @Author: Seven
 * @Date: 2024/8/8
 */
@Data
public class ExtraData implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 网关
     */
    private String payGatewayUrl;
    /**
     * 回调地址
     */
    private String notifyUrl;
    /**
     * 私钥
     */
    private String privateKeyPem;

    private boolean enableH5;

    private String merchantId;

    private String serialNumber;
}

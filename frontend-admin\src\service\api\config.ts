import { request } from '../request';

export function fetchFormData() {
  return request({
    url: '/sys/getForm',
    method: 'get'
  });
}

export function fetchSaveFormData(data: any) {
  return request({
    url: '/sys/saveForm',
    method: 'post',
    data
  });
}

export function testEmail(params: any) {
  return request({
    url: '/sys/test',
    method: 'get',
    params
  });
}

export function fetchLicenseInfo() {
  return request({
    url: '/sys/licenseInfo',
    method: 'get'
  });
}

<script setup lang="tsx">
import { computed, onMounted, ref } from 'vue';
import {
  ElButton,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInputNumber,
  ElMessage,
  ElMessageBox,
  ElPopconfirm,
  ElTable,
  ElTableColumn
} from 'element-plus';
import { fetchUpdateUserDrawQuota, fetchUserQuotaPage, removeUserQuotaBatch } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import UserQuotaSearch from './modules/user-quota-search.vue';

// Define the proper type for your table data
interface UserQuota {
  id: string;
  username: string;
  userId: number;
  totalQuota: number;
  usedQuota: number;
  remainingQuota: number;
  resetAt: string;
  createdAt: string;
  updatedAt: string;
}

const wrapperRef = ref<HTMLElement | null>(null);
const maxHeight = ref(window.innerHeight - 220);

const { columns, columnChecks, data, loading, pagination, getData, searchParams, getDataByPage, resetSearchParams } =
  useTable({
    apiFn: fetchUserQuotaPage,
    apiParams: {
      current: 1,
      size: 10,
      username: undefined
    },
    columns: () => [
      { type: 'selection', width: 48 },
      { prop: 'index', label: $t('common.index'), width: 64 },
      { prop: 'username', label: '用户名', minWidth: 200, showOverflowTooltip: true },
      { prop: 'totalQuota', label: '总额度', minWidth: 200, align: 'center' },
      { prop: 'usedQuota', label: '使用额度', minWidth: 120, align: 'center' },
      { prop: 'remainingQuota', label: '剩余额度', minWidth: 120, align: 'center' },
      { prop: 'resetAt', label: '重置时间', width: 180, align: 'center' },
      { prop: 'createdAt', label: '创建时间', width: 180, align: 'center', sortable: true },
      { prop: 'updatedAt', label: '更新时间', width: 180, align: 'center', sortable: true },
      {
        prop: 'operate',
        label: $t('common.operate'),
        align: 'center',
        fixed: 'right',
        width: 140,
        formatter: (row: any) => (
          <div class="flex-center">
            <ElButton type="primary" plain size="small" onClick={() => handleEdit(row)}>
              {$t('common.edit')}
            </ElButton>
            <ElPopconfirm title="确定删除该记录吗？" onConfirm={() => handleDelete(row.id)}>
              {{
                reference: () => (
                  <ElButton size="small" plain type="danger">
                    删除
                  </ElButton>
                )
              }}
            </ElPopconfirm>
          </div>
        )
      }
    ]
  });

const { checkedRowKeys, onBatchDeleted } = useTableOperate(data as any, getData);

const dialogVisible = ref(false);
const currentQuota = ref<UserQuota | null>(null);
const formRef = ref();

const quotaForm = ref({
  totalQuota: 0,
  usedQuota: 0,
  userId: 0,
});

// 计算剩余额度
const remainingQuota = computed(() => {
  return quotaForm.value.totalQuota - quotaForm.value.usedQuota;
});

const rules = {
  totalQuota: [
    { required: true, message: '请输入总额度', trigger: 'blur' },
    { type: 'number' as const, min: 0, message: '总额度不能小于0', trigger: 'blur' }
  ],
  usedQuota: [
    { required: true, message: '请输入使用额度', trigger: 'blur' },
    { type: 'number' as const, min: 0, message: '使用额度不能小于0', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (value > quotaForm.value.totalQuota) {
          callback(new Error('使用额度不能大于总额度'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
};

function handleEdit(row: UserQuota) {
  currentQuota.value = row;
  quotaForm.value = {
    totalQuota: row.totalQuota,
    usedQuota: row.usedQuota,
    userId: row.userId
  };
  dialogVisible.value = true;
}

async function handleSubmit() {
  if (!formRef.value || !currentQuota.value) return;

  try {
    await formRef.value.validate();
    await fetchUpdateUserDrawQuota({
      id: currentQuota.value.id,
      totalQuota: quotaForm.value.totalQuota,
      usedQuota: quotaForm.value.usedQuota,
      userId: quotaForm.value.userId
    });

    ElMessage.success('修改成功');
    dialogVisible.value = false;
    getData();
  } catch {
    ElMessage.error('修改失败');
  }
}

// Handle single record deletion
async function handleDelete(id: string) {
  try {
    await removeUserQuotaBatch([id]);
    ElMessage({
      message: '删除成功',
      type: 'success'
    });
    await getData();
  } catch {
    ElMessage.error('删除失败');
  }
}

// Handle batch deletion
async function handleBatchDelete() {
  if (checkedRowKeys.value.length < 1) {
    ElMessage({
      message: '请先勾选数据',
      type: 'warning'
    });
    return;
  }

  try {
    await ElMessageBox.confirm('确定批量删除选中记录吗？', '注意', {
      confirmButtonText: '确 定',
      cancelButtonText: '取 消',
      type: 'warning'
    });

    await removeUserQuotaBatch(checkedRowKeys.value);
    onBatchDeleted();

    ElMessage({
      message: '删除成功',
      type: 'success'
    });
  } catch {
    // 取消删除或发生错误
  }
}

onMounted(() => {
  getData();
});
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <UserQuotaSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            @delete="handleBatchDelete"
            @refresh="getData"
          />
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          :max-height="maxHeight"
          @selection-change="(selection: UserQuota[]) => checkedRowKeys = selection.map(item => String(item.id))"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
    </ElCard>

    <ElDialog v-model="dialogVisible" title="修改额度" width="500px" :close-on-click-modal="false">
      <ElForm ref="formRef" :model="quotaForm" :rules="rules" label-width="100px">
        <ElFormItem label="用户名">
          <span>{{ currentQuota?.username }}</span>
        </ElFormItem>
        <ElFormItem label="总额度" prop="totalQuota">
          <ElInputNumber v-model="quotaForm.totalQuota" :min="0" :precision="0" />
        </ElFormItem>
        <ElFormItem label="使用额度" prop="usedQuota">
          <ElInputNumber v-model="quotaForm.usedQuota" :min="0" :precision="0" />
        </ElFormItem>
        <ElFormItem label="剩余额度">
          <span>{{ remainingQuota }}</span>
        </ElFormItem>
      </ElForm>
      <template #footer>
        <span class="dialog-footer">
          <ElButton @click="dialogVisible = false">取消</ElButton>
          <ElButton type="primary" @click="handleSubmit">确定</ElButton>
        </span>
      </template>
    </ElDialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>

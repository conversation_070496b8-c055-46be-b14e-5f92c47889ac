<template>
  <div class="min-h-screen flex items-center justify-center bg-white">
    <div class="w-full max-w-sm p-8">
      <!-- Logo -->
      <div class="text-center mb-8">
        <div class="flex justify-center mb-6">
          <svg
            v-if="showGptLogo"
            t="1730423274094"
            class="icon"
            viewBox="0 0 1089 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="4351"
            width="64"
            height="64"
          >
            <path
              d="M466.63477617 31.24645905c61.12522043 0.88373813 118.42090898 24.45008817 165.40631938 71.43549856 6.92261532 6.92261532 12.96149253 8.54280189 22.24074285 7.21719472 134.77006433-19.00036972 253.04368363 66.13306981 277.78835118 200.01939602 6.48074626 35.20223538 4.41869063 70.25718107-5.59700813 104.57567833-2.50392469 8.3955122-0.88373813 13.69794096 4.56598031 20.32597691 111.49829366 136.83211996 44.7760651 343.77413131-125.63810368 388.84477577-9.72111939 2.50392469-13.99252034 6.92261532-17.38018316 15.75999661-63.33456576 164.66987094-276.02087492 209.00406699-400.03879204 83.36596328-6.1861669-6.1861669-11.48859565-7.51177407-19.88410786-6.33345656-136.68483027 19.2949491-255.10573925-65.98578013-279.55582743-201.34500323-6.33345658-34.7603663-3.97682157-69.37344294 6.03887721-103.25007113 2.50392469-8.3955122 0.88373813-13.69794096-4.41869062-20.32597692-111.94016272-136.97940963-44.03961664-345.39431787 126.96371087-389.28664484 8.54280189-2.20934532 12.51962347-5.89158752 15.6127069-13.84523064 36.82242196-96.32745582 126.22726243-157.59996593 233.89602422-157.15809688z m148.17342591 593.13557275c-77.32708608 44.6287754-152.1502475 87.49007455-226.38425013 131.08782212-16.79102442 9.86840908-31.07812413 9.5738297-47.86914855-0.29457938-58.17942668-34.46578693-116.80072242-67.90054606-175.42201815-101.48259488-4.27140094-2.35663501-7.80635344-7.2171947-14.58167908-5.15513907-7.95364315 71.58278827 14.4343894 131.23511182 73.20297482 173.50725221 59.2104545 42.56671978 124.3124965 47.86914853 188.82537976 13.69794097 65.98578013-34.907656 129.76221494-74.08671296 194.42238788-111.49829366 3.68224221-2.06205563 7.80635344-3.68224221 7.80635345-9.13196065v-90.73044766z m-41.24111259-487.38157692c-1.47289687-5.15513907-5.30242877-6.62803595-8.24822251-8.69009157-58.91587511-39.9155054-121.66128212-46.24896197-185.29042725-14.87625848-64.21830387 31.81457257-98.38951143 86.16446736-100.30427737 157.89454531-1.91476594 72.90839547-0.29457937 145.81679091-0.73644845 218.72518637 0 7.80635344 2.65121438 11.93046471 9.27925032 15.46541723 21.65158411 11.93046471 43.00858883 24.59737787 64.36559358 36.82242194 2.65121438 1.47289687 5.0078494 4.71327001 9.42654001 1.91476595 0-86.31175705 0.29457937-173.06538315-0.14728967-259.81900928-0.1472897-17.82205222 6.62803595-29.31064787 22.09345315-38.00073943 44.7760651-25.18653662 89.11026111-51.10952167 133.59174684-76.88521704 18.70579035-10.75214722 37.4115807-21.79887379 55.97008135-32.551021z m85.28072925 341.56478599v18.85308004c0 81.89306641-0.1472897 163.93342252 0.14728967 245.82648892 0.1472897 16.64373472-6.03887719 27.98504069-20.62055629 35.93868382-20.47326659 11.19401627-40.50466414 23.12448098-60.68335137 34.76036634-43.45045791 25.18653662-87.04820548 50.37307323-134.62277464 77.91624483 4.12411126 1.47289687 6.1861669 1.76747625 7.65906377 2.94579376 54.64447417 39.9155054 114.29679772 49.04746604 176.89491503 24.00821909 62.5981173-25.03924691 102.95549177-72.6138161 110.61455554-139.63062402 9.27925033-81.89306641 1.91476594-164.66987094 3.3876628-247.00480644 0.1472897-4.41869063-1.91476594-6.92261532-5.44971843-8.98467094-25.03924691-14.4343894-49.93120416-28.86877882-77.32708608-44.6287754zM554.71400945 367.2142369c6.03887719 3.68224221 10.16298847 6.03887719 14.13981004 8.39551221 71.2882089 41.24111259 142.42912809 82.62951485 214.01191636 123.42875836 15.46541722 8.83738127 22.09345317 20.47326659 21.94616348 38.14802915-0.44186907 68.19512544-0.1472897 136.39025089-0.14728969 204.58537633 0 5.0078494 0.44186907 9.86840908 0.73644844 15.90728628 3.68224221-1.3256072 5.89158752-2.06205563 8.10093282-2.94579374 64.0710142-29.75251694 102.07175363-79.6837211 108.11063085-149.79361249 6.33345658-72.6138161-22.68261193-130.94053245-84.98614986-169.38314097-62.745407-38.73718787-127.55286961-74.08671296-191.32930443-111.20371428-5.59700814-3.24037312-9.72111939-3.68224221-15.61270692-0.29457938-24.15550879 14.13981003-48.60559698 27.83775099-74.97045109 43.15587853zM216.24230691 270.44491203c-6.62803595-0.88373813-10.60485751 2.20934532-14.72896877 4.27140094-60.97793075 30.63625506-96.62203518 80.12559015-102.21904332 147.87884654-5.74429781 70.25718107 21.50429441 127.9947387 81.15661796 165.70089876 63.92372449 40.35737445 130.64595307 76.44334797 196.04257446 114.44408742 3.82953187 2.20934532 7.36448439 4.12411126 11.78317501 1.47289688 25.33382629-14.72896878 50.66765259-29.45793756 77.47437578-44.92335477-5.59700814-3.38766283-9.42654002-5.89158752-13.40336158-8.24822253-71.2882089-41.24111259-142.42912809-82.62951485-214.01191636-123.28146868-16.05457598-9.13196064-22.53532223-21.06242535-22.38803255-39.32634664 0.58915875-72.31923671 0.29457937-144.7857631 0.29457937-217.98873792z m190.15098694 130.79324274c6.1861669-3.24037312 10.31027814-5.30242877 14.28709973-7.65906375 70.55176044-40.65195383 141.25081059-81.00932829 211.36070197-122.39773056 16.79102442-9.86840908 31.07812413-10.16298847 47.86914854-0.29457937 59.0631648 34.907656 118.56819867 68.78428419 177.92594284 102.95549177 3.5349525 2.06205563 6.48074626 5.89158752 12.51962347 4.12411125 7.80635344-70.10989139-13.40336159-129.32034589-70.69905013-171.73977596-59.50503387-44.18690634-125.34352431-50.22578353-191.03472509-15.6127069-65.98578013 34.907656-129.76221494 74.08671296-194.56967756 111.35100396-3.68224221 2.06205563-7.65906378 3.82953187-7.65906377 9.27925033v89.99399923z m208.41490823 115.9169843c0-20.17868721-0.44186907-36.96971163 0.14728969-53.61344636 0.29457937-7.65906378-2.20934532-12.0777544-8.98467096-15.75999658-29.45793756-16.49644503-58.76858542-33.43475913-87.78465391-50.6676526-5.59700814-3.38766283-9.72111939-3.38766283-15.46541723-0.14728971-29.01606849 17.23289347-58.32671636 34.17120756-87.78465393 50.66765261-6.77532564 3.82953187-8.83738127 8.24822251-8.83738127 15.90728628 0.44186907 32.84560039 0.58915875 65.83849045 0 98.68409082-0.1472897 8.98467097 3.09308345 13.69794096 10.75214722 17.67476253 13.99252034 7.2171947 27.24859225 15.61270692 40.94653321 23.41906036 17.38018317 9.86840908 34.7603663 27.6904613 51.99325978 27.54317163 17.96934191-0.1472897 35.79139413-17.52747284 53.46615667-27.54317163 16.9383141-9.72111939 39.17905696-16.20186565 48.75288666-30.78354473 9.86840908-15.46541722 1.76747625-39.03176725 2.79850407-55.38092262z"
              fill="#030303"
              p-id="4352"
            ></path>
          </svg>
          <svg v-else-if="!showGptLogo && showClaudeLogo" t="1734104009687" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4276" width="64" height="64"><path d="M198.47838 672.59985l198.862285-111.588103 3.327168-9.725569-3.327168-5.374656h-9.725569l-33.271682-2.047488-113.635591-3.071232-98.535366-4.094976-95.464134-5.118721-24.057986-5.11872L0.127968 496.771807l2.303424-14.844289 20.218945-13.564609 28.92077 2.55936 63.984004 4.350913 95.976006 6.654336 69.614597 4.094976 103.142214 10.749313h16.379905l2.303424-6.654336-5.630592-4.094977-4.350912-4.094976-99.303175-67.311172-107.493126-71.150213-56.305924-40.949762L99.431142 281.785554l-15.356161-19.451138-6.654336-42.485378L105.061735 189.392652l37.110722 2.55936 9.469632 2.55936 37.622595 28.92077 80.363909 62.192452 104.933766 77.292677 15.356161 12.7968 6.142465-4.350912 0.767808-3.071232-6.910273-11.517121L332.844789 253.632592l-60.912772-104.933767-27.129218-43.509122-7.166208-26.105474c-2.55936-10.749313-4.350912-19.707073-4.350912-30.712322L264.765809 5.630592l17.403649-5.630592 41.973506 5.630592 17.659585 15.356161 26.105474 59.633092 42.229443 93.928518 65.51962 127.712072 19.195201 37.87853 10.237441 35.063234 3.83904 10.749313h6.654336v-6.142464l5.374657-71.918021 9.981504-88.297925 9.725569-113.635591 3.327168-31.992002 15.868033-38.390403L591.340165 14.844289l24.569857 11.773057 20.218946 28.920769-2.815296 18.68333-12.028993 78.060485-23.546114 122.337415-15.356161 81.899525h8.957761l10.237441-10.23744 41.461634-55.026244 69.614597-87.018245 30.712321-34.551362 35.831043-38.134467 23.034241-18.171457h43.509123l31.992002 47.604099-14.332417 49.139715-44.788803 56.817796-37.110722 48.115971-53.234691 71.662084-33.271683 57.329668 3.071233 4.606848 7.934016-0.767808 120.289928-25.593602 65.007748-11.773056 77.548613-13.308673 35.063234 16.379905 3.83904 16.635841-13.820545 34.03949-82.923269 20.474881-97.255686 19.451138-144.859785 34.295426-1.791552 1.27968 2.047488 2.55936 65.263684 6.142464 27.897026 1.535616h68.334916l127.2002 9.469633 33.271682 22.010497 19.963009 26.873282-3.327168 20.474881-51.187203 26.105474-69.102725-16.379905-161.23969-38.390402-55.282179-13.820545h-7.678081v4.606848l46.068483 45.044739 84.458886 76.268933 105.701574 98.27943 5.374657 24.313921-13.564609 19.195201-14.332417-2.047488-92.904774-69.870532-35.831042-31.48013-81.131717-68.334916h-5.374657v7.166208l18.68333 27.385154 98.791302 148.442889 5.11872 45.556611-7.166208 14.844289-25.593602 8.95776-28.152962-5.11872L665.56161 848.427893l-59.633092-91.369158-48.115971-81.899525-5.886529 3.327168-28.408897 305.84354-13.308673 15.612096-30.712322 11.773057-25.593602-19.451137-13.564608-31.48013 13.564608-62.192452 16.379905-81.131717 13.308673-64.495876 12.028993-80.107973 7.166208-26.617346-0.511872-1.791552-5.886528 0.767808-60.4009 82.92327-91.881029 124.128967-72.685829 77.804549-17.403649 6.910273-30.20045-15.612097 2.815296-27.897026 16.891777-24.825794 100.582855-127.968008 60.656835-79.340165 39.158211-45.812547-0.255936-6.654336h-2.303424L174.164459 782.396401l-47.604099 6.142464-20.474881-19.195201 2.55936-31.48013 9.725568-10.237441 80.363909-55.282179-0.255936 0.255936z" fill="#D97757" p-id="4277"></path></svg>
        </div>
        <h1 class="text-3xl font-bold text-center text-gray-800 mb-10">
          欢迎回来
        </h1>
      </div>

      <!-- Login Form -->
      <form @submit.prevent="handleSubmit">
        <!-- Email Field -->
        <div class="custom-form-item" v-if="loginType ==='account'">
          <div class="custom-input-container">
            <input
              v-model="form.username"
              required
              class="custom-input"
              :class="{ 'has-content': form.username }"
              @focus="setFocus('username', true)"
              @blur="setFocus('username', false)"
            />
            <label :class="{ 'is-focused': emailFocused || form.username }"
              >用户名/邮箱</label
            >
          </div>
        </div>

        <!-- Password Field -->
        <div class="custom-form-item" v-if="loginType ==='account'">
          <div class="custom-input-container">
            <input
              v-model="form.password"
              required
              autocomplete
              :type="showPassword ? 'text' : 'password'"
              class="custom-input"
              :class="{ 'has-content': form.password }"
              @focus="setFocus('password', true)"
              @blur="setFocus('password', false)"
            />
            <label :class="{ 'is-focused': passwordFocused || form.password }"
              >密码</label
            >
            <el-icon
              class="password-icon"
              @click="showPassword = !showPassword"
            >
              <View v-if="showPassword" />
              <Hide v-else />
            </el-icon>
          </div>
        </div>

        <!-- Auth Code Field -->
        <div class="custom-form-item" v-if="loginType ==='code'">
          <div class="custom-input-container">
            <input
              v-model="form.authCode"
              required
              class="custom-input"
              :class="{ 'has-content': form.authCode }"
              @focus="setFocus('authCode', true)"
              @blur="setFocus('authCode', false)"
            />
            <label :class="{ 'is-focused': authCodeFocused || form.authCode }"
              >授权码</label
            >
            <p class="mt-2">
              <el-link
                type="primary"
                :underline="false"
                @click="goToPurchase"
                class="text-emerald-600"
              >
              没有授权码？<span class="ml-1 text-emerald-600">去购买</span>
              </el-link>
            </p>
          </div>
        </div>
        <!-- Forgot Password Link -->
        <div class="flex justify-center mb-6" v-if="loginType ==='account'">
          <el-link type="primary" @click="forgot" :underline="false"
          >忘记密码? <span class="ml-1 text-emerald-600">点此</span></el-link
          >
        </div>
        <!-- Login Button -->
        <el-button
          type="primary"
          native-type="submit"
          :loading="loading"
          class="w-full mb-6 rounded-[7px]"
          size="large"
        >
          登录
        </el-button>
        <!-- Register Link -->
        <div class="text-center mb-4" v-if="loginType ==='account'">
          <el-link @click="register" type="primary" :underline="false"
          >没有账号? <span class="ml-1 text-emerald-600">注册</span></el-link
          >
        </div>
        <!-- Divider -->
        <div class="relative mb-4">

          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-200"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white">OR</span>
          </div>
        </div>

        <!-- Normal Login Link -->
        <div class="text-center" v-if="loginType ==='account'">
          <el-link @click="changeLoginType" type="primary" :underline="false"
            >使用授权码登录 <span class="ml-1 text-emerald-600">点此</span></el-link
          >
        </div>
        <div class="text-center" v-if="loginType ==='code'">
          <el-link @click="changeLoginType" type="primary" :underline="false"
            >使用账号密码登录 <span class="ml-1 text-emerald-600">点此</span></el-link
          >
        </div>
      </form>
    </div>
    <!-- <AnnouncementPopupVue v-if="loginNotice" :notice="loginNotice" /> -->
  </div>
</template>

<script setup>
import { ref, reactive, computed  } from 'vue';
import { View, Hide } from '@element-plus/icons-vue';
import { getTime } from '@/utils/date';
import { getToken } from '@/utils/auth';
import Cookies from 'js-cookie';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/store/modules/user';
import { useRouter } from 'vue-router';
import { encrypt, decrypt } from '@/utils/encrypt';
import { useDark } from '@vueuse/core';
import { useSiteStore } from '@/store/modules/site';
import AnnouncementPopupVue from '../components/AnnouncementPopup.vue';
const loginType = ref('account');
const siteStore = useSiteStore();
const showGptLogo = computed(() => {
  return siteStore.freeNodeName || siteStore.normalNodeName || siteStore.plusNodeName || siteStore.soruxGptSideBarName
})
const loginNotice = computed(() => siteStore.loginAnnouncement);

const showClaudeLogo = computed(() => {
  return siteStore.claudeNodeName
})
const isDark = ref(useDark());
const router = useRouter(); // 使用vue-router
const userStore = useUserStore();
const form = reactive({
  username: '',
  password: '',
  authCode: '',
});

const loading = ref(false);
const showPassword = ref(false);
const emailFocused = ref(false);
const passwordFocused = ref(false);
const authCodeFocused = ref(false);

const setFocus = (field, value) => {
  if (field === 'username') {
    emailFocused.value = value;
  } else if (field === 'password') {
    passwordFocused.value = value;
  } else if (field === 'authCode') {
    authCodeFocused.value = value;
  }
};

const handleSubmit = async () => {
  try {
    Cookies.remove('gfsessionid');
    loading.value = true;
    if (loginType.value === "account") {
      await userStore.login(form);
      if (getToken()) {
        Cookies.set('username', form.username, { expires: 30 });
        Cookies.set('password', encrypt(form.password), {
          expires: 30,
          secure: true,
        });
        Cookies.set('visitor', 'false', { expires: 30 });
        ElMessage({
          message: `${userStore.username}, ${getTime()}`,
          type: 'success',
          plain: true,
        });
      }
    }else {
      await userStore.loginWithAuthCode(form.authCode);
      Cookies.set('username', form.authCode, { expires: 30 });
    }
    router.push('/home');
  } catch (error) {
    console.error('授权码验证失败:', error);
  } finally {
    loading.value = false;
  }
};

const forgot = () => {
  router.push('/forgot-password');
};
const register = () => {
  router.push('/register');
};

const changeLoginType = () => {
  loginType.value === 'account' ? loginType.value = 'code' : loginType.value = 'account'
};

const getCookie = () => {
  const username = Cookies.get('username');
  const password = Cookies.get('password');
  form.username = username === undefined ? form.username : username;
  form.password = password === undefined ? form.password : decrypt(password);
};
getCookie();
// 清除暗黑模式
const clearDark = () => {
  isDark.value = false;
};
clearDark();

const goToPurchase = () => {
  if (!siteStore.fkAddress) { 
    ElMessage({
          message: "还未上架授权码，请联系管理员",
          type: 'warning',
          plain: true,
        });
        return
  }
  window.open(siteStore.fkAddress, '_blank');
};
</script>

<style scoped>
.custom-form-item {
  margin-bottom: 20px;
}

.custom-input-container {
  position: relative;
}

.custom-input {
  width: 100%;
  height: 56px;
  border: 1px solid #dcdfe6;
  border-radius: 7px;
  padding: 0 15px;
  font-size: 14px;
  transition: all 0.3s;
}

.custom-input:focus {
  border-color: #4caf50;
  outline: none;
}

.custom-input-container label {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  background-color: white;
  padding: 0 5px;
  color: #909399;
  font-size: 14px;
  transition: all 0.3s;
  pointer-events: none;
}

.custom-input-container label.is-focused,
.custom-input.has-content + label {
  top: 0;
  font-size: 12px;
  color: #10a37f;
}

.password-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #909399;
}

:deep(.el-button--primary:not(.is-disabled)) {
  background-color: #000000;
  border-color: #000000;
  color: #ffffff;
}

:deep(.el-button--primary:not(.is-disabled):hover) {
  background-color: #333333;
  border-color: #333333;
}

:deep(.dark .el-button--primary:not(.is-disabled)) {
  background-color: #ffffff;
  border-color: #ffffff;
  color: #000000;
}

:deep(.dark .el-button--primary:not(.is-disabled):hover) {
  background-color: #e5e5e5;
  border-color: #e5e5e5;
}

:deep(.el-link.el-link--primary) {
  color: #000000;
}

:deep(.el-link.el-link--primary:hover) {
  color: #333333;
}

:deep(.dark .el-link.el-link--primary) {
  color: #ffffff;
}

:deep(.dark .el-link.el-link--primary:hover) {
  color: #e5e5e5;
}
</style>

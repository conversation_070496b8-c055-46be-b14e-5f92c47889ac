package org.seven.share.common.util;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * JSON工具类
 */
@Slf4j
@Component
public class JsonUtil {

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 将对象转换为JSON字符串
     * @param obj 要转换的对象
     * @return JSON字符串
     */
    public String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("对象转JSON字符串失败", e);
            return null;
        }
    }

    /**
     * 将对象的部分字段转换为JSON字符串，过滤空值字段
     * @param obj 要转换的对象
     * @param fields 要转换的字段名数组
     * @return JSON字符串
     */
    public String toJsonStringWithFields(Object obj, String... fields) {
        if (obj == null || fields == null || fields.length == 0) {
            return null;
        }
        try {
            Map<String, Object> fieldMap = BeanUtil.beanToMap(obj);
            Map<String, Object> resultMap = new HashMap<>();
            for (String field : fields) {
                if (fieldMap.containsKey(field) && fieldMap.get(field) != null) {
                    resultMap.put(field, fieldMap.get(field));
                }
            }
            return objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL)
                    .writeValueAsString(resultMap);
        } catch (Exception e) {
            log.error("对象部分字段转JSON字符串失败", e);
            return null;
        }
    }
}

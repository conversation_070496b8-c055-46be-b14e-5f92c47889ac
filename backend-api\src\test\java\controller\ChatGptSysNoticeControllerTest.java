package controller;


import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.seven.share.Application;
import org.seven.share.common.pojo.entity.ChatGptSysNoticeEntity;
import org.seven.share.common.pojo.entity.SysUser;
import org.seven.share.common.util.JwtTokenUtil;
import org.seven.share.common.util.SysUserDetail;
import org.seven.share.security.service.UserDetailsServiceImpl;
import org.seven.share.service.ChatGptSysNoticeService;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.test.web.servlet.MockMvc;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import static org.mockito.ArgumentMatchers.anyString;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class ChatGptSysNoticeControllerTest {
    @Resource
    private MockMvc mockMvc;

    @MockBean
    private ChatGptSysNoticeService chatGptSysNoticeService;

    @Resource
    private JwtTokenUtil jwtTokenUtil;


    @Resource
    private UserDetailsServiceImpl userDetailsService;


    private String generateValidTestToken() {
        String username = "Expander";
        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
        return jwtTokenUtil.generateToken(userDetails.getUsername());
    }

    private void setSecurityContext(String username) {
        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                userDetails, null, userDetails.getAuthorities()
        );
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }

    // 测试场景 1：正常请求（Host 有效且存在公告）
    @Test
    public void getByDomain_ValidHost_ReturnNotice() throws Exception {
        // 模拟服务层返回公告
        ChatGptSysNoticeEntity mockNotice = new ChatGptSysNoticeEntity();
        mockNotice.setId(1L);
        mockNotice.setContent("有效公告");
        Mockito.when(chatGptSysNoticeService.getDomainNotice(anyString()))
                .thenReturn(mockNotice);

        String validToken = generateValidTestToken();
        setSecurityContext("Expander");
        // 模拟请求（携带 Token 和 Host 头）
        mockMvc.perform(get("/api/notice/getByDomain")
                        .header("Host", "test.example.com:8080")
                        .header("Authorization", "Bearer " + validToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("200")) // 匹配项目 ResultCode.SUCCESS
                .andExpect(jsonPath("$.data.content").value("有效公告"));
    }



}

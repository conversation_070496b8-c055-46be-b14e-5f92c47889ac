package org.seven.share.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.seven.share.common.pojo.entity.ChatGptSubTypeEntity;

import java.util.List;

/**
 * @ClassName: ChatGptSubTypeDao
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/11
 */
@Mapper
public interface ChatGptSubTypeMapper extends BaseMapper<ChatGptSubTypeEntity> {
    int logicDeleteSubtype(@Param("ids") List<String> ids);

    @InterceptorIgnore(tenantLine = "true")
    ChatGptSubTypeEntity getSubtypeByIdWithoutTenant(@Param("id") String id);


    Page<ChatGptSubTypeEntity> selectPageWithTenant(Page<ChatGptSubTypeEntity> pageInfo, @Param("query") String query);

    @InterceptorIgnore(tenantLine = "true")
    List<ChatGptSubTypeEntity> listSubtype(@Param("tenantId") String tenantId);
}

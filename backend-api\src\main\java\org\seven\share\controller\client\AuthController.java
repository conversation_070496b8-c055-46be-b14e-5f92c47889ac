package org.seven.share.controller.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import org.seven.share.common.annotation.RateLimit;
import org.seven.share.common.api.R;
import org.seven.share.common.pojo.dto.AuthBody;
import org.seven.share.common.pojo.dto.LoginDto;
import org.seven.share.common.pojo.dto.RegisterDto;
import org.seven.share.common.pojo.dto.ResetPasswordDto;
import org.seven.share.common.util.LicenseUtil;
import org.seven.share.service.ChatGptUserService;
import org.seven.share.service.ClaudeSessionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping({"/client-api", "/api"}) // 支持多个路径前缀
@RequiredArgsConstructor
public class AuthController {

    private final ChatGptUserService chatGptUserService;

    private final ClaudeSessionService claudeSessionService;

    /**
     * 用户选车前校验接口1
     * @return 校验通过并返回真实的车号
     */
    @PostMapping("/openai/auth")
    public String auth(@RequestBody AuthBody authBody){
        return chatGptUserService.authUserInfo(authBody.getUsertoken(), authBody.getCarid(), authBody.getNodeType(), authBody.getPlanType());
    }

    /**
     * 用户选车前校验接口2，主要是为了重写xy选车认证接口
     * @param userToken 用户名
     * @param carId 车号
     * @return 登录结果
     */
    @RequestMapping("/openai/oauth")
    public R auth(@RequestParam(value = "usertoken") String userToken,
                  @RequestParam(value = "carid") String carId,
                  @RequestParam(value = "nodeType") String nodeType,
                  @RequestParam(value = "planType", required = false) Integer planType){
        chatGptUserService.authUserInfo(userToken, carId, nodeType, planType);
        return R.authSuccess();
    }

    /**
     * 用户选车前校验接口2，主要是为了重写xy选车认证接口
     * @param userToken 用户名
     * @param carId 车号
     * @return 登录结果
     */
    @RequestMapping("/user/oauth")
    public R userAuth(@RequestParam(value = "usertoken") String userToken,
                  @RequestParam(value = "carid") String carId,
                  @RequestParam(value = "nodeType") String nodeType,
                  @RequestParam(value = "planType", required = false) Integer planType){
        chatGptUserService.authUserInfo(userToken, carId, nodeType, planType);
        return R.authSuccess();
    }

    /**
     * 授权码登录
     * @param code
     * @return
     */
    @GetMapping("/auth-code")
    @RateLimit(20)
    public R authCode(@RequestParam("code") String code) {
        return R.ok(chatGptUserService.authCode(code));
    }

    /**
     * 返回用户当前选车的登录地址
     * @param userToken
     * @param carId
     * @return
     * @throws JsonProcessingException
     */
    @GetMapping("/claude/auth")
    public String auth(@RequestParam(value = "usertoken") String userToken,
                       @RequestParam(value = "carid") String carId,
                       @RequestParam(value = "isPlus") Integer isPlus) throws IOException {
        return claudeSessionService.authUserInfoAndGetLoginUrl(userToken, carId, isPlus);
    }

    @PostMapping("/claude/oauth")
    public Map<String, Object> oauth(@RequestParam(value = "usertoken") String userToken,
                   @RequestParam(value = "carid") String carId,
                   @RequestParam(value = "isPlus") Integer isPlus) {
        return claudeSessionService.oauthUserInfo(userToken, carId, isPlus);
    }


    @RequestMapping("/grok/oauth")
    public Map<String, Object> grokAuth(@RequestParam(value = "usertoken") String userToken, @RequestParam("isSuper") Integer isSuper){
        return chatGptUserService.authGrokAccess(userToken, isSuper);
    }


    /**
     * 退出登录
     * @param request
     * @param username
     */
    @GetMapping("/logout")
    public void logout(HttpServletRequest request, String username) {
        chatGptUserService.logout(request, username);
    }

    /**
     * 生成sass gpt登录地址
     * @param request
     * @return
     */
    @GetMapping("/sass/logintoken")
    public String getSassLoginToken(HttpServletRequest request){
        return chatGptUserService.getSassLoginToken(request);
    }

    @GetMapping("/grok/loginToken")
    public String getGrokLoginToken(HttpServletRequest request, @RequestParam("isSuper") Integer isSuper) {
        return chatGptUserService.getGrokLoginToken(request, isSuper);
    }


    @GetMapping("/getClaudeLoginUrl")
    public String getClaudeLoginUrl(String username){
        return claudeSessionService.getClaudeLoginUrl(username);
    }


    @GetMapping("/license")
    public String generateLicense() {
        return LicenseUtil.generateServerHash();
    }

    @PostMapping("/login")
    @RateLimit(20)
    public R login(@RequestBody  @Validated LoginDto loginDto){
        return R.ok(chatGptUserService.login(loginDto));
    }

    @PostMapping("/reset")
    @RateLimit(20)
    public R reset(@RequestBody  @Validated ResetPasswordDto resetPasswordDto){
        chatGptUserService.resetPassword(resetPasswordDto);
        return R.ok();
    }

    @PostMapping("/register")
    @RateLimit(20)
    public R register(@RequestBody  @Validated RegisterDto registerDto){
        chatGptUserService.verifyCodeAndSaveUserInfo(registerDto);
        return R.ok();
    }
    @RequestMapping("/register-code")
    @RateLimit(20)
    public R registerCode(@RequestParam String username, @RequestParam String email) {
        chatGptUserService.getRegisterCode(username, email);
        return R.ok();
    }

    @RequestMapping("/password-rest-code")
    @RateLimit(20)
    public R passwordRestCode(@RequestParam String username, @RequestParam String email) {
        chatGptUserService.getPasswordRestCode(username, email);
        return R.ok();
    }

    /**
     * 会员有效期和使用量
     * @param username
     * @return
     */
    @GetMapping("/validity-usage")
    public Map<String, Object> getValidityAndUsage(@RequestParam("username") String username){
        return chatGptUserService.getValidityAndUsage(username);
    }
}

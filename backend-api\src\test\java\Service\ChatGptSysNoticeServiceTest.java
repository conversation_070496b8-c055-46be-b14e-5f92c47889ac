package Service;


import org.junit.jupiter.api.Test;
import org.seven.share.Application;
import org.seven.share.common.pojo.entity.ChatGptSysNoticeEntity;
import org.seven.share.common.util.JwtTokenUtil;
import org.seven.share.security.service.UserDetailsServiceImpl;
import org.seven.share.service.ChatGptSysNoticeService;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

import static org.junit.jupiter.api.Assertions.*;
import javax.annotation.Resource;

@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class ChatGptSysNoticeServiceTest {

    @Resource
    private ChatGptSysNoticeService chatGptSysNoticeService;

    @Resource
    private JwtTokenUtil jwtTokenUtil;

    @Resource
    private UserDetailsServiceImpl userDetailsService;


    private void setSecurityContext(String username) {
        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                userDetails, null, userDetails.getAuthorities()
        );
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }


    @Test
    public void testSendSysNotice() throws Exception {
        // 测试发送系统通知
        String domain = "http://192.168.0.105";
        setSecurityContext("Expander");
        ChatGptSysNoticeEntity result = chatGptSysNoticeService.getDomainNotice(domain);
        assertNotNull(result);
        assertEquals("u001", result.getTenantId());
    }
}

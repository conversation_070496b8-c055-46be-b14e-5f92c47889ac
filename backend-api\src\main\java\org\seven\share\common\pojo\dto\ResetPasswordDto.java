package org.seven.share.common.pojo.dto;

import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: ResetPasswordDto
 * @Description: 重置密码Dto
 * @Author: Seven
 * @Date: 2024/7/30
 */
@Data
public class ResetPasswordDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "用户名不能为空")
    private String username;

    @NotBlank(message = "邮箱不能为空")
    @Email
    private String email;

    private String code;

    @NotBlank(message = "新密码不能为空")
    private String newPassword;
}

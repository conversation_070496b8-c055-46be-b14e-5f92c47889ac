package org.seven.share.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * @ClassName: SmartLongSerializer
 * @Description: 自定义Long类型序列化器
 * @Author: Seven
 * @Date: 2025/3/1
 */
public class SmartLongSerializer extends JsonSerializer<Long> {
    // JavaScript安全整数范围：[-2^53+1, 2^53-1]
    private static final long JS_MAX_SAFE_INTEGER = 9007199254740991L;  // 2^53-1
    private static final long JS_MIN_SAFE_INTEGER = -9007199254740991L; // -(2^53-1)

    @Override
    public void serialize(Long value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
    // 判断是否超出安全范围
        if (value > JS_MAX_SAFE_INTEGER || value < JS_MIN_SAFE_INTEGER) {
            gen.writeString(value.toString()); // 超出范围时转为字符串
        } else {
            gen.writeNumber(value); // 在范围内保持数值类型
        }
    }
}

package org.seven.share.common.pojo.vo;

import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: LoginConfigVo
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/3
 */
@Data
@Builder
public class LoginConfigVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String version;

    private String EnableRegister;

    private String loginNotice;

    private String notice;

    private String scripts;

    private String siteName;


}

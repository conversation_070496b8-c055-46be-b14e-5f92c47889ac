package org.seven.share.common.pojo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;

import java.time.LocalDateTime;

/**
 * @ClassName: UserDto
 * @Description:
 * @Author: Seven
 * @Date: 2025/1/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserDto extends ChatGptUserEntity {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "exclusive_expire_time")
    private LocalDateTime exclusiveExpireTime;
}

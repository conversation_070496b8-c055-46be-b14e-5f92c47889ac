package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: UserDrawingQuotaEntity
 * @Description:
 * @Author: Seven
 * @Date: 2025/4/25
 */
@Data
@TableName("user_drawing_quota")
public class UserDrawingQuotaEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;

    private Long userId;

    /**
     * 总额度
     */
    private Long totalQuota;

    /**
     * 使用额度
     */
    private Long usedQuota;

    /**
     * 剩余额度
     */
    private Long remainingQuota;

    @TableField(value = "reset_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime resetAt;

    @TableField(exist = false)
    private String username;

}

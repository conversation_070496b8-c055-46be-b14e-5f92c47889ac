import { request } from '../request';

interface NoticePageParams {
  page?: number;
  size?: number;
  [key: string]: any;
}

interface NoticeData {
  content: string;
  type: string;
  [key: string]: any;
}

export function fetchNoticePage(params: NoticePageParams) {
  return request({
    url: '/notice/page',
    method: 'get',
    params
  });
}

export function updateNotice(data: NoticeData) {
  return request({
    url: '/notice/update',
    method: 'post',
    data
  });
}

export function addNotice(data: NoticeData) {
  return request({
    url: '/notice/create',
    method: 'post',
    data
  });
}

export function removeNoticeBatchByIds(data: string[]) {
  return request({
    url: '/notice/delete',
    method: 'delete',
    data
  });
}

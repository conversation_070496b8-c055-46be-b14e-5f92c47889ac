<script setup>
import { ref, computed } from 'vue'
import { LayoutDashboard, ShoppingBag, Key, CreditCard, Handshake, BookOpen, Megaphone, Bell, Languages, User, Moon, Sun, ChevronLeft, ChevronRight, MonitorCog, LayoutGrid, Bot} from 'lucide-vue-next'
import { Fold, Expand} from '@element-plus/icons-vue'
import { useDark, useToggle } from '@vueuse/core';
import { useSiteStore } from '@/store/modules/site';
import UserProfileDialog from './UserProfileDialog.vue'
import NotificationDialog from './NotificationDialog.vue'
import { useI18n } from 'vue-i18n';
import { getToken } from '@/utils/auth';
import { ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
import { Gift } from 'lucide-vue-next';
import SignInDialog from './SignInDialog.vue'
const userStore = useUserStore();

const router = useRouter();

const { locale, t } = useI18n();

const userProfileDialogRef = ref()
const isDark = useDark();
const siteStore = useSiteStore();
const siteName = computed(() => siteStore.siteName);
const enableSiteShop = computed(() => siteStore.enableSiteShop === 'true');
const isLogin = computed(() => (getToken() ? true : false));
const enableSignIn = computed(() => siteStore.enableSignIn === 'true');
const toggleDark = () => {
  useToggle(isDark)()
};
const props = defineProps({
  activeTab: String,
  darkMode: Boolean,
  sidebarOpen: Boolean
})
const logoUrl = computed(() => {
  if(siteStore.logoUrl) {
    return siteStore.logoUrl
  }
  return '/app/logo.svg';
})

// Logo错误处理 - 防止无限循环
const logoErrorHandled = ref(false)
const handleLogoError = (event) => {
  if (!logoErrorHandled.value) {
    console.log('Sidebar Logo加载失败，尝试备用Logo')
    logoErrorHandled.value = true
    event.target.src = '/app/logo.svg' // 尝试PNG格式
  } else {
    // 如果所有logo都失败，隐藏图片
    console.log('所有Logo都加载失败，隐藏Logo')
    event.target.style.display = 'none'
  }
}

const emit = defineEmits(['setActiveTab', 'toggleDarkMode', 'toggleSidebar'])

// 修改导航项为computed属性，使用t()函数进行国际化
const allNavItems = computed(() => [
  {
    name: t('sidebar.dashboard'),
    icon: LayoutDashboard,
    value: 'dashboard',
    show: true
  },
  {
    name: t('sidebar.shop'),
    icon: ShoppingBag,
    value: 'shop',
    show: enableSiteShop.value
  },
  {
    name: t('sidebar.buy'),
    icon: CreditCard,
    value: 'buy',
    show: siteStore.fkAddress ? true : false
  },
  {
    name: t('sidebar.redeem'),
    icon: Key,
    value: 'redeem',
    show: siteStore.closeCardExchange === 'true' ? false : true
  },
  {
    name: t('sidebar.promotion'),
    icon: Handshake,
    value: 'promotion',
    show: siteStore.enableInvite === 'true' ? true : false
  },
  {
    name: t('sidebar.announcements'),
    icon: Megaphone,
    value: 'announcements',
    show: siteStore.siteAnnouncement ? true : false
  },
  {
    name: t('sidebar.instructions'),
    icon: BookOpen,
    value: 'instructions',
    show: siteStore.userGuideUrl ? true : false
  },
  { 
    name: siteStore.customerSidebarName,
    icon: LayoutGrid,
    value: 'customerSidebar',
    show: siteStore.customerSidebarName ? true : false
  },
])
const navItems = computed(() =>
  allNavItems.value.filter(item => item.show)
)

const isMobile = computed(() => window.innerWidth <= 768)

const sidebarWidth = computed(() => {
  if (isMobile.value) return 'w-48'
  return props.sidebarOpen ? 'w-64' : 'w-16'
})

const sidebarClass = computed(() => [
  'fixed inset-y-0 left-0 z-50 border-r dark:border-gray-700 p-4 flex flex-col transition-all duration-300 ease-in-out bg-white dark:bg-gray-800',
  sidebarWidth.value,
  {
    'translate-x-0': props.sidebarOpen && isMobile.value,
    '-translate-x-full': !props.sidebarOpen && isMobile.value,
    'top-16': isMobile.value,
  }
])

const handleShowProfile = () => {
  if (!isLogin.value) {
    goToLogin()
    return
  }
  userProfileDialogRef.value.openDialog()
}
const noticeVisible = ref(false)

const handleShowNotice = () => {
  noticeVisible.value = true
}

const languages = [
  { code: 'en', name: 'English' },
  { code: 'zh', name: '中文' },
  { code: 'fa', name: 'فارسی' },
  { code: 'my', name: 'မြန်မာ' }
];

const changeLanguage = (lang) => {
  locale.value = lang;
  localStorage.setItem('language', lang);
  window.location.reload();
};

const goToLogin = () => {
  ElMessageBox.confirm(
    t('auth.loginPrompt'),
    t('auth.notLoggedIn'),
    {
      confirmButtonText: t('common.login'),
      cancelButtonText: t('common.register'),
      type: 'warning',
    }
  )
    .then(() => {
      router.push('/login');
    })
    .catch(() => { 
      router.push('/register');
    });
};

const signInDialogRef = ref()

const handleSignIn = () => {
  if (!isLogin.value) {
    goToLogin()
    return
  }
  signInDialogRef.value.openDialog()
}

// 添加缺失的 handleTabClick 函数
const handleTabClick = (tabValue) => {
  emit('setActiveTab', tabValue)
}
</script>

<template>
  <div class="relative">
    <!-- 顶部导航栏 - 仅在移动端显示 -->
    <nav v-if="isMobile" class="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 shadow-md h-16 px-4 flex items-center justify-between">
      <div class="flex items-center">
        {{ logoUrl }}
        <img :src="logoUrl" alt="Logo" class="w-8 h-8 object-contain filter dark:invert" @error="handleLogoError" />
        <h1 class="ml-3 text-lg font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-indigo-500">
          {{ siteName }}
        </h1>
      </div>
      
      <!-- 右侧功能按钮组 -->
      <div class="flex items-center space-x-3">
        <button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-all" @click="handleShowProfile">
          <User class="w-5 h-5 text-gray-700 dark:text-gray-300" />
        </button>
        <button v-if="enableSignIn" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-all" @click="handleSignIn">
          <Gift class="w-5 h-5 text-gray-700 dark:text-gray-300" />
        </button>
        <button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-all" @click="handleShowNotice">
          <Bell class="w-5 h-5 text-gray-700 dark:text-gray-300" />
        </button>
        <button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-all" @click="emit('toggleSidebar')">
          <component :is="props.sidebarOpen ? Fold : Expand" class="w-5 h-5 text-gray-700 dark:text-gray-300" />
        </button>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <aside 
      :class="[
        'fixed top-0 left-0 h-full bg-white dark:bg-gray-900 shadow-lg transition-all duration-300 z-40',
        isMobile ? 'mt-16' : '',
        props.sidebarOpen ? 'w-64' : 'w-16',
        isMobile && !props.sidebarOpen ? '-translate-x-full' : 'translate-x-0'
      ]"
    >
      <!-- Logo区域 - 仅在非移动端显示 -->
      <div class="flex items-center p-4" v-if="!isMobile">
        <div class="w-8 h-8 flex items-center justify-center">
          <img :src="logoUrl" alt="Logo" class="w-8 h-8 object-contain" @error="handleLogoError" />
        </div>
        <h1 v-if="props.sidebarOpen"
          class="ml-3 text-lg font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-indigo-500 truncate">
          {{ siteName }}
        </h1>
      </div>

      <!-- 导航菜单 -->
      <nav class="mt-4 px-2 overflow-y-auto h-[calc(100vh-80px)]">
        <ul class="space-y-1">
          <li v-for="item in navItems" :key="item.value">
            <button
              @click="handleTabClick(item.value)"
              :class="[
                'w-full flex items-center px-3 py-2.5 rounded-lg transition-all',
                props.activeTab === item.value
                  ? 'bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-md'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300'
              ]"
            >
              <component :is="item.icon" class="w-5 h-5 flex-shrink-0" />
              <span v-if="props.sidebarOpen" class="ml-3 text-sm font-medium truncate">{{ item.name }}</span>
            </button>
          </li>
        </ul>

        <!-- 底部功能按钮 - 仅在非移动端显示 -->
        <div v-if="!isMobile" class="absolute bottom-4 left-0 right-0 px-2">
          <ul class="space-y-1">
            <li>
              <button
                @click="toggleDark"
                class="w-full flex items-center px-3 py-2.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 transition-all"
              >
                <component :is="isDark ? Sun : Moon" class="w-5 h-5 flex-shrink-0" />
                <span v-if="props.sidebarOpen" class="ml-3 text-sm font-medium">
                  {{ isDark ? t('sidebar.lightMode') : t('sidebar.darkMode') }}
                </span>
              </button>
            </li>
            <li>
              <button
                @click="handleShowProfile"
                class="w-full flex items-center px-3 py-2.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 transition-all"
              >
                <User class="w-5 h-5 flex-shrink-0" />
                <span v-if="props.sidebarOpen" class="ml-3 text-sm font-medium">
                  {{ t('sidebar.profile') }}
                </span>
              </button>
            </li>
            <li v-if="enableSignIn">
              <button
                @click="handleSignIn"
                class="w-full flex items-center px-3 py-2.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 transition-all"
              >
                <Gift class="w-5 h-5 flex-shrink-0" />
                <span v-if="props.sidebarOpen" class="ml-3 text-sm font-medium">
                  {{ t('sidebar.signIn') }}
                </span>
              </button>
            </li>
            <li>
              <button
                @click="handleShowNotice"
                class="w-full flex items-center px-3 py-2.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 transition-all"
              >
                <Bell class="w-5 h-5 flex-shrink-0" />
                <span v-if="props.sidebarOpen" class="ml-3 text-sm font-medium">
                  {{ t('sidebar.notifications') }}
                </span>
              </button>
            </li>
            <li>
              <el-dropdown trigger="click" @command="changeLanguage" :hide-on-click="false">
                <div>
                  <el-tooltip v-if="!props.sidebarOpen && !isMobile" :content="$t('tooltip.language')" placement="right">
                    <button class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                      <Languages class="w-5 h-5" />
                    </button>
                  </el-tooltip>
                  <button v-else class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                    <Languages class="w-5 h-5" />
                  </button>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-for="lang in languages" 
                                     :key="lang.code" 
                                     :command="lang.code"
                                     :class="{ 'text-purple-600': locale === lang.code }">
                      {{ lang.name }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </li>
          </ul>
        </div>
      </nav>
    </aside>

    <UserProfileDialog ref="userProfileDialogRef" />

    <NotificationDialog v-model:visible="noticeVisible" />

    <SignInDialog ref="signInDialogRef" />
  </div>
</template>

<style scoped>
/* 移动端样式调整 */
@media (max-width: 768px) {
  .sidebar-button {
    display: flex;
    justify-content: center;
    width: 100%;
  }

  nav ul li button {
    padding: 0.75rem;
  }

  aside {
    position: fixed !important;
    height: 100vh !important; /* 修改：使用全屏高度 */
    top: 0 !important; /* 修改：从顶部开始 */
    overflow-y: auto;
  }

  /* 添加：调整 Logo 区域在移动端的样式 */
  .logo-area {
    margin-top: 1rem;
    margin-bottom: 1rem;
    padding-top: 3rem; /* 为顶部工具栏留出空间 */
  }
}
.sidebar-open {
  transform: translateX(0);
}

.sidebar-close {
  transform: translateX(-100%);
}


:deep(.el-dropdown) {
  color: inherit;  /* 继承父元素的颜色 */
  display: inline-flex;
  line-height: 1;
}
</style>

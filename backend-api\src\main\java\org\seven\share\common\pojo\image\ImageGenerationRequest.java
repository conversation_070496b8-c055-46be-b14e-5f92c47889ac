package org.seven.share.common.pojo.image;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

@Data
public class ImageGenerationRequest {

    @NotEmpty(message = "Prompt cannot be empty")
    private String prompt; // 图像描述

    @Min(value = 1, message = "Number of images must be at least 1")
    @Max(value = 10, message = "Number of images cannot exceed 10")
    private Integer n; // 生成图像数量，默认为 1

    @Pattern(regexp = "256x256|512x512|1024x1024", message = "Size must be 256x256, 512x512, or 1024x1024")
    private String size; // 图像尺寸，如 "1024x1024"

    @Pattern(regexp = "url|b64_json", message = "Response format must be url or b64_json")
    private String responseFormat; // 响应格式，如 "url" 或 "b64_json"

    @NotEmpty
    private String model;
}

package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: ChatGptRedemption
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/28
 */
@Data
@TableName("chatgpt_redemption")
public class ChatGptRedemptionEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    @TableField(value = "createTime", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "updateTime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField("deleted_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deletedAt;

    @TableField("`key`")
    private String key;

    private Integer status;

    @TableField("subTypeId")
    private Long subTypeId;

    @TableField("userId")
    private Long userId;

    @TableField("redeemedTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime redeemedTime;

    @TableField(value = "usedUserId", updateStrategy = FieldStrategy.ALWAYS)
    private Long usedUserId;

    private String remark;

    @TableField("isPlus")
    private Integer isPlus;

}

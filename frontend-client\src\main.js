import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router';
import './style/index.css'
import './permission' // permission control
import 'element-plus/theme-chalk/dark/css-vars.css' //引入样式文件
import '@/style/dark.css'  // 引入自定义样式文件

import { createI18n } from 'vue-i18n'; // 引入vue-i18n
import enLocale from 'element-plus/es/locale/lang/en'; //引入英文
import zhLocale from 'element-plus/es/locale/lang/zh-cn'; // 引入中文
import faLocale from 'element-plus/es/locale/lang/fa'; // 引入中文
import myElementLocale from './locales/element-my' // 新增这行
// svg图标
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon'
// 导入语言包
import en from './locales/en.json';
import zh from './locales/zh.json';
import fa from './locales/fa.json';
import my from './locales/my.json';
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import pinia from './store'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import "default-passive-events";
const messages = {
    en: {
      ...en,
      ...enLocale
    },
    zh: {
      ...zh,
      ...zhLocale
    },
    fa: {
      ...fa,
      ...faLocale
    },
    my: {
      ...my,
      ...myElementLocale
    }
  };
  const i18n = createI18n({
    locale: 'zh', // 默认语言
    fallbackLocale: 'en', // 回退语言
    messages
  });

const app = createApp(App)

app.use(router) // 使用路由
app.use(i18n); // 使用多语言
app.use(ElementPlus, {
  locale: zhCn,
}) // 设置element默认语言
app.component('svg-icon', SvgIcon)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.use(pinia) // 使用pina
app.mount('#app')

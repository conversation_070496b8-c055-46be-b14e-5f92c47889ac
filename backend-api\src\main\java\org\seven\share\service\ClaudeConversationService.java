package org.seven.share.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.pojo.entity.ChatGptConversationEntity;
import org.seven.share.common.pojo.entity.ClaudeConversationEntity;
import org.seven.share.common.pojo.entity.ClaudeSessionEntity;

/**
 * @InterfaceName: ClaudeConversationService
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/11
 */
public interface ClaudeConversationService extends IService<ClaudeConversationEntity> {
}

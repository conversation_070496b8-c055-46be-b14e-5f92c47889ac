package org.seven.share.controller.admin;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.api.R;
import org.seven.share.common.pojo.entity.RiskControlRecordEntity;
import org.seven.share.service.RiskControlRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: RiskControlRecordController
 * @Description:
 * @Author: Seven
 * @Date: 2025/1/26
 */
@RestController
@RequestMapping("/expander-api/risk-control")
public class RiskControlRecordController {
    @Resource
    private RiskControlRecordService riskControlRecordService;

    @GetMapping("/page")
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer page,
                  @RequestParam(value = "size", defaultValue = "10") Integer size,
                  @RequestParam(required = false) String username,
                  @RequestParam(required = false) String prompt,
                  @RequestParam(required = false) String keyword) {
        LambdaQueryWrapper<RiskControlRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotEmpty(username), RiskControlRecordEntity::getUsername, username);
        wrapper.like(StrUtil.isNotEmpty(prompt), RiskControlRecordEntity::getPrompt, prompt);
        wrapper.like(StrUtil.isNotEmpty(keyword), RiskControlRecordEntity::getKeyword, keyword);
        Page<RiskControlRecordEntity> pageInfo = riskControlRecordService.page(new Page<>(page, size), wrapper);
        return R.ok(pageInfo);
    }

    @DeleteMapping("/delete")
    public R delete(@RequestBody List<String> ids){
        return riskControlRecordService.removeBatchByIds(ids) ? R.ok() : R.error();
    }

}

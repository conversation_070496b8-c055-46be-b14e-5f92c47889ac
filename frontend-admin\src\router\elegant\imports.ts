/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  about: () => import("@/views/about/index.vue"),
  account_claude: () => import("@/views/account/claude/index.vue"),
  account_grok: () => import("@/views/account/grok/index.vue"),
  account_openai: () => import("@/views/account/openai/index.vue"),
  "draw_quota-change": () => import("@/views/draw/quota-change/index.vue"),
  "draw_quota-manage": () => import("@/views/draw/quota-manage/index.vue"),
  draw_record: () => import("@/views/draw/record/index.vue"),
  home: () => import("@/views/home/<USER>"),
  manage_log: () => import("@/views/manage/log/index.vue"),
  manage_menu: () => import("@/views/manage/menu/index.vue"),
  manage_role: () => import("@/views/manage/role/index.vue"),
  "manage_user-detail": () => import("@/views/manage/user-detail/[id].vue"),
  manage_user: () => import("@/views/manage/user/index.vue"),
  marketing_cdkey: () => import("@/views/marketing/cdkey/index.vue"),
  marketing_coupon: () => import("@/views/marketing/coupon/index.vue"),
  marketing_notice: () => import("@/views/marketing/notice/index.vue"),
  marketing_withdrawals: () => import("@/views/marketing/withdrawals/index.vue"),
  order_info: () => import("@/views/order/info/index.vue"),
  order_payconfig: () => import("@/views/order/payconfig/index.vue"),
  "platform_claude-topic": () => import("@/views/platform/claude-topic/index.vue"),
  "platform_grok-topic": () => import("@/views/platform/grok-topic/index.vue"),
  platform_riskcontrol: () => import("@/views/platform/riskcontrol/index.vue"),
  platform_sysconfig: () => import("@/views/platform/sysconfig/index.vue"),
  platform_topic: () => import("@/views/platform/topic/index.vue"),
  platform_word: () => import("@/views/platform/word/index.vue"),
  subtype: () => import("@/views/subtype/index.vue"),
  tenant: () => import("@/views/tenant/index.vue"),
  "user-center": () => import("@/views/user-center/index.vue"),
  user: () => import("@/views/user/index.vue"),
};

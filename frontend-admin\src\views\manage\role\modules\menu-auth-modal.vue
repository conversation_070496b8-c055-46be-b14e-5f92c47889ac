<script setup lang="ts">
import { computed, ref, shallowRef, watch } from 'vue';
import { $t } from '@/locales';
import { bindRoleResource, fetchGetAllPages, fetchGetMenuTree, getRoleResource } from '@/service/api';

defineOptions({ name: 'MenuAuthModal' });

interface Props {
  /** the roleId */
  roleId: number;
}

const props = defineProps<Props>();

const visible = defineModel<boolean>('visible', {
  default: false
});

function closeModal() {
  visible.value = false;
}

const title = computed(() => $t('common.edit') + $t('page.manage.role.menuAuth'));

const home = shallowRef('');

async function getHome() {
  home.value = 'home';
}

const pages = shallowRef<string[]>([]);

async function getPages() {
  const { error, data } = await fetchGetAllPages();

  if (!error) {
    pages.value = data;
  }
}

const pageSelectOptions = computed(() => {
  const opts: CommonType.Option[] = pages.value.map(page => ({
    label: page,
    value: page
  }));

  return opts;
});

const tree = shallowRef<Api.SystemManage.MenuTree[]>([]);
const treeRef = ref();
const isAllSelected = ref(false);

async function getTree() {
  const { error, data } = await fetchGetMenuTree();

  if (!error) {
    tree.value = data;
  }
}

const checks = shallowRef<number[]>([]);

async function getChecks() {
  const { error, data } = await getRoleResource(props.roleId);

  if (!error) {
    checks.value = data;
  }
}

// 获取所有节点ID（包括子节点）
function getAllNodeIds(nodes: Api.SystemManage.MenuTree[]): number[] {
  const ids: number[] = [];
  const traverse = (nodeList: Api.SystemManage.MenuTree[]) => {
    nodeList.forEach(node => {
      ids.push(node.id);
      if (node.children && node.children.length) {
        traverse(node.children);
      }
    });
  };
  traverse(nodes);
  return ids;
}

// 全选/取消选中
function toggleSelection() {
  if (isAllSelected.value) {
    treeRef.value?.setCheckedKeys([]);
  } else {
    const allIds = getAllNodeIds(tree.value);
    treeRef.value?.setCheckedKeys(allIds);
  }
  isAllSelected.value = !isAllSelected.value;
}

// 监听选中状态变化
watch(
  () => treeRef.value?.getCheckedKeys(),
  newVal => {
    const allIds = getAllNodeIds(tree.value);
    isAllSelected.value = newVal?.length === allIds.length;
  },
  { deep: true }
);

async function handleSubmit() {
  // 获取所有选中的节点数据（包括子节点）
  const checkedKeys = treeRef.value?.getCheckedKeys();
  const halfCheckedKeys = treeRef.value?.getHalfCheckedKeys();
  const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys];

  // 获取所有选中节点的完整数据
  const getAllCheckedNodes = (treeData: Api.SystemManage.MenuTree[], keys: number[]): Api.SystemManage.MenuTree[] => {
    const result: Api.SystemManage.MenuTree[] = [];

    const traverse = (nodes: Api.SystemManage.MenuTree[]) => {
      nodes.forEach(node => {
        if (keys.includes(node.id)) {
          result.push(node);
        }
        if (node.children && node.children.length) {
          traverse(node.children);
        }
      });
    };

    traverse(treeData);
    return result;
  };

  const checkedNodes = getAllCheckedNodes(tree.value, allCheckedKeys);
  const selectIds = checkedNodes.map(item => item.id);
  // request
  await bindRoleResource(selectIds, props.roleId);
  window.$message?.success?.($t('common.modifySuccess'));

  closeModal();
}

function init() {
  getHome();
  getPages();
  getTree();
  getChecks();
}

watch(visible, val => {
  if (val) {
    init();
  }
});
</script>

<template>
  <ElDialog v-model="visible" :title="title" preset="card" class="w-480px">
    <div class="flex-y-center gap-16px pb-12px">
      <div>{{ $t('page.manage.menu.home') }}</div>
      <ElSelect v-model="home" :options="pageSelectOptions" size="small" class="w-160px">
        <ElOption v-for="{ value, label } in pageSelectOptions" :key="value" :value="value" :label="label"></ElOption>
      </ElSelect>
      <ElButton size="small" @click="toggleSelection">
        {{ isAllSelected ? $t('common.cancelSelectAll') : $t('common.selectAll') }}
      </ElButton>
    </div>
    <ElScrollbar>
      <ElTree
        ref="treeRef"
        v-model:checked-keys="checks"
        :data="tree"
        node-key="id"
        show-checkbox
        :default-checked-keys="checks"
        :check-strictly="true"
      />
    </ElScrollbar>
    <template #footer>
      <ElSpace class="w-full justify-end">
        <ElButton size="small" class="mt-16px" @click="closeModal">
          {{ $t('common.cancel') }}
        </ElButton>
        <ElButton type="primary" size="small" class="mt-16px" @click="handleSubmit">
          {{ $t('common.confirm') }}
        </ElButton>
      </ElSpace>
    </template>
  </ElDialog>
</template>

<style scoped></style>

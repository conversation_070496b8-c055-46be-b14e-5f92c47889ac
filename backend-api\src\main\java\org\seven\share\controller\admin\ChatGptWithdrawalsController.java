package org.seven.share.controller.admin;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.api.R;

import org.seven.share.common.pojo.entity.ChatGptWithdrawalsEntity;
import org.seven.share.service.ChatGptWithdrawalsService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ChatGptWithdrawalsController
 * @Description:
 * @Author: Seven
 * @Date: 2024/10/15
 */
@RestController
@RequestMapping("/expander-api/withdrawals")
public class ChatGptWithdrawalsController {
    @Resource
    private ChatGptWithdrawalsService chatGptWithdrawalsService;

    /**
     * 提现记录分页
     * @return
     */
    @GetMapping("/getWithdrawPage")
    public R getUserWithdrawalsPage(@RequestParam(value = "current", defaultValue = "1") Integer current,
                                    @RequestParam(value = "size", defaultValue = "10") Integer size,
                                    String username) {
        LambdaQueryWrapper<ChatGptWithdrawalsEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotEmpty(username), ChatGptWithdrawalsEntity::getUsername, username);
        Page<ChatGptWithdrawalsEntity> pageInfo = chatGptWithdrawalsService.page(new Page<>(current, size), wrapper);
        return R.ok(pageInfo);
    }


    @GetMapping("/approval")
    public R approval(String id){
        chatGptWithdrawalsService.approvalWithdrawal(id);
        return R.ok();
    }


    @GetMapping("/reject")
    public R reject(String id, String reason){
        chatGptWithdrawalsService.rejectWithdrawal(id, reason);
        return R.ok();
    }

    @DeleteMapping("/delete")
    public R delete(@RequestBody List<String> ids) {
        chatGptWithdrawalsService.removeBatchByIds(ids);
        return R.ok();
    }

    /**
     * 获取已审批和待审批数据
     */
    @GetMapping("/todoData")
    public R getToDoData() {
        Map<String, Integer> map = chatGptWithdrawalsService.getToDoData();
        return R.ok(map);
    }
}

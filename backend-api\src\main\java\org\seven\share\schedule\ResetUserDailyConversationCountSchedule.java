package org.seven.share.schedule;

import lombok.extern.slf4j.Slf4j;
import org.seven.share.mapper.ChatGptUserMapper;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @ClassName: ResetUserDailyConversationCountSchedule
 * @Description:
 * @Author: Seven
 * @Date: 2024/10/12
 */
@Slf4j
@Component
public class ResetUserDailyConversationCountSchedule {
    @Resource
    private ChatGptUserMapper chatGptUserMapper;

    @Scheduled(cron = "0 0 0 * * ?") // Cron 表达式：每天零点
    public void resetDailyConversationCount(){
        log.info("开始重置每日对话次数任务...");
        chatGptUserMapper.resetDailyConversations();
        log.info("完成重置每日对话次数...");
    }
}

<template>
  <div class="min-h-screen bg-white dark:bg-black flex items-center justify-center px-4 transition-colors duration-300">
    <div class="text-center max-w-md mx-auto">
      <!-- 错误代码 -->
      <div class="mb-8">
        <h1 class="text-8xl md:text-9xl font-bold text-black dark:text-white mb-2 tracking-tight">
          401
        </h1>
        <div class="w-16 h-1 bg-black dark:bg-white mx-auto"></div>
      </div>

      <!-- 错误信息 -->
      <div class="mb-8 space-y-4">
        <h2 class="text-2xl md:text-3xl font-semibold text-black dark:text-white">
          登录已过期
        </h2>
        <p class="text-gray-600 dark:text-gray-400 text-base md:text-lg leading-relaxed">
          您的登录凭证已过期，请重新登录以继续使用。
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="space-y-3">
        <button
          @click="handleLogin"
          class="w-full px-6 py-3 bg-black dark:bg-white text-white dark:text-black font-medium rounded-lg hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 transform hover:scale-105"
        >
          重新登录
        </button>
        <button
          @click="goBack"
          class="w-full px-6 py-3 border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-200"
        >
          返回上页
        </button>
      </div>

      <!-- 装饰元素 -->
      <div class="mt-12 flex justify-center space-x-2">
        <div class="w-2 h-2 bg-black dark:bg-white rounded-full animate-pulse"></div>
        <div class="w-2 h-2 bg-gray-400 dark:bg-gray-600 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
        <div class="w-2 h-2 bg-gray-300 dark:bg-gray-700 rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const handleLogin = () => {
  router.replace('/login')
}

const goBack = () => {
  router.go(-1)
}
</script>


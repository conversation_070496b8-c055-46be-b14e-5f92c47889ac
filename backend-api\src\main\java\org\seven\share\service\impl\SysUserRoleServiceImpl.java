package org.seven.share.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.api.Result;
import org.seven.share.common.enums.DelStatusEnums;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.pojo.entity.SysRole;
import org.seven.share.common.pojo.entity.SysUserRole;
import org.seven.share.mapper.CreateTableMapper;
import org.seven.share.mapper.SysUserRoleMapper;
import org.seven.share.service.SysRoleService;
import org.seven.share.service.SysUserRoleService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole>
        implements SysUserRoleService {


    final SysRoleService sysRoleService;

    final CreateTableMapper createTableMapper;

    @PostConstruct
    public void init() {
        createTableMapper.createSysUserRoleTable();
        if (count() == 0) {
            createTableMapper.insertSysUserRole();
        }
    }

    public void delUserRoleInfo(Long userId) {

        log.info("根据用户ID删除用户角色: {}", userId);

        LambdaQueryWrapper<SysUserRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserRole::getUserId, userId);

        baseMapper.delete(queryWrapper);
    }

    /**
     * 绑定用户角色
     *
     * @param roleCodes 角色code集合
     * @param userId    用户Id
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> bindingUserRoles(List<String> roleCodes, Long userId) {

        // 先删除
        delUserRoleInfo(userId);

        // 根据角色编码查找系统角色信息
        List<SysRole> sysRoles = sysRoleService.queryRoleListByRoleCode(roleCodes);

        if (sysRoles.isEmpty()) {
            log.error("bindingUserRoles -> 入参: {}", JSONUtil.parse(roleCodes));
            throw new CustomException("未找到传入角色CODE信息");
        }

        List<SysUserRole> userRoles = new ArrayList<>();

        for (SysRole sysRole : sysRoles) {
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setRoleId(sysRole.getId());
            sysUserRole.setUserId(userId);
            sysUserRole.setIsDeleted(DelStatusEnums.DISABLE.getCode());

            userRoles.add(sysUserRole);
        }

        log.info("绑定用户角色 -> saveBatch: {}", JSONUtil.parse(userRoles));

        return Result.success(saveBatch(userRoles));
    }

    /**
     * 批量删除用户与角色信息
     * @param ids 用户id集合
     */
    @Override
    public void deleteUserRoleRelationBatch(List<Long> ids) {
        LambdaQueryWrapper<SysUserRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysUserRole::getUserId, ids);
        baseMapper.delete(queryWrapper);
    }

}

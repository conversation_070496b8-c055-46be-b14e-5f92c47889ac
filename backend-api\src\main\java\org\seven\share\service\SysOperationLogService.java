package org.seven.share.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.pojo.entity.SysOperationLog;
import org.seven.share.common.pojo.entity.SysRole;

import java.util.List;
import java.util.Map;

public interface SysOperationLogService extends IService<SysOperationLog> {

    /**
     * 清空
     *
     * @return
     */
    int clean();

    /**
     * 导出
     *
     * @param sysOperationLog
     * @return
     */
    List<SysOperationLog> getExportList(SysOperationLog sysOperationLog);

    IPage<SysOperationLog> getPage(Map<String, Object> params);
}

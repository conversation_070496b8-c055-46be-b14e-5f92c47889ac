<template>
  <div class="container max-w-10xl mx-auto px-2 sm:px-4 pb-4 md:py-4">
    
    <div class="bg-white dark:bg-slate-800 rounded-md">
      <div class="w-full border dark:border-gray-700 px-4 md:px-6">
        <div class="pt-2 md:pr-6 md:pt-6">
          <div class="flex flex-col md:flex-row md:justify-between md:items-center">
            <!-- 移动端使用满宽导航栏 -->
            <div class="w-full md:w-[600px] mb-3 md:mb-0">
              <!-- 移动端使用2x2网格 -->
              <div class="grid grid-cols-2 md:grid-cols-4 bg-slate-100 dark:bg-slate-700 rounded-md p-1">
                <button 
                  v-for="tab in tabs" 
                  :key="tab.value"
                  @click="activeTab = tab.value"
                  :class="[
                    'flex items-center justify-center py-2 px-2 sm:px-4 rounded-md transition-all duration-200 text-xs sm:text-sm',
                    activeTab === tab.value
                      ? 'bg-black dark:bg-white text-white dark:text-black shadow-md'
                      : 'hover:bg-slate-200 dark:hover:bg-slate-600'
                  ]"
                >
                  <component :is="tab.icon" class="h-4 w-4 mr-1 sm:mr-2" />
                  <span class="truncate">{{ tab.label }}</span>
                </button>
              </div>
            </div>
            
            <!-- 桌面端的额度显示 -->
            <div v-if="userStore.isLogin" class="hidden md:flex items-center text-sm ml-4 bg-slate-100 dark:bg-slate-700 px-3 py-2 rounded-md">
              <el-tooltip placement="top">
                <template #content>
                  <div class="text-xs">
                    <div class="mb-1">{{ t('draw.quota.used') }}：{{ quotaUsed }} 次</div>
                    <div>{{ t('draw.quota.resetTime') }}：{{ formatResetDate }}</div>
                  </div>
                </template>
                <div class="flex items-center cursor-pointer">
                  <el-icon class="mr-1 text-gray-700 dark:text-gray-200"><Picture /></el-icon>
                  <span class="font-medium">{{ t('draw.quota.title') }}：</span>
                  <template v-if="loading">
                    <el-skeleton style="width: 60px" animated>
                      <template #template>
                        <el-skeleton-item variant="text" style="width: 100%" />
                      </template>
                    </el-skeleton>
                  </template>
                  <template v-else>
                    <span class="font-bold text-gray-700 dark:text-gray-200 mr-1">{{ quotaRemaining }}</span>
                    <span class="text-gray-700 dark:text-gray-200">/ {{ quotaTotal }}</span>
                    <button @click="fetchQuota" class="ml-1 p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors duration-200">
                      <el-icon class="text-black dark:text-white"><Refresh /></el-icon>
                    </button>
                  </template>
                </div>
              </el-tooltip>
            </div>
          </div>
          
          <!-- 移动端的额度显示 - 简化版本 -->
          <div v-if="userStore.isLogin" class="md:hidden mb-2 flex items-center text-xs mt-2 bg-slate-100 dark:bg-slate-700 px-3 py-2 rounded-md">
            <div class="flex items-center flex-1">
              <el-icon class="mr-1 text-gray-700"><Picture /></el-icon>
              <span class="font-medium">{{ t('draw.quota.title') }}：</span>
              <template v-if="loading">
                <el-skeleton style="width: 40px" animated>
                  <template #template>
                    <el-skeleton-item variant="text" style="width: 100%" />
                  </template>
                </el-skeleton>
              </template>
              <template v-else>
                <span class="font-bold text-gray-700 dark:text-gray-200 mr-1">{{ quotaRemaining }}</span>
                <span class="text-gray-500">/ {{ quotaTotal }}</span>
              </template>
            </div>
            <div class="text-gray-500 text-xs flex items-center">
              <span class="mr-1">{{ t('draw.quota.used') }}:{{ quotaUsed }}</span>
              <el-button type="primary" link @click="fetchQuota" class="p-0">
                <el-icon size="small"><Refresh /></el-icon>
              </el-button>
            </div>
          </div>
        </div>

        <div class="p-3 md:p-6">
          <ImageGenerator 
            v-if="activeTab === 'generate'" 
            @error="handleError" 
            @image-generated="addToHistory" 
          />
          <ImageEditor 
            v-if="activeTab === 'edit'" 
            @error="handleError" 
            @image-edited="addToHistory" 
          />
          <ImageHistory 
            v-if="activeTab === 'history'" 
            :history="imageHistory" 
          />
          <QuotaRecord
            v-if="activeTab === 'record'" 
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { Sparkles, Wand2, PencilRuler, History, ReceiptText } from 'lucide-vue-next';
import { InfoFilled, Close, Picture, Refresh, Check, Calendar } from '@element-plus/icons-vue';
import ImageGenerator from './image/ImageGenerator.vue';
import ImageEditor from './image/ImageEditor.vue';
import ImageHistory from './image/ImageHistory.vue';
import QuotaRecord from './image/QuotaRecord.vue';
import { useSiteStore } from '@/store/modules/site';
import { useUserStore } from '@/store/modules/user';
import { queryQuota } from '@/api/image';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const siteStore = useSiteStore();
const userStore = useUserStore();
const activeTab = ref('generate');
const error = ref(null);
const imageHistory = ref([]);
const showAnnouncement = ref(true);
const loading = ref(false);
const quotaRemaining = ref(0);
const quotaTotal = ref(0);
const quotaUsed = ref(0);
const resetDate = ref('');
const quotaCreatedDate = ref('');

// 计算剩余额度百分比
const quotaPercentage = computed(() => {
  if (quotaTotal.value === 0) return 0;
  return Math.round((quotaRemaining.value / quotaTotal.value) * 100);
});

// 根据剩余额度确定状态
const quotaStatus = computed(() => {
  if (quotaPercentage.value <= 20) return 'exception';
  if (quotaPercentage.value <= 50) return 'warning';
  return 'success';
});

// 根据剩余额度设置颜色
const quotaColor = computed(() => {
  if (quotaPercentage.value <= 20) return '#f56c6c';
  if (quotaPercentage.value <= 50) return '#e6a23c';
  return '#67c23a';
});

// 格式化重置时间为更友好的显示
const formatResetDate = computed(() => {
  if (!resetDate.value) return t('draw.quota.notSet');

  try {
    const date = new Date(resetDate.value);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays <= 0) return t('draw.quota.today');
    if (diffDays === 1) return t('draw.quota.tomorrow');
    if (diffDays < 30) return t('draw.quota.daysLater', { days: diffDays });

    return t('draw.quota.dateFormat', {
      year: date.getFullYear(),
      month: date.getMonth() + 1,
      day: date.getDate()
    });
  } catch (e) {
    return resetDate.value;
  }
});

// 获取用户绘图额度
const fetchQuota = async () => {
  if (!userStore.isLogin || !userStore.id) return;
  
  loading.value = true;
  try {
    const res = await queryQuota(userStore.id);
    quotaRemaining.value = res.remainingQuota || 0;
    quotaTotal.value = res.totalQuota || 0;
    quotaUsed.value = res.usedQuota || 0;
    resetDate.value = res.resetAt || '';
    quotaCreatedDate.value = res.createdAt || '';
  } catch (error) {
    console.error(t('draw.quota.fetchError'), error);
  } finally {
    loading.value = false;
  }
};

const tabs = computed(() => [
  { value: 'generate', label: t('draw.tabs.generate'), icon: Wand2 },
  { value: 'edit', label: t('draw.tabs.edit'), icon: PencilRuler },
  { value: 'history', label: t('draw.tabs.history'), icon: History },
  { value: 'record', label: t('draw.tabs.record'), icon: ReceiptText }
]);

const handleError = (errorMessage) => {
  error.value = errorMessage;
  console.error("Error:", errorMessage);
};

const addToHistory = (image) => {
  const newImage = {
    ...image,
    id: `img-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
  };
  
  imageHistory.value = [newImage, ...imageHistory.value];
  
  // 更新绘图额度
  fetchQuota();
};

// 从 localStorage 加载历史记录
onMounted(() => {
  try {
    const savedHistory = localStorage.getItem("imageHistory");
    if (savedHistory) {
      const parsedHistory = JSON.parse(savedHistory);
      // 将字符串日期转换回 Date 对象
      const historyWithDates = parsedHistory.map(item => ({
        ...item,
        timestamp: new Date(item.timestamp),
      }));
      imageHistory.value = historyWithDates;
    }
    
    
    // 获取绘图额度
    fetchQuota();
  } catch (error) {
    console.error("Error loading data from localStorage:", error);
  }
});

// 当历史记录变化时保存到 localStorage
watch(imageHistory, (newHistory) => {
  try {
    localStorage.setItem("imageHistory", JSON.stringify(newHistory));
  } catch (error) {
    console.error("Error saving history to localStorage:", error);
  }
}, { deep: true });
</script>

<style scoped>
/* 公告区域的样式 */
.announcement-container {
  position: relative;
}

:deep(.el-icon) {
  vertical-align: middle;
}

:deep(.announcement-content a) {
  color: #8b5cf6;
  text-decoration: underline;
}

:deep(.announcement-content p) {
  margin-bottom: 0.5rem;
}

:deep(.announcement-content p:last-child) {
  margin-bottom: 0;
}

.container {
  min-height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .container {
    min-height: calc(100vh - 80px);
    padding: 0.5rem;
  }
  
  :deep(.el-button) {
    font-size: 0.875rem;
    padding: 6px 12px;
  }
  
  :deep(.el-input__inner) {
    font-size: 0.875rem;
    padding: 0 10px;
    height: 32px;
  }
  
  :deep(.el-textarea__inner) {
    font-size: 0.875rem;
    padding: 8px;
  }
  
  :deep(.el-form-item__label) {
    font-size: 0.875rem;
    padding-bottom: 4px;
  }
}
</style>
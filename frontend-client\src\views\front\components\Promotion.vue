<template>
    <div>
      <h2 class="text-2xl md:text-3xl font-bold mb-6">{{ $t('promotion.title') }}</h2>
      <p class="mb-1">{{ $t('promotion.description') }}</p>
      <promotion-benefits :user="user" />
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { useUserStore } from '@/store/modules/user';
import PromotionBenefits from './promotion/PromotionBenefits.vue';
import { getUser } from '@/api/user';
import { getToken } from '@/utils/auth.js'
const userStore = useUserStore();
const user = ref({});
const fetchUserInfo = async () => {
    if(!getToken()) return
    const param = {
        userName: userStore.username,
    };
    const res = await getUser(param);
    user.value = res;
};
onMounted(async () => {
    await fetchUserInfo();
});
</script>


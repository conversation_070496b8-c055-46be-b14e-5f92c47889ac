<template>
    <el-dialog
      v-model="dialogVisible"
      :title="$t('withdrawDialog.title')"
      :width="dialogWidth"
      @close="handleClose"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="formRef"
      >
        <el-form-item :label="$t('withdrawDialog.amount')" prop="money">
          <div class="flex items-center space-x-2">
            <el-input
              v-model="formData.money"
              :placeholder="$t('withdrawDialog.amountPlaceholder')"
              class="flex-1"
            />
            <button
              @click="formData.money = maxAmount"
              class="px-3 py-1 text-black dark:text-white font-bold hover:bg-gray-100 dark:hover:bg-gray-800 rounded transition-colors duration-200"
            >
              {{ $t('withdrawDialog.extractAll') }}
            </button>
          </div>
        </el-form-item>
  
        <el-form-item :label="$t('withdrawDialog.password')" prop="password">
          <el-input
            v-model="formData.password"
            show-password
            :placeholder="$t('withdrawDialog.passwordPlaceholder')"
          />
        </el-form-item>
  
        <el-form-item :label="$t('withdrawDialog.contact')" prop="contact">
          <el-input
            v-model="formData.contact"
            :placeholder="$t('withdrawDialog.contactPlaceholder')"
          />
        </el-form-item>
  
        <el-form-item :label="$t('withdrawDialog.qrcode')" prop="qrcode">
          <el-upload
            :data="{ userId: userStore.id }"
            accept="image/*"
            :limit="1"
            :headers="importHeaders"
            :action="importUrl"
            list-type="picture-card"
            :auto-upload="true"
            :file-list="formData.qrcode"
            :on-change="handleFileChange"
            :before-upload="beforeAvatarUpload"
            :on-error="handleFileError"
            :on-success="handleFileSuccess"
            :on-exceed="handleFileExceed"
          >
            <el-icon><Plus /></el-icon>
            
            <template #file="{ file }">
              <div>
                <img
                  class="el-upload-list__item-thumbnail"
                  :src="file.url"
                  alt="收款码"
                />
                <span class="el-upload-list__item-actions">
                  <span
                    v-if="!disabled"
                    class="el-upload-list__item-delete"
                    @click="handleRemove(file)"
                  >
                    <el-icon><Delete /></el-icon>
                  </span>
                </span>
              </div>
            </template>
          </el-upload>
        </el-form-item>
  
        <div class="flex justify-end space-x-2 mt-4">
          <button
            @click="handleClose"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
          >
            {{ $t('withdrawDialog.cancel')}}
          </button>
          <button
            :disabled="loading"
            @click="handleSubmit"
            class="px-4 py-2 bg-black dark:bg-white text-white dark:text-black rounded-lg font-medium hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 disabled:opacity-50"
          >
            {{ loading ? '提交中...' : $t('withdrawDialog.confirm') }}
          </button>
        </div>
      </el-form>
    </el-dialog>
  </template>
  
  <script setup>
  import { ref, reactive, computed } from 'vue';
  import { ElMessage } from 'element-plus';
  import { Plus, Delete } from '@element-plus/icons-vue';
  import { getToken } from '@/utils/auth';
  
  const props = defineProps({
    visible: {
      type: Boolean,
      required: true
    },
    dialogWidth: {
      type: [String, Number],
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    userStore: {
      type: Object,
      required: true
    },
    maxAmount: {
      type: Number,
      required: true
    }
  });
  
  const emit = defineEmits(['update:visible', 'submit']);
  
  const importUrl = import.meta.env.VITE_API_URL + '/common/upload-qrcode';
  const disabled = ref(false);
  const formRef = ref(null);
  const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
  });
  
  // 动态计算 headers
  const importHeaders = computed(() => ({
    Authorization: `Bearer ${getToken()}`
  }));
  
  const formData = reactive({
    money: 0,
    password: '',
    contact: '',
    qrcode: [],
    userId: props.userStore.id
  });
  
  // 校验收款码是否为空
  const validateQrCode = (rule, value, callback) => {
    if (formData.qrcode.length === 0) {
      callback(new Error('请上传收款码'));
    } else {
      callback();
    }
  };
  
  const rules = {
    password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],
    money: [{ required: true, message: '请输入提现金额', trigger: 'blur' }],
    contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
    qrcode: [{ validator: validateQrCode, trigger: 'change' }]
  };
  
  const handleClose = () => {
    dialogVisible.value = false;
    formRef.value?.resetFields();
  };
  
  const beforeAvatarUpload = (file) => {
    if (file.type !== 'image/jpeg' && file.type !== 'image/png') {
      ElMessage({
        message: '图片格式要以jpg/png结尾',
        type: 'error',
        plain: true
      });
      return false;
    }
    if (file.size / 1024 / 1024 > 2) {
      ElMessage({
        message: '图片大小不能超过2MB，请重新选择!',
        type: 'error',
        plain: true
      });
      return false;
    }
    return true;
  };
  
  const handleFileError = (error) => {
    ElMessage({
      message: `文件上传失败: ${error}`,
      type: 'error',
      plain: true
    });
  };
  
  const handleFileExceed = () => {
    ElMessage({
      message: '最多只能上传1个文件',
      type: 'error',
      plain: true
    });
  };
  
  const handleFileSuccess = (response, file, fileList) => {
    if (response && response.code === 200) {
      file.status = 'success';
      ElMessage({
        message: '文件上传成功',
        type: 'success',
        plain: true
      });
    } else {
      ElMessage({
        message: `文件上传失败: ${response.message}`,
        type: 'error',
        plain: true
      });
    }
  };
  
  const handleFileChange = (file, fileList) => {
    formData.qrcode = fileList;
  };
  
  const handleRemove = () => {
    formData.qrcode = [];
  };
  
  const handleSubmit = () => {
    formRef.value?.validate((valid) => {
      if (valid) {
        emit('submit', { ...formData });
      }
    });
  };
  </script>
  
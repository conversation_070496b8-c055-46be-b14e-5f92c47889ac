<template>
  <div>
    <h2 class="text-xl sm:text-2xl md:text-3xl font-bold mb-4 sm:mb-6">{{ t('shop.title') }}</h2>
    <!-- 套餐选择区域 -->
    <div class="max-w-7xl mx-auto">
      <!-- 筛选区域 -->
      <div class="flex flex-col sm:flex-row justify-center mb-4 gap-3 sm:gap-4">
        <!-- 套餐类型筛选 -->
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-1 sm:gap-2">
          <span class="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">选择类型：</span>
          <div class="relative w-full sm:w-auto">
            <select
              v-model="activeCategory"
              class="appearance-none bg-gray-100 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-xl px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-900 dark:text-white cursor-pointer focus:outline-none focus:ring-2 focus:ring-black dark:focus:ring-white focus:border-transparent transition-all duration-200 w-full sm:min-w-[140px]">
              <option v-for="category in categories" :key="category" :value="category">
                {{ category }}
              </option>
            </select>
            <!-- 下拉箭头 -->
            <div class="absolute inset-y-0 right-0 flex items-center pr-2 sm:pr-3 pointer-events-none">
              <svg class="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- 时长筛选 -->
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-1 sm:gap-2">
          <span class="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">选择时长：</span>
          <div class="relative w-full sm:w-auto">
            <select
              v-model="selectedDuration"
              class="appearance-none bg-gray-50 dark:bg-gray-800/30 border border-gray-200 dark:border-gray-700 rounded-xl px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-900 dark:text-white cursor-pointer focus:outline-none focus:ring-2 focus:ring-black dark:focus:ring-white focus:border-transparent transition-all duration-200 w-full sm:min-w-[120px]">
              <option :value="null">{{ t('shop.categories.all') }}</option>
              <option v-for="duration in availableDurations" :key="duration" :value="duration">
                {{ duration }}{{ t('shop.days') }}
              </option>
            </select>
            <!-- 下拉箭头 -->
            <div class="absolute inset-y-0 right-0 flex items-center pr-2 sm:pr-3 pointer-events-none">
              <svg class="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading 状态 -->
      <div v-if="loading" class="flex flex-col items-center justify-center py-16">
        <div class="relative">
          <!-- 旋转的圆环 -->
          <div class="w-12 h-12 border-4 border-gray-200 dark:border-gray-700 rounded-full animate-spin border-t-black dark:border-t-white"></div>
          <!-- 内部小圆点 -->
          <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-black dark:bg-white rounded-full animate-pulse"></div>
        </div>
        <p class="mt-4 text-gray-600 dark:text-gray-400 text-sm">{{ t('shop.loading') }}</p>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="filteredPlans.length === 0" class="flex flex-col items-center justify-center py-16">
        <div class="text-center space-y-4">
          <!-- 空盒子图标 -->
          <div class="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
            <svg class="w-8 h-8 text-gray-400 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 9h.01M15 9h.01"/>
            </svg>
          </div>

          <!-- 提示文字 -->
          <div class="space-y-2">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              {{ t('shop.noProducts.title') }}
            </h3>
            <p class="text-gray-500 dark:text-gray-400 text-sm max-w-sm mx-auto">
              {{ t('shop.noProducts.description') }}
            </p>
          </div>

          <!-- 操作按钮 -->
          <div class="flex flex-col sm:flex-row gap-3 justify-center mt-6">
            <button
              @click="fetchData"
              class="px-4 py-2 bg-black dark:bg-white text-white dark:text-black rounded-lg hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 text-sm font-medium">
              {{ t('shop.noProducts.refresh') }}
            </button>
            <button
              @click="resetFilters"
              class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200 text-sm font-medium">
              {{ t('shop.noProducts.clearFilters') }}
            </button>
          </div>
        </div>
      </div>

      <!-- 套餐卡片展示 -->
      <div v-else class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div
          v-for="(plan, index) in filteredPlans"
          :key="index"
          class="relative bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-lg hover:border-gray-300 dark:hover:border-gray-600"
          :class="[
            plan.isHotSale === 1 ? 'border-black dark:border-white' : '',
            plan.exclusive === 1 ? 'border-black dark:border-white' : ''
          ]">

          <!-- 推荐标签 (热销改为推荐) -->
          <div v-if="plan.isHotSale === 1" class="absolute -top-2 -right-2">
            <div class="bg-black dark:bg-white text-white dark:text-black text-xs font-medium px-2 py-1 rounded shadow-sm">
              {{ t('shop.recommended') }}
            </div>
          </div>

          <!-- 独家标签 -->
          <div v-if="plan.exclusive === 1" class="absolute -top-2 -left-2">
            <div class="bg-black dark:bg-white text-white dark:text-black text-xs font-medium px-2 py-1 rounded shadow-sm">
              {{ t('shop.exclusive') }}
            </div>
          </div>

          <!-- 套餐图标和标题 -->
          <div class="text-center mb-3">
            <div class="flex justify-center items-center mb-2">
              <div class="flex space-x-1">
                <img v-if="plan.subType === '1' || plan.subType === '3' || plan.subType === '5' || plan.subType === '7'"
                  src="@/assets/chatgpt.png" class="w-5 h-5 sm:w-6 sm:h-6 rounded" />
                <img v-if="plan.subType === '2' || plan.subType === '3' || plan.subType === '6' || plan.subType === '7'"
                  src="@/assets/claude.png" class="w-5 h-5 sm:w-6 sm:h-6 rounded" />
                <img v-if="isDark && (plan.subType === '4' || plan.subType === '5' || plan.subType === '6' || plan.subType === '7')"
                  src="@/assets/grok-dark.png" class="w-5 h-5 sm:w-6 sm:h-6 rounded" />
                <img v-if="!isDark && (plan.subType === '4' || plan.subType === '5' || plan.subType === '6' || plan.subType === '7')"
                  src="@/assets/grok.png" class="w-5 h-5 sm:w-6 sm:h-6 rounded" />
                <img v-if="!isDark && (plan.subType === '8')"
                  src="@/assets/draw.jpg" class="w-5 h-5 sm:w-6 sm:h-6 rounded" />
                <img v-if="isDark && (plan.subType === '8')"
                  src="@/assets/draw-dark.png" class="w-5 h-5 sm:w-6 sm:h-6 rounded" />
              </div>
            </div>
            <h3 class="text-sm sm:text-base font-semibold text-gray-900 dark:text-white mb-1 truncate" :title="plan.name">
              {{ plan.name }}
            </h3>
            <!-- <p class="text-xs text-gray-500 dark:text-gray-400">{{ categoryMap[plan.subType] }}</p> -->
          </div>

          <!-- 价格和天数/张数显示 -->
          <div class="text-center mb-3">
            <!-- 价格和天数/张数在一行 -->
            <div class="flex items-baseline justify-center gap-2 mb-1">
              <span class="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">
                ¥{{ plan.money }}
              </span>
              <span class="text-xs text-gray-500 dark:text-gray-400">
                <template v-if="plan.subType === '8'">
                  / {{ plan.drawQuota }} 张
                </template>
                <template v-else>
                  / {{ plan.validDays }} {{ t('shop.days') }}
                </template>
              </span>
            </div>
            <!-- 平均每日/每张价格 -->
            <div class="text-xs text-gray-400 dark:text-gray-500">
              <template v-if="plan.subType === '8'">
                {{ t('shop.perImagePrice') }}: ¥{{ (plan.money / plan.drawQuota).toFixed(2) }}
              </template>
              <template v-else>
                {{ t('shop.dailyPrice') }}: ¥{{ (plan.money / plan.validDays).toFixed(2) }}
              </template>
            </div>
          </div>
          <!-- 模型限制信息 -->
          <div class="mb-3" v-if="plan.modelLimits && plan.subType != 8">
            <div class="text-xs text-gray-600 dark:text-gray-400">
              <template v-if="showRateLimit">
                <div class="group relative">
                  <!-- 默认显示"模型速率信息" -->
                  <div class="cursor-help bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-center font-medium transition-all duration-200" :title="getFullModelLimitsText(plan.modelLimits)">
                    模型速率信息
                  </div>
                  <!-- 悬停时显示所有模型限制 -->
                  <div class="opacity-0 invisible group-hover:opacity-100 group-hover:visible absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-20 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-xl p-3 min-w-[200px] transition-all duration-200 pointer-events-none group-hover:pointer-events-auto">
                    <div class="text-xs font-medium text-gray-900 dark:text-white mb-2 border-b border-gray-200 dark:border-gray-600 pb-1">
                      详细速率信息
                    </div>
                    <div v-for="(limit, modelName) in plan.modelLimits" :key="modelName"
                         class="mb-1 last:mb-0 text-xs">
                      <span class="font-medium text-gray-700 dark:text-gray-300">
                        {{ getModelDisplayName(modelName) }}:
                      </span>
                      <span class="text-gray-600 dark:text-gray-400 ml-1">
                        {{ convertUsageLimit(limit.per, limit.limit) }}
                      </span>
                    </div>
                    <!-- 小三角形 -->
                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white dark:border-t-gray-800"></div>
                  </div>
                </div>
              </template>
              <template>
                <div class="group relative">
                  <div class="cursor-help bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-center font-medium transition-all duration-200">
                    <span v-if="(plan.subType === '1' || plan.subType === '3' || plan.subType === '5' || plan.subType === '7') && plan.modelLimits['gpt-4o']">
                      GPT: {{ convertUsageLimit(plan.modelLimits['gpt-4o'].per, plan.modelLimits['gpt-4o'].limit) }}
                    </span>
                    <span v-if="(plan.subType === '2' || plan.subType === '3' || plan.subType === '6' || plan.subType === '7') && plan.modelLimits['claude-3.5']"
                      :class="[(plan.subType === '3' || plan.subType === '6' || plan.subType === '7') && plan.modelLimits['gpt-4o'] ? 'ml-2' : '']">
                      Claude: {{ convertUsageLimit(plan.modelLimits['claude-3.5'].per, plan.modelLimits['claude-3.5'].limit) }}
                    </span>
                    <span v-if="(plan.subType === '4' || plan.subType === '5' || plan.subType === '6' || plan.subType === '7') && plan.modelLimits['grok-3']"
                      :class="[((plan.subType === '5' || plan.subType === '6' || plan.subType === '7') && (plan.modelLimits['gpt-4o'] || plan.modelLimits['claude-3.5'])) ? 'ml-2' : '']">
                      Grok: {{ convertUsageLimit(plan.modelLimits['grok-3'].per, plan.modelLimits['grok-3'].limit) }}
                    </span>
                  </div>
                  <!-- 悬停时显示详细信息 -->
                  <div class="opacity-0 invisible group-hover:opacity-100 group-hover:visible absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-20 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-xl p-3 min-w-[200px] transition-all duration-200 pointer-events-none group-hover:pointer-events-auto">
                    <div class="text-xs font-medium text-gray-900 dark:text-white mb-2 border-b border-gray-200 dark:border-gray-600 pb-1">
                      详细速率信息
                    </div>
                    <div v-for="(limit, modelName) in plan.modelLimits" :key="modelName"
                         class="mb-1 last:mb-0 text-xs">
                      <span class="font-medium text-gray-700 dark:text-gray-300">
                        {{ getModelDisplayName(modelName) }}:
                      </span>
                      <span class="text-gray-600 dark:text-gray-400 ml-1">
                        {{ convertUsageLimit(limit.per, limit.limit) }}
                      </span>
                    </div>
                    <!-- 小三角形 -->
                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white dark:border-t-gray-800"></div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- 功能特性 -->
          <div class="space-y-1 mb-4">
            <div v-if="plan.features && plan.features.length > 0">
              <!-- 始终显示的功能（前4个或全部如果展开） -->
              <transition-group name="feature" tag="div" class="space-y-1">
                <div v-for="(feature, idx) in (isExpanded(plan.id) ? plan.features : plan.features.slice(0, 4))"
                     :key="`${plan.id}-${idx}`"
                     class="flex items-start feature-item">
                  <CheckCircle class="w-3 h-3 text-black dark:text-white shrink-0 mt-0.5" />
                  <span class="ml-2 text-xs text-gray-600 dark:text-gray-300 truncate">{{ feature }}</span>
                </div>
              </transition-group>
              <!-- 更多功能按钮 -->
              <div v-if="plan.features.length > 4"
                   @click="toggleFeatures(plan.id)"
                   class="text-xs text-gray-500 dark:text-gray-400 text-center cursor-pointer hover:text-gray-700 dark:hover:text-gray-300 transition-colors duration-200 py-1 feature-toggle-btn rounded-md">
                <span class="flex items-center justify-center gap-1">
                  <template v-if="isExpanded(plan.id)">
                    {{ t('shop.showLess') }}
                    <svg class="w-3 h-3 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                    </svg>
                  </template>
                  <template v-else>
                    +{{ plan.features.length - 4 }} {{ t('shop.moreFeatures') }}
                    <svg class="w-3 h-3 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </template>
                </span>
              </div>
            </div>

            <!-- 当没有功能特性时显示默认功能 -->
            <div v-else>
              <!-- 始终显示的默认功能（前4个或全部如果展开） -->
              <transition-group name="feature" tag="div" class="space-y-1">
                <div v-for="(feature, idx) in (isExpanded(plan.id) ? getDefaultFeatures(plan.subType) : getDefaultFeatures(plan.subType).slice(0, 4))"
                     :key="`${plan.id}-default-${idx}`"
                     class="flex items-start feature-item">
                  <CheckCircle class="w-3 h-3 text-black dark:text-white shrink-0 mt-0.5" />
                  <span class="ml-2 text-xs text-gray-600 dark:text-gray-300 truncate">{{ feature }}</span>
                </div>
              </transition-group>
              <!-- 更多功能按钮 -->
              <div v-if="getDefaultFeatures(plan.subType).length > 4"
                   @click="toggleFeatures(plan.id)"
                   class="text-xs text-gray-500 dark:text-gray-400 text-center cursor-pointer hover:text-gray-700 dark:hover:text-gray-300 transition-colors duration-200 py-1 feature-toggle-btn rounded-md">
                <span class="flex items-center justify-center gap-1">
                  <template v-if="isExpanded(plan.id)">
                    {{ t('shop.showLess') }}
                    <svg class="w-3 h-3 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                    </svg>
                  </template>
                  <template v-else>
                    +{{ t('shop.moreFeatures') }}
                    <svg class="w-3 h-3 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </template>
                </span>
              </div>
            </div>
          </div>

          <!-- 购买按钮 -->
          <button
            @click="subscription(plan)"
            class="w-full py-2 px-3 rounded-lg font-medium transition-all duration-200 bg-black dark:bg-white text-white dark:text-black hover:bg-gray-800 dark:hover:bg-gray-200 text-xs sm:text-sm">
            {{ t('shop.subscribe') }}
          </button>
        </div>
      </div>
    </div>

    <!-- 支付对话框 -->
    <el-dialog v-model="payDialogVisible" :width="dialogWidth" :close-on-click-modal="false" align-center
      :before-close="handleBeforeClose" class="payment-dialog">
        <template #header>
          <div class="text-lg font-semibold text-black dark:text-white">
            {{ t('shop.payment.title') }}
          </div>
        </template>

        <div class="space-y-4">
          <!-- 订单信息卡片 -->
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div class="space-y-3">
              <!-- 套餐名称和价格 -->
              <div class="flex items-center justify-between">
                <div>
                  <div class="font-medium text-black dark:text-white">{{ form.planName }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    <template v-if="form.subType == 8">
                      {{ form.drawQuota }} 张
                    </template>
                    <template v-else>
                      {{ form.validDays }} {{ t('shop.days') }}
                    </template>
                  </div>
                </div>
                <div class="text-right">
                  <template v-if="form.originalMoney != form.money">
                    <div class="text-sm text-gray-400 dark:text-gray-500 line-through">
                      ¥{{ form.originalMoney }}
                    </div>
                    <div class="text-lg font-bold text-black dark:text-white">
                      ¥{{ form.money }}
                    </div>
                  </template>
                  <template v-else>
                    <span class="text-lg font-bold text-black dark:text-white">
                      ¥{{ form.originalMoney }}
                    </span>
                  </template>
                </div>
              </div>
              <!-- 使用限制 -->
              <div v-if="form.modelLimits && form.subType != 8" class="text-xs text-gray-500 dark:text-gray-400">
                <template v-if="showRateLimit">
                  <div v-for="(limit, modelName) in form.modelLimits" :key="modelName"
                       class="mb-1 last:mb-0">
                    <span class="cursor-help" :title="`${modelName}: ${convertUsageLimit(limit.per, limit.limit)}`">
                      {{ getModelDisplayName(modelName) }}: {{ convertUsageLimit(limit.per, limit.limit) }}
                    </span>
                  </div>
                </template>
                <template>
                  <span v-if="form.planType == 1 && form.modelLimits['gpt-4o']">
                    ChatGPT: {{ convertUsageLimit(form.modelLimits['gpt-4o'].per, form.modelLimits['gpt-4o'].limit) }}
                  </span>
                  <span v-if="form.planType == 2 && form.modelLimits['claude-3.5']">
                    Claude: {{ convertUsageLimit(form.modelLimits['claude-3.5'].per, form.modelLimits['claude-3.5'].limit) }}
                  </span>
                  <span v-if="form.planType == 3 && form.modelLimits['gpt-4o'] && form.modelLimits['claude-3.5']">
                    ChatGPT: {{ convertUsageLimit(form.modelLimits['gpt-4o'].per, form.modelLimits['gpt-4o'].limit) }} |
                    Claude: {{ convertUsageLimit(form.modelLimits['claude-3.5'].per, form.modelLimits['claude-3.5'].limit) }}
                  </span>
                </template>
              </div>
            </div>
          </div>

          <!-- 二维码部分 -->
          <div class="text-center" v-if="qrCodeData">
            <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 inline-block">
              <qrcode v-if="qrcodeShow" :value="qrCodeData" :width="160" :height="160" type="image/png" />
              <img v-if="qrcodeHpShow" :src="qrCodeData" width="160" height="160" alt="支付二维码获取失败" class="rounded" />
            </div>
            <div class="mt-3">
              <p class="text-sm text-black dark:text-white font-medium flex items-center justify-center gap-2">
                <span class="w-1.5 h-1.5 bg-black dark:bg-white rounded-full animate-pulse"></span>
                {{ t('shop.payment.scanToPay', { payMethod: payName }) }}
              </p>
            </div>
          </div>

          <!-- 支付方式选择 -->
          <div v-if="!qrCodeData">
            <h4 class="text-base font-medium text-black dark:text-white mb-3">
              {{ t('shop.payment.selectPayment') }}
            </h4>
            <div class="grid grid-cols-3 gap-2">
              <button v-for="option in paymentOptions" :key="option.id"
                class="flex flex-col items-center justify-center p-3 rounded-lg border-2 transition-all duration-200 min-h-[70px]"
                :class="[
                  form.payType === option.paymentsType
                    ? 'bg-black dark:bg-white border-black dark:border-white text-white dark:text-black'
                    : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-500 text-gray-700 dark:text-gray-300'
                ]" @click="handleRadioChange(option)">
                <img v-if="option.iconUrl" :src="option.iconUrl" :alt="option.title"
                  class="w-6 h-6 mb-1"
                  :class="form.payType === option.paymentsType ? 'filter invert dark:invert-0' : ''" />
                <img v-else :src="getIcon(option.paymentsType)"
                  class="w-6 h-6 mb-1"
                  :class="form.payType === option.paymentsType ? 'filter invert dark:invert-0' : ''" />
                <span class="text-xs font-medium text-center">
                  {{ option.title }}
                </span>
              </button>
            </div>
          </div>

          <!-- 按钮组 -->
          <div class="flex gap-3" v-if="!qrCodeData">
            <button @click="goPay" :disabled="buttonLoading || !form.payType"
              class="flex-1 bg-black dark:bg-white text-white dark:text-black font-medium px-4 py-2.5 rounded-lg transition-all duration-200 hover:bg-gray-800 dark:hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm">
              {{ buttonLoading ? t('shop.payment.processing') : t('shop.payment.confirmPay') }}
            </button>
            <button @click="openCouponDialog"
              class="px-4 py-2.5 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 text-sm">
              {{ t('shop.payment.coupon') }}
            </button>
          </div>

          <!-- 支付完成后的提示 -->
          <div class="flex gap-3" v-if="qrCodeData">
            <button @click="handleBeforeClose(() => {})"
              class="flex-1 px-4 py-2.5 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 text-sm">
              {{ t('shop.payment.close') }}
            </button>
          </div>
        </div>
      </el-dialog>

    <!-- 优惠券对话框 -->
    <el-dialog v-model="couponVisible" :width="couponDialogWidth" class="coupon-dialog" align-center
      @close="handleCouponClose">
        <template #header>
          <div class="text-lg font-medium text-black dark:text-white">{{ t('shop.coupon.title') }}</div>
        </template>
        <div class="space-y-4">
          <div>
            <el-input
              v-model.trim="couponData"
              :placeholder="t('shop.coupon.placeholder')"
              clearable
              class="w-full" />
          </div>
          <div class="flex justify-end gap-3">
            <button @click="couponVisible = false"
              class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 text-sm">
              {{ t('shop.coupon.cancel') }}
            </button>
            <button @click="handleCouponChange"
              class="px-4 py-2 rounded-lg bg-black dark:bg-white text-white dark:text-black font-medium hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 text-sm">
              {{ t('shop.coupon.confirm') }}
            </button>
          </div>
        </div>
      </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { ElNotification, ElMessageBox } from 'element-plus';
import { useI18n } from 'vue-i18n';
import * as api from '@/api/subtype.js';
import {
  createQrCode,
  queryF2FOrderStatus,
  queryEpayOrderStatus,
  queryXhOrderStatus,
  queryLanTuPayStatus,
  queryWxNativeStatus,
  getPayList,
} from '@/api/pay';
import { CheckCircle } from 'lucide-vue-next';
import Qrcode from 'vue-qrcode';
import { convertUsageLimit } from '@/utils/date.js';
import { useDark } from '@vueuse/core';
import { getToken } from '@/utils/auth';
import alipayIcon from '@/assets/icons/alipay.svg';
import wechatIcon from '@/assets/icons/wechat.svg';
import usdtIcon from '@/assets/icons/usdt.svg';
import { useSiteStore } from '@/store/modules/site';
const siteStore = useSiteStore();

const showRateLimit = computed(() => siteStore.closeSubTypeShow === "false")
const { t } = useI18n(); // 引入 i18n 的 t 方法
import { useRouter } from 'vue-router';
const router = useRouter();

const isLogin = computed(() => (getToken() ? true : false));

// 套餐筛选状态
const activeCategory = ref(t('shop.categories.all'));
const selectedDuration = ref(null); // null表示显示所有时长

// 套餐分类映射
const categoryMap = {
  1: 'ChatGPT',
  2: 'Claude',
  3: 'ChatGPT&Claude',
  4: 'Grok',
  5: 'ChatGPT&Grok',
  6: 'Claude&Grok',
  7: 'ChatGPT&Claude&Grok',
  8: 'Draw'
};

// 动态生成分类选项
const categories = computed(() => {
  const uniqueTypes = [...new Set(plans.value.map(plan => plan.subType))];
  const cats = [t('shop.categories.all')];

  uniqueTypes.forEach(type => {
    if (categoryMap[type]) {
      cats.push(categoryMap[type]);
    }
  });

  return cats;
});

// 动态生成时长选项（移除"全部时长"选项）
const availableDurations = computed(() => {
  const uniqueDurations = [...new Set(plans.value.filter(plan => plan.subType != 8).map(plan => plan.validDays))];
  return uniqueDurations.sort((a, b) => a - b);
});

// 筛选后的套餐
const filteredPlans = computed(() => {
  let filtered = plans.value;

  // 按分类筛选
  if (activeCategory.value !== t('shop.categories.all')) {
    filtered = filtered.filter(plan => categoryMap[plan.subType] === activeCategory.value);
  }

  // 按时长筛选
  if (selectedDuration.value !== null) {
    filtered = filtered.filter(plan => plan.validDays === selectedDuration.value);
  }

  return filtered;
});

// 恢复原有的数据获取和处理逻辑
const plans = ref([]);

// 获取默认功能特性
const getDefaultFeatures = (subType) => {
  const defaultFeatures = {
    '1': [
      t('shop.defaultFeatures.chatgpt'),
      t('shop.defaultFeatures.webAccess'),
      t('shop.defaultFeatures.mobileApp'),
      t('shop.defaultFeatures.basicSupport')
    ],
    '2': [
      t('shop.defaultFeatures.claude'),
      t('shop.defaultFeatures.webAccess'),
      t('shop.defaultFeatures.mobileApp'),
      t('shop.defaultFeatures.basicSupport')
    ],
    '3': [
      t('shop.defaultFeatures.chatgptClaude'),
      t('shop.defaultFeatures.webAccess'),
      t('shop.defaultFeatures.mobileApp'),
      t('shop.defaultFeatures.prioritySupport')
    ],
    '4': [
      t('shop.defaultFeatures.grok'),
      t('shop.defaultFeatures.realTimeInfo'),
      t('shop.defaultFeatures.webAccess'),
      t('shop.defaultFeatures.basicSupport')
    ],
    '5': [
      t('shop.defaultFeatures.chatgptGrok'),
      t('shop.defaultFeatures.realTimeInfo'),
      t('shop.defaultFeatures.webAccess'),
      t('shop.defaultFeatures.prioritySupport')
    ],
    '6': [
      t('shop.defaultFeatures.claudeGrok'),
      t('shop.defaultFeatures.realTimeInfo'),
      t('shop.defaultFeatures.webAccess'),
      t('shop.defaultFeatures.prioritySupport')
    ],
    '7': [
      t('shop.defaultFeatures.allModels'),
      t('shop.defaultFeatures.realTimeInfo'),
      t('shop.defaultFeatures.apiAccess'),
      t('shop.defaultFeatures.premiumSupport')
    ],
    '8': [
      t('shop.defaultFeatures.aiDrawing'),
      t('shop.defaultFeatures.multipleStyles'),
      t('shop.defaultFeatures.hdGeneration'),
      t('shop.defaultFeatures.commercialLicense')
    ]
  };

  return defaultFeatures[subType] || [
    t('shop.defaultFeatures.basicService'),
    t('shop.defaultFeatures.webAccess'),
    t('shop.defaultFeatures.mobileApp'),
    t('shop.defaultFeatures.basicSupport')
  ];
};

// 获取模型显示名称
const getModelDisplayName = (modelName) => {
  const modelDisplayMap = {
    'gpt-4o': 'GPT-4o',
    'gpt-4': 'GPT-4',
    'gpt-3.5-turbo': 'GPT-3.5',
    'claude-3.5-sonnet': 'Claude-3.5',
    'claude-3.5': 'Claude-3.5',
    'claude-3-opus': 'Claude-3-Opus',
    'claude-3-sonnet': 'Claude-3-Sonnet',
    'claude-3-haiku': 'Claude-3-Haiku',
    'grok-3': 'Grok-3',
    'grok-2': 'Grok-2',
    'gemini-pro': 'Gemini-Pro',
    'gemini-flash': 'Gemini-Flash'
  };
  
  return modelDisplayMap[modelName] || modelName;
};

// 获取完整的模型限制文本
const getFullModelLimitsText = (modelLimits) => {
  return Object.entries(modelLimits)
    .map(([modelName, limit]) => `${getModelDisplayName(modelName)}: ${convertUsageLimit(limit.per, limit.limit)}`)
    .join('\n');
};

const fetchData = async () => {
  try {
    loading.value = true;
    const res = await api.getSubTypeList();
    plans.value = res.map((plan) => ({
      ...plan,
      category: categoryMap[plan.subType] || '未分类',
    }));
  } catch (error) {
    console.error(t('shopDialog.fetchError'));
    ElNotification({
      title: t('shop.error.title'),
      message: t('shop.error.fetchFailed'),
      type: 'error',
      position: 'top-right',
      duration: 3000
    });
  } finally {
    loading.value = false;
  }
};

// 重置筛选条件
const resetFilters = () => {
  activeCategory.value = t('shop.categories.all');
  selectedDuration.value = null;
};

// 切换功能特性展开状态
const toggleFeatures = (planId) => {
  if (expandedFeatures.value.has(planId)) {
    expandedFeatures.value.delete(planId);
  } else {
    expandedFeatures.value.add(planId);
  }
};

// 检查是否展开
const isExpanded = (planId) => {
  return expandedFeatures.value.has(planId);
};

const payName = computed(() => {
  if (form.value.payType === 1 || form.value.payType === 3 || form.value.payType === 8) {
    return t('shop.payment.methods.alipay'); // '支付宝'
  } else if (form.value.payType === 6) {
    return t('shop.payment.methods.usdt'); // 'USTD'
  } else {
    return t('shop.payment.methods.wechat'); // '微信'
  }
});

const isDark = useDark();

const dialogWidth = computed(() => (windowWidth.value < 768 ? '95%' : '420px'));
const couponDialogWidth = computed(() =>
  windowWidth.value < 768 ? '90%' : '380px'
);

const couponVisible = ref(false);
const loading = ref(false);
const payDialogVisible = ref(false);
const expandedFeatures = ref(new Set()); // 跟踪展开状态的卡片ID
const form = ref({
  planId: '',
  payType: '',
  coupon: '',
  originalMoney: '',
  money: '',
  planName: '',
  validDays: '',
  limit: '',
  per: '',
  claudeLimit: '',
  claudePer: '',
  planType: '',
  subType: '',
  modelLimits: null,
  drawQuota: '',
});
const qrcodeShow = ref(false);
const qrcodeHpShow = ref(false);
const intervalId = ref(null);
const windowWidth = ref(window.innerWidth);
const paymentOptions = ref([]);
const qrCodeData = ref(null);
const jumpUrl = ref('');
const payMethod = ref('');
const tradeNo = ref('');
const enableH5 = ref(null);
const couponData = ref('');
const buttonLoading = ref(false);
const vPayFlag = ref(false);

const isMobile = computed(() => {
  const userAgent = navigator.userAgent.toLowerCase();
  const mobileKeywords = [
    'android',
    'iphone',
    'ipad',
    'ipod',
    'windows phone',
    'mqqbrowser',
  ];
  const isNarrowScreen = window.innerWidth <= 768;
  return (
    isNarrowScreen ||
    mobileKeywords.some((keyword) => userAgent.includes(keyword))
  );
});

const jumpFlag = computed(
  () =>
    form.value.payType === 6 ||
    form.value.payType === 7 ||
    (isMobile.value && enableH5.value)
);

const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

onMounted(() => {
  fetchData();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  clearTimers();
});

const getIcon = (type) => {
  switch (type) {
    case 1:
    case 3:
    case 8:
    case 9:
      return alipayIcon;
    case 2:
    case 4:
    case 5:
    case 7:
      return wechatIcon;
    case 6:
      return usdtIcon;
  }
};
const subscription = async (plan) => {
  payDialogVisible.value = true;
  form.value = {
    planId: plan.id,
    planName: plan.name,
    originalMoney: plan.money,
    money: plan.money,
    validDays: plan.validDays,
    limit: plan.limit,
    per: plan.per,
    claudeLimit: plan.claudeLimit,
    claudePer: plan.claudePer,
    payType: '',
    planType: plan.subType,
    subType: plan.subType,
    coupon: '',
    modelLimits: plan.modelLimits,
    drawQuota: plan.drawQuota || '',
  };
  await fetchPayList();
};


// 处理关闭前的确认
const handleBeforeClose = (done) => {
  // 如果已经生成了二维码，说明订单已创建，需要确认关闭
  if (qrCodeData.value || tradeNo.value) {
    ElMessageBox.confirm(
      t('shop.payment.confirmClose.message'),
      t('shop.payment.confirmClose.title'),
      {
        confirmButtonText: t('shop.payment.confirmClose.confirm'),
        cancelButtonText: t('shop.payment.confirmClose.cancel'),
        type: 'warning',
        customClass: 'confirm-close-dialog'
      }
    ).then(() => {
      closeDialog();
      done();
    }).catch(() => {
      // 用户取消关闭，不执行任何操作
    });
  } else {
    // 没有生成订单，直接关闭
    closeDialog();
    done();
  }
};

const closeDialog = () => {
  payDialogVisible.value = false;
  qrCodeData.value = null;
  form.value = {
    planId: '',
    payType: '',
    coupon: '',
    originalMoney: '',
    money: '',
    planName: '',
    validDays: '',
    limit: '',
    per: '',
    claudeLimit: '',
    claudePer: '',
    planType: '',
    subType: '',
    modelLimits: null,
    drawQuota: '',
  };
  clearTimers();
};

const fetchQrcode = async () => {
  clearTimers();
  try {
    loading.value = true;
    const userInfo = JSON.parse(localStorage.getItem('user') || '{}');
    const params = {
      typeId: form.value.planId,
      payType: form.value.payType,
      userToken: userInfo.username,
      coupon: form.value.coupon,
      isMobile: isMobile.value,
    };
    const res = await createQrCode(params);
    if (res != null) {
      tradeNo.value = res.tradeNo;
      payMethod.value = form.value.payType;

      switch (form.value.payType) {
        case 1:
        case 2:
        case 6:
          if (jumpFlag.value || res.payUrl) {
            vPayFlag.value = true;
            jumpUrl.value = res.payUrl;
          } else {
            qrCodeData.value = res.qrcode;
          }
          break;
        case 3:
        case 7:
          qrCodeData.value = res.qrcode;
          break;
        case 4:
        case 8:
          const isWeixinBrowser = /micromessenger/i.test(navigator.userAgent);
          if (isWeixinBrowser && enableH5.value) {
            vPayFlag.value = true;
            jumpUrl.value = res.url;
          } else {
            qrCodeData.value = res.url_qrcode;
          }
          break;
        case 5:
          if (jumpFlag.value) {
            vPayFlag.value = true;
            jumpUrl.value = res.payUrl;
          } else {
            qrCodeData.value = res.qrcode;
          }
          break;
        case 9:
          if (res.html) {
            const div = document.createElement('div');
            div.innerHTML = res.html;
            document.body.appendChild(div);
            // 手动提交表单
            const form = div.querySelector('form[name="punchout_form"]');
            if (form) {
              form.submit();
            } else {
              console.error('Form not found in the response HTML');
            }
          }
          break;
      }
    }
  } catch (error) {
    console.error('Payment error:', error);
  } finally {
    loading.value = false;
  }
};

const clearTimers = () => {
  if (intervalId.value) {
    clearInterval(intervalId.value);
    intervalId.value = null;
  }
};



const fetchPayList = async () => {
  const data = await getPayList();
  if (data != null && data.length > 0) {
    paymentOptions.value = data;
    form.value.payType = paymentOptions.value[0].paymentsType;
    enableH5.value = paymentOptions.value[0].enableH5;
  }
};

const getPayStatus = async (tradeNo, time) => {
  if (intervalId.value) {
    clearTimers();
  }

  intervalId.value = setInterval(async () => {
    try {
      let paymentStatus;

      switch (form.value.payType) {
        case 1:
        case 2:
          paymentStatus = await queryEpayOrderStatus({ tradeNo });
          break;
        case 3:
          paymentStatus = await queryF2FOrderStatus({ tradeNo });
          break;
        case 4:
        case 8:
          paymentStatus = await queryXhOrderStatus({ tradeNo, payMethod: form.value.payType });
          break;
        case 5:
          paymentStatus = await queryLanTuPayStatus({ tradeNo });
          break;
        case 7:
          paymentStatus = await queryWxNativeStatus({ tradeNo });
          break;
      }

      if (paymentStatus === 'TRADE_SUCCESS' || paymentStatus === 'SUCCESS') {
        ElNotification.success(t('paymentSuccess'));
        closeDialog();
      }
    } catch (error) {
      console.error(error);
      clearTimers();
    }
  }, time);
};
const loginConfirm = () => {
  ElMessageBox.confirm(t('auth.loginPrompt'), t('auth.notLoggedIn'), {
    confirmButtonText: t('common.login'),
    cancelButtonText: t('common.register'),
    type: 'warning',
  })
    .then(() => {
      router.push('/login');
    })
    .catch(() => {
      router.push('/register');
    });
};
// 处理支付方式选择
const handleRadioChange = (option) => {
  form.value.payType = option.paymentsType;
  enableH5.value = option.enableH5;
};

// 打开优惠券对话框
const openCouponDialog = () => {
  couponVisible.value = true;
};

// 处理优惠券关闭
const handleCouponClose = () => {
  couponVisible.value = false;
  couponData.value = '';
};

// 处理优惠券变化
const handleCouponChange = async () => {
  if (couponData.value) {
    form.value.coupon = couponData.value
    try {
      const params = {
        planId: form.value.planId,
        coupon: couponData.value,
        originalMoney: form.value.originalMoney,
      };
      const money = await api.calcOfferAmount(params);
      if (money) {
        form.value.money = money;
        couponVisible.value = false;
      }
    } catch (error) {
      console.error('Coupon validation error:', error);
      form.value.money = form.value.originalMoney;
    }
  }
};



const goPay = async () => {
  if (!isLogin.value) {
    loginConfirm()
    return
  }
  try {
    buttonLoading.value = true;
    if (!form.value.payType) {
      ElNotification.error(t('shop.errors.noPayment'));
      return;
    }

    jumpUrl.value = '';
    qrCodeData.value = null;
    await fetchQrcode();

    if (form.value.payType === 6) {
      window.open(jumpUrl.value);
    } else {
      if (vPayFlag.value) {
        window.location.href = jumpUrl.value;
      } else if (qrCodeData.value) {
        if (form.value.payType === 4 || form.value.payType === 8) {
          qrcodeHpShow.value = true;
        } else {
          qrcodeShow.value = true;
        }
        getPayStatus(tradeNo.value, 2000);
      }
    }
  } catch (error) {
    console.error('Payment error:', error);
  } finally {
    buttonLoading.value = false;
  }
};
</script>

<style scoped>
/* 功能特性展开/折叠动画 */
.feature-enter-active,
.feature-leave-active {
  transition: all 0.3s ease;
}

.feature-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.feature-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.feature-move {
  transition: transform 0.3s ease;
}

.feature-item {
  transition: all 0.3s ease;
}

/* 更多功能按钮悬停效果 */
.feature-toggle-btn {
  transition: all 0.2s ease;
}

.feature-toggle-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .feature-toggle-btn:hover {
  background-color: rgba(255, 255, 255, 0.05);
}
</style>

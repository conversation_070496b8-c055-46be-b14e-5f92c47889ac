<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.seven.share.mapper.SysTenantMapper">

    <!-- resultMap映射 -->
    <resultMap id="BaseResultMap" type="org.seven.share.common.pojo.entity.SysTenant">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tenant_id" property="tenantId" jdbcType="INTEGER"/>
        <result column="contact_user_name" property="contactUserName" jdbcType="VARCHAR"/>
        <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="domain" property="domain" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="package_id" property="packageId" jdbcType="INTEGER"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="create_id" property="createId" jdbcType="BIGINT"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_id" property="updateId" jdbcType="BIGINT"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
        <result column="delete_time" property="deleteTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- SQL片段，列出表的所有基础字段 -->
    <sql id="baseColumns">
        t.id,
        t.tenant_id,
        t.contact_user_name,
        t.contact_phone,
        t.tenant_name,
        t.domain,
        t.remark,
        t.package_id,
        t.expire_time,
        t.status,
        t.create_id,
        t.update_id,
        t.create_time,
        t.update_time,
        t.is_deleted,
        t.delete_time
    </sql>


</mapper>

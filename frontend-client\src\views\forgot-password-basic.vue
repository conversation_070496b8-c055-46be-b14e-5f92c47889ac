<template>
  <div class="min-h-screen flex items-center justify-center bg-white">
    <div class="w-full max-w-sm p-8">
      <!-- Logo -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-center text-gray-800 mb-2">
          Reset your password
        </h1>
        <h4>通过电子邮件地址验证、重置您的密码。</h4>
      </div>

      <!-- Login Form -->
      <form @submit.prevent="handleSubmit">
        <!-- Email Field -->
        <div class="custom-form-item">
          <div class="custom-input-container">
            <input
              v-model="form.username"
              required
              class="custom-input"
              :class="{ 'has-content': form.username }"
              @focus="setFocus('username', true)"
              @blur="setFocus('username', false)"
            />
            <label :class="{ 'is-focused': usernameFocused || form.username }"
              >用户名</label
            >
          </div>
        </div>
        <div class="custom-form-item">
          <div class="custom-input-container">
            <input
              v-model="form.email"
              required
              type="email"
              class="custom-input"
              :class="{ 'has-content': form.username }"
              @focus="setFocus('email', true)"
              @blur="setFocus('email', false)"
            />
            <label :class="{ 'is-focused': emailFocused || form.email }"
              >邮箱地址</label
            >
          </div>
        </div>
        <!-- Password Field -->
        <div class="custom-form-item">
          <div class="custom-input-container">
            <input
              v-model="form.newPassword"
              required
              autocomplete
              :type="showPassword ? 'text' : 'password'"
              class="custom-input"
              :class="{ 'has-content': form.newPassword }"
              @focus="setFocus('newPassword', true)"
              @blur="setFocus('newPassword', false)"
            />
            <label
              :class="{ 'is-focused': newPasswordFocused || form.newPassword }"
              >新的密码</label
            >
            <el-icon
              class="password-icon"
              @click="showPassword = !showPassword"
            >
              <View v-if="showPassword" />
              <Hide v-else />
            </el-icon>
          </div>
        </div>
        <div class="custom-form-item">
          <div class="custom-input-container">
            <input
              v-model="form.confirmPassword"
              required
              autocomplete
              :type="showPassword ? 'text' : 'password'"
              class="custom-input"
              :class="{ 'has-content': form.confirmPassword }"
              @focus="setFocus('confirmPassword', true)"
              @blur="setFocus('confirmPassword', false)"
            />
            <label
              :class="{
                'is-focused': confirmPasswordFocused || form.confirmPassword,
              }"
              >确认密码</label
            >
            <el-icon
              class="password-icon"
              @click="showPassword = !showPassword"
            >
              <View v-if="showPassword" />
              <Hide v-else />
            </el-icon>
          </div>
        </div>
        <div class="custom-form-item">
          <div class="custom-input-container flex">
            <input
              v-model="form.code"
              required
              autocomplete
              class="custom-input"
              :class="{ 'has-content': form.code }"
              @focus="setFocus('code', true)"
              @blur="setFocus('code', false)"
            />
            <label
              :class="{ 'is-focused': verificationCodeFocused || form.code }"
              >验证码</label
            >
            <button
              type="button"
              class="absolute right-1 top-1/2 -translate-y-1/2 px-4 py-1.5 bg-transparent text-emerald-500 focus:outline-non"
              @click="sendVerificationCode"
            >
              {{ isGettingCode ? `${countdown}s` : '发送' }}
            </button>
          </div>
        </div>
        <!-- Forgot Password Link -->
        <div class="flex justify-center mb-6">
          <el-link type="primary" @click="goLogin" :underline="false"
            >已有账号? <span class="ml-1 text-emerald-600">登录</span></el-link
          >
        </div>

        <!-- Login Button -->
        <button
          type="submit"
          :disabled="loading"
          class="w-full mb-6 py-3 bg-black dark:bg-white text-white dark:text-black rounded-lg font-medium hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 disabled:opacity-50"
        >
          {{ loading ? '处理中...' : '确定' }}
        </button>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { View, Hide } from '@element-plus/icons-vue';
import Cookies from 'js-cookie';
import { ElNotification } from 'element-plus';
import * as api from '@/api/user.js';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { encrypt } from '@/utils/encrypt';

const { t } = useI18n();

const router = useRouter(); // 使用vue-router
const form = reactive({
  username: '',
  email: '',
  code: '',
  newPassword: '',
  confirmPassword: '',
});

const loading = ref(false);
const showPassword = ref(false);
const emailFocused = ref(false);
const usernameFocused = ref(false);
const newPasswordFocused = ref(false);
const confirmPasswordFocused = ref(false);
const verificationCodeFocused = ref(false);

const setFocus = (field, value) => {
  if (field === 'username') {
    usernameFocused.value = value;
  } else if (field === 'email') {
    emailFocused.value = value;
  } else if (field === 'newPassword') {
    newPasswordFocused.value = value;
  } else if (field === 'confirmPassword') {
    confirmPasswordFocused.value = value;
  } else if (field === 'code') {
    verificationCodeFocused.value = value;
  }
};

const handleSubmit = async () => {
  // 校验密码和确认密码是否一致
  if (form.newPassword !== form.confirmPassword) {
    ElNotification.error('密码与确认密码不一致');
    return; // 阻止表单提交
  }
  try {
    loading.value = true;
    // 实际登录逻辑
    const res = await api.resetPassword(form);
    ElNotification({
      message: t('resetPassword.errors.passwordResetSuccess'),
      type: 'success',
    });
    // 将用户名和密码更新到cookie中
    Cookies.set('username', form.username, { expires: 30 });
    Cookies.set('password', encrypt(form.newPassword), {
      expires: 30,
      secure: true,
    });
    router.push('/login');
  } catch (error) {
    console.error('重置密码失败:', error);
  } finally {
    loading.value = false;
  }
};
const goLogin = () => {
  router.push('/login');
};

const isGettingCode = ref(false);
const countdown = ref(60);

const sendVerificationCode = async () => {
  if (isGettingCode.value) return;

  // 验证邮箱
  if (!form.email) {
    ElNotification.error(t('resetPassword.errors.emailMissing'));
    return;
  }

  // 在这里添加发送验证码的逻辑
  const param = {
    username: form.username,
    email: form.email,
  };
  await api.getResetPasswordCode(param);
  startCountDown();
  ElNotification.success(t('resetPassword.errors.codeSent'));
};

const startCountDown = () => {
  // 设置倒计时的持续时间（秒）
  const countdownDuration = 60;

  // 记录倒计时的结束时间
  const endTime = Date.now() + countdownDuration * 1000;
  localStorage.setItem('countdownEndTime', endTime);

  isGettingCode.value = true;

  // 开始倒计时
  const updateCountdown = () => {
    const remainingTime = Math.max(
      0,
      Math.floor((endTime - Date.now()) / 1000)
    );
    countdown.value = remainingTime;

    if (remainingTime <= 0) {
      clearInterval(timer);
      isGettingCode.value = false;
      localStorage.removeItem('countdownEndTime'); // 清除存储的倒计时
    }
  };

  const timer = setInterval(updateCountdown, 1000);

  // 初始化时更新一次倒计时
  updateCountdown();
};

// 页面加载时检查是否有未完成的倒计时
const savedEndTime = localStorage.getItem('countdownEndTime');
if (savedEndTime) {
  const remainingTime = Math.max(
    0,
    Math.floor((savedEndTime - Date.now()) / 1000)
  );
  if (remainingTime > 0) {
    isGettingCode.value = true;
    countdown.value = remainingTime;

    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
        isGettingCode.value = false;
        localStorage.removeItem('countdownEndTime');
      }
    }, 1000);
  } else {
    localStorage.removeItem('countdownEndTime');
  }
}
</script>

<style scoped>
.custom-form-item {
  margin-bottom: 20px;
}

.custom-input-container {
  position: relative;
}

.custom-input {
  width: 100%;
  height: 56px;
  border: 1px solid #dcdfe6;
  border-radius: 7px;
  padding: 0 15px;
  font-size: 14px;
  transition: all 0.3s;
}

.custom-input:focus {
  border-color: #4caf50;
  outline: none;
}

.custom-input-container label {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  background-color: white;
  padding: 0 5px;
  color: #909399;
  font-size: 14px;
  transition: all 0.3s;
  pointer-events: none;
}

.custom-input-container label.is-focused,
.custom-input.has-content + label {
  top: 0;
  font-size: 12px;
  color: #10a37f;
}

.password-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #909399;
}

:deep(.el-button--primary:not(.is-disabled)) {
  background-color: #10a37f;
  border-color: #10a37f;
}

:deep(.el-button--primary:not(.is-disabled):hover) {
  background-color: #0e9272;
  border-color: #0e9272;
}

:deep(.el-link.el-link--primary) {
  color: #000000;
}

:deep(.el-link.el-link--primary:hover) {
  color: #333333;
}
</style>

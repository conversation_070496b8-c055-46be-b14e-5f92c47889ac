<template>
  <el-dialog 
    v-model="dialogVisible" 
    title="通知" 
    :width="dialogWidth" 
    :before-close="handleClose"
    class="dark-theme-dialog"
  >
    <div class="overflow-y-auto" style="max-height: calc(80vh);">
      <div class="notice-content">
        <div v-html="fetchedNoticeContent"></div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <button
          class="px-4 py-2 bg-black dark:bg-white text-white dark:text-black rounded-lg font-medium hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200"
          @click="handleClose"
        >已读</button>
      </span>
    </template>
  </el-dialog>
</template>
  
<script setup>
import { ref, watch, computed, onMounted, onUnmounted } from 'vue'
import { getLatestNotice } from '@/api/notice.js'

const fetchedNoticeContent = ref('')
const props = defineProps({
  visible: Boolean
})

const emit = defineEmits(['update:visible'])

const dialogVisible = ref(props.visible)
const windowWidth = ref(window.innerWidth)

const dialogWidth = computed(() => {
  if (windowWidth.value < 768) return '90%'
  if (windowWidth.value < 1024) return '70%'
  return '50%'
})

const handleClose = () => {
  emit('update:visible', false)
  dialogVisible.value = false
  closeNotice()
}

const getNoticeData = async () => {
  try {
    const data = await getLatestNotice()
    fetchedNoticeContent.value = data.content || ''
  } catch (error) {
    console.error('获取通知失败:', error)
    fetchedNoticeContent.value = ''
  }
}

const handleResize = () => {
  windowWidth.value = window.innerWidth
}

onMounted(async() => {
  window.addEventListener('resize', handleResize)
  await getNoticeData()
  checkNotice()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

watch(() => props.visible, (newValue) => {
  dialogVisible.value = newValue
})

const checkNotice = () => {
  // 检查通知内容是否为空
  if (!fetchedNoticeContent.value || fetchedNoticeContent.value.trim() === '') {
    dialogVisible.value = false
    return
  }

  const storedNoticeContent = localStorage.getItem('noticeContent')
  if (storedNoticeContent !== fetchedNoticeContent.value) {
    dialogVisible.value = true
  }
}

const closeNotice = () => {
  localStorage.setItem('noticeContent', fetchedNoticeContent.value)
}
</script>

<style scoped>
.dark-theme-dialog :deep(.el-dialog) {
  background-color: #1e2430;
  border: 1px solid #2d3440;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark-theme-dialog :deep(.el-dialog__title) {
  color: #ffffff;
  font-size: 1.2rem;
}

.dark-theme-dialog :deep(.el-dialog__header) {
  border-bottom: 1px solid #2d3440;
  padding: 20px;
  margin-right: 0;
}

.dark-theme-dialog :deep(.el-dialog__body) {
  color: #e5eaf3;
  padding: 24px;
}

.dark-theme-dialog :deep(.el-dialog__footer) {
  border-top: 1px solid #2d3440;
  padding: 16px 20px;
}

.notice-content {
  line-height: 1.6;
}



.dark-theme-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: #e5eaf3;
}

.dark-theme-dialog :deep(.el-dialog__headerbtn:hover .el-dialog__close) {
  color: #ffffff;
}
</style>
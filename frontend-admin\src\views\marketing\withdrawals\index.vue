<script setup lang="tsx">
import { computed, ref } from 'vue';
import { ElButton, ElImage, ElMessage, ElMessageBox, ElTable, ElTableColumn, ElTag } from 'element-plus';
import {
  fetchApproval,
  fetchWithdrawPage,
  getInviteDetails,
  rejectApproval,
  removeWithdrawBatchByIds
} from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import { usePermission } from '@/hooks/business/auth'
import WithdrawalsSearch from './modules/withdrawals-search.vue';
const { hasPermission } = usePermission();

// Define the proper type for your table data
interface Withdrawal {
  id: string;
  username: string;
  withdrawalMoney: string;
  contact: string;
  withdrawalTime: string;
  withdrawalQrcode: string;
  status: number;
  remark: string;
  userId: number;
}

const downloadUrl = '/client-api/common/download';
const wrapperRef = ref<HTMLElement | null>(null);
const dialogVisible = ref(false);
const currentDetails = ref<Withdrawal | null>(null);
const inviteDetails = ref<any[]>([]);
const isMobile = computed(() => window.innerWidth < 768);
const dialogWidth = computed(() => (isMobile.value ? '90%' : '50%'));

const { columns, columnChecks, data, loading, pagination, getData, searchParams, getDataByPage, resetSearchParams } =
  useTable({
    apiFn: fetchWithdrawPage,
    columns: () => [
      { type: 'selection', width: 48 },
      { prop: 'index', label: $t('common.index'), width: 64 },
      { prop: 'username', label: '提现人', minWidth: 100, align: 'center', showOverflowTooltip: true },
      { prop: 'withdrawalMoney', label: '提现金额', minWidth: 100, align: 'center' },
      { prop: 'contact', label: '联系方式', minWidth: 150, align: 'center' },
      { prop: 'withdrawalTime', label: '提现申请时间', minWidth: 180, align: 'center', sortable: true },
      {
        prop: 'withdrawalQrcode',
        label: '付款码',
        width: 200,
        align: 'center',
        formatter: (row: any) => (
          <ElImage
            style={{ width: '100px', height: '100px' }}
            src={`${downloadUrl}?name=${row.withdrawalQrcode}`}
            preview-src-list={[`${downloadUrl}?name=${row.withdrawalQrcode}`]}
            zoom-rate={1.2}
            max-scale={7}
            min-scale={0.2}
            preview-teleported={true}
            fit="cover"
          />
        )
      },
      {
        prop: 'status',
        label: '审核状态',
        width: 120,
        align: 'center',
        sortable: true,
        formatter: (row: any) => {
          if (row.status === 1) {
            return <ElTag type="success">已审批</ElTag>;
          } else if (row.status === 0) {
            return <ElTag type="primary">待审批</ElTag>;
          }
          return <ElTag type="warning">已驳回</ElTag>;
        }
      },
      { prop: 'remark', label: '审批结果', width: 240, align: 'center', showOverflowTooltip: true },
      { prop: 'createTime', label: '创建时间', width: 180, align: 'center', sortable: true },
      { prop: 'updateTime', label: '更新时间', width: 180, align: 'center', sortable: true },
      {
        prop: 'operate',
        label: $t('common.operate'),
        align: 'center',
        fixed: 'right',
        width: 210,
        formatter: (row: any) => (
          <div class="flex-center">
            {hasPermission('withdrawals/getInviteDetails') && (
              <ElButton size="small" plain type="primary" onClick={() => handleDetails(row)}>
                详情
              </ElButton>
            )}
            <ElButton
              size="small"
              plain
              type="success"
              disabled={row.status !== 0}
              onClick={() => handleSuccessPay(row)}
            >
              审批
            </ElButton>
            <ElButton size="small" plain type="danger" disabled={row.status !== 0} onClick={() => handleReject(row)}>
              驳回
            </ElButton>
          </div>
        )
      }
    ]
  });

const { checkedRowKeys, onBatchDeleted } = useTableOperate(data as any, getData);

async function handleBatchDelete() {
  if (checkedRowKeys.value.length < 1) {
    ElMessage({
      message: '请先勾选数据',
      type: 'warning'
    });
    return;
  }

  try {
    await ElMessageBox.confirm('是否进行批量删除， 继续?', '注意', {
      confirmButtonText: '确 定',
      cancelButtonText: '取 消',
      type: 'warning'
    });

    await removeWithdrawBatchByIds(checkedRowKeys.value);
    onBatchDeleted();

    ElMessage({
      message: '删除成功',
      type: 'success'
    });
  } catch {
    // 取消删除或发生错误
  }
}

async function handleReject(row: Withdrawal) {
  if (row.status === 0) {
    ElMessageBox.prompt('请输入驳回原因', '驳回审批', {
      confirmButtonText: '确认',
      cancelButtonText: '取消'
    })
      .then(async ({ value }) => {
        try {
          await rejectApproval({ id: row.id, reason: value });
          ElMessage({
            message: '驳回成功',
            type: 'success'
          });
          await getData();
        } catch {
          ElMessage.error('驳回失败');
        }
      })
      .catch(() => {
        // 已取消驳回
      });
  }
}

async function handleSuccessPay(row: Withdrawal) {
  if (row.status === 1) {
    ElMessage.warning('当前订单状态已支付，无需设置');
    return;
  }

  try {
    await ElMessageBox.confirm('请确认是否已经完成付款码扫码转账操作?', '注意', {
      confirmButtonText: '确 定',
      cancelButtonText: '取 消',
      type: 'warning'
    });

    await fetchApproval({ id: row.id });
    ElMessage({
      message: '处理返佣成功',
      type: 'success'
    });

    await getData();
  } catch {
    // 取消操作或发生错误
  }
}

async function handleDetails(row: Withdrawal) {
  currentDetails.value = row;
  dialogVisible.value = true;

  try {
    const response = await getInviteDetails({ id: row.userId });
    // Ensure the response data is properly handled
    inviteDetails.value = response.data.records || [];
  } catch {}
}
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <WithdrawalsSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            :show-delete="hasPermission('withdrawals/delete')"
            @delete="handleBatchDelete"
            @refresh="getData"
          />
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="(selection: Withdrawal[]) => checkedRowKeys = selection.map(item => String(item.id))"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
    </ElCard>

    <ElDialog v-model="dialogVisible" title="邀请详情" :width="dialogWidth">
      <template v-if="currentDetails">
        <div class="mt-4">
          <ElTable :data="inviteDetails" border>
            <ElTableColumn type="index" label="序号" width="60" align="center" />
            <ElTableColumn prop="userToken" label="用户名" align="center" />
            <ElTableColumn prop="createTime" label="交易时间" align="center" width="180" />
            <ElTableColumn prop="affMoney" label="金额" align="center" />
            <ElTableColumn prop="orderType" label="订单类型" align="center">
              <template #default="scope">
                <ElTag v-if="scope.row.orderType === 1" type="success">在线充值</ElTag>
                <ElTag v-else type="primary">卡密兑换</ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="status" label="状态" align="center" width="120">
              <template #default="scope">
                <ElTag v-if="scope.row.status === 0" type="primary">未处理</ElTag>
                <ElTag v-else type="success">已处理</ElTag>
              </template>
            </ElTableColumn>
          </ElTable>
        </div>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <ElButton type="primary" @click="dialogVisible = false">关闭</ElButton>
        </span>
      </template>
    </ElDialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}

.demo-image__error .image-slot {
  font-size: 30px;
}

.demo-image__error .image-slot .el-icon {
  font-size: 30px;
}

.demo-image__error .el-image {
  width: 100%;
  height: 200px;
}
</style>

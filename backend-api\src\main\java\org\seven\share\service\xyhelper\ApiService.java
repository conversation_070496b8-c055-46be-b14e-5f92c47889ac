package org.seven.share.service.xyhelper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.pojo.entity.ChatGptSessionEntity;
import org.seven.share.common.util.IpUtils;
import org.seven.share.service.ChatGptConfigService;
import org.seven.share.service.ChatGptSessionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static org.seven.share.common.util.ConstantUtil.*;

/**
 * @ClassName: ApiService
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/26
 */
@Slf4j
@Service
public class ApiService {

    private final static String GPT_SESSION_SAVE_ENDPOINT = "/adminapi/chatgpt/session/add";
    private final static String GPT_SESSION_UPDATE_ENDPOINT = "/adminapi/chatgpt/session/update";
    private final static String GPT_SESSION_DELETE_ENDPOINT = "/adminapi/chatgpt/session/delete";

    private final static String CLAUDE_SESSION_SAVE_ENDPOINT = "/adminapi/claude/session/add";
    private final static String CLAUDE_SESSION_UPDATE_ENDPOINT = "/adminapi/claude/session/update";
    private final static String CLAUDE_SESSION_DELETE_ENDPOINT = "/adminapi/claude/session/delete";

    private final static String GROK_SESSION_SAVE_ENDPOINT = "/adminapi/grok/session/add";
    private final static String GROK_SESSION_UPDATE_ENDPOINT = "/adminapi/grok/session/update";
    private final static String GROK_SESSION_DELETE_ENDPOINT = "/adminapi/grok/session/delete";

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private HttpServletRequest request;

    @Resource
    private ObjectMapper objectMapper;

    @Value("${api-auth}")
    private String apiAuth;

    @Resource
    private ChatGptSessionService chatGptSessionService;

    @Autowired
    @Qualifier("commonAsyncExecutor")
    private Executor commonAsyncExecutor;

    @Resource
    private ChatGptConfigService configService;

    public <T> void sendSaveOrUpdateRequest(T entity, String flag, String businessType){
        log.info("开始获取accessToken，更新标准：{}，业务类型：{}", flag, businessType);
        String domain = getDomain(businessType);
        String url = domain + getUrlByOperatorType(flag, businessType);

        // 创建 HTTP 头信息
        HttpHeaders headers = getHttpHeaders();
        // 将头信息和请求体封装成 HttpEntity 对象
        HttpEntity<T> requestEntity = new HttpEntity<>(entity, headers);
        log.info("请求的url:{}", url);
        log.info("请求参数：{}", requestEntity);
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        // 使用 RestTemplate 发送 POST 请求
        log.info("请求响应结果：{}", response);
        // 根据响应状态码判断请求是否成功
        int code = dealResponse(response);
        if (code != 1000) {
            log.warn("更新请求出错了，状态码为：{}，错误信息为：{}", code, response.getBody());
        }else{
            if ("gpt".equals(businessType)) {
                asyncUpdateSessionType();
            }
            log.info("处理完成");
        }
    }

    /**
     * 异步更新gpt session的订阅类型
     */
    private void asyncUpdateSessionType() {
        log.info("异步更新gpt plan 类型");
        List<ChatGptSessionEntity> carList = chatGptSessionService.list(new LambdaQueryWrapper<ChatGptSessionEntity>()
                .eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS)
                .eq(ChatGptSessionEntity::getIsPlus, PLAN_TYPE_PLUS)
                .isNull(ChatGptSessionEntity::getDeletedAt));
        List<ChatGptSessionEntity> todoList = carList.stream()
                .peek(e -> {
                    if (e.getOfficialSession().contains("\"plan_type\":\"team\"")) {
                        e.setIsPlus(2);
                    } else if (e.getOfficialSession().contains("\"plan_type\":\"pro\"")) {
                        e.setIsPlus(3);
                    }
                }).toList();
        // 执行批量更新
        if (CollectionUtils.isNotEmpty(todoList)) {
            log.info("有{}条待更新plan类型的gpt数据", todoList.size());
            CompletableFuture.runAsync(() -> chatGptSessionService.updateBatchById(todoList), commonAsyncExecutor);
        }
        log.info("异步更新gpt plan 类型完成");
    }

    private int dealResponse(ResponseEntity<String> response) {
        if (response.getStatusCode().is2xxSuccessful()) {
            try {
                String body = response.getBody();
                log.info("请求成功，响应体为：{}", body);
                JsonNode jsonNode = objectMapper.readTree(body);
                int code = jsonNode.get("code").asInt();
                log.info("解析后的数据:{}", jsonNode);
                return code;

            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
        return 0;
    }

    private HttpHeaders getHttpHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set(API_AUTH_HEADER, apiAuth);
        headers.setContentType(MediaType.APPLICATION_JSON); // 设置请求体的内容类型为 JSON
        return headers;
    }

    public void sendDeleteRequest(List<String> ids, String businessType){
        String domain = getDomain(businessType);
        String url = domain + getUrlByOperatorType("delete", businessType);
        // 创建 HTTP 头信息
        HttpHeaders headers = getHttpHeaders();
        // 将头信息和请求体封装成 HttpEntity 对象
        Map<String, Object> map = new HashMap<>();
        map.put("ids", ids);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(map, headers);
        // 使用 RestTemplate 发送 POST 请求
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
            int code = dealResponse(response);
            if (code != 1000) {
                log.warn("删除请求出错了，状态码为：{}，错误信息为：{}", code, response.getBody());
            }else{
                log.info("处理完成");
            }
        } catch (Exception e) {
            // 处理异常
            log.error("删除失败:", e);
            throw new CustomException("删除失败");
        }
        ResponseEntity.badRequest().build();
    }

    /**
     *
     * @param operatorType
     * @param businessType
     * @return
     */
    private String getUrlByOperatorType(String operatorType, String businessType) {
        return switch (operatorType) {
            case "add" -> switch (businessType) {
                case "gpt" -> GPT_SESSION_SAVE_ENDPOINT;
                case "claude" -> CLAUDE_SESSION_SAVE_ENDPOINT;
                case "grok" -> GROK_SESSION_SAVE_ENDPOINT;
                default -> "";
            };
            case "update" -> switch (businessType) {
                case "gpt" -> GPT_SESSION_UPDATE_ENDPOINT;
                case "claude" -> CLAUDE_SESSION_UPDATE_ENDPOINT;
                case "grok" -> GROK_SESSION_UPDATE_ENDPOINT;
                default -> "";
            };
            case "delete" -> switch (businessType) {
                case "gpt" -> GPT_SESSION_DELETE_ENDPOINT;
                case "claude" -> CLAUDE_SESSION_DELETE_ENDPOINT;
                case "grok" -> GROK_SESSION_DELETE_ENDPOINT;
                default -> "";
            };
            default -> "";
        };
    }

    private String getDomain(String businessType) {
        return switch (businessType) {
            case "gpt" -> IpUtils.getRefererDomain(request);
            case "claude" -> configService.getValueByKey("sxClaudeUrl");
            case "grok" -> configService.getValueByKey("grokUrl");
            default -> throw new CustomException("不支持的业务类型:" + businessType);
        };
    }

}

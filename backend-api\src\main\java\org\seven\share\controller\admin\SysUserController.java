package org.seven.share.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.Result;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.vo.SysUserVO;
import org.seven.share.param.CreateUserParam;
import org.seven.share.service.SysUserService;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "SysUserController", description = "用户信息控制层")
//@RequestMapping("/sys/user")
@RequestMapping("/expander-api/systemManage")
public class SysUserController {

    private final SysUserService sysUserService;

    @Operation(summary = "list 分页列表")
    @Parameters({
            @Parameter(name = "current", description = "当前页", required = true, example = "1"),
            @Parameter(name = "size", description = "每页显示条数", required = true, example = "10"),
            @Parameter(name = "username", description = "用户名称"),
    })
    @GetMapping(value = "/getUserList")
    @SysLogInterface(title = "查询用户信息", businessType = BusinessType.OTHER)
    public Result<IPage<SysUserVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> params) {
        IPage<SysUserVO> sysUsers = sysUserService.getList(params);
        return Result.success(sysUsers);
    }

    @Operation(summary = "创建用户信息")
    @PostMapping(value = "/createUser")
    @SysLogInterface(title = "创建用户信息", businessType = BusinessType.INSERT)
    public Result<String> createUser(@RequestBody CreateUserParam createUserParam) {
        return sysUserService.createUser(createUserParam);
    }

    @Operation(summary = "修改用户信息")
    @PostMapping(value = "/update")
    @SysLogInterface(title = "修改用户信息", businessType = BusinessType.UPDATE)
    public Result<String> updateUser(@RequestBody CreateUserParam createUserParam) {
        return sysUserService.updateUser(createUserParam);
    }

    @Operation(summary = "删除用户信息")
    @PostMapping(value = "/delete")
    @SysLogInterface(title = "删除用户信息", businessType = BusinessType.DELETE)
    public Result<String> deleteSysUser(@RequestBody List<Long> ids) {
        sysUserService.deleteSysUserBatch(ids);
        return Result.success();
    }

}

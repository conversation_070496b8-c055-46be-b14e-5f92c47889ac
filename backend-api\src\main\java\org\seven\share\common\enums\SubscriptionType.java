package org.seven.share.common.enums;

import lombok.Getter;
@Getter
public enum SubscriptionType {
    GPT_ONLY("1", "gpt_only"),
    CLAUDE_ONLY("2", "claude_only"),
    GPT_AND_CLAUDE("3", "gpt_and_claude"),
    GROK_ONLY("4", "grok_only"),
    GPT_AND_GROK("5", "gpt_and_grok"),
    CLAUDE_AND_GROK("6", "claude_and_grok"),
    GPT_CLAUDE_AND_GROK("7", "gpt_claude_grok"),
    DRAW("8", "draw"),;

    private final String type;

    private final String description;

    SubscriptionType(String type, String description) {
        this.type = type;
        this.description = description;
    }

    // 根据String type查找枚举
    public static SubscriptionType fromType(String type) {
        if (type == null) {
            throw new IllegalArgumentException("套餐类型不能为空");
        }
        for (SubscriptionType sub : values()) {
            if (sub.getType().equals(type)) {
                return sub;
            }
        }
        throw new IllegalArgumentException("未知的套餐类型：" + type);
    }
}

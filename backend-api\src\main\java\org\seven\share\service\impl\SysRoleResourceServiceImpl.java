package org.seven.share.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.api.Result;
import org.seven.share.common.enums.DelStatusEnums;
import org.seven.share.common.pojo.entity.SysRoleResource;
import org.seven.share.mapper.CreateTableMapper;
import org.seven.share.mapper.SysRoleResourceMapper;
import org.seven.share.service.SysRoleResourceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SysRoleResourceServiceImpl extends ServiceImpl<SysRoleResourceMapper, SysRoleResource>
        implements SysRoleResourceService {

    @Resource
    private CreateTableMapper createTableMapper;

    @PostConstruct
    public void init() {
        createTableMapper.createSysRoleResourceTable();
        if (count() == 0) {
            createTableMapper.insertSysRoleResource();
        }
    }

    /**
     * 绑定角色基础资源信息
     *
     * @param roleId 角色Id
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> bindingRoleBasicResource(Long roleId, List<Long> longList) {

        log.info("绑定角色基础资源信息 -> 角色Id: {} , 资源信息: {}", roleId, JSONUtil.parse(longList));

        List<SysRoleResource> sysRoleResourceList = new ArrayList<>();

        longList.forEach(item -> {
            SysRoleResource sysRoleResource = new SysRoleResource();
            sysRoleResource.setRoleId(roleId);
            sysRoleResource.setResourceId(item);
            sysRoleResource.setIsDeleted(DelStatusEnums.DISABLE.getCode());
            sysRoleResourceList.add(sysRoleResource);
        });

        return Result.success(saveBatch(sysRoleResourceList));
    }

    @Override
    public Result<List<Long>> getRoleResourceId(Long roleId) {


        LambdaQueryWrapper<SysRoleResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysRoleResource::getRoleId, roleId);
        queryWrapper.eq(SysRoleResource::getIsDeleted, DelStatusEnums.DISABLE.getCode());

        List<SysRoleResource> roleResources = list(queryWrapper);

        List<Long> longList = new ArrayList<>();

        for (SysRoleResource roleResource : roleResources) {
            longList.add(roleResource.getResourceId());
        }

        return Result.success(longList);
    }

    @Override
    public Boolean deleteDataByRoleId(Long roleId) {

        log.info("删除角色下所有资源信息 -> 角色ID: {}", roleId);

        LambdaQueryWrapper<SysRoleResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysRoleResource::getRoleId, roleId);

        return remove(queryWrapper);
    }

    /**
     * 取消角色与资源的关联关系
     * @param sourceIds 资源id集合
     */
    @Override
    public void unbindingRoleResource(List<Long> sourceIds) {
        log.info("取消角色与资源的关联关系，:菜单信息 {}", JSONUtil.parse(sourceIds));
        LambdaQueryWrapper<SysRoleResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SysRoleResource::getResourceId, sourceIds);
        remove(wrapper);
    }

    /**
     * 批量删除角色资源数据
     * @param ids 角色id集合
     */
    @Override
    public void removeRoleResourceBatchByIds(List<Long> ids) {
        LambdaQueryWrapper<SysRoleResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SysRoleResource::getRoleId, ids);
        remove(wrapper);
    }
}

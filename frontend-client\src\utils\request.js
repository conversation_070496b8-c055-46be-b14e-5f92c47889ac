import axios from 'axios';
import { ElNotification } from 'element-plus';
import router from '../router';
import { getToken } from '@/utils/auth';

const request = axios.create({
    baseURL: import.meta.env.VITE_API_URL,
    timeout: 60000,
});

// 请求拦截器
request.interceptors.request.use(
    (config) => {
        config.headers['device-id'] = localStorage.getItem("device_id");
        if (getToken()) {
            config.headers.Authorization = `Bearer ${getToken()}`; // 添加 Bearer 前缀
        }
        return config;
    },
    (error) => {
        ElNotification({
            title: '请求错误',
            message: "请求配置错误",
            type: 'error',
            position: 'top-right',
            duration: 3000
        });
        return Promise.reject(error);
    }
);

// 响应拦截器
request.interceptors.response.use(
    (response) => {
        const res = response.data;
        if (response.config.responseType === 'blob') {
            return response;
        }
        // 处理字符串响应
        if (typeof res === 'string') {
            try {
                return JSON.parse(res);
            } catch (e) {
                return res;
            }
        }
        if (typeof res === 'number') {
            return res;
        }
        // 处理业务状态码
        if (res.code === 401) {
            localStorage.removeItem('accessToken');
            router.push('/401'); // 自动跳转到登录页面
            return Promise.reject(new Error('登录信息过期，请重新登录'));
        }
        // 其他业务状态码处理...
        if (res.code !== 200) {
            ElNotification({
                title: '操作失败',
                message: res.msg || '操作失败',
                type: 'error',
                position: 'top-right',
                duration: 3000
            });
            return Promise.reject(new Error(res.msg || '操作失败'));
        }
        return res.data;
    },
    (error) => {
        console.log('error:', error)
        let message = '系统异常';
        if (error.response) {
            console.log(error.response.status);
            switch (error.response.status) {
                case 403:
                    const errorData = error.response.data.error;
                    if (errorData.includes('授权失败')) {
                        message = errorData;
                    } else { 
                        message = '您没有权限访问该资源';
                        router.push('/403');
                        break;
                    }
                    break;
                case 404:
                    router.push('/404');
                    break;
                case 500:
                    message = '服务器内部错误';
                    break;
                default:
                    message =
                        error.response.data.msg || `连接错误${error.response.status}`;
            }
        } else if (error.request) {
            message = '网络异常，请检查您的网络连接';
        } else if (error.msg.includes('timeout')) {
            message = '请求超时，请稍后重试';
        }
        ElNotification({
            title: '请求失败',
            message: message,
            type: 'error',
            position: 'top-right',
            duration: 4000
        });
        return Promise.reject(error);
    }
);
// 下载文件的通用请求封装
export function downloadFile (url, params) {
    return request({
        url: url,
        method: 'GET', // 或者 POST, 取决于后端的接口
        params: params,
        responseType: 'blob', // 确保返回的是 Blob 数据
    })
        .then((response) => {
            // 从 Content-Disposition 中提取文件名
            const contentDisposition = response.headers['content-disposition'];
            const fileName = contentDisposition
                ? contentDisposition.split('filename=')[1].replace(/"/g, '')
                : 'download.txt';

            // 创建 Blob 对象
            const blob = new Blob([response.data], {
                type: 'application/octet-stream',
            });
            const url = window.URL.createObjectURL(blob);

            // 创建一个隐藏的下载链接
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', fileName); // 指定下载文件名
            document.body.appendChild(link);
            link.click();

            // 移除临时链接
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url); // 释放 URL 对象
        })
        .catch((error) => {
            console.error('文件下载失败：', error);
        });
}
export default request;

package org.seven.share.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.server.HttpServerRequest;
import com.auth0.jwt.interfaces.Claim;
import org.seven.share.common.exception.CustomException;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;

import static org.seven.share.common.util.JwtUtil.parseToken;

/**
 * @ClassName: HttpUtils
 * @Description:
 * @Author: Seven
 * @Date: 2024/12/22
 */
public class HttpUtils {
    public static String getCookieValue(HttpServletRequest request, String cookieName) {
        if (request.getCookies() != null) {
            Optional<Cookie> cookie = Arrays.stream(request.getCookies())
                    .filter(c -> c.getName().equals(cookieName))
                    .findFirst();
            return cookie.map(Cookie::getValue).orElse("");
        }
        return "";
    }
    public static String getToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        return token;
    }

    public static Long getUid(HttpServletRequest request){
        String token = getToken(request);
        if (StrUtil.isEmpty(token)) {
            throw new CustomException("角色认证失败：token 未携带");
        }
        Map<String, Claim> map = parseToken(token);
        return Optional.ofNullable(map.get("uid")).filter(claim -> !claim.isNull()).map(Claim::asLong).orElse(null);
    }

    public static String getBaseUrlFromRequest(HttpServletRequest request) {
        if (request == null) {
            return null;
        }

        String scheme = request.getScheme();
        String serverName = request.getServerName();
        int serverPort = request.getServerPort();

        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append(scheme).append("://").append(serverName);

        if ((scheme.equals("http") && serverPort != 80) ||
                (scheme.equals("https") && serverPort != 443)) {
            urlBuilder.append(":").append(serverPort);
        }

        return urlBuilder.toString();
    }

}

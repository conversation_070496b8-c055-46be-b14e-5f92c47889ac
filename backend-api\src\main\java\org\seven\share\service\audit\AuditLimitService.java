package org.seven.share.service.audit;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.ConsumptionProbe;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.pojo.dto.ChatGptSessionDto;
import org.seven.share.common.pojo.dto.ModelLimitDto;
import org.seven.share.common.pojo.entity.ChatGptSessionEntity;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.common.pojo.entity.RiskControlRecordEntity;
import org.seven.share.mapper.ChatGptSessionMapper;
import org.seven.share.service.ChatGptConfigService;
import org.seven.share.service.ChatGptSensitiveWordService;
import org.seven.share.service.ChatGptUserService;
import org.seven.share.service.RiskControlRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.seven.share.common.util.ConstantUtil.*;
import static org.seven.share.common.util.DateTimeUtil.formatDuration;
import static org.seven.share.common.util.HttpUtils.getCookieValue;
import static org.seven.share.common.util.HttpUtils.getToken;
import static org.seven.share.constant.CacheConstant.*;


/**
 * @ClassName: AuditLimitService
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/5
 */
@Service
@Slf4j
public class AuditLimitService {

    @Resource
    private ChatGptUserService chatGptUserService;

    @Resource
    private RateLimitPersistService rateLimitPersistService;

    @Resource
    private ChatGptSessionMapper chatGptSessionMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ChatGptSensitiveWordService chatGptSensitiveWordService;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private RiskControlRecordService riskControlRecordService;

    @Autowired
    @Qualifier("commonAsyncExecutor")
    private Executor commonAsyncExecutor;


    /**
     * 系统支持的模型
     */
    private final Set<String> SUPPORT_MODELS = Set.of("auto", "gpt-4o-mini","gpt-4",
            "gpt-4o",
            "gpt-4-browsing",
            "gpt-4-plugins",
            "gpt-4-mobile",
            "gpt-4-code-interpreter",
            "gpt-4-dalle",
            "gpt-4-gizmo",
            "gpt-4-magic-create",
            "gpt-4o-canmore",
            "o1",
            "o1-mini",
            "o1-pro",
            "o3-mini",
            "o3-mini-high",
            "r1-mini",
            "r1-fast",
            "r1-full",
            "qwq",
            "gpt-4-5"
    );

    /**
     * plus模型集合
     */
    private final Set<String> GPT4_MODELS= Set.of(
            "gpt-4o",
            "gpt-4-browsing",
            "gpt-4-plugins",
            "gpt-4-mobile",
            "gpt-4-code-interpreter",
            "gpt-4-dalle",
            "gpt-4-gizmo",
            "gpt-4-magic-create",
            "gpt-4o-canmore");
    private final Set<String> GPT_ADVANCE_MODELS = Set.of("o3", "o1-pro", "o4-mini", "o4-mini-high", "gpt-4-5");
    private final Set<String> THIRD_MODELS = Set.of("r1-mini", "r1-full", "qwq", "r1-fast", "third-models");
    private final Set<String> NORMAL_MODELS = Set.of("gpt-4o-mini", "auto");
    private int consumeTokens;

    private boolean checkAccountFlag = true;

    public ResponseEntity<?> gptConversationRateLimit(HttpServletRequest request, Map<String, Object> requestData, boolean flag) {
        checkAccountFlag = flag;
        // 获取 Bearer Token
        String token = getToken(request);
        log.info("token: {}", token);
        String currentSessionId = getCookieValue(request, "gfsessionid");
        log.info("currentSessionId: {}", currentSessionId);
        String carid = Optional.ofNullable(request.getHeader("carid"))
                .map(e -> new String(e.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8)).orElse(null);
        log.info("carid:{}",carid);
        String action = (String)requestData.get("action");
        log.info("action:{}",action);
        String model = (String)requestData.get("model");
        log.info("model:{}",model);
        // 获取插件模型数据
        List<String> systemHints = (List<String>)requestData.get("system_hints");
        log.info("systemHints:{}",systemHints);

        // 获取用户输入的对话内容
        List<Map<String, Object>> messages = (List<Map<String, Object>>) requestData.get("messages");

        // 新建对话风控记录
        RiskControlRecordEntity record = new RiskControlRecordEntity();
        String prompt = null;
        try{
            prompt = getPrompt(action, messages, record);
        }catch (Exception e){
            log.error("获取 prompt 失败，输入内容为：{}", messages);
        }

        // 敏感词命中检查
        ResponseEntity<Map<String, String>> sensitiveResponse = handleSensitivePrompt(token, prompt, record);
        if (sensitiveResponse != null) return sensitiveResponse;

        // 查询系统配置信息
        Map<String, String> configMap = chatGptConfigService.getKeyValueMapByKeys(List.of(
                "enableNoLogin","visitorLimit", "dialogModelMultiplier", "freeNodes", "visitorUsagePeriod",
                "enableSSO", "maxDevices", "expireTime", "thirdModelLimits"));

        String enableSSO = Optional.ofNullable(configMap.get("enableSSO"))
                .filter(e -> StrUtil.isNotEmpty(e) && !"null".equals(e))
                .orElse("false");
        String maxDevices = Optional.ofNullable(configMap.get("maxDevices"))
                .filter(e -> StrUtil.isNotEmpty(e) && !"null".equals(e))
                .orElse("0");
        String expireTime = Optional.ofNullable(configMap.get("expireTime"))
                .filter(e -> StrUtil.isNotEmpty(e) && !"null".equals(e))
                .orElse("21600");

        // 检查是否多设备登录
        if ("true".equals(enableSSO) && Integer.parseInt(maxDevices) > 0) {
            ResponseEntity<Map<String, String>> response = checkMultiLogin(token, currentSessionId, maxDevices, expireTime);
            if (response != null) {
                return response;
            }
        }
        // 查询用户模型倍率map
        Map<String, Integer> modelMultipMap = getModelMultiplierMap(configMap.get("dialogModelMultiplier"));

        // 获得消耗tokens
        consumeTokens = getConsumeTokens(model, modelMultipMap);

        // 查询用户信息
        ChatGptUserEntity user = chatGptUserService.getUserInfoByUsername(token);

        if (ObjectUtil.isEmpty(user)) {
            // 处理游客速率
            return handleGuestUser(configMap, model, token);
        } else {
            // 处理用户账号被封禁
            if (user.getStatus() == 0){
                return blockedAccountResponse();
            } else {
                // 未禁用用户，处理deep research模型速率
                if (!CollectionUtils.isEmpty(systemHints) && Objects.equals("research", systemHints.get(0))) {
                    return handleSystemHint(systemHints.get(0), user, carid);
                } else {
                    return handleRegisteredUser(user, model, carid, token, configMap);
                }
            }
        }
    }

    private ResponseEntity<?> handleSystemHint(String systemHint, ChatGptUserEntity user, String carid) {
        log.info("deal systemHint limit");
        Map<String, ModelLimitDto> modelLimits = user.getModelLimits();
        ModelLimitDto modelLimitDto = modelLimits.get(systemHint);
        Integer limit = modelLimitDto.getLimit();
        String per = modelLimitDto.getPer();
        return handleAdvanceModelLimit(systemHint, carid, user.getUserToken(), user.getPlusExpireTime(), limit, per);
    }

    private ResponseEntity<Map<String, String>> checkMultiLogin(String token, String currentSessionId, String maxDevices, String expireTime) {
        String key = USER_SESSIONS + token;
        ZSetOperations<String, String> zSetOperations = stringRedisTemplate.opsForZSet();
        Set<String> members = zSetOperations.range(key, 0, -1);
        if (members == null) {
            members = new HashSet<>();
        }

        // 如果当前会话已存在，更新时间戳并返回成功
        if (members.contains(currentSessionId)) {
            zSetOperations.add(key, currentSessionId, System.currentTimeMillis());
            return null;
        }

        int maxDevicesLimit = Integer.parseInt(maxDevices);
        // 当设备数达到上限时
        if (members.size() >= maxDevicesLimit) {
            log.warn("当前登录设备数已达到上限[{}]，正在挤掉其他设备", maxDevicesLimit);
            // 返回错误信息，让前端进行重试
            zSetOperations.removeRange(key, 0, 0);
            Map<String, String> errorMap = new HashMap<>();
            errorMap.put("error", "当前登录设备数已达到上限:" + maxDevicesLimit + ",将挤掉其他设备，请重试。");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorMap);
        }

        // 添加新会话
        zSetOperations.add(key, currentSessionId, System.currentTimeMillis());
        long expireInMinutes = Long.parseLong(StrUtil.isEmpty(expireTime) ? String.valueOf(EXPIRE_TIME) : expireTime);
        stringRedisTemplate.expire(key, expireInMinutes, TimeUnit.MINUTES);

        return ResponseEntity.ok().build();
    }

    private ResponseEntity<?> handleRegisteredUser(ChatGptUserEntity user,
                                                   String model,
                                                   String carid,
                                                   String token,
                                                   Map<String, String> configMap) {
        Map<String, ModelLimitDto> modelLimitsMap = getUserModelLimit(user, configMap.get("thirdModelLimits"));

        // 查询用户的模型速率
        String originModel = model;
        if (GPT4_MODELS.contains(model) || NORMAL_MODELS.contains(model)) {
            model = "gpt-4o";
        } else if (THIRD_MODELS.contains(model)) {
            model = "third-models";
        }
        log.info("查询用户速率的key为:{}", model);
        ModelLimitDto modelLimitDto = modelLimitsMap.get(model);
        log.info("用的的模型速率为：:{}", modelLimitDto);
        if (ObjectUtil.isEmpty(modelLimitDto)) {
            log.error("用户模型速率为空");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("error",
                    "The user model rate is not set, please contact the administrator." +
                            " \n用户模型速率未设置，请联系管理员")
            );
        }
        Integer limit = modelLimitDto.getLimit();
        String per = modelLimitDto.getPer();
        log.info("{}模型速率信息，{}, 速率：{}，周期：{}", originModel, modelLimitDto,limit, per);
        // 获取高级模型过期时间
        LocalDateTime plusExpireTime = user.getPlusExpireTime();
        LocalDateTime expireTime = user.getExpireTime();
        if (GPT4_MODELS.contains(originModel)) {
            log.info("handleGPT4ModelLimit");
            return handleGPT4ModelLimit(carid, token, plusExpireTime, limit, per);
        } else if (NORMAL_MODELS.contains(originModel)){
            log.info("handleNormalModelLimit");
            return handleNormalModelLimit(token, configMap, expireTime, limit, per);
        } else {
            log.info("handleAdvanceModelLimit");
            return handleAdvanceModelLimit(originModel, carid, token, plusExpireTime, limit, per);
        }
    }

    private static Map<String, ModelLimitDto> getUserModelLimit(ChatGptUserEntity user, String thirdModelLimits) {
        // 查询用户模型周期信息
        Map<String, ModelLimitDto> modelLimitsMap = user.getModelLimits();
        if (modelLimitsMap == null) {
            modelLimitsMap = new HashMap<>();
        }
        // 第三方模型速率
        if (StrUtil.isNotEmpty(thirdModelLimits)) {
            Map<String, ModelLimitDto> thirdModelLimitDto = getSysModelLimitMap(thirdModelLimits);

            // 合并两个Map，优先使用用户自定义的模型限制
            if (thirdModelLimitDto != null) {
                for (Map.Entry<String, ModelLimitDto> entry : thirdModelLimitDto.entrySet()) {
                    // 只有当用户没有设置特定模型限制时，才使用系统默认的第三方模型限制
                    if (!modelLimitsMap.containsKey(entry.getKey())) {
                        modelLimitsMap.put(entry.getKey(), entry.getValue());
                    }
                }
            }
        }
        return modelLimitsMap;
    }

    private ResponseEntity<?> handleNormalModelLimit(String username,
                                                     Map<String, String> configMap,
                                                     LocalDateTime expireTime,
                                                     Integer limit,
                                                     String per) {
        // 普通模型，判断是否有免费节点，如果没有则过期用户不让用
        ResponseEntity<Map<String, String>> expireResponse = checkNormalModelExpire(expireTime, configMap.get("freeNodes"));
        if (ObjectUtil.isNotEmpty(expireResponse)) return expireResponse;

        // 再判断用户速率信息，如果后台未设置用户速率或者模型倍率设置为0
        ResponseEntity<?> response = checkModelLimit(username, limit, per);
        if (response != null) return response;

        Duration duration = parseDuration(per);
        Bucket bucket = rateLimitPersistService.getBucket(limit, duration, USER_LIMIT_PREFIX + username);
        long remain = bucket.getAvailableTokens();
        if (remain < consumeTokens) {
            long wait = rateLimitPersistService.getWaitTime(bucket);
            log.warn("{}模型速率超过限制，请等待{}秒后重试", "gpt-4o",wait);
            Map<String, String> map = new HashMap<>();
            map.put("error", "The normal model usage of your account has been capped, the current limit is "
                    + limit + " times/" + per + ", please wait " + wait +
                    " seconds before trying again or buy a plan with a higher rate limit\n" +
                    "您账号的普通模型使用次数已上限,当前限制为 " + limit + " 次/" + formatDuration(per) + ", 请等待 "
                    + wait + " 秒后再试或者购买更高速率限制的套餐");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(map);
        } else {
            // 消耗一个令牌
            ConsumptionProbe probe = bucket.tryConsumeAndReturnRemaining(consumeTokens);
            long remainingTokens = probe.getRemainingTokens();
            log.info("普通模型对话后剩余次数: {}", remainingTokens);
            stringRedisTemplate.opsForValue().set(USER_GPT_USAGE + username, "4o剩余次数:" + remainingTokens + "/" + limit + "(" + formatDuration(per) + ")", parseDuration(per));
            // 统计次数 + 1
            incrementCounter(TODAY_NORMAL_USAGE);

            // 更新用户每日使用量、最后时间
            chatGptUserService.updateDailyConversationCount(username, false);
            return ResponseEntity.ok().build();
        }
    }

    private ResponseEntity<?> handleAdvanceModelLimit(String model,
                                                      String carid,
                                                      String token,
                                                      LocalDateTime plusExpireTime,
                                                      Integer limit,
                                                      String per) {
        ResponseEntity<Map<String, String>> expireResponse = checkAdvanceModelExpire(plusExpireTime, carid);
        if (ObjectUtil.isNotEmpty(expireResponse)) return expireResponse;

        // 如果配置文件中没有填写限制，则不对该模型做限制。
        ResponseEntity<?> response = checkModelLimit(token, limit, per);
        if (response != null) return response;

        Duration duration = parseDuration(per);
        log.info("{}模型速率信息:速率: {}, 时间: {}", model, limit, duration);
        Bucket bucket = rateLimitPersistService.getBucket(limit, duration, USER_LIMIT_PREFIX + token + model);
        // 获取剩余次数
        long remain = bucket.getAvailableTokens();
        if (remain < consumeTokens) {
            long wait = rateLimitPersistService.getWaitTime(bucket);
            log.warn("{}模型速率超过限制，请等待{}秒后重试",model ,wait);
            Map<String, String> errorMap = Map.of(
                    "error", String.format("The advanced model %s usage of your account has been capped, the current limit is %d times/%s, please wait %d seconds before trying again\n" +
                                    "您的%s使用次数已上限,当前限制为 %d 次/%s, 请等待 %d 秒后再试",
                            model, limit, per, wait,
                            model, limit, formatDuration(per), wait)
            );
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorMap);
        } else {
            ConsumptionProbe probe = bucket.tryConsumeAndReturnRemaining(consumeTokens);
            long remainingTokens = probe.getRemainingTokens();
            stringRedisTemplate.opsForValue().set(
                    USER_GPT_USAGE + token,
                    String.format("%s剩余次数:%d/%d(%s)", model, remainingTokens, limit, formatDuration(per)),
                    parseDuration(per)
            );
            log.info("{}模型对话后剩余次数: {}", model, remainingTokens);
            incrementCounter(TODAY_ADVANCE_USAGE);

            // 更新用户每日使用量、最后时间
            chatGptUserService.updateDailyConversationCount(token, false);
            return ResponseEntity.ok().build();
        }
    }


    private ResponseEntity<?> handleGPT4ModelLimit(String carid,
                                                   String username,
                                                   LocalDateTime plusExpireTime,
                                                   Integer limit, String per) {
        // 先判断用户权益是否过期
        ResponseEntity<Map<String, String>> expireResponse = checkAdvanceModelExpire(plusExpireTime, carid);
        if (ObjectUtil.isNotEmpty(expireResponse)) return expireResponse;

        // 再判断用户速率信息，如果后台未设置用户速率或者模型倍率设置为0，则不限制用户高级模型的次数
        ResponseEntity<?> response = checkModelLimit(username, limit, per);
        if (response != null) return response;

        // 计算用户的速率周期
        Duration duration = parseDuration(per);

        // 设置高级模型令牌桶
        Bucket bucket = rateLimitPersistService.getBucket(limit, duration, USER_LIMIT_PREFIX + username);

        // 获取剩余次数
        long remain = bucket.getAvailableTokens();
        if (remain < consumeTokens) {
            long wait = rateLimitPersistService.getWaitTime(bucket);
            log.warn("{}模型速率超过限制，请等待{}秒后重试", "gpt-4o",wait);
            Map<String, String> errorMap = Map.of(
                    "error", String.format("The advanced model %s usage of your account has been capped, the current limit is %d times/%s, please wait %d seconds before trying again\n" +
                                    "您的%s使用次数已上限,当前限制为 %d 次/%s, 请等待 %d 秒后再试",
                            "gpt-4o", limit, per, wait,
                            "gpt-4o", limit, formatDuration(per), wait)
            );
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorMap);
        } else {
            // 消耗一个令牌
            ConsumptionProbe probe = bucket.tryConsumeAndReturnRemaining(consumeTokens);
            long remainingTokens = probe.getRemainingTokens();
            log.info("高級对话后剩余次数: {}", remainingTokens);

            // 更新用户剩余次数
            stringRedisTemplate.opsForValue().set(USER_GPT_USAGE + username,
                    "4o剩余次数:" + remainingTokens + "/" + limit + "(" + formatDuration(per) + ")",
                    parseDuration(per));

            // 统计次数 + 1
            incrementCounter(TODAY_ADVANCE_USAGE);

            // 更新用户每日使用量、最后时间
            chatGptUserService.updateDailyConversationCount(username, false);

            return ResponseEntity.ok().build();
        }
    }

    private ResponseEntity<?> checkModelLimit(String username, Integer limit, String per) {
        if (ObjectUtil.isEmpty(limit) || StrUtil.isEmpty(per) || consumeTokens == 0) {
            // 统计次数 + 1
            incrementCounter(TODAY_ADVANCE_USAGE);
            log.warn("高级模型速率或者速率周期未设置，不限制用户使用次数");

            // 更新用户每日使用量、最后时间
            chatGptUserService.updateDailyConversationCount(username, false);
            return ResponseEntity.ok().build();
        } else if (limit == 0 ){
            return banModelUse();
        }
        return null;
    }

    /**
     * 用户账号被封
     * @return
     */
    private static ResponseEntity<Map<String, String>> blockedAccountResponse() {
        Map<String, String> map = Map.of("error", "The current account has been blocked, please contact the administrator\n" + "当前账号已被封禁，请联系管理员");
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(map);
    }

    /**
     * 处理游客速率
     * @param configMap
     * @param model
     * @param username
     * @return
     */
    private ResponseEntity<?> handleGuestUser(Map<String, String> configMap, String model, String username) {
        // 用户名在数据库中没有，说明是游客
        String enableNoLogin = configMap.get("enableNoLogin");
        String visitorLimit = configMap.get("visitorLimit");
        String visitorUsagePeriod = configMap.get("visitorUsagePeriod");

        // 开启了游客模式
        if ("true".equals(enableNoLogin)) {
            // 游客不允许使用o1模型
            if (GPT_ADVANCE_MODELS.contains(model)) {
                log.warn("游客使用了高级系列模型：{}", model);
                Map<String, String> errorMap = Map.of(
                        "error", "The o1 o3 model has limited resources and is temporarily closed to visitors. You can unlock more models and usage by clicking on Sign Up at the bottom left and logging in.\no1、o3模型资源有限，暂时不对游客开放。您可以通过点击左下角【注册】并【登录】后，解锁更多模型和次数。"
                );
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorMap);
            }
            // 开启游客模式
            if(StrUtil.isEmpty(visitorLimit) || StrUtil.isEmpty(visitorUsagePeriod)) {
                // 没有设置游客速率或者游客速率为0
                log.info("游客速率或者周期为空, 不限制使用");
                // 游客使用量+1
                incrementCounter(TODAY_VISITOR_USAGE);
                return ResponseEntity.ok().build();
            } else {
                log.info("游客速率：{}， 周期：{}", visitorLimit, visitorUsagePeriod);
                long visitorLimitLong = Long.parseLong(visitorLimit);
                // 游客速率周期
                Bucket bucket = rateLimitPersistService.getBucket(visitorLimitLong, parseDuration(visitorUsagePeriod), USER_LIMIT_PREFIX + username);
                long remain = bucket.getAvailableTokens();
                if (remain < 1) {
                    long wait = rateLimitPersistService.getWaitTime(bucket);
                    log.warn("{}模型速率超过限制，请等待{}秒后重试", model,wait);
                    Map<String, String> errorMap = Map.of(
                            "error", "Your current free quota has been used up. You can unlock more models and usage by clicking on Sign Up at the bottom left and logging in.\n您当前的免费次数已用完。您可以通过点击左下角【注册】并【登录】后，解锁更多模型和次数。"
                    );
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(errorMap);
                } else {
                    // 消耗一个令牌
                    ConsumptionProbe probe = bucket.tryConsumeAndReturnRemaining(1);
                    long remainingTokens = probe.getRemainingTokens();
                    log.info("游客对话后剩余次数: {}", remainingTokens);

                    // 游客使用量+1
                    incrementCounter(TODAY_VISITOR_USAGE);
                    return ResponseEntity.ok().build();
                }
            }
        } else {
            // 管理员关闭游客模式
            Map<String, String> errorMap = Map.of("error", "Administrator has disabled guest access. Please [Register] or [Login] to continue\n" + "管理员未开启游客模式，请使用【注册】【登录】后继续使用本服务。");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(errorMap);
        }
    }

    /**
     * 文本审核
     * @return
     */
    private ResponseEntity<Map<String, String>> handleSensitivePrompt(String username,
                                                                      String prompt,
                                                                      RiskControlRecordEntity record) {

        // 判断提问内容是否包含禁止词
        String word = containsAny(prompt, chatGptSensitiveWordService.listSensitiveWords());
        if (StrUtil.isNotEmpty(word)) {
            Map<String, String> map = new HashMap<>();
            map.put("error", "请珍惜账号,不要提问违禁内容[" + word + "],如系统误判，请联系管理员处理！");
            record.setUsername(username);
            record.setKeyword("敏感词匹配【" + word + "】");
            record.setPrompt(prompt);
            // 异步保存信息
            CompletableFuture.runAsync(() -> {
                try {
                    riskControlRecordService.save(record);
                } catch (Exception e) {
                    log.error("Failed to save risk control record for username: " + username + ", keyword: " + word, e);
                }
            }, commonAsyncExecutor);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(map);
        }
        return null;
    }

    /**
     * 获取模型倍率
     * @param dialogModelMultiplier
     * @return
     */
    private Map<String, Integer> getModelMultiplierMap(String dialogModelMultiplier) {
        Map<String, Integer>  modelMultipMap = null;
        if (StrUtil.isNotEmpty(dialogModelMultiplier)) {
            try {
                modelMultipMap = objectMapper.readValue(dialogModelMultiplier, Map.class);
            } catch (JsonProcessingException e) {
                log.error("模型对话速率不是json格式，请修改格式", e);
                throw new RuntimeException(e);
            }
        }
        return modelMultipMap;
    }

    /**
     * 获取模型倍率
     * @param model
     * @param modelMultipliers
     * @return
     */
    private int getConsumeTokens(String model, Map<String, Integer> modelMultipliers) {
        if (ObjectUtil.isEmpty(modelMultipliers)) {
            return 1;
        }
        return modelMultipliers.getOrDefault(model, 1);
    }

    /**
     * 校验普通账号的过期时间
     * @param expireTime
     * @return
     */
    private ResponseEntity<Map<String, String>> checkNormalModelExpire(LocalDateTime expireTime, String freeNodes) {
        // 先判断没有开启免费节点并且过期了则不让用。
        if ((StrUtil.isEmpty(freeNodes) || Long.parseLong(freeNodes) == 0)
                && (expireTime.isBefore(LocalDateTime.now()))) {
            Map<String, String> errorMap = Map.of("error", "Your regular benefits have expired. Please return to the homepage to purchase a plan and continue to use it." +
                    "\n您的普通权益已过期，请回到首页购买套餐后继续使用。");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(errorMap);
        }
        return null;
    }

    private void incrementCounter(String key) {
        stringRedisTemplate.opsForValue().increment(key);
        long secondsUntilMidnight = getSecondsUntilMidnight();
        stringRedisTemplate.expire(key, secondsUntilMidnight, TimeUnit.SECONDS);
    }

    /**
     * 计算距离午夜的秒数
     * @return
     */
    private long getSecondsUntilMidnight() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime midnight = now.toLocalDate().atTime(LocalTime.MAX);
        return Duration.between(now, midnight).getSeconds();
    }

    private static String getPrompt(String action, List<Map<String, Object>> messages, RiskControlRecordEntity record) {
        String prompt = null;
        if (!"continue".equals(action)) {
            Map<String, Object> firstMessage = messages.get(0);
            // 获取 content 对象
            Map<String, Object> content = (Map<String, Object>) firstMessage.get("content");
            // 获取 parts 数组
            List<String> parts = (List<String>) content.get("parts");
            // 获取content_type，content_type已知值：text和multimodal_text
            Object content_type = content.get("content_type");
            log.info("content_type:{}", content_type);
            String contentType;
            if (content_type instanceof LinkedHashMap) {
                // 将 LinkedHashMap 转换为 JSON 字符串
                ObjectMapper mapper = new ObjectMapper();
                try {
                    contentType = mapper.writeValueAsString(content_type);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            } else if (content_type instanceof String) {
                contentType = (String) content_type;
            } else {
                throw new IllegalArgumentException("Unexpected type: " + content_type.getClass().getName());
            }
            if ("text".equals(contentType)) {
                prompt = parts.get(0);
            }else if ("multimodal_text".equals(contentType)){
                prompt = parts.get(1);
            }
            record.setContentType(contentType);
            record.setPrompt(prompt);
        }
        return prompt;
    }

    /**
     * 检查高级模型使用权益
     * @param plusExpireTime 过期时间
     * @return 响应结果
     */
    private  ResponseEntity<Map<String, String>> checkAdvanceModelExpire(LocalDateTime plusExpireTime, String carId) {
        if (checkAccountFlag) {
            log.info("校验用户高级模型使用权限...");
            // 获取gpt账号信息
            ChatGptSessionEntity session = chatGptSessionMapper.selectOne(new LambdaQueryWrapper<ChatGptSessionEntity>()
                    .eq(ChatGptSessionEntity::getCarID, carId)
                    .isNull(ChatGptSessionEntity::getDeletedAt)
                    .eq(ChatGptSessionEntity::getStatus, CAR_ENABLE_STATUS));
            if (ObjectUtil.isEmpty(session)) {
                log.warn("获取session失败，session信息为空");
                return null;
            }
            // 如果是高级权益的账号需要校验过期时间，如果只是普通账号的4o模型则不校验过期时间。
            if (!PLAN_TYPE_FREE.equals(session.getIsPlus())) {
                log.info("使用高级账号，开始校验用户的plus过期时间");
                // 根据用户token查找用户的速率信息
                if (ObjectUtil.isEmpty(plusExpireTime) || plusExpireTime.isBefore(LocalDateTime.now())) {
                    log.warn("用户高级权益已过期：{}", plusExpireTime);
                    Map<String, String> map = new HashMap<>();
                    map.put("error", "Your advanced model rights have expired. Please return to the homepage to purchase the package and continue to use it.\n" + "您的高级模型权益已过期，请回到首页购买套餐后继续使用。" );
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(map);
                }
            }
        } else{
            if (ObjectUtil.isEmpty(plusExpireTime) || plusExpireTime.isBefore(LocalDateTime.now())) {
                log.warn("用户高级权益已过期：{}", plusExpireTime);
                Map<String, String> map = new HashMap<>();
                map.put("error", "Your advance model membership has expired. Please renew your subscription and try again.\n" + "您的高级模型权益已过期，请续费后重试" );
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(map);
            }
        }
        return null;
    }

    private Duration parseDuration(String period) {
        if (StrUtil.isEmpty(period)){
            return Duration.ofSeconds(1);
        } else {
            // 定义正则表达式，支持秒、分钟、小时、天、周、年
            String regex = "(\\d+)([smhdwMy])";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(period);
            int value = 0;
            String unit = null;
            // 检查是否有匹配项
            if (matcher.find()) {
                value = Integer.parseInt(matcher.group(1));  // 提取数字部分
                unit= matcher.group(2);
            } else {
                log.info("No match found:{}", period);
            }
            // 根据单位返回不同的 Duration
            return switch (Objects.requireNonNull(unit)) {
                case "s" -> // 秒
                        Duration.ofSeconds(value);
                case "m" -> // 分钟
                        Duration.ofMinutes(value);
                case "h" -> // 小时
                        Duration.ofHours(value);
                case "3h" -> // 小时
                        Duration.ofHours(value* 3L);
                case "5h" -> // 小时
                        Duration.ofHours(value* 5L);
                case "d" -> // 天
                        Duration.ofDays(value);
                case "w" -> // 周，7天
                        Duration.ofDays(value * 7L);
                case "M" -> // 月，30天
                        Duration.ofDays(value * 30L);
                case "y" -> // 年，365天
                        Duration.ofDays(value * 365L);
                default -> throw new IllegalArgumentException("Unknown time unit: " + unit);
            };
        }
    }

    /**
     * 敏感词校验
     * @param text
     * @param forbiddenWords
     * @return
     */
    private String containsAny(String text, List<String> forbiddenWords) {
        if (StrUtil.isEmpty(text)) {
            return null;
        }
        if (CollectionUtils.isEmpty(forbiddenWords)) {
            return null;
        }
        return forbiddenWords.stream().filter(text::contains).findFirst().orElse(null);
    }

    public ResponseEntity<?> claudeConversationLimit(HttpServletRequest request, Map<String, Object> requestData) {
        String loginToken = getToken(request);
        log.info("claude限速请求:{}",loginToken);
        String model = (String)requestData.get("model");
        log.info("model:{}",model);
        // 根据token查询用户速率
        ChatGptUserEntity userInfo = chatGptUserService.getUserByLoginToken(loginToken);
        if (ObjectUtil.isEmpty(userInfo)) {
            throw new CustomException("用户信息查询失败,loginToken：" + loginToken);
        }
        // 校验账号是否被封禁
        if (userInfo.getStatus() == 0) {
            return blockedAccountResponse();
        }
        // 校验claude过期时间
        boolean expired = checkUserExpireTime(userInfo.getClaudeExpireTime(), userInfo.getClaudeProExpireTime());
        if (expired) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
                    Map.of("error", Map.of("message", "Your benefit has expired, Please return to the homepage to purchase the package and continue to use it." +
                            "\n您的权益已过期，请回到首页购买套餐后继续使用。"), "type", "user-access-error")
            );
        }
        Map<String, ModelLimitDto> modelLimits = getUserModelLimit(userInfo, chatGptConfigService.getValueByKey("thirdModelLimits"));

        if (ObjectUtil.isEmpty(modelLimits)) {
            return banModelUse();
        }

        ModelLimitDto modelLimitDto = modelLimits.containsKey(model) ? modelLimits.get(model) : modelLimits.get("claude-3.5");

        if (ObjectUtil.isEmpty(modelLimitDto)) {
            return banModelUse();
        }
        // 都不为空，速率生效。
        Integer claudeLimit = modelLimitDto.getLimit();
        String claudePer = modelLimitDto.getPer();
        log.info("claude速率信息：{}, {}", claudeLimit, claudePer);
        if (claudeLimit != null && StrUtil.isNotEmpty(claudePer)) {
            if(claudeLimit == 0) {
                return banModelUse();
            }
            Duration duration = parseDuration(claudePer);
            Bucket bucket = rateLimitPersistService.getBucket(claudeLimit, duration, USER_CLAUDE_LIMIT_PREFIX + loginToken);
            long remain = bucket.getAvailableTokens();
            // 查询用户模型倍率map
            // 查询系统配置信息
            Map<String, String> configMap = chatGptConfigService.getKeyValueMapByKeys(List.of(
                    "enableNoLogin","visitorLimit", "dialogModelMultiplier", "freeNodes", "visitorUsagePeriod",
                    "enableSSO", "maxDevices", "expireTime", "thirdModelLimits"));
            Map<String, Integer> modelMultipMap = getModelMultiplierMap(configMap.get("dialogModelMultiplier"));

            // 获得消耗tokens
            consumeTokens = getConsumeTokens(model, modelMultipMap);
            if (remain < consumeTokens) {
                long wait = rateLimitPersistService.getWaitTime(bucket);
                log.warn("模型速率超过限制，请等待{}秒后重试",wait);
                Map<String, String> map = new HashMap<>();
                map.put("message", "The model usage of your account has been capped, the current limit is "
                        + claudeLimit + " times/" + claudePer + ", please wait " + wait +
                        " seconds before trying again or buy a plan with a higher rate limit\n" +
                        "您账号的模型使用次数已上限,当前限制为 " + claudeLimit + " 次/" + formatDuration(claudePer) + ", 请等待 "
                        + wait + " 秒后再试或者购买更高速率限制的套餐");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Map.of("error", map, "type", "user-rate-error"));
            } else {
                // 消耗一个令牌
                ConsumptionProbe probe = bucket.tryConsumeAndReturnRemaining(consumeTokens);
                long remainingTokens = probe.getRemainingTokens();
                log.info("claude模型对话后剩余次数: {}", remainingTokens);
                // 新增claude次数统计
                incrementCounter(TODAY_CLAUDE_USAGE);
                // 更新用户每日使用量、最后时间
                chatGptUserService.updateDailyConversationCount(userInfo.getUserToken(), true);
                return ResponseEntity.ok().build();
            }
        } else  {
            // 没有设置速率，直接放行
            log.warn("用户未配置claude速率周期，不限制使用");
            // 新增claude次数统计
            incrementCounter(TODAY_CLAUDE_USAGE);
            // 更新用户每日使用量、最后时间
            chatGptUserService.updateDailyConversationCount(userInfo.getUserToken(), true);
            return ResponseEntity.ok().build();
        }
    }

    /**
     * 禁止使用模型
     * @return
     */
    private static ResponseEntity<Map<String, String>> banModelUse() {
        log.warn("模型速率为0，则禁止使用该模型");
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("error", "You do not have permission to use this model, please purchase another package or contact the administrator \n您没有此模型的使用权限，请购买其他套餐或者联系管理员"));
    }

    private boolean checkUserExpireTime(LocalDateTime claudeExpireTime, LocalDateTime claudeProExpireTime) {
        LocalDateTime now = LocalDateTime.now();
        // 如果两个时间都为空，返回true表示需要报错
        if (claudeExpireTime == null && claudeProExpireTime == null) {
            return true;
        }
        // 检查过期状态
        boolean isClaudeExpired = claudeExpireTime == null || claudeExpireTime.isBefore(now);
        boolean isClaudeProExpired = claudeProExpireTime == null || claudeProExpireTime.isBefore(now);
        return isClaudeExpired && isClaudeProExpired;
    }

    public ResponseEntity<?> grokConversationAuditLimit(HttpServletRequest request, Map<String, Object> requestData) {
        String loginToken = getToken(request);
        log.info("user loginToken:{}",loginToken);

        String modelName = (String) requestData.get("modelName");
        log.info("modelName:{}",modelName);
        // 查询用户
        ChatGptUserEntity user = chatGptUserService.getUserByLoginToken(loginToken);
        log.info("user modelLimits:{}",user.getModelLimits());
        if (ObjectUtil.isEmpty(user)) {
            log.warn("用户信息不存在，请重新登录后重试。");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("error", "user not found, please come back home and try again" + "\n 用户信息不存在，请回到首页重试"));
        }

        // 用户敏感词校验
        String message = (String)requestData.get("message");
        RiskControlRecordEntity record = new RiskControlRecordEntity();
        record.setPrompt(message);
        record.setContentType("grok3 sensitive");
        ResponseEntity<Map<String, String>> sensitiveResponse = handleSensitivePrompt(user.getUserToken(), message, record);
        if (sensitiveResponse != null) return sensitiveResponse;
        // 判断用户是否被禁用
        if (user.getStatus() == 0) {
            log.warn("{}用户已被禁用", user.getUserToken());
            return blockedAccountResponse();
        }
        // 判断用户权益是否过期
        boolean expired = checkUserExpireTime(user.getGrokExpireTime(), user.getGrokSuperExpireTime());
        if (expired) {
            log.warn("用户的所有grok权益已过期");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
                    Map.of("error", "Your benefit has expired, Please return to the homepage to purchase the package and continue to use it." +
                            "\n您的权益已过期，请回到首页购买套餐后继续使用。")
            );
        }

        Map<String, ModelLimitDto> modelLimits = user.getModelLimits();
        if (ObjectUtil.isEmpty(modelLimits) || Objects.isNull(modelLimits.get("grok-3"))) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("error",
                    "The user model rate is not set, please contact the administrator." +
                            " \n用户模型速率未设置，请联系管理员"));
        }
        ModelLimitDto modelLimitDto = modelLimits.get("grok-3");
        Integer limit = modelLimitDto.getLimit();
        String per = modelLimitDto.getPer();
        if (limit == 0) {
            return banModelUse();
        }

        // 模型不限制使用
        if (StrUtil.isEmpty(per)) {
            return ResponseEntity.ok().build();
        }

        // 模型限制
        Duration duration = parseDuration(per);
        Bucket bucket = rateLimitPersistService.getBucket(limit, duration, USER_GROK_LIMIT_PREFIX + loginToken);
        long remain = bucket.getAvailableTokens();
        if (remain < 1) {
            long wait = rateLimitPersistService.getWaitTime(bucket);
            log.warn("grok模型速率超过限制，请等待{}秒后重试",wait);
            Map<String, String> map = new HashMap<>();
            map.put("error", "The model usage of your account has been capped, the current limit is "
                    + limit + " times/" + per + ", please wait " + wait +
                    " seconds before trying again or buy a plan with a higher rate limit\n" +
                    "您账号的模型使用次数已上限,当前限制为 " + limit + " 次/" + formatDuration(per) + ", 请等待 "
                    + wait + " 秒后再试或者购买更高速率限制的套餐");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(map);
        } else {
            // 消耗一个令牌
            ConsumptionProbe probe = bucket.tryConsumeAndReturnRemaining(1);
            long remainingTokens = probe.getRemainingTokens();
            log.info("grok模型对话后剩余次数: {}", remainingTokens);
            return ResponseEntity.ok().build();
        }
    }
}

package org.seven.share.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.seven.share.common.exception.CustomException;
import org.seven.share.mapper.ChatGptCouponMapper;
import org.seven.share.common.pojo.entity.ChatGptCouponEntity;
import org.seven.share.service.ChatGptCouponService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: ChatGptCouponServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/13
 */
@Service
public class ChatGptCouponServiceImpl extends ServiceImpl<ChatGptCouponMapper, ChatGptCouponEntity>
        implements ChatGptCouponService {

    @Resource
    private ChatGptCouponMapper chatGptCouponMapper;
    @Override
    public void saveCoupon(ChatGptCouponEntity coupon) {
        // 查询是否有相同的优惠卷
        long count = chatGptCouponMapper.selectCount(new LambdaQueryWrapper<ChatGptCouponEntity>()
                .eq(ChatGptCouponEntity::getCoupon, coupon.getCoupon())
                .eq(ChatGptCouponEntity::getStatus, "0"));
        if (count > 0) {
            throw new CustomException("优惠卷已存在");
        }
        List<String> ids = coupon.getIds();
        if (!CollectionUtils.isEmpty(ids)) {
            String collect = String.join(",", ids);
            coupon.setSubTypeIds(collect);
        }
        chatGptCouponMapper.insert(coupon);
    }

    /**
     * 生成优惠券码
     * @return
     */
    @Override
    public String generateCouponCode() {
        String randomString = RandomUtil.randomString(16);
        return randomString.toUpperCase();
    }
}

package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: BaseSysParamEntity
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/1
 */
@Data
@TableName("base_sys_param")
public class BaseSysParamEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    @TableField(value = "createTime", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "updateTime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField("keyName")
    private String keyName;

    private String name;

    private String data;

    @TableField("dataType")
    private String dataType;

    private String remark;

}

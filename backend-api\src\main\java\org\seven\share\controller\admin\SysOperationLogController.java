package org.seven.share.controller.admin;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.Result;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.entity.SysOperationLog;
import org.seven.share.service.SysOperationLogService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "SysOperationLogController", description = "日志信息控制层")
@RequestMapping("/expander-api/sys/log")
public class SysOperationLogController {

    @Resource
    private SysOperationLogService sysOperationLogService;

    @Operation(summary = "导出数据")
    @PostMapping(value = "/getExportList")
    public Result<List<SysOperationLog>> add(@RequestBody SysOperationLog sysOperationLog) {
        return Result.success(sysOperationLogService.getExportList(sysOperationLog));
    }

    @Operation(summary = "list 分页列表")
    @Parameters({
            @Parameter(name = "current", description = "当前页", required = true, example = "1"),
            @Parameter(name = "size", description = "每页显示条数", required = true, example = "10")
    })
    @GetMapping(value = "/page")
    public Result<IPage<SysOperationLog>> page(@Parameter(hidden = true) @RequestParam Map<String, Object> params) {
        IPage<SysOperationLog> sysOperationLogPage = sysOperationLogService.getPage(params);
        return Result.success(sysOperationLogPage);
    }

    @Operation(summary = "清空数据")
    @PostMapping(value = "/clean")
    @SysLogInterface(title = "清空数据", businessType = BusinessType.CLEAN)
    public Result<Integer> clean() {
        return Result.success(sysOperationLogService.clean());
    }

    @Operation(summary = "批量删除数据")
    @PostMapping("/delete")
    public Result<?> delete(@RequestBody List<Long> ids) {
        sysOperationLogService.removeBatchByIds(ids);
        return Result.success();
    }
}

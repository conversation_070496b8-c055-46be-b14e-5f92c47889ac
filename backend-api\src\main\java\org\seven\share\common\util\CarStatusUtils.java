package org.seven.share.common.util;

import cn.hutool.core.util.ObjectUtil;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.pojo.dto.CarStatus;
import org.seven.share.common.pojo.vo.CarInfoVo;

import java.util.Optional;

/**
 * @ClassName: CarStatusUtils
 * @Description:
 * @Author: Seven
 * @Date: 2025/2/28
 */
public class CarStatusUtils {
    public static CarInfoVo convertToCarInfoVo(String carId, Integer isPlus , CarStatus carStatus, boolean isGpt) {
        if (ObjectUtil.isEmpty(carStatus)) {
            throw new CustomException("carStatus为空");
        }
        CarInfoVo carInfoVo = new CarInfoVo();
        carInfoVo.setCarID(carId);
        carInfoVo.setCount(Optional.ofNullable(carStatus.getCount()).map(Integer::longValue).orElse(0L));
        carInfoVo.setIsPlus(carId.toUpperCase().contains("MAX")? 2 : isPlus);
        int secondsUntilRecovery = 0;
        if (carStatus.getClears_in() != null && carStatus.getClears_in() > 0) {
            secondsUntilRecovery = carStatus.getClears_in();
        } else if (carStatus.getResetTime() != null && carStatus.getResetTime() > 0) {
            secondsUntilRecovery = carStatus.getResetTime();
        }
        dealStatusAndDetail(carInfoVo.getCount(), secondsUntilRecovery, carInfoVo, isGpt);
        carInfoVo.setCarStatus(carStatus);
        return carInfoVo;
    }

    private static void dealStatusAndDetail(Long count, int secondsUntilRecovery, CarInfoVo carInfoVo, boolean isGpt) {
        if (secondsUntilRecovery > 0) {
            carInfoVo.setStatus("停运");
            carInfoVo.setDetail("将于" + secondsUntilRecovery + "秒后恢复");
        } else {
            if (count < (isGpt? 80 : 20)) {
                carInfoVo.setStatus("空闲");
                carInfoVo.setDetail("推荐");
            } else {
                carInfoVo.setStatus("繁忙");
                carInfoVo.setDetail("可用");
            }
        }
    }
}

/* 自定义暗黑模式样式 */
html.dark {
    /* 主背景色 */
    background: #1A2331;

    /* 基础变量 */
    --el-color-primary: #60A5FA;  /* 主题色 */
    --el-text-color-primary: #E5E7EB;  /* 主要文字 */
    --el-text-color-regular: #9CA3AF;  /* 常规文字 */
    --el-text-color-secondary: #6B7280;  /* 次要文字 */

    /* 背景色阶 */
    --el-bg-color: #1A2331;  /* 主背景 */
    --el-bg-color-overlay: #1F2937;  /* 浮层背景 */
    --el-bg-color-secondary: #243447;  /* 次级背景 */

    /* 边框颜色 */
    --el-border-color: #2C3B52;  /* 主边框 */
    --el-border-color-light: #374151;  /* 浅色边框 */
    --el-border-color-lighter: #4B5563;  /* 更浅边框 */

    /* 组件相关 */
    --el-fill-color: #243447;  /* 填充色 */
    --el-mask-color: rgba(0, 0, 0, 0.7);  /* 遮罩颜色 */
    --el-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);  /* 阴影 */

    /* 输入框相关 */
    --el-input-bg-color: #1F2937;
    --el-input-border-color: #2C3B52;
    --el-input-hover-border-color: #4B5563;
    --el-input-focus-border-color: #60A5FA;
}

/* 输入框样式优化 */
.dark .el-input__wrapper {
    background-color: var(--el-input-bg-color) !important;
    box-shadow: 0 0 0 1px var(--el-input-border-color) !important;
}

.dark .el-input__wrapper:hover {
    box-shadow: 0 0 0 1px var(--el-input-hover-border-color) !important;
}

.dark .el-input__wrapper.is-focus {
    box-shadow: 0 0 0 1px var(--el-input-focus-border-color) !important;
}

/* Dialog 样式 */
.dark .el-dialog {
    background-color: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color);
    box-shadow: var(--el-box-shadow);
}

/* Button 样式 */
.dark .el-button {
    --el-button-hover-bg-color: #243447;
    --el-button-hover-border-color: var(--el-border-color-lighter);
}

/* Table 样式 */
.dark .el-table {
    --el-table-header-bg-color: #1F2937;
    --el-table-row-hover-bg-color: #243447;
    background-color: var(--el-bg-color);
}

/* Card 样式 */
.dark .el-card {
    background-color: var(--el-bg-color-overlay);
    border-color: var(--el-border-color);
}

/* Menu 样式 */
.dark .el-menu {
    background-color: var(--el-bg-color);
    border-color: var(--el-border-color);
}

.dark .el-menu-item:hover {
    background-color: var(--el-fill-color);
}

/* Select 下拉框样式 */
.dark .el-select-dropdown {
    background-color: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color);
}

.dark .el-select-dropdown__item:hover {
    background-color: var(--el-fill-color);
}

html{
    background: #fff;
}


/* 在普通模式下的样式 */
.car-div {
    background-color: #fafafa;
    color: black;
    border-radius: 15px;
    box-shadow: 1px 1px 1px 1px rgba(0.1, 0.1, 0.1, 0.1);
    padding: 1rem;
    cursor: pointer;
    border: 2px solid transparent; /* Initially set the border to transparent */
}
.car-header{
    border-bottom-width: 1px;
    border-color: rgb(226 232 240);
}
.dark .car-header {
    border-color: #1c1c1c;
}

.car-div:active {
    transform: scale(0.98) rotateX(0deg);
}

/* 在暗黑模式下的样式 */
.dark .car-div {
    background-color: #2c2c2c;
    color: white;
}
.notice {
    background-color: #fefffe;
}

.dark .notice {
    background-color: #2c2c2c;
    color: white;
}
.custom-header {
    background-color: #fefffe;
}
.dark .custom-header {
    background-color: #2c2c2c;
}

.dark .el-tabs__nav-wrap::after {
    background-color: #374151;
    height: 2px;
}

.dark .el-tabs--card > .el-tabs__header .el-tabs__item {
    border: 1px solid #374151;
    border-bottom: none;
    background-color: #1A2331;
}

.dark .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
    background-color: #1F2937;
    border-color: #374151;
}

.dark .el-tabs--border-card {
    background: #1F2937;
    border: 1px solid #374151;
}

.dark .el-tabs--border-card > .el-tabs__header {
    background-color: #1A2331;
    border-bottom: 1px solid #374151;
}

/* 输入框样式优化 - 更新版本 */
.dark .el-input__wrapper {
    background-color: #1F2937 !important;
    box-shadow: 0 0 0 1px #374151 !important;
}

.dark .el-input__wrapper:hover {
    box-shadow: 0 0 0 1px #4B5563 !important;
}

.dark .el-input__wrapper.is-focus {
    box-shadow: 0 0 0 1px #60A5FA !important;
}

.dark .el-input__inner {
    color: #E5E7EB !important;
}

.dark .el-input__inner::placeholder {
    color: #6B7280 !important;
}

/* Tabs 样式优化 */
.dark .el-tabs__nav-wrap::after {
    background-color: #374151;
    height: 1px;
    opacity: 0.6;
}

.dark .el-tabs__item {
    color: #9CA3AF;
    padding: 0 20px;
    height: 40px;
    line-height: 40px;
    position: relative;
    transition: all 0.3s ease;
}

.dark .el-tabs__item.is-active {
    color: var(--el-tabs-active-color);
    font-weight: 500;
}

.dark .el-tabs__item.is-active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--el-tabs-active-color);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.dark .el-tabs__item:hover {
    color: var(--el-tabs-hover-color);
}

/* 卡片式 Tabs */
.dark .el-tabs--card > .el-tabs__header .el-tabs__item {
    border: 1px solid #374151;
    background-color: #1A2331;
    margin-right: 4px;
    border-radius: 4px 4px 0 0;
}

.dark .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
    background-color: #1F2937;
    border-bottom-color: #1F2937;
    color: var(--el-tabs-active-color);
}

/* 边框式 Tabs */
.dark .el-tabs--border-card {
    background-color: #1F2937;
    border: 1px solid #374151;
    border-radius: 4px;
}

.dark .el-tabs--border-card > .el-tabs__header {
    background-color: #1A2331;
    border-bottom: 1px solid #374151;
}

.dark .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
    background-color: #1F2937;
    border-right-color: #374151;
    border-left-color: #374151;
}

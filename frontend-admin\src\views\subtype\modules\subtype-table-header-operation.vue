<script setup lang="ts">
import { ElMessage } from 'element-plus';
import { $t } from '@/locales';
import { asyncUserModelLimit } from '@/service/api';

defineOptions({ name: 'SubtypeTableHeaderOperation' });

interface Props {
  disabledDelete?: boolean;
  loading?: boolean;
  showAdd?: boolean;
  showDelete?: boolean;
  showSync?: boolean;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'add'): void;
  (e: 'delete'): void;
  (e: 'refresh'): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<UI.TableColumnCheck[]>('columns', {
  default: () => []
});

function add() {
  emit('add');
}

function batchDelete() {
  emit('delete');
}

function refresh() {
  emit('refresh');
}

async function updateUserModelRate() {
  try {
    await asyncUserModelLimit();
    ElMessage.success('套餐速率同步成功');
    emit('refresh');
  } catch {
    ElMessage.error('套餐速率同步失败');
  }
}
</script>

<template>
  <ElSpace direction="horizontal" wrap justify="end" class="lt-sm:w-200px">
    <slot name="prefix"></slot>
    <slot name="default">
      <ElButton v-show="showAdd" plain type="success" @click="add">
        <template #icon>
          <icon-ic-round-plus class="text-icon" />
        </template>
        新增
      </ElButton>
      <ElButton v-show="showSync" plain type="primary" @click="updateUserModelRate">
        <template #icon>
          <icon-ic-round-plus class="text-icon" />
        </template>
        套餐同步用户速率
      </ElButton>
      <ElPopconfirm :title="$t('common.confirmDelete')" @confirm="batchDelete">
        <template #reference>
          <ElButton v-show="showDelete" type="danger" plain :disabled="disabledDelete">
            <template #icon>
              <icon-ic-round-delete class="text-icon" />
            </template>
            {{ $t('common.batchDelete') }}
          </ElButton>
        </template>
      </ElPopconfirm>
    </slot>
    <ElButton @click="refresh">
      <template #icon>
        <icon-mdi-refresh class="text-icon" :class="{ 'animate-spin': loading }" />
      </template>
      {{ $t('common.refresh') }}
    </ElButton>
    <TableColumnSetting v-model:columns="columns" />
    <slot name="suffix"></slot>
  </ElSpace>
</template>

<style scoped></style>

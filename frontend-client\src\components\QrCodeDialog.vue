<template>

        <div class="qr-container">
            <qrcode v-if="type === 3" :value="qrData" :size="qrCodeSize" :color="{ dark: '#000', light: '#FFF' }" type="image/png" />
            <img v-if="type === 4" :src="qrData" width="200" height="200" alt="支付二维码获取失败">
        </div>
        <div class="text-center">
            <div class="mb-2">{{ $t('qr.qrCodeRefreshIn') }}：{{ remainingTime }} 秒</div>
            <el-alert :title="$t('qr.qrCodeExpiration')" type="warning" show-icon />
            <el-button @click="refreshQrCode" :disabled="remainingTime > 0" type="primary" class="mt-2">
                {{ $t('qr.refreshQrButton') }}
            </el-button>
        </div>
</template>

<script>
import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
import Qrcode from 'vue-qrcode';
import { ElMessageBox } from 'element-plus';
import { useI18n } from 'vue-i18n';

export default defineComponent({
    components: {
        Qrcode,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        qrData: {
            type: String,
            required: true,
        },
        type: {
            type: Number,
            required: true,
        },
    },
    emits: ['refresh-qr', 'close'],
    setup (props, { emit }) {
        const { t } = useI18n();
        const qrCodeSize = ref(200);
        const dialogWidth = ref('30%');
        const remainingTime = ref(300);  // 5分钟倒计时（以秒为单位）
        let timer = null;
        // 刷新二维码
        const refreshQrCode = () => {
            if (remainingTime.value === 0) {
                emit('refresh-qr');
                startCountdown();  // 重新开始倒计时
            }
        };

        // 启动倒计时
        const startCountdown = () => {
            remainingTime.value = 300;  // 5分钟倒计时
            timer = setInterval(() => {
                if (remainingTime.value > 0) {
                    remainingTime.value--;
                } else {
                    clearInterval(timer);
                }
            }, 1000);
        };

        // 监听窗口大小调整
        const handleResize = () => {
            const windowWidth = window.innerWidth;
            if (windowWidth < 768) {
                qrCodeSize.value = 150;
                dialogWidth.value = '90%';
            } else {
                qrCodeSize.value = 200;
                dialogWidth.value = '30%';
            }
        };

        // 页面挂载时启动倒计时并监听窗口变化
        onMounted(() => {
            startCountdown();
            handleResize();
            window.addEventListener('resize', handleResize);
        });

        // 页面卸载时清理倒计时
        onBeforeUnmount(() => {
            clearInterval(timer);
            window.removeEventListener('resize', handleResize);
        });

        const confirmClose = (done) => {
            ElMessageBox.confirm(
                t('qr.confirmCloseMessage'),
                t('qr.tip'),
                {
                    confirmButtonText: t('qr.confirmButton'),
                    cancelButtonText: t('qr.cancelButton'),
                    type: 'warning',
                }
            )
            .then(() => {
                done();
                emit('close');
            })
            .catch(() => {
                // 用户选择了继续支付，不关闭弹窗
            });
        };

        return {
            qrCodeSize,
            dialogWidth,
            remainingTime,
            refreshQrCode,
            confirmClose,
        };
    },
});
</script>

<style scoped>
.qr-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
}

.qr-dialog .el-dialog__header {
    display: none;
}

@media (max-width: 768px) {
    .qr-container {
        padding: 10px 0;
    }

    .qr-dialog .el-dialog {
        max-width: 100%;
    }
}
</style>

package org.seven.share.security.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.enums.CacheConstants;
import org.seven.share.common.enums.StatusEnums;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.pojo.entity.SysUser;
import org.seven.share.common.util.SysUserDetail;
import org.seven.share.mapper.SysResourceMapper;
import org.seven.share.mapper.SysRoleMapper;
import org.seven.share.mapper.SysUserMapper;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    private final CacheManager cacheManager;

    private final SysUserMapper sysUserMapper;

    private final SysRoleMapper sysRoleMapper;

    private final SysResourceMapper sysResourceMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {

        Cache cache = cacheManager.getCache(CacheConstants.USER_DETAILS);
        if (cache != null && cache.get(username) != null) {
            return (SysUserDetail) Objects.requireNonNull(cache.get(username)).get();
        }

        // 获取登录用户信息
        SysUser user = sysUserMapper.selectOne(Wrappers.<SysUser>lambdaQuery().eq(SysUser::getUserName, username));

        // 用户不存在
        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("用户不存在");
        }

        // 用户停用
        if (StatusEnums.DISABLE.getCode().equals(user.getStatus())) {
            throw new CustomException("用户已被禁用");
        }

        // 获取用户菜单权限标识
        List<String> permissions = sysResourceMapper.getUserPermissions(user.getId());

        // 获取用户角色
        List<String> userRole = sysRoleMapper.getUserRole(user.getId());


        SysUserDetail userDetails = new SysUserDetail(user, permissions, userRole,user.getTenantId());

        if (cache != null) {
            cache.put(username, userDetails);
        }
        return userDetails;
    }
}

<script setup lang="tsx">
import { ref } from 'vue';
import { ElButton, ElMessage, ElMessageBox, ElPopconfirm, ElSwitch, ElTag } from 'element-plus';
import { useBoolean } from '@sa/hooks';
import { fetchGrokSessionPage, removeGrokSessionBatch, updateGrokSession } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import SubtypeSearch from './modules/Grok-search.vue';
import GrokTableHeaderOperation from './modules/Grok-table-header-operation.vue';
import GrokOperateModel, { type OperateType } from './modules/Grok-operate-modal.vue';

// Define the proper type for your table data
interface Grok {
  id: string;
  email: string;
  isPro: number;
  status: number;
  officialSession: string;
  remark: string;
  createTime: string;
  updateTime: string;
  password?: string;
  userToken?: string;
  count?: number;
  grok2?: number;
  grok3?: number;
  deepsearch?: number;
  deepersearch?: number;
  updatemodel: string;
}

const { columns, columnChecks, data, loading, pagination, getData, searchParams, resetSearchParams, getDataByPage } =
  useTable({
    apiFn: fetchGrokSessionPage,
    columns: () => [
      { type: 'selection', width: 48 },
      { type: 'index', label: '序号', align: 'center', width: 60 },
      { prop: 'email', label: '邮箱', align: 'center', minWidth: 300, showOverflowTooltip: true },
      {
        prop: 'isPro',
        label: '账号类型',
        align: 'center',
        sortable: true,
        width: 100,
        formatter: (row: any) => {
          if (row.isPro === 0) return <ElTag type="primary">grok</ElTag>;
          if (row.isPro === 1) return <ElTag type="success">super</ElTag>;
          return <ElTag type="info">Unknown</ElTag>;
        }
      },
      {
        prop: 'status',
        label: '账号状态',
        align: 'center',
        sortable: true,
        width: 100,
        formatter: (row: any) => (
          <ElSwitch v-model={row.status} active-value={1} inactive-value={0} onChange={() => changeSwitch(row)} />
        )
      },
      { prop: 'count', label: '日请求量', align: 'center', width: 100 },
      { prop: 'grok2', label: 'grok2', align: 'center', width: 100 },
      { prop: 'grok3', label: 'grok3', align: 'center', width: 100 },
      { prop: 'deepsearch', label: 'deepsearch', align: 'center', width: 100 },
      { prop: 'deepersearch', label: 'deepersearch', align: 'center', width: 100 },
      { prop: 'updatemodel', label: '模型更新时间', align: 'center', width: 180 },
      { prop: 'officialSession', label: '官方session', align: 'center', width: 300, showOverflowTooltip: true },
      { prop: 'remark', label: '备注', align: 'center', width: 200, showOverflowTooltip: true },
      { prop: 'createTime', label: '创建时间', align: 'center', sortable: true, width: 180 },
      { prop: 'updateTime', label: '更新时间', align: 'center', sortable: true, width: 180 },
      {
        prop: 'operate',
        label: '操作',
        align: 'center',
        fixed: 'right',
        width: 160,
        formatter: (row: any) => (
          <div class="flex-center">
            <ElButton type="primary" plain size="small" onClick={() => handleEdit(row)}>
              {$t('common.edit')}
            </ElButton>
            <ElPopconfirm title={$t('common.confirmDelete')} onConfirm={() => handleDelClick(row.id)}>
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small">
                    {$t('common.delete')}
                  </ElButton>
                )
              }}
            </ElPopconfirm>
          </div>
        )
      }
    ]
  });

const { checkedRowKeys, onBatchDeleted } = useTableOperate(data as any, getData);

const wrapperRef = ref<HTMLElement | null>(null);
const operateType = ref<OperateType>('create');
const { bool: visible, setTrue: openModal } = useBoolean();
const currentData = ref<any>(null);

function handleEdit(row: any) {
  operateType.value = 'edit';
  currentData.value = data.value.find(item => item.id === row.id);
  openModal();
}

async function changeSwitch(row: Grok) {
  try {
    await updateGrokSession(row);
    ElMessage.success('更新成功');
    getData();
  } catch {
    ElMessage.error('更新失败');
  }
}

async function handleDelClick(id: string) {
  try {
    await ElMessageBox.confirm('是否要删除该条数据， 继续?', '注意', {
      confirmButtonText: '确 定',
      cancelButtonText: '取 消',
      type: 'warning'
    });

    await removeGrokSessionBatch([id]);
    ElMessage({
      message: '删除成功',
      type: 'success',
      plain: true
    });
    getData();
  } catch {}
}

async function handleBatchDelete() {
  await removeGrokSessionBatch(checkedRowKeys.value);
  onBatchDeleted();
  ElMessage({
    message: '删除成功',
    type: 'success',
    plain: true
  });
}

function handleAdd() {
  operateType.value = 'create';
  currentData.value = null;
  openModal();
}
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <SubtypeSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <div class="flex items-center">
            <GrokTableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              @add="handleAdd"
              @delete="handleBatchDelete"
              @refresh="getData"
            />
          </div>
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="(selection: Grok[]) => checkedRowKeys = selection.map(item => item.id)"
        >
          <ElTableColumn
            v-for="col in columns"
            :key="col.prop"
            v-bind="col"
            :show-overflow-tooltip="col.showOverflowTooltip"
          />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
      <GrokOperateModel
        v-model:visible="visible"
        :operate-type="operateType"
        :data="currentData"
        @submitted="getData"
      />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>

package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.seven.share.common.pojo.dto.ModelLimitDto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: UserToken
 * @Description:
 * @Author: Seven
 * @Date: 2024/6/22
 */
@Data
@TableName(value = "chatgpt_user", autoResultMap = true)
public class ChatGptUserEntity implements Serializable {
    @Serial
    private final static long serialVersionUID = 1L;

    @TableId
    private Long id;

    @TableField("userToken")
    @NotBlank(message = "用户名不能为空")
    private String userToken;

    @TableField(value = "expireTime")
    @NotNull(message = "过期时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireTime;

    @TableField(value = "plusExpireTime", updateStrategy = FieldStrategy.ALWAYS)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime plusExpireTime;

    @TableField(value = "createTime", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "updateTime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField("deleted_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deletedAt;

    @TableField("isPlus")
    private Integer isPlus;

    @TableField("isAdmin")
    private Integer isAdmin;

    private String remark;

    private String email;

    @TableField(updateStrategy = FieldStrategy.NOT_EMPTY)
    private String password;
    /**
     * 1 是启用，0 是禁用
     */
    private Integer status;

    @TableField("subTypeId")
    private Integer subTypeId;

    @TableField("affCode")
    private String affCode;

    @TableField("inviterId")
    private Long inviterId;

    @TableField(exist = false)
    private String inviter;

    @TableField(value = "`limit`" ,updateStrategy = FieldStrategy.ALWAYS)
    private Long limit;

    @TableField(value = "per", updateStrategy = FieldStrategy.ALWAYS)
    private String per;

    @TableField(value = "carids", updateStrategy = FieldStrategy.ALWAYS)
    private String carids;

    @TableField(exist = false)
    private List<String> ids;

    /**
     * 用户类型：1：普通用户，2：免费用户
     */
    @TableField("userType")
    private Integer userType;

    @TableField(value = "invitor", exist = false)
    private String invitor;

    @TableField("dailyConversationCount")
    private Integer dailyConversationCount;


    @TableField("dailyClaudeConversationCount")
    private Integer dailyClaudeConversationCount;

    /**
     * 最后登录时间
     */
    @TableField("lastActiveTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastActiveTime;

    /**
     * 可提现金额
     */
    @TableField("affQuota")
    private String affQuota;

    /**
     * 推广已提现金额
     */
    @TableField("affHistoryQuota")
    private String affHistoryQuota;

    /**
     * 推广总金额
     */
    @TableField("affTotalQuota")
    private String affTotalQuota;

    /**
     * 推广人数
     */
    @TableField("affCount")
    private Integer affCount;

    /**
     * 收款码
     */
    @TableField("receiptFile")
    private String receiptFile;

    /**
     * 返佣比例
     */
    @TableField("affRate")
    private Double affRate;

    /**
     * claude过期时间
     */
    @TableField(value = "claudeExpireTime", updateStrategy = FieldStrategy.ALWAYS)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime claudeExpireTime;

    /**
     * claude pro过期时间
     */
    @TableField(value = "claudeProExpireTime", updateStrategy = FieldStrategy.ALWAYS)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime claudeProExpireTime;

    @TableField(value = "claudeLimit" ,updateStrategy = FieldStrategy.ALWAYS)
    private Long claudeLimit;

    @TableField(value = "claudePer", updateStrategy = FieldStrategy.ALWAYS)
    private String claudePer;

    @TableField("isPro")
    private Integer isPro;

    @TableField("clientIp")
    private String clientIp;

    @TableField("deviceId")
    private String deviceId;

    /**
     * 登录方式：1：账号密码，2：授权码，3：预留
     */
    @TableField("loginType")
    private Integer loginType;

    @TableField(value = "grokExpireTime", updateStrategy = FieldStrategy.ALWAYS)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime grokExpireTime;

    @TableField(value = "grokSuperExpireTime", updateStrategy = FieldStrategy.ALWAYS)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime grokSuperExpireTime;

    @TableField(value = "model_limits", typeHandler = JacksonTypeHandler.class)
    private Map<String, ModelLimitDto> modelLimits;

    @TableField("loginToken")
    private String loginToken;

    @Schema(description = "租户编号")
    private String tenantId;

    @Schema(description = "租户名称")
    @TableField(value = "tenant_name", exist = false)
    private String tenantName;
}

package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("chatgpt_aff_record")
public class ChatGptAffRecordEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("inviterId")
    private Long inviterId;

    @TableField("inviteeId")
    private Long inviteeId;

    @TableField("affMoney")
    private Double affMoney;

    // 订单类型 1-充值 2-兑换
    @TableField("orderType")
    private Integer orderType;

    // 状态 0-未处理 1-已处理
    @TableField("status")
    private Integer status;

    @TableField(value = "createTime", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}


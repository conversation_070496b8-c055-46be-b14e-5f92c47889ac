<script setup lang="ts">
import { $t } from '@/locales';

defineOptions({ name: 'SubtypeTableHeaderOperation' });

interface Props {
  disabledDelete?: boolean;
  loading?: boolean;
  showAdd?: boolean;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'add'): void;
  (e: 'batchAdd'): void;
  (e: 'delete'): void;
  (e: 'refresh'): void;
  (e: 'export'): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<UI.TableColumnCheck[]>('columns', {
  default: () => []
});

function add() {
  emit('add');
}

function batchAdd() {
  emit('batchAdd');
}

function batchDelete() {
  emit('delete');
}

function refresh() {
  emit('refresh');
}

function exportData() {
  emit('export');
}
</script>

<template>
  <ElSpace direction="horizontal" wrap justify="end" class="lt-sm:w-200px">
    <slot name="prefix"></slot>
    <slot name="default">
      <ElButton plain type="primary" @click="add">
        <template #icon>
          <icon-ic-round-plus class="text-icon" />
        </template>
        新增
      </ElButton>
      <ElButton plain type="success" @click="batchAdd">
        <template #icon>
          <icon-ic-round-plus class="text-icon" />
        </template>
        批量新增
      </ElButton>
      <ElPopconfirm :title="$t('common.confirmDelete')" @confirm="batchDelete">
        <template #reference>
          <ElButton type="danger" plain :disabled="disabledDelete">
            <template #icon>
              <icon-ic-round-delete class="text-icon" />
            </template>
            {{ $t('common.batchDelete') }}
          </ElButton>
        </template>
      </ElPopconfirm>
      <ElButton plain type="warning" @click="exportData">
        <template #icon>
          <icon-charm:download class="text-icon" />
        </template>
        导出数据
      </ElButton>
    </slot>
    <ElButton @click="refresh">
      <template #icon>
        <icon-mdi-refresh class="text-icon" :class="{ 'animate-spin': loading }" />
      </template>
      {{ $t('common.refresh') }}
    </ElButton>
    <TableColumnSetting v-model:columns="columns" />
    <slot name="suffix"></slot>
  </ElSpace>
</template>

<style scoped></style>

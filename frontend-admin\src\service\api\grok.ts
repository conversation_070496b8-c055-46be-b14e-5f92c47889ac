import { request } from '../request';

export function removeGrokSessionBatch(data: any) {
  return request({
    url: '/grok/session/delete',
    method: 'post',
    data
  });
}

export function fetchGrokSessionPage(params: any) {
  return request({
    url: '/grok/session/page',
    method: 'get',
    params
  });
}

export function updateGrokSession(data: any) {
  return request({
    url: '/grok/session/update',
    data,
    method: 'put'
  });
}

export function saveGrokSession(data: any) {
  return request({
    url: '/grok/session/create',
    data,
    method: 'post'
  });
}

export function removeGrokConversationsBatch(data: any) {
  return request({
    url: '/grok/conversations/delete',
    method: 'post',
    data
  });
}

export function fetchGrokConversationsPage(params: any) {
  return request({
    url: '/grok/conversations/page',
    method: 'get',
    params
  });
}

# 端口配置
server:
  port: 8888
  forward-headers-strategy: native

spring:
  application:
    name: expander-sass
  profiles:
    active: dev
  main:
    allow-circular-references: true
  servlet:
    multipart:
      # 单个文件的最大大小：10MB
      max-file-size: 100MB
      # 整个请求的最大大小：10MB
      max-request-size: 100MB

jwt:
  tokenHeader: Authorization #JWT存储的请求头
  secret: BfHiGpMfztZYwAEKBRGB #JWT加解密使用的密钥
  expiration: 1296000 #JWT的超期限时间(12*60*60) 24小时
  tokenHead: 'Bearer '  #JWT负载中拿到开头

#logging:
#  level:
#    org.springframework.security: DEBUG

api:
  security:
    normal-notallowed-apis:
      - /expander/**
      - /app/**
      - /api/**
    exclude-apis:
      - /expander-api/route/**
      - /expander-api/auth/getUserInfo
      - /expander-api/auth/code
      - /expander-api/auth/login
      - /expander-api/user/statistic
      - /expander-api/user/page
      - /expander-api/session/list
      - /expander-api/withdrawals/todoData
      - /expander-api/payLogs/dailyIncome
      - /expander-api/payLogs/months/income
      - /expander-api/payLogs/years/income
      - /client-api/** #前台接口
      - /expander/** #静态路径
      - /app/** # 静态路径
      - /expander-api/user/reset-pwd
      - /expander-api/images/** # 放行图片接口
      - /expander-api/systemManage/update # 放行修改管理员用户信息接口
      - /expander-api/auth/updatePassword # 放行修改管理员用户信息接口


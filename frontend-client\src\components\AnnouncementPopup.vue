<template>
  <el-dialog v-model="visible" title="登录公告" :width="dialogWidth" @close="close" align-center>
      <iframe :srcdoc="notice" style="width: 100%; border: none; min-height: calc(80vh);" ></iframe>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">关闭</el-button>
        <el-button type="primary" @click="dontShowFor24Hours">24小时不弹出</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
const props = defineProps({
  notice: String,
  default: ''
})

const visible = ref(false)

const dialogWidth = computed(() => {
  return window.innerWidth <= 768 ? '95%' : '70%';
})

const show = () => {
  visible.value = true;
}

const close = () => {
  visible.value = false;
}

const dontShowFor24Hours = () => {
  const now = new Date();
  const expireTime = now.getTime() + 24 * 60 * 60 * 1000; // 当前时间 + 24小时
  localStorage.setItem('announcementExpireTime', expireTime);
  close();
}

onMounted(() => {
  const expireTime = localStorage.getItem('announcementExpireTime');
  const now = new Date().getTime();
  if (!expireTime || now > expireTime) {
    show();
  }
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  flex-wrap: wrap;
  gap: 10px;
}

</style>

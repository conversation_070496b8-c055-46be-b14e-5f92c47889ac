package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.seven.share.common.pojo.dto.ExtraData;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: ChatGptPayConfig
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/12
 */
@Data
@TableName(value = "chatgpt_pay_config", autoResultMap = true)
public class ChatGptPayConfigEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    private String title;

    @TableField("appId")
    private String appId;

    @TableField("appKey")
    private String appKey;

    @TableField(value = "extraData", typeHandler = JacksonTypeHandler.class)
    private ExtraData extraData;

    @TableField("paymentsType")
    private Integer paymentsType;

    private Integer status;

    private Integer sort;

    @TableField("iconUrl")
    private String iconUrl;

    @TableField(value = "createTime", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "updateTime", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
}

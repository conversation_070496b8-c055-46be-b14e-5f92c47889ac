<script setup lang="ts">
import { $t } from '@/locales';

defineOptions({ name: 'CdkeyTableHeaderOperation' });

interface Props {
  disabledDelete?: boolean;
  loading?: boolean;
  showAdd?: boolean;
  showDelete?: boolean;
  showExport?: boolean;
}

defineProps<Props>();

interface Emits {
  (e: 'add'): void;
  (e: 'delete'): void;
  (e: 'refresh'): void;
  (e: 'export'): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<UI.TableColumnCheck[]>('columns', {
  default: () => []
});

function add() {
  emit('add');
}

function exportCdkey() {
  emit('export');
}

function batchDelete() {
  emit('delete');
}

function refresh() {
  emit('refresh');
}
</script>

<template>
  <ElSpace direction="horizontal" wrap justify="end" class="lt-sm:w-200px">
    <slot name="prefix"></slot>
    <slot name="default">
      <ElButton v-show="showAdd" plain type="success" @click="add">
        <template #icon>
          <icon-ic-round-plus class="text-icon" />
        </template>
        批量新增
      </ElButton>
      <ElButton v-show="showExport" plain type="primary" @click="exportCdkey">
        <template #icon>
          <icon-ic-round-plus class="text-icon" />
        </template>
        导出
      </ElButton>
      <ElPopconfirm :title="$t('common.confirmDelete')" @confirm="batchDelete">
        <template #reference>
          <ElButton v-show="showDelete" type="danger" plain :disabled="disabledDelete">
            <template #icon>
              <icon-ic-round-delete class="text-icon" />
            </template>
            {{ $t('common.batchDelete') }}
          </ElButton>
        </template>
      </ElPopconfirm>
    </slot>
    <ElButton @click="refresh">
      <template #icon>
        <icon-mdi-refresh class="text-icon" :class="{ 'animate-spin': loading }" />
      </template>
      {{ $t('common.refresh') }}
    </ElButton>
    <TableColumnSetting v-model:columns="columns" />
    <slot name="suffix"></slot>
  </ElSpace>
</template>

<style scoped></style>

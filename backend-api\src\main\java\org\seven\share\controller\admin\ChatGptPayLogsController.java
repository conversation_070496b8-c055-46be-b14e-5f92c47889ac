package org.seven.share.controller.admin;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.R;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.entity.ChatGptEPayLogsEntity;
import org.seven.share.service.ChatGptPayLogsService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ChatGptPayLogsController
 * @Description: 支付记录控制器
 * @Author: Seven
 * @Date: 2024/8/8
 */
@RestController
@RequestMapping("/expander-api/payLogs")
public class ChatGptPayLogsController {
    @Resource
    private ChatGptPayLogsService chatGptPayLogsService;

    @GetMapping("/page")
    @SysLogInterface(title = "查询支付记录", businessType = BusinessType.QUERY)
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer current,
                  @RequestParam(value = "size", defaultValue = "10") Integer size,
                  String username,
                  String tradeNo,
                  String status){
        LambdaQueryWrapper<ChatGptEPayLogsEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotEmpty(username), ChatGptEPayLogsEntity::getUserToken, username);
        queryWrapper.like(StrUtil.isNotEmpty(tradeNo), ChatGptEPayLogsEntity::getTradeNo, tradeNo);
        queryWrapper.eq(StrUtil.isNotEmpty(status), ChatGptEPayLogsEntity::getStatus, status);
        queryWrapper.orderByDesc(ChatGptEPayLogsEntity::getUpdateTime);
        Page<ChatGptEPayLogsEntity> pageInfo = chatGptPayLogsService.page(new Page<>(current, size), queryWrapper);
        return R.ok(pageInfo);
    }

    @PutMapping("/change-status/{id}")
    @SysLogInterface(title = "手工处理订单", businessType = BusinessType.OTHER)
    public R changeStatus(@PathVariable("id") String id){
        return chatGptPayLogsService.changeStatus(id) ? R.ok() : R.error();
    }

    @DeleteMapping("/delete")
    @SysLogInterface(title = "删除支付记录", businessType = BusinessType.DELETE)
    public R del(@RequestBody List<String> ids){
        return chatGptPayLogsService.removeBatchByIds(ids) ? R.ok() : R.error();
    }

    @GetMapping("dailyIncome")
    @SysLogInterface(title = "统计日收入", businessType = BusinessType.QUERY)
    public R getLast15DaysIncome(){
        List<Map<String, Object>> data =  chatGptPayLogsService.getLast15DaysIncome();
        return R.ok(data);
    }

    // 新增：最近12个月收入接口
    @GetMapping("/months/income")
    @SysLogInterface(title = "最近12个月收入接口", businessType = BusinessType.QUERY)
    public R getLast12MonthsIncome() {
        List<Map<String, Object>> data = chatGptPayLogsService.getLast12MonthsIncome();
        return R.ok(data);
    }

    // 新增：年度收入接口
    @GetMapping("/years/income")
    @SysLogInterface(title = "查询年度收入", businessType = BusinessType.QUERY)
    public R getYearlyIncome() {
        List<Map<String, Object>> data = chatGptPayLogsService.getYearlyIncome();
        return R.ok(data);
    }
}

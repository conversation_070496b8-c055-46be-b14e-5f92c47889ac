package org.seven.share.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.seven.share.common.pojo.entity.ChatGptEPayLogsEntity;

import java.util.List;
import java.util.Map;

/**
 * @InterfaceName: ChatGptPayLogsDao
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/5
 */
@Mapper
public interface ChatGptPayLogsMapper extends BaseMapper<ChatGptEPayLogsEntity> {
    @Select("SELECT DATE(updateTime) AS xAxis, ROUND(SUM(money), 2) AS yAxis " +
            "FROM chatgpt_epaylogs " +
            "WHERE updateTime >= CURDATE() - INTERVAL 30 DAY " +
            "AND status = 'success'" +
            "GROUP BY DATE(updateTime) " +
            "ORDER BY DATE(updateTime)")
    List<Map<String, Object>> getDailyIncome();

    // 月度收入查询（最近12个月）
    @Select("SELECT DATE_FORMAT(updateTime, '%Y-%m') AS xAxis, ROUND(SUM(money), 2) AS yAxis " +
            "FROM chatgpt_epaylogs " +
            "WHERE updateTime >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH) " +
            "AND status = 'success' " +
            "GROUP BY DATE_FORMAT(updateTime, '%Y-%m') " +
            "ORDER BY xAxis")
    List<Map<String, Object>> getMonthlyIncome();

    // 年度收入查询（最近5年）
    @Select("SELECT DATE_FORMAT(updateTime, '%Y') AS xAxis, ROUND(SUM(money), 2) AS yAxis " +
            "FROM chatgpt_epaylogs " +
            "WHERE updateTime >= DATE_SUB(CURDATE(), INTERVAL 5 YEAR) " +
            "AND status = 'success' " +
            "GROUP BY DATE_FORMAT(updateTime, '%Y') " +
            "ORDER BY xAxis")
    List<Map<String, Object>> getYearlyIncome();

    Map<String, Object> getOverallPayStats();
}


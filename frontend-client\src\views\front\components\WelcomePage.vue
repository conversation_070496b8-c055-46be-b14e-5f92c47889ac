<template>
  <div class="welcome-page">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            欢迎来到
            <span class="gradient-text">{{ siteName }}</span>
          </h1>
          <p class="hero-description">
            体验最先进的AI助手服务，让智能科技为您的工作和生活带来无限可能
          </p>
          <div class="hero-actions">
            <button class="primary-button" @click="handleGetStarted">
              <Rocket class="w-5 h-5" />
              立即开始
            </button>
            <button class="secondary-button" @click="handleLearnMore">
              <BookOpen class="w-5 h-5" />
              了解更多
            </button>
          </div>
        </div>
        <div class="hero-visual">
          <div class="floating-cards">
            <div class="feature-card card-1">
              <Zap class="w-8 h-8 text-yellow-500" />
              <span class="card-text">极速响应</span>
            </div>
            <div class="feature-card card-2">
              <Shield class="w-8 h-8 text-green-500" />
              <span class="card-text">安全可靠</span>
            </div>
            <div class="feature-card card-3">
              <Globe class="w-8 h-8 text-blue-500" />
              <span class="card-text">全球服务</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 功能特性 -->
    <section class="features-section">
      <div class="section-header">
        <h2 class="section-title">核心功能</h2>
        <p class="section-description">探索我们为您提供的强大AI服务</p>
      </div>
      
      <div class="features-grid">
        <div class="feature-item" v-for="feature in features" :key="feature.id">
          <div class="feature-icon">
            <component :is="feature.icon" class="w-8 h-8" />
          </div>
          <h3 class="feature-title">{{ feature.title }}</h3>
          <p class="feature-description">{{ feature.description }}</p>
          <button class="feature-button" @click="handleFeatureClick(feature.action)">
            {{ feature.buttonText }}
            <ArrowRight class="w-4 h-4" />
          </button>
        </div>
      </div>
    </section>

    <!-- 统计数据 -->
    <section class="stats-section">
      <div class="stats-grid">
        <div class="stat-item" v-for="stat in stats" :key="stat.id">
          <div class="stat-number">{{ stat.number }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
      </div>
    </section>

    <!-- 快速开始 -->
    <section class="quick-start-section">
      <div class="quick-start-content">
        <h2 class="quick-start-title">准备好开始了吗？</h2>
        <p class="quick-start-description">
          只需几个简单步骤，即可开始使用我们的AI服务
        </p>
        <div class="quick-start-steps">
          <div class="step" v-for="(step, index) in quickStartSteps" :key="step.id">
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <h3 class="step-title">{{ step.title }}</h3>
              <p class="step-description">{{ step.description }}</p>
            </div>
          </div>
        </div>
        <button class="cta-button" @click="handleGetStarted">
          <Play class="w-5 h-5" />
          开始体验
        </button>
      </div>
    </section>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useSiteStore } from '@/store/modules/site'
import { 
  Rocket, 
  BookOpen, 
  Zap, 
  Shield, 
  Globe, 
  ArrowRight,
  MessageSquare,
  Image as ImageIcon,
  Code,
  Sparkles,
  Play,
  Users,
  Clock,
  Award
} from 'lucide-vue-next'

const router = useRouter()
const siteStore = useSiteStore()

const siteName = computed(() => siteStore.siteName || 'AI助手平台')

const features = [
  {
    id: 1,
    icon: MessageSquare,
    title: '智能对话',
    description: '与最先进的AI模型进行自然对话，获得专业的回答和建议',
    buttonText: '开始对话',
    action: 'chat'
  },
  {
    id: 2,
    icon: ImageIcon,
    title: 'AI绘图',
    description: '使用AI技术创作精美的图像和艺术作品',
    buttonText: '开始创作',
    action: 'draw'
  },
  {
    id: 3,
    icon: Code,
    title: '代码助手',
    description: '获得编程帮助，代码审查和技术问题解答',
    buttonText: '编程助手',
    action: 'code'
  },
  {
    id: 4,
    icon: Sparkles,
    title: '创意写作',
    description: 'AI协助您进行创意写作，文案创作和内容生成',
    buttonText: '开始写作',
    action: 'write'
  }
]

const stats = [
  { id: 1, number: '10K+', label: '活跃用户' },
  { id: 2, number: '1M+', label: '对话次数' },
  { id: 3, number: '99.9%', label: '服务可用性' },
  { id: 4, number: '24/7', label: '全天候服务' }
]

const quickStartSteps = [
  {
    id: 1,
    title: '注册账户',
    description: '创建您的专属账户，享受个性化服务'
  },
  {
    id: 2,
    title: '选择服务',
    description: '从多种AI服务中选择最适合您的功能'
  },
  {
    id: 3,
    title: '开始使用',
    description: '立即开始与AI交互，体验智能服务'
  }
]

const handleGetStarted = () => {
  // 触发父组件切换到实例页面
  emit('switchTab', 'dashboard')
}

const handleLearnMore = () => {
  // 切换到说明页面
  emit('switchTab', 'instructions')
}

const handleFeatureClick = (action) => {
  switch (action) {
    case 'chat':
      emit('switchTab', 'dashboard')
      break
    case 'draw':
      // 如果有绘图功能，切换到绘图
      emit('switchTab', 'dashboard')
      break
    case 'code':
      emit('switchTab', 'dashboard')
      break
    case 'write':
      emit('switchTab', 'dashboard')
      break
    default:
      emit('switchTab', 'dashboard')
  }
}

const emit = defineEmits(['switchTab'])
</script>

<style scoped>
.welcome-page {
  min-height: calc(100vh - 8rem);
}

/* 英雄区域 */
.hero-section {
  padding: 4rem 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
  border-radius: 2rem;
  margin-bottom: 4rem;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: #1f2937;
}

.dark .hero-title {
  color: #f9fafb;
}

.gradient-text {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hero-description {
  font-size: 1.25rem;
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.dark .hero-description {
  color: #d1d5db;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.primary-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border: none;
  border-radius: 1rem;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px -3px rgba(59, 130, 246, 0.3);
}

.primary-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px -3px rgba(59, 130, 246, 0.4);
}

.secondary-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: transparent;
  color: #3b82f6;
  border: 2px solid #3b82f6;
  border-radius: 1rem;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondary-button:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-2px);
}

/* 浮动卡片 */
.floating-cards {
  position: relative;
  height: 400px;
}

.feature-card {
  position: absolute;
  background: white;
  border-radius: 1.5rem;
  padding: 1.5rem;
  box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.dark .feature-card {
  background: #1f2937;
  border-color: rgba(59, 130, 246, 0.2);
}

.card-1 {
  top: 20px;
  left: 20px;
  animation: float 6s ease-in-out infinite;
}

.card-2 {
  top: 120px;
  right: 40px;
  animation: float 6s ease-in-out infinite 2s;
}

.card-3 {
  bottom: 40px;
  left: 60px;
  animation: float 6s ease-in-out infinite 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.card-text {
  font-weight: 600;
  color: #374151;
}

.dark .card-text {
  color: #d1d5db;
}

/* 功能特性 */
.features-section {
  margin-bottom: 4rem;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1f2937;
}

.dark .section-title {
  color: #f9fafb;
}

.section-description {
  font-size: 1.1rem;
  color: #6b7280;
}

.dark .section-description {
  color: #d1d5db;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-item {
  background: white;
  border-radius: 1.5rem;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.dark .feature-item {
  background: #1f2937;
  border-color: rgba(59, 130, 246, 0.2);
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px -3px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.dark .feature-title {
  color: #f9fafb;
}

.feature-description {
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.dark .feature-description {
  color: #d1d5db;
}

.feature-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.feature-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px -3px rgba(59, 130, 246, 0.3);
}

/* 统计数据 */
.stats-section {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-radius: 2rem;
  padding: 3rem 2rem;
  margin-bottom: 4rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: #3b82f6;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #d1d5db;
  font-size: 1.1rem;
}

/* 快速开始 */
.quick-start-section {
  text-align: center;
}

.quick-start-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1f2937;
}

.dark .quick-start-title {
  color: #f9fafb;
}

.quick-start-description {
  font-size: 1.1rem;
  color: #6b7280;
  margin-bottom: 3rem;
}

.dark .quick-start-description {
  color: #d1d5db;
}

.quick-start-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  text-align: left;
}

.step-number {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.step-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.dark .step-title {
  color: #f9fafb;
}

.step-description {
  color: #6b7280;
  line-height: 1.6;
}

.dark .step-description {
  color: #d1d5db;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1.25rem 2.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border: none;
  border-radius: 1rem;
  font-weight: 600;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 15px 35px -3px rgba(59, 130, 246, 0.3);
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 20px 45px -3px rgba(59, 130, 246, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .floating-cards {
    height: 200px;
  }
  
  .feature-card {
    position: static;
    margin-bottom: 1rem;
  }
  
  .step {
    text-align: center;
    flex-direction: column;
  }
}
</style>

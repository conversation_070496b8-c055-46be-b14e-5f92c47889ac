package org.seven.share.common.pojo.image;

import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class ImageGenerationResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long created;
    private List<ImageData> data;

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImageData implements Serializable{
        @Serial
        private static final long serialVersionUID = 1L;
        private String url; // 图像 URL
        private String b64_json; // Base64 编码的图像（如果 response_format 为 b64_json）

    }
}

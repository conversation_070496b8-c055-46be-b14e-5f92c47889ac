<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>密码重置验证码</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 80%;
            width: 100%;
        }
        h1 {
            color: #333;
        }
        .code-container {
            background-color: #333;
            color: white;
            font-size: 24px;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .code {
            flex-grow: 1;
            text-align: center;
        }
        p {
            color: #666;
        }
        .warning {
            color: #ff4500;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>密码重置验证码</h1>
    <p>我们收到了重置您账户密码的请求。请使用以下验证码来完成密码重置流程：</p>
    <div class="code-container">
        <div class="code" id="verificationCode" th:text="${verificationCode}">123456</div>
    </div>
    <p class="warning">注意：此验证码将在30分钟后过期</p>
    <p>请在密码重置页面输入此验证码以确认您的身份。如果您没有请求重置密码，请忽略此邮件。</p>
</div>
</body>
</html>

package org.seven.share.controller.admin;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.Result;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.dto.SysTenantDto;
import org.seven.share.common.pojo.entity.SysTenant;
import org.seven.share.service.SysTenantService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "SysTenantController", description = "租户管理控制层")
@RequestMapping("/expander-api/tenant")
public class SysTenantController {

    @Resource
    private SysTenantService sysTenantService;


    @Operation(summary = "获取租户列表")
    @Parameters({
            @Parameter(name = "current", description = "当前页", required = true, example = "1"),
            @Parameter(name = "size", description = "每页显示条数", required = true, example = "10"),
    })
    @SysLogInterface(title = "分页查询租户", businessType = BusinessType.OTHER)
    @GetMapping("/getTenantPage")
    public Result<IPage<SysTenant>> getTenantPage(@Parameter(hidden = true) @RequestParam Map<String, Object> params) {
        IPage<SysTenant> sysTenant =  sysTenantService.getTenantPage(params);
        return Result.success(sysTenant);
    }

    @PostMapping("/add")
    @Operation(summary = "添加租户")
    @SysLogInterface(title = "添加租户", businessType = BusinessType.INSERT)
    public Result<String> addTenant(@RequestBody SysTenantDto sysTenantDto) {
        return sysTenantService.addTenant(sysTenantDto);
    }

    @PutMapping("/update")
    @Operation(summary = "修改租户")
    @SysLogInterface(title = "修改租户", businessType = BusinessType.UPDATE)
    public Result<String> updateTenant(@RequestBody SysTenantDto sysTenantDto) {
        return sysTenantService.updateTenant(sysTenantDto);
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除租户")
    @SysLogInterface(title = "删除租户", businessType = BusinessType.DELETE)
    public Result<String> deleteTenant(@PathVariable Long id) {
        return sysTenantService.deleteTenant(id);
    }

    @DeleteMapping("/deleteByIds")
    @Operation(summary = "批量删除租户")
    @SysLogInterface(title = "批量删除租户", businessType = BusinessType.DELETE)
    public Result<String> batchDeleteTenant(@RequestBody List<String> ids) {

        return sysTenantService.batchDeleteTenant(ids);
    }

    @Operation(summary = "获取租户列表")
    @SysLogInterface(title = "查询所有租户", businessType = BusinessType.OTHER)
    @GetMapping("/getTenantList")
    public Result<List<SysTenant>> getTenantList() {
        List<SysTenant> sysTenant =  sysTenantService.getTenantList();
        return Result.success(sysTenant);
    }
}

<template>
  <div class="space-y-6">
    <!-- 在移动端改为单列布局 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
      <div class="space-y-4 md:space-y-6">
        <div class="space-y-2 relative">
          <label 
            for="prompt"
            class="flex items-center text-base md:text-lg font-medium text-slate-800 dark:text-slate-200"
          >
            <MessageSquare class="w-4 h-4 md:w-5 md:h-5 mr-2 text-black dark:text-white" />
            {{ t('draw.generator.prompt') }}
          </label>
          <div class="relative">
            <textarea
              id="prompt"
              v-model="prompt"
              :placeholder="t('draw.generator.promptPlaceholder')"
              class="min-h-[120px] md:min-h-[150px] text-sm md:text-base resize-y bg-slate-50 dark:bg-slate-900 border-slate-200 dark:border-slate-700 focus:border-black dark:focus:border-white focus:ring-black dark:focus:ring-white pr-10 w-full rounded-md p-2"
            ></textarea>
            <button
              @click="toggleSuggestions"
              class="absolute right-2 top-2 text-slate-400 hover:text-black dark:hover:text-white p-1 rounded-md"
            >
              <Sparkles class="h-4 w-4" />
            </button>
          </div>

          <div 
            v-if="showSuggestions"
            class="absolute z-10 mt-1 w-full bg-white dark:bg-slate-800 rounded-md shadow-lg border border-slate-200 dark:border-slate-700 py-1"
          >
            <button
              v-for="(suggestion, index) in PROMPT_SUGGESTIONS"
              :key="index"
              class="w-full text-left px-4 py-2 text-xs md:text-sm hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
              @click="applyPromptSuggestion(suggestion)"
            >
              {{ suggestion }}
            </button>
          </div>
        </div>

        <div class="grid grid-cols-3 gap-3 md:gap-4">
          <div class="space-y-1 md:space-y-2">
            <label for="quality" class="text-xs md:text-sm font-medium text-slate-600 dark:text-slate-400">
              {{ t('draw.editor.imageQuality') }}
            </label>
            <select
              id="quality"
              v-model="imageQuality"
              class="w-full text-xs md:text-sm bg-slate-50 dark:bg-slate-900 border-slate-200 dark:border-slate-700 rounded-md p-2"
            >
              <option value="low">{{ t('draw.editor.quality.low') }}</option>
              <option value="medium">{{ t('draw.editor.quality.medium') }}</option>
              <option value="high">{{ t('draw.editor.quality.high') }}</option>
            </select>
          </div>

          <div class="space-y-1 md:space-y-2">
            <label for="size" class="text-xs md:text-sm font-medium text-slate-600 dark:text-slate-400">
              {{ t('draw.editor.imageSize') }}
            </label>
            <select
              id="size"
              v-model="imageSize"
              class="w-full text-xs md:text-sm bg-slate-50 dark:bg-slate-900 border-slate-200 dark:border-slate-700 rounded-md p-2"
            >
              <option value="1024x1024">{{ t('draw.editor.size.square') }}</option>
              <option value="1024x1536">{{ t('draw.editor.size.portrait') }}</option>
              <option value="1536x1024">{{ t('draw.editor.size.landscape') }}</option>
            </select>
          </div>
          <div v-if="drawCount > 1" class="space-y-1 md:space-y-2">
          <label for="edit-count" class="text-xs md:text-sm font-medium text-slate-600 dark:text-slate-400">
            {{ t('draw.editor.generateCount') }}
          </label>
          <select
            id="edit-count"
            v-model="imageCount"
            class="w-full text-xs md:text-sm bg-slate-50 dark:bg-slate-900 border-slate-200 dark:border-slate-700 rounded-md p-2"
          >
            <option v-for="n in drawCount" :key="n" :value="n">{{ n }}{{ t('common.unit.piece') }}</option>
          </select>
        </div>
        </div>

        <button
          @click="handleGenerateImage"
          :disabled="isGenerating || !prompt"
          class="w-full h-9 text-sm font-medium bg-black dark:bg-white text-white dark:text-black rounded-md flex items-center justify-center hover:bg-gray-800 dark:hover:bg-gray-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <template v-if="isGenerating">
            <Loader2 class="w-4 h-4 mr-2 animate-spin" />
            {{ t('draw.generator.generating') }}
          </template>
          <template v-else>
            <Wand2 class="w-4 h-4 mr-2" />
            {{ t('draw.generator.generateButton') }}
          </template>
        </button>

        <div 
          v-if="statusMessage"
          class="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-800 dark:text-gray-200 p-3 md:p-4 rounded-md text-xs md:text-sm"
        >
          <div class="flex items-center">
            <Loader2 v-if="isGenerating" class="w-4 h-4 mr-2 animate-spin text-gray-600 dark:text-gray-400" />
            <div v-else class="w-4 h-4 mr-2">✓</div>
            {{ statusMessage }}
          </div>
        </div>

        <div 
          v-if="error"
          class="bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 text-red-800 dark:text-red-200 p-3 md:p-4 rounded-md text-xs md:text-sm overflow-hidden"
        >
          <div class="flex items-start gap-2">
            <span class="flex-shrink-0 text-lg">⚠️</span>
            <span class="break-all">{{ error }}</span>
          </div>
        </div>
      </div>

      <div class="flex flex-col justify-center items-center rounded-xl md:rounded-2xl bg-slate-50 dark:bg-slate-900 p-4 md:p-8 min-h-[200px] md:min-h-[300px]">
        <div v-if="generatedImages.length > 0" class="w-full h-full">
          <div class="relative">
            <!-- 图片展示区域 -->
            <div class="relative rounded-md overflow-hidden bg-slate-100 dark:bg-slate-800 flex items-center justify-center">
              <img
                :src="generatedImages[currentImageIndex]"
                :alt="'Generated ' + (currentImageIndex + 1)"
                class="w-full h-auto object-contain rounded-md shadow-md"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent rounded-md flex items-end justify-between p-4">
                <span class="bg-white/90 text-slate-800 px-2 py-1 rounded-md text-xs md:text-sm">
                  {{ imageSize.replace("x", " × ") }}
                </span>
                <div class="flex gap-2">
                  <button
                    @click="viewOriginalImage(generatedImages[currentImageIndex])"
                    class="h-8 w-8 md:h-9 md:w-9 rounded-full bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm shadow-lg hover:scale-105 transition-all duration-200 flex items-center justify-center"
                    title="查看原图"
                  >
                    <Search class="h-3.5 w-3.5 md:h-4 md:w-4" />
                  </button>

                  <button
                    @click="downloadImage(generatedImages[currentImageIndex])"
                    class="h-8 w-8 md:h-9 md:w-9 rounded-full bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm shadow-lg hover:scale-105 transition-all duration-200 flex items-center justify-center"
                    title="下载图片"
                  >
                    <Download class="h-3.5 w-3.5 md:h-4 md:w-4" />
                  </button>
                </div>
              </div>
            </div>

            <!-- 导航按钮 -->
            <div v-if="generatedImages.length > 1" class="absolute inset-0 flex items-center justify-between pointer-events-none">
              <button
                @click="prevImage"
                :disabled="currentImageIndex === 0"
                class="pointer-events-auto h-8 w-8 md:h-10 md:w-10 rounded-full bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm shadow-lg hover:scale-105 transition-all duration-200 flex items-center justify-center ml-2"
                :class="{ 'opacity-50 cursor-not-allowed': currentImageIndex === 0 }"
              >
                <ChevronLeft class="h-5 w-5" />
              </button>
              <button
                @click="nextImage"
                :disabled="currentImageIndex === generatedImages.length - 1"
                class="pointer-events-auto h-8 w-8 md:h-10 md:w-10 rounded-full bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm shadow-lg hover:scale-105 transition-all duration-200 flex items-center justify-center mr-2"
                :class="{ 'opacity-50 cursor-not-allowed': currentImageIndex === generatedImages.length - 1 }"
              >
                <ChevronRight class="h-5 w-5" />
              </button>
            </div>

            <!-- 缩略图预览 -->
            <div v-if="generatedImages.length > 1" class="mt-4 flex justify-center gap-2">
              <button
                v-for="(_, index) in generatedImages"
                :key="index"
                @click="currentImageIndex = index"
                class="w-12 h-12 rounded-md overflow-hidden border-2 transition-all duration-200"
                :class="[
                  currentImageIndex === index
                    ? 'border-black dark:border-white scale-110'
                    : 'border-transparent hover:border-black dark:hover:border-white'
                ]"
              >
                <img
                  :src="generatedImages[index]"
                  :alt="'Thumbnail ' + (index + 1)"
                  class="w-full h-full object-cover"
                />
              </button>
            </div>

            <!-- 图片计数器 -->
            <div v-if="generatedImages.length > 1" class="mt-2 text-center text-sm text-slate-500 dark:text-slate-400">
              {{ t('draw.editor.imageCount', { current: currentImageIndex + 1, total: generatedImages.length }) }}
            </div>
          </div>
        </div>
        <div v-else class="flex flex-col items-center justify-center text-center">
          <div class="w-16 h-16 md:w-20 md:h-20 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-3 md:mb-4">
            <ImageIcon class="h-8 w-8 md:h-10 md:w-10 text-black dark:text-white" />
          </div>
          <h3 class="text-base md:text-lg font-medium text-slate-900 dark:text-slate-200 mb-1 md:mb-2">{{ t('draw.editor.imageWillShowHere') }}</h3>
          <p class="text-xs md:text-sm text-slate-500 dark:text-slate-400 max-w-xs">
            {{ t('draw.generator.tips.0') }}
          </p>
          <p class="text-xs md:text-sm text-gray-500 dark:text-gray-400 max-w-xs">
            {{ t('draw.generator.tips.3') }}
          </p>
        </div>
      </div>
    </div>

    <div 
      v-if="generatedImages.length > 0 && prompt"
      class="bg-slate-50 dark:bg-slate-900 border-l-4 border-black dark:border-white rounded-md p-3 md:p-4 shadow-md transition-all duration-300 hover:translate-x-1"
    >
      <div class="flex items-start">
        <MessageSquare class="w-4 h-4 md:w-5 md:h-5 mr-2 md:mr-3 text-black dark:text-white mt-0.5 flex-shrink-0" />
        <p class="text-slate-700 dark:text-slate-300 text-xs md:text-sm leading-relaxed">{{ prompt }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onUnmounted, computed, watchEffect } from 'vue';
import { MessageSquare, Loader2, Wand2, Search, Download, Sparkles, ImageIcon, ChevronLeft, ChevronRight } from 'lucide-vue-next';
import { generateImage, fetchImageStatus } from '@/api/image';
import { useSiteStore } from '@/store/modules/site';
import { useI18n } from 'vue-i18n';
const emit = defineEmits(['error', 'image-generated']);
const { t } = useI18n();
const siteStore = useSiteStore();

// 状态
const prompt = ref('');
const imageQuality = ref('medium');
const imageSize = ref('1024x1024');
const generatedImages = ref([]);
const isGenerating = ref(false);
const error = ref('');
const imageCount = ref(1)
const statusMessage = ref('');
const taskId = ref(null);
const showSuggestions = ref(false);
const currentImageIndex = ref(0);
let pollingInterval = null;
let timeout = null;
const drawCount = computed(() => {
  return siteStore.drawCount ? Number(siteStore.drawCount) : 1;
});

const drawModel = computed(() => {
  return siteStore.drawModel && siteStore.drawModel !== '' ? siteStore.drawModel : 'gpt-image-1';
});

// 监听drawCount变化，自动设置imageCount
// 确保当drawCount为1时，imageCount也为1
// 当drawCount>1时，保持imageCount的当前值，除非它大于drawCount
// watchEffect(() => {
//   if (drawCount.value === 1) {
//     imageCount.value = 1;
//   } else if (imageCount.value > drawCount.value) {
//     imageCount.value = drawCount.value;
//   }
// });

// 提示词建议
const PROMPT_SUGGESTIONS = computed(() => [
  t('draw.generator.suggestions.cat'),
  t('draw.generator.suggestions.cyberpunk'),
  t('draw.generator.suggestions.forest'),
  t('draw.generator.suggestions.landscape'),
  t('draw.generator.suggestions.portrait'),
]);

// 方法
const handleGenerateImage = async () => {
  if (!prompt.value) {
    error.value = t('draw.generator.errors.promptRequired');
    emit('error', t('draw.generator.errors.promptRequired'));
    return;
  }

  error.value = "";
  statusMessage.value = t('draw.generator.status.connecting');
  isGenerating.value = true;

  try {
    statusMessage.value = t('draw.generator.status.generating');
    const data = {
      prompt: prompt.value,
      quality: imageQuality.value,
      size: imageSize.value,
      model: drawModel.value,
      n: imageCount.value,
      response_format: 'url',
    };

    const id = await generateImage(data);
    console.log("任务ID:", id);

    if (id) {
      taskId.value = id;
      startPollingStatus();
    } else {
      throw new Error(t('draw.generator.errors.noTaskId'));
    }
  } catch (err) {
    console.error("生成图片错误:", err);
    
    if (err.response) {
      console.error("错误响应:", err.response.data);
      error.value = err.response.data?.error?.message || `请求失败 (${err.response.status})`;
    } else if (err.request) {
      console.error("无响应:", err.request);
      error.value = "服务器没有响应，请检查网络连接或服务器状态";
    } else {
      error.value = err.message || "图像生成失败";
    }
    
    statusMessage.value = "";
    isGenerating.value = false;
    emit('error', error.value);
  }
};

const startPollingStatus = async () => {
  if (pollingInterval) {
    clearInterval(pollingInterval);
    pollingInterval = null;
  }

  statusMessage.value = t('draw.generator.status.processing');
  
  
  // 设置轮询间隔，每5秒检查一次
  pollingInterval = setInterval(async () => {
    await checkImageStatus();
  }, 5000);
  
  // 设置最长轮询时间为5分钟
  timeout = setTimeout(() => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      pollingInterval = null;
      
      if (isGenerating.value) {
        isGenerating.value = false;
        error.value = t('draw.generator.status.timeout');
        statusMessage.value = "";
        emit('error', t('draw.generator.status.timeout'));
      }
    }
  }, 300000);
};

const checkImageStatus = async () => {
  if (!taskId.value) return;
  
  try {
    const response = await fetchImageStatus(taskId.value);
    
    if (response) {
      const status = response.status;
      const result = response.result;
      console.log("状态:", status);
      console.log("结果:", result);
      
      if (status === "COMPLETED") {
        if (result && result.data && Array.isArray(result.data)) {
          // 任务完成，显示所有图片
          generatedImages.value = result.data.map(item => item.url);
          statusMessage.value = t('draw.generator.status.success');
          
          // 发出图片生成成功事件
          emit('image-generated', {
            urls: generatedImages.value,
            prompt: prompt.value,
            timestamp: new Date(),
            type: 'generated',
            size: imageSize.value,
            quality: imageQuality.value
          });
        } else {
          statusMessage.value = t('draw.generator.status.failed');
        }
      
        // 清除轮询
        if (pollingInterval) {
          clearInterval(pollingInterval);
          pollingInterval = null;
        }
        
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }
        
        isGenerating.value = false;
        taskId.value = null;
        
        // 3秒后清除状态消息
        setTimeout(() => {
          statusMessage.value = "";
        }, 3000);
      } else if (status === "FAILED") {
        // 任务失败
        error.value = response?.errorMessage || t('draw.generator.errors.generateFailed');
        statusMessage.value = "";
        emit('error', error.value);
        
        // 清除轮询
        if (pollingInterval) {
          clearInterval(pollingInterval);
          pollingInterval = null;
        }
        
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }
        
        isGenerating.value = false;
        taskId.value = null;
      } else {
        // 仍在处理中
        statusMessage.value = t('draw.generator.status.waiting');
      }
    } else {
      throw new Error("获取状态失败");
    }
  } catch (err) {
    console.error("检查状态错误:", err);
    // 出错时不停止轮询，让超时机制处理
    statusMessage.value = t('draw.generator.status.checking');
  }
};

const toggleSuggestions = () => {
  showSuggestions.value = !showSuggestions.value;
};

const applyPromptSuggestion = (suggestion) => {
  prompt.value = suggestion;
  showSuggestions.value = false;
};

const viewOriginalImage = (url) => {
  if (url) {
    window.open(url, "_blank");
  }
};


const downloadImage = async (url) => {
  try {
    // 强制将 HTTP 转换为 HTTPS（仅当确认服务支持 HTTPS 时使用）
    let secureUrl = url;
    if (url.startsWith('http://')) {
      secureUrl = url.replace('http://', 'https://');
      console.warn(`将 HTTP URL 转换为 HTTPS: ${secureUrl}`);
    }

    // 获取文件名
    const fileName = secureUrl.split('/').pop() || `ai-image-${new Date().getTime()}`;

    // 使用 fetch 获取图片
    const response = await fetch(secureUrl);
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }

    // 获取 blob
    const blob = await response.blob();

    // 创建下载链接
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = fileName;

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error('下载图片失败:', error);
    alert(t('draw.generator.errors.downloadFailed'));
  }
};

const nextImage = () => {
  if (currentImageIndex.value < generatedImages.value.length - 1) {
    currentImageIndex.value++;
  }
};

const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--;
  }
};

// 组件销毁时清理
onUnmounted(() => {
  if (pollingInterval) {
    clearInterval(pollingInterval);
    pollingInterval = null;
  }
  
  if (timeout) {
    clearTimeout(timeout);
    timeout = null;
  }
});
</script>
package org.seven.share.common.pojo.image;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class TaskStatus implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String taskId;
    private String status; // PENDING, COMPLETED, FAILED
    private ImageGenerationResponse result;
    private String errorMessage;

    public TaskStatus(String taskId, String status) {
        this.taskId = taskId;
        this.status = status;
    }

    public TaskStatus(String taskId, String status, String errorMessage) {
        this.taskId = taskId;
        this.status = status;
        this.errorMessage = errorMessage;
    }
}

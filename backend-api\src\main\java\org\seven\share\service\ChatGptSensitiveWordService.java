package org.seven.share.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.pojo.entity.ChatGptSensitiveWordEntity;

import java.util.List;

/**
 * @ClassName: ChatGptSensitiveWordService
 * @Description:
 * @Author: Seven
 * @Date: 2024/10/9
 */
public interface ChatGptSensitiveWordService extends IService<ChatGptSensitiveWordEntity> {
    void deleteBatchWordByIds(List<String> ids);

    void saveWord(ChatGptSensitiveWordEntity entity);

    void updateWord(ChatGptSensitiveWordEntity entity);

    List<String> listSensitiveWords();

    void saveBatchWords(List<String> words);
}

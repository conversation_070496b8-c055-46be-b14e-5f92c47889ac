package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: ModelLimitsEntity
 * @Description:
 * @Author: Seven
 * @Date: 2025/1/8
 */
@Data
@TableName("model_limits")
public class ModelLimitsEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    @TableField("user_id")
    private long userId;

    @TableField("model_type")
    private String modelType;

    @TableField("rate_limit")
    private Integer rateLimit;

    @TableField("rate_period")
    private String ratePeriod;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}

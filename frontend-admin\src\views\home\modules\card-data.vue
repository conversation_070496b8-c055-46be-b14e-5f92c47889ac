<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { statistic } from '@/service/api/statistic';
import { useAuthStore } from '@/store/modules/auth';

defineOptions({ name: 'CardData' });

interface StatisticData {
  totalNum: number;
  addNum: number;
  payNum: number;
  unPayNum: number;
  totalMoney: number;
  topics: number;
  normalUsage: number;
  advanceUsage: number;
  monthTotalMoney: number;
  onlineNums: number;
  visitorUsage?: number;
  claudeUsage?: number;
  unsettledCommission?: number;
}

interface CardData {
  key: string;
  title: string;
  value: number;
  color: {
    start: string;
    end: string;
  };
  icon: string;
}

const statisticData = ref<StatisticData>({
  totalNum: 0,
  addNum: 0,
  payNum: 0,
  unPayNum: 0,
  totalMoney: 0.0,
  topics: 0,
  normalUsage: 0,
  advanceUsage: 0,
  monthTotalMoney: 0,
  onlineNums: 0,
  visitorUsage: 0,
  claudeUsage: 0,
  unsettledCommission: 0
});

const authStore = useAuthStore();

// API 请求数据
const getStatistic = async () => {
  const { data } = await statistic();
  if (data) {
    statisticData.value = data as StatisticData;
  }
};

onMounted(async () => {
  await getStatistic();
});

const cardData = computed<CardData[]>(() => {
  const isTenant = authStore.userInfo?.roles?.includes('TENANT');
  
  const allCards = [
    {
      key: 'totalNum',
      title: '总用户数',
      value: statisticData.value.totalNum,
      color: {
        start: '#ec4786',
        end: '#b955a4'
      },
      icon: 'ant-design:user-outlined'
    },
    {
      key: 'addNum',
      title: '今日新增用户',
      value: statisticData.value.addNum,
      color: {
        start: '#865ec0',
        end: '#5144b4'
      },
      icon: 'ant-design:user-add-outlined'
    },
    {
      key: 'onlineNums',
      title: '在线人数',
      value: statisticData.value.onlineNums,
      color: {
        start: '#56cdf3',
        end: '#719de3'
      },
      icon: 'ant-design:team-outlined'
    },
    {
      key: 'unsettledCommission',
      title: '未结算佣金',
      value: statisticData.value.unsettledCommission || 0,
      color: {
        start: '#fcbc25',
        end: '#f68057'
      },
      icon: 'ant-design:money-collect-outlined'
    },
    {
      key: 'monthTotalMoney',
      title: '当月总收入',
      value: statisticData.value.monthTotalMoney,
      color: {
        start: '#fcbc25',
        end: '#f68057'
      },
      icon: 'ant-design:money-collect-outlined'
    },
    {
      key: 'payNum',
      title: '今日新增订单',
      value: statisticData.value.payNum,
      color: {
        start: '#ec4786',
        end: '#b955a4'
      },
      icon: 'ant-design:shopping-cart-outlined'
    },
    {
      key: 'unPayNum',
      title: '今日未支付订单',
      value: statisticData.value.unPayNum,
      color: {
        start: '#865ec0',
        end: '#5144b4'
      },
      icon: 'ant-design:shopping-outlined'
    },
    {
      key: 'totalMoney',
      title: '今日总收益',
      value: statisticData.value.totalMoney,
      color: {
        start: '#56cdf3',
        end: '#719de3'
      },
      icon: 'ant-design:account-book-outlined'
    },
    {
      key: 'topics',
      title: '今日新增话题数',
      value: statisticData.value.topics,
      color: {
        start: '#fcbc25',
        end: '#f68057'
      },
      icon: 'ant-design:message-outlined'
    },
    {
      key: 'normalUsage',
      title: '普通模型使用次数',
      value: statisticData.value.normalUsage,
      color: {
        start: '#ec4786',
        end: '#b955a4'
      },
      icon: 'ant-design:robot-outlined'
    },
    {
      key: 'advanceUsage',
      title: '高级模型使用次数',
      value: statisticData.value.advanceUsage,
      color: {
        start: '#865ec0',
        end: '#5144b4'
      },
      icon: 'ant-design:robot-outlined'
    },
    {
      key: 'visitorUsage',
      title: '游客使用次数',
      value: statisticData.value.visitorUsage || 0,
      color: {
        start: '#56cdf3',
        end: '#719de3'
      },
      icon: 'ant-design:user-outlined'
    },
    {
      key: 'claudeUsage',
      title: 'Claude使用次数',
      value: statisticData.value.claudeUsage || 0,
      color: {
        start: '#fcbc25',
        end: '#f68057'
      },
      icon: 'ant-design:robot-outlined'
    }
  ];

  // 如果是租户角色，只显示指定的卡片
  if (isTenant) {
    return allCards.filter(card => 
      ['totalNum', 'addNum', 'onlineNums', 'unsettledCommission'].includes(card.key)
    );
  }

  // 管理员角色显示所有卡片，但不显示未结算佣金
  return allCards.filter(card => card.key !== 'unsettledCommission');
});

interface GradientBgProps {
  gradientColor: string;
}

const [DefineGradientBg, GradientBg] = createReusableTemplate<GradientBgProps>();

function getGradientColor(color: CardData['color']) {
  return `linear-gradient(to bottom right, ${color.start}, ${color.end})`;
}
</script>

<template>
  <ElCard class="card-wrapper">
    <!-- define component start: GradientBg -->
    <DefineGradientBg v-slot="{ $slots, gradientColor }">
      <div class="rd-8px px-16px pb-4px pt-8px text-white" :style="{ backgroundImage: gradientColor }">
        <component :is="$slots.default" />
      </div>
    </DefineGradientBg>
    <!-- define component end: GradientBg -->
    <ElRow :gutter="16">
      <ElCol v-for="item in cardData" :key="item.key" :lg="6" :md="12" :sm="24" class="my-8px">
        <GradientBg :gradient-color="getGradientColor(item.color)" class="flex-1">
          <h3 class="text-16px">{{ item.title }}</h3>
          <div class="flex justify-between pt-12px">
            <SvgIcon :icon="item.icon" class="text-32px" />
            <CountTo
              :prefix="item.unit"
              :start-value="1"
              :end-value="item.value"
              class="text-30px text-white dark:text-dark"
            />
          </div>
        </GradientBg>
      </ElCol>
    </ElRow>
  </ElCard>
</template>

<style scoped></style>

import { request } from '../request';

/** get role list */
export function fetchGetRoleList(params?: Api.SystemManage.RoleSearchParams) {
  return request<Api.SystemManage.RoleList>({
    url: '/systemManage/getRoleList',
    method: 'get',
    params
  });
}

/**
 * get all roles
 *
 * these roles are all enabled
 */
export function fetchGetAllRoles() {
  return request<Api.SystemManage.AllRole[]>({
    url: '/systemManage/getAllRoles',
    method: 'get'
  });
}

/**
 * add roles
 *
 * add user role
 */
export function fetchCreateRole(data: Api.SystemManage.Role) {
  return request<Api.SystemManage.AllRole[]>({
    url: '/systemManage/addRole',
    method: 'post',
    data
  });
}

/**
 * update roles
 *
 * update user role
 */
export function fetchUpdateRole(data: Api.SystemManage.Role) {
  return request<Api.SystemManage.AllRole[]>({
    url: '/systemManage/updateRoleResourceInfo',
    method: 'post',
    data
  });
}

/** get user list */
export function fetchGetUserList(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.SystemManage.UserList>({
    url: '/systemManage/getUserList',
    method: 'get',
    params
  });
}

/** create user */
export function fetchCreateUser(data: Api.SystemManage.CreateUserParam) {
  return request<string>({
    url: '/systemManage/createUser',
    method: 'post',
    data
  });
}

/** update user */
export function fetchUpdateUser(data: Api.SystemManage.CreateUserParam) {
  return request<string>({
    url: '/systemManage/update',
    method: 'post',
    data
  });
}

/** delete users */
export function fetchDeleteUsers(ids: number[]) {
  return request<string>({
    url: '/systemManage/delete',
    method: 'post',
    data: ids
  });
}

/** get menu list */
export function fetchGetMenuList(params: Api.SystemManage.CommonSearchParams) {
  return request<Api.SystemManage.MenuList>({
    url: '/systemManage/getMenuList/v2',
    method: 'get',
    params
  });
}

/** get all pages */
export function fetchGetAllPages() {
  return request<string[]>({
    url: '/systemManage/getAllPages',
    method: 'get'
  });
}

/** get menu tree */
export function fetchGetMenuTree() {
  return request<Api.SystemManage.MenuTree[]>({
    url: '/systemManage/getMenuTree',
    method: 'get'
  });
}

/** get role resource id */
export function getRoleResourceId(roleId: number) {
  return request({
    url: '/systemManage/getRoleResourceId',
    method: 'post',
    data: roleId
  });
}

/** update role resource info */
export function updateRoleResourceInfo(roleId: number, resourceId: number[]) {
  return request({
    url: '/systemManage/updateRoleResourceInfo',
    method: 'post',
    data: {
      roleId,
      resourceId
    }
  });
}

export function addResource(data: Api.SystemManage.Resource) {
  return request({
    url: '/systemManage/addResource',
    method: 'post',
    data
  });
}

export function updateResource(data: Api.SystemManage.Resource) {
  return request({
    url: '/systemManage/updateResource',
    method: 'put',
    data
  });
}

export function deleteResourceBatch(data: number[]) {
  return request({
    url: '/systemManage/deleteResourceBatch',
    method: 'post',
    data
  });
}

export function bindRoleResource(data: number[], roleId: number) {
  return request({
    url: `/systemManage/bindRoleResource/${roleId}`,
    method: 'post',
    data
  });
}

export function getRoleResource(roleId: number) {
  return request({
    url: `/systemManage/getRoleResource/${roleId}`,
    method: 'get'
  });
}

export function deleteRoleBatch(data: number[]) {
  return request({
    url: '/systemManage/deleteRoleBatch',
    method: 'post',
    data
  });
}

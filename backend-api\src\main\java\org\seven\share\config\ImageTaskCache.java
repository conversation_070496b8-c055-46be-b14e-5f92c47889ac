package org.seven.share.config;

import org.seven.share.common.pojo.image.ImageGenerationResponse;
import org.seven.share.common.pojo.image.TaskStatus;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static org.seven.share.constant.CacheConstant.TASK_PREFIX;

/**
 * @ClassName: TaskCache
 * @Description:
 * @Author: Seven
 * @Date: 2025/4/25
 */
@Component
public class ImageTaskCache {
    @Resource(name = "stringKeyRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 存储redis数据
     * @param taskId
     * @param task
     */
    public void saveTask(String taskId, TaskStatus task) {
        redisTemplate.opsForValue().set(TASK_PREFIX + taskId, task, 30, TimeUnit.DAYS);
    }

    /**
     * 获取redis数据
     * @param taskId
     * @return
     */
    public TaskStatus getTask(String taskId) {
        return (TaskStatus) redisTemplate.opsForValue().get(TASK_PREFIX + taskId);
    }

    /**
     * 更新redis数据
     * @param taskId
     * @param status
     * @param result
     * @param errorMessage
     */
    public void updateTaskStatus(String taskId, String status, ImageGenerationResponse result, String errorMessage) {
        TaskStatus task = getTask(taskId);
        if (task != null) {
            task.setStatus(status);
            task.setResult(result);
            task.setErrorMessage(errorMessage);
            saveTask(taskId, task);
        }
    }

}

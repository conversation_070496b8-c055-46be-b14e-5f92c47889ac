package org.seven.share.controller.admin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.R;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.entity.ClaudeConversationEntity;
import org.seven.share.service.ClaudeConversationService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: ClaudeConversationController
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/1
 */
@RestController
@RequestMapping("/expander-api/claude/conversations")
public class ClaudeConversationController {

    @Resource
    private ClaudeConversationService claudeConversationService;

    @GetMapping("/page")
    @SysLogInterface(title = "分页查询Claude对话记录", businessType = BusinessType.QUERY)
    public R page(@RequestParam(value = "current", defaultValue = "1") Integer current,
                  @RequestParam(value = "size", defaultValue = "10") Integer size){
        LambdaQueryWrapper<ClaudeConversationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(ClaudeConversationEntity::getDeletedAt);
        Page<ClaudeConversationEntity> pageInfo = claudeConversationService.page(new Page<>(current,size), queryWrapper);
        return R.ok(pageInfo);
    }


    @DeleteMapping("/delete")
    @SysLogInterface(title = "删除Claude对话记录", businessType = BusinessType.DELETE)
    public R del(@RequestBody List<String> ids){
        return claudeConversationService.removeBatchByIds(ids) ? R.ok() : R.error();
    }
}

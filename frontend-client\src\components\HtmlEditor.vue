<template>
    <el-dialog title="内容编辑器" v-model="dialogVisible" fullscreen :before-close="closeDialog">
        <el-form :model="formData" style="height: 100%">
            <el-form-item label="类型">
                <el-radio-group v-model="formData.type" @change="handleValueChange">
                    <el-radio label="代码" value="string"></el-radio>
                    <el-radio label="富文本" value="fuwenben"></el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="内容" style="flex: 1;">
                <!-- 如果是字符串类型，显示文本输入框及实时预览 -->
                <div v-if="showEditor" style="display: flex; height: 100%; width: 100%;">
                    <!-- 左侧输入框 -->
                    <div class="w-1/2 pr-4">
                        <el-input type="textarea" :autosize="{ minRows: 20, maxRows: 40 }" v-model="formData.content"
                            placeholder="请输入内容" style="height: 100%; width: 100%;"></el-input>
                    </div>

                    <!-- 右侧预览 -->
                    <div class="w-1/2 pl-4">
                        <div v-if="formData.content" v-html="formData.content"
                            style="border: 1px solid #eee; padding: 20px; height: 100%; width: 100%; overflow-y: auto;">
                        </div>
                        <!-- 占位符 -->
                        <div v-else
                            style="border: 1px solid rgb(128, 129, 132); padding: 20px; height: 100%; width: 100%; overflow-y: auto; text-align: center; color: #aaa;">
                            代码预览区
                        </div>
                    </div>
                </div>

                <!-- 如果是富文本类型，仅显示富文本编辑器 -->
                <wangeditor v-if="!showEditor" v-model="formData.content" style="height: 100%;" />
            </el-form-item>

            <!-- 操作按钮 -->
            <div class=" text-right mr-5">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="handleSave">保存</el-button>
            </div>
        </el-form>
    </el-dialog>
</template>
  
<script setup>
import { ref } from 'vue';
import wangeditor from '@/components/WangEditor.vue'; // 引入富文本编辑器组件
import { ElMessageBox } from 'element-plus'

// 表单数据
const formData = ref({
    type: 'string', // 默认类型为字符串
    content: ''
});

// 控制弹窗显示与隐藏
const dialogVisible = ref(false);

// 计算属性，控制输入框与富文本编辑器的显示
const showEditor = ref(false);

// 打开弹窗
const openDialog = (initialContent) => {
    dialogVisible.value = true;
    showEditor.value = true
    formData.value.content = initialContent
};

// 关闭弹窗
const closeDialog = async() => {
    try {
        await ElMessageBox.confirm(
            '是否保存文件更改？如果不保存，更改会丢失！ 确定?',
            '注意',
            {
                confirmButtonText: '确 定',
                cancelButtonText: '取 消',
                type: 'warning',
            }
        );
        dialogVisible.value = false;
    formData.value = {
        type: 'string', // 默认类型为字符串
        content: ''
        }
        showEditor.value = false
    } catch (error) {
        console.log("用户取消操作", error);
    }
    
};
const emit = defineEmits(['updateContent']);

// 保存按钮操作
const handleSave = () => {
    // 通过 emit 将编辑器内容传递给父组件
    emit('updateContent', formData.value.content);
    dialogVisible.value = false;
};
const handleValueChange = async (val) => {
    try {
        await ElMessageBox.confirm(
            '切换后将清空原来的数据 确定?',
            '注意',
            {
                confirmButtonText: '确 定',
                cancelButtonText: '取 消',
                type: 'warning',
            }
        );
        showEditor.value = !showEditor.value
        formData.value.content = ''
    } catch (error) {
        console.log("用户取消操作", error);
    }
}
// 显式暴露方法给父组件
defineExpose({
    openDialog
});
</script>
  
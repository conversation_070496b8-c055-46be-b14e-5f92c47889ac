const LAYOUT_PREFIX = 'layout.';
const VIEW_PREFIX = 'view.';
const FIRST_LEVEL_ROUTE_COMPONENT_SPLIT = '$';

export function getLayoutAndPage(component?: string | null) {
  let layout = '';
  let page = '';

  const [layoutOrPage = '', pageItem = ''] = component?.split(FIRST_LEVEL_ROUTE_COMPONENT_SPLIT) || [];

  layout = getLayout(layoutOrPage);
  page = getPage(pageItem || layoutOrPage);

  return { layout, page };
}

function getLayout(layout: string) {
  return layout.startsWith(LAYOUT_PREFIX) ? layout.replace(LAYOUT_PREFIX, '') : '';
}

function getPage(page: string) {
  return page.startsWith(VIEW_PREFIX) ? page.replace(VIEW_PREFIX, '') : '';
}

export function transformLayoutAndPageToComponent(layout: string, page: string) {
  const hasLayout = Boolean(layout);
  const hasPage = Boolean(page);

  if (hasLayout && hasPage) {
    return `${LAYOUT_PREFIX}${layout}${FIRST_LEVEL_ROUTE_COMPONENT_SPLIT}${VIEW_PREFIX}${page}`;
  }

  if (hasLayout) {
    return `${LAYOUT_PREFIX}${layout}`;
  }

  if (hasPage) {
    return `${VIEW_PREFIX}${page}`;
  }

  return '';
}

/**
 * Get route name by route path
 *
 * @param routeName
 */
export function getRoutePathByRouteName(routeName: string) {
  return `/${routeName.replace(/_/g, '/')}`;
}

/**
 * Get path param from route path
 *
 * @param routePath route path
 */
export function getPathParamFromRoutePath(routePath: string) {
  const [path, param = ''] = routePath.split('/:');

  return {
    path,
    param
  };
}

/**
 * Get route path with param
 *
 * @param routePath route path
 * @param param path param
 */
export function getRoutePathWithParam(routePath: string, param: string) {
  if (param.trim()) {
    return `${routePath}/:${param}`;
  }

  return routePath;
}

export function getModelValueToParams(model: any): Api.SystemManage.Resource {
  return {
    id: model.id,
    parentId: model.parentId,
    uiPath: model.uiPath,
    menuType: model.menuType,
    status: model.status,
    menuName: model.menuName,
    routeName: model.routeName,
    routePath: model.routePath,
    component: model.component,
    weight: model.weight,
    i18nKey: model.i18nKey,
    keepAlive: model.keepAlive,
    constant: model.constant,
    order: model.order,
    href: model.href,
    hideInMenu: model.hideInMenu,
    activeMenu: model.activeMenu,
    multiTab: model.multiTab,
    fixedIndexInTab: model.fixedIndexInTab,
    query: model.query,
    iconType: model.iconType,
    icon: model.icon,
    title: model.title,
    localIcon: model.localIcon,
    createBy: model.createBy,
    createTime: model.createTime,
    updateBy: model.updateBy,
    updateTime: model.updateTime,
    buttons: model.buttons
  };
}

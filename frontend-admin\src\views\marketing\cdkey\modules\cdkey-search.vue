<script setup lang="ts">
import { $t } from '@/locales';
import { useForm } from '@/hooks/common/form';

defineOptions({ name: 'WordSearch' });

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const status = [
  {
    label: '未兑换', value: 0
  },
  {
    label: '已兑换', value: 1
  },
]

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useForm();

const model = defineModel<any>('model', { required: true });


async function reset() {
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <ElCard class="card-wrapper">
    <ElCollapse>
      <ElCollapseItem :title="$t('common.search')" name="user-search">
        <ElForm ref="formRef" :model="model"  label-position="right" :label-width="80">
          <ElRow :gutter="24">
            <ElCol :lg="6" :md="8" :sm="12">
              <ElFormItem label="激活码" prop="key">
                <ElInput v-model="model.key" clearable placeholder="请输入激活码" />
              </ElFormItem>
            </ElCol>
            <ElCol :lg="6" :md="8" :sm="12">
              <ElFormItem label="兑换人" prop="userToken">
                <ElInput v-model="model.userToken" clearable placeholder="请输入激活码" />
              </ElFormItem>
            </ElCol>
            <ElCol :lg="6" :md="8" :sm="12" label="状态">
              <ElFormItem label="状态" prop="status">
                <ElSelect v-model="model.status" clearable placeholder="请选择状态">
                  <ElOption
                    v-for="(item, idx) in status"
                    :key="idx"
                    :label="item.label"
                    :value="item.value"
                  ></ElOption>
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <!-- <ElCol :lg="6" :md="8" :sm="12">
              <ElFormItem :label="$t('page.manage.user.nickName')" prop="nickName">
                <ElInput v-model="model.nickName" :placeholder="$t('page.manage.user.form.nickName')" />
              </ElFormItem>
            </ElCol>
            <ElCol :lg="6" :md="8" :sm="12">
              <ElFormItem :label="$t('page.manage.user.userPhone')" prop="userPhone">
                <ElInput v-model="model.userPhone" :placeholder="$t('page.manage.user.form.userPhone')" />
              </ElFormItem>
            </ElCol>
            <ElCol :lg="6" :md="8" :sm="12">
              <ElFormItem :label="$t('page.manage.user.userEmail')" prop="userEmail">
                <ElInput v-model="model.userEmail" :placeholder="$t('page.manage.user.form.userEmail')" />
              </ElFormItem>
            </ElCol>
            <ElCol :lg="6" :md="8" :sm="12">
              <ElFormItem :label="$t('page.manage.user.userStatus')" prop="userStatus">
                <ElSelect v-model="model.userGender" clearable :placeholder="$t('page.manage.user.form.userStatus')">
                  <ElOption
                    v-for="{ label, value } in translateOptions(enableStatusOptions)"
                    :key="value"
                    :label="label"
                    :value="value"
                  ></ElOption>
                </ElSelect>
              </ElFormItem>
            </ElCol>  -->
            <ElCol :lg="6" :md="24" :sm="24">
              <ElSpace class="w-full justify-end" alignment="end">
                <ElButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </ElButton>
                <ElButton type="primary" plain @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </ElButton>
              </ElSpace>
            </ElCol>
          </ElRow>
        </ElForm>
      </ElCollapseItem>
    </ElCollapse>
  </ElCard>
</template>

<style scoped></style>

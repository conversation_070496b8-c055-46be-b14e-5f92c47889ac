package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: BaseSysParamEntity
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/1
 */
@Data
@TableName("draw_record")
public class DrawRecordEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;

    private Long userId;

    private String prompt;

    private int drawType;

    private String taskId;

    private String taskStatus;

    private String imageUrl;

    private String description;

    private String model;

    private int num;

    private String imageSize;

    private String response;

    private String responseFormat;

    @TableField(exist = false)
    private String username;

}

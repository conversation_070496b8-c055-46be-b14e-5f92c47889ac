package org.seven.share.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.api.Result;
import org.seven.share.common.pojo.entity.SysRoleResource;


import java.util.List;

public interface SysRoleResourceService extends IService<SysRoleResource> {

    /**
     * 绑定角色资源信息
     *
     * @param roleId 角色Id
     * @param longList 资源Id
     * @return boolean
     */
    Result<Boolean> bindingRoleBasicResource(Long roleId, List<Long> longList);

    Result<List<Long>> getRoleResourceId(Long roleId);

    Boolean deleteDataByRoleId(Long roleId);

    void unbindingRoleResource(List<Long> sourceIds);

    void removeRoleResourceBatchByIds(List<Long> ids);
}

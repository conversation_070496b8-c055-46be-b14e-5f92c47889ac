<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员到期通知</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #0056b3;
        }
        .button {
            display: inline-block;
            background-color: #0056b3;
            color: #fff;
            padding: 10px 20px;
            text-align: center;
            border-radius: 5px;
            text-decoration: none;
            font-weight: bold;
            margin-top: 20px;
        }
        .footer {
            margin-top: 30px;
            font-size: 0.9em;
            color: #777;
        }
    </style>
</head>
<body>
<div class="container">
    <h1 th:text="'会员即将到期提醒'"></h1>
    <p>尊敬的 <span th:text="${userName}">[User's Name]</span>,</p>
    <p>您的会员资格将于 <strong th:text="${expirationDate}">[Expiration Date]</strong> 到期。</p>
    <p>为了继续享受我们的服务，请在会员到期之前续费。</p>
    <p>如果您有任何问题或需要帮助，请随时与我们联系。</p>
    <p class="footer">感谢您成为我们社区的宝贵成员。</p>
    <p class="footer">此致,<br th:text="${siteName}"></p>
</div>
</body>
</html>

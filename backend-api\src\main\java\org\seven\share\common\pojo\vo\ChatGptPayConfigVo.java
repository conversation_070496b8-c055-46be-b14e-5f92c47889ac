package org.seven.share.common.pojo.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: ChatGptPayConfigVo
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/3
 */
@Data
public class ChatGptPayConfigVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private  Long id;

    private String title;

    private Integer paymentsType;

    private String iconUrl;

    private boolean enableH5;
}

package org.seven.share.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.seven.share.common.pojo.entity.ChatGptConversationEntity;
import org.seven.share.common.pojo.entity.ClaudeConversationEntity;
import org.seven.share.mapper.ChatGptConversationsMapper;
import org.seven.share.mapper.ClaudeConversationMapper;
import org.seven.share.service.ChatGptConversationService;
import org.seven.share.service.ClaudeConversationService;
import org.springframework.stereotype.Service;

/**
 * @ClassName: ChatGptConversationServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/11
 */
@Service
public class ClaudeConversationServiceImpl extends ServiceImpl<ClaudeConversationMapper, ClaudeConversationEntity>
        implements ClaudeConversationService {
}

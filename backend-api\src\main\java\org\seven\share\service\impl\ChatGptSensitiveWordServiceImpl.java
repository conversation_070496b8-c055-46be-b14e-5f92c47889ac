package org.seven.share.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.seven.share.mapper.ChatGptSensitiveWordMapper;
import org.seven.share.common.pojo.entity.ChatGptSensitiveWordEntity;
import org.seven.share.service.ChatGptSensitiveWordService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static org.seven.share.constant.CacheConstant.REDIS_SENSITIVE_WORD_KEY;
import static org.seven.share.common.util.ConstantUtil.ENABLE_SENSITIVE_WORDS;

/**
 * @ClassName: ChatGptSensitiveWordServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/10/9
 */
@Service
public class ChatGptSensitiveWordServiceImpl extends ServiceImpl<ChatGptSensitiveWordMapper, ChatGptSensitiveWordEntity>
        implements ChatGptSensitiveWordService {

    @Resource
    private ChatGptSensitiveWordMapper chatGptSensitiveWordMapper;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public void deleteBatchWordByIds(List<String> ids) {
        // 删除数据库中的敏感词
        chatGptSensitiveWordMapper.deleteByIds(ids);

        // 删除Redis中的敏感词
        redisTemplate.delete(REDIS_SENSITIVE_WORD_KEY);
    }

    @Override
    public void saveWord(ChatGptSensitiveWordEntity entity) {
        chatGptSensitiveWordMapper.insert(entity);
        // 删除Redis中的敏感词
        redisTemplate.delete(REDIS_SENSITIVE_WORD_KEY);
    }

    @Override
    public void updateWord(ChatGptSensitiveWordEntity entity) {
        // 更新数据库中的敏感词
        chatGptSensitiveWordMapper.updateById(entity);

        // 删除Redis中的敏感词
        redisTemplate.delete(REDIS_SENSITIVE_WORD_KEY);
    }

    @Override
    public List<String> listSensitiveWords(){
        Long size = redisTemplate.opsForSet().size(REDIS_SENSITIVE_WORD_KEY);
        if (size == 0) {
            List<ChatGptSensitiveWordEntity> sensitiveWordEntities = chatGptSensitiveWordMapper
                    .selectList(new LambdaQueryWrapper<ChatGptSensitiveWordEntity>()
                            .eq(ChatGptSensitiveWordEntity::getStatus, ENABLE_SENSITIVE_WORDS));
            if (CollectionUtil.isNotEmpty(sensitiveWordEntities)) {
                List<String> words = sensitiveWordEntities.stream().map(ChatGptSensitiveWordEntity::getWord).toList();
                words.forEach(word -> redisTemplate.opsForSet().add(REDIS_SENSITIVE_WORD_KEY, word));
                return words;
            }else{
                return Collections.emptyList();
            }
        }else {
            return new ArrayList<>(Objects.requireNonNull(redisTemplate.opsForSet().members(REDIS_SENSITIVE_WORD_KEY)));
        }
    }

    @Override
    public void saveBatchWords(List<String> words) {
        if (CollectionUtil.isNotEmpty(words)){
            List<ChatGptSensitiveWordEntity> list = words.stream().map(e -> {
                ChatGptSensitiveWordEntity word = new ChatGptSensitiveWordEntity();
                word.setWord(e);
                word.setRemark("批量导入");
                word.setStatus(ENABLE_SENSITIVE_WORDS);
                return word;
            }).toList();
            this.saveBatch(list);
            // 清理缓存数据
            redisTemplate.delete(REDIS_SENSITIVE_WORD_KEY);
        }
    }

}

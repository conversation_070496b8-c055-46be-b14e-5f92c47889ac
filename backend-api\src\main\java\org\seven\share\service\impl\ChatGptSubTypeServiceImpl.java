package org.seven.share.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.enums.SubscriptionType;
import org.seven.share.common.exception.CustomException;
import org.seven.share.mapper.ChatGptCouponMapper;
import org.seven.share.mapper.ChatGptSubTypeMapper;
import org.seven.share.common.pojo.dto.ModelLimitDto;
import org.seven.share.common.pojo.entity.ChatGptCouponEntity;
import org.seven.share.common.pojo.entity.ChatGptEPayLogsEntity;
import org.seven.share.common.pojo.entity.ChatGptSubTypeEntity;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.seven.share.common.util.ConstantUtil.SYS_TENANT_ID;
import static org.seven.share.common.util.ConstantUtil.getSysModelLimitMap;
import static org.seven.share.common.util.ServletUtils.getHost;


/**
 * @ClassName: ChatGptSubTypeServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/11
 */
@Slf4j
@Service
public class ChatGptSubTypeServiceImpl extends ServiceImpl<ChatGptSubTypeMapper, ChatGptSubTypeEntity> implements ChatGptSubTypeService {

    @Resource
    private ChatGptCouponMapper chatGptCouponMapper;

    @Resource
    private ChatGptSubTypeMapper chatGptSubTypeMapper;

    @Resource
    private ChatGptUserService chatGptUserService;

    @Resource
    private ChatGptPayLogsService payLogsService;

    @Resource
    private ChatGptConfigService configService;

    @Resource
    private SysTenantService sysTenantService;

    @Override
    public Double calcOfferAmount(Long planId, String coupon, double originalMoney, boolean paySuccess) {
        ChatGptCouponEntity entity = checkCouponValid(coupon);
        // 如果不为空要校验，否则不校验商品是否可用优惠券
        String subTypeIds = entity.getSubTypeIds();
        if (StrUtil.isNotEmpty(subTypeIds)) {
            boolean contains = subTypeIds.contains(String.valueOf(planId));
            if (!contains) {
                throw new CustomException("当前商品已是最优惠，暂时不适用此优惠券喔~");
            }
        }
        // 优惠卷是否有次数限制
        Integer discountCount = entity.getDiscountCount();
        Integer usageDiscountCount = entity.getUsageDiscountCount();
        int usage = usageDiscountCount == null ? 0 : usageDiscountCount;
        if (ObjectUtil.isNotEmpty(discountCount) && discountCount > 0) {
            // 判断优惠券是否超过可用次数
            if (usage >= discountCount) {
                throw new CustomException("优惠券已经超过最大使用次数");
            }
        }
        // 如果支付成功了，则更新使用次数
        if (paySuccess) {
            // 增加优惠次数，更新入库。
            entity.setUsageDiscountCount(usage + 1);
            chatGptCouponMapper.updateById(entity);
        }
        // 检查折扣是否合法
        Double discount = entity.getDiscount();
        if (discount == null) {
            discount = 1.0;
        } else if (discount <= 0 || discount > 1){
            throw new CustomException("优惠券折扣无效，请检查优惠券。");
        }
        Double discountAmount = entity.getDiscountAmount();
        BigDecimal originalMoneyDecimal  = BigDecimal.valueOf(originalMoney);
        BigDecimal discountAmountDecimal  = BigDecimal.valueOf(discountAmount == null ? 0.0 : discountAmount);
        BigDecimal discountDecimal = BigDecimal.valueOf(discount);
        BigDecimal afterDiscountDecimal = originalMoneyDecimal.multiply(discountDecimal);
        BigDecimal finalAmount = afterDiscountDecimal.subtract(discountAmountDecimal);
        // 保留两位小数，使用四舍五入模式
        finalAmount = finalAmount.setScale(2, RoundingMode.HALF_UP);
        if (finalAmount.doubleValue() < 0) {
            log.warn("折扣后的金额为：{}", finalAmount.doubleValue());
            throw new CustomException("商品优惠后的金额为负数，请重新设置优惠券");
        }
        return finalAmount.doubleValue();
    }

    private ChatGptCouponEntity checkCouponValid(String coupon) {
        // 先检查优惠券是否为空
        if (StrUtil.isEmpty(coupon)) {
            throw new CustomException("优惠券不能为空，请检查。");
        }
        // 查询优惠券信息
        ChatGptCouponEntity entity = chatGptCouponMapper.selectOne(new LambdaQueryWrapper<ChatGptCouponEntity>()
                .eq(StrUtil.isNotEmpty(coupon),ChatGptCouponEntity::getCoupon, coupon).eq(ChatGptCouponEntity::getStatus, 0));
        // 判断优惠券是否存在
        if (ObjectUtil.isEmpty(entity)) {
            throw new CustomException("优惠券不存在，请检查。");
        }
        // 判断优惠券是否过期
        if (entity.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new CustomException("该优惠券已过期");
        }
        return entity;
    }

    @Override
    public List<ChatGptSubTypeEntity> listSubTypeByDomain() {
        String tenantId = sysTenantService.getTenantIdByHost(getHost());
        List<ChatGptSubTypeEntity> list = chatGptSubTypeMapper.listSubtype(tenantId);
        return list.stream().peek(e -> {
            if (StrUtil.isNotBlank(e.getRemark())){
                e.setFeatures(Arrays.asList(e.getRemark().split(",")));
            }
        }).sorted(Comparator.comparing(ChatGptSubTypeEntity::getIsHotSale)
                .reversed()).toList();
    }

    @Override
    public int logicDeleteSubtype(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return 0;
        }
        return chatGptSubTypeMapper.logicDeleteSubtype(ids);
    }

    /**
     * 将labels处理成string存储
     * @param chatGptSubType
     */
    private static void dealRemark(ChatGptSubTypeEntity chatGptSubType) {
        Map<String, ModelLimitDto> modelLimits = chatGptSubType.getModelLimits();
        if (ObjectUtil.isNotEmpty(chatGptSubType.getSubType()) && !SubscriptionType.DRAW.getType().equals(chatGptSubType.getSubType()) && CollectionUtil.isEmpty(modelLimits)) {
            throw new CustomException("对话模型速率未填写");
        }
        if (StrUtil.isEmpty(chatGptSubType.getTenantId())) {
            chatGptSubType.setTenantId(SYS_TENANT_ID);
        }
        List<String> labels = chatGptSubType.getFeatures();
        if (CollectionUtil.isNotEmpty(labels)) {
            String remark = String.join(",", labels);
            chatGptSubType.setRemark(remark);
        }
    }

    @Override
    @CacheEvict(cacheNames = "subtype", allEntries = true)
    public void updateSubType(ChatGptSubTypeEntity chatGptSubType) {
        dealRemark(chatGptSubType);
        chatGptSubTypeMapper.updateById(chatGptSubType);
    }

    @Override
    @CacheEvict(cacheNames = "subtype", allEntries = true)
    public void saveSubType(ChatGptSubTypeEntity chatGptSubType) {
        dealRemark(chatGptSubType);
        chatGptSubTypeMapper.insert(chatGptSubType);
    }

    @Override
    public void updateSubtypeStatus(Long id, Integer status) {
        UpdateWrapper<ChatGptSubTypeEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.set("isNotValued", status);
        chatGptSubTypeMapper.update(updateWrapper);
    }

    @Override
    public List<ChatGptSubTypeEntity> listAdminSubType() {
        List<ChatGptSubTypeEntity> list = chatGptSubTypeMapper.selectList(new LambdaQueryWrapper<ChatGptSubTypeEntity>()
                        .isNull(ChatGptSubTypeEntity::getDeletedAt)
                        .orderByAsc(ChatGptSubTypeEntity::getSort));
        return list.stream().peek(e -> {
            if (StrUtil.isNotBlank(e.getRemark())){
                e.setFeatures(Arrays.asList(e.getRemark().split(",")));
            }
        }).collect(Collectors.toList());
    }

    @Override
    public Page<ChatGptSubTypeEntity> getPage(Integer page, Integer size, String query) {
        // 创建分页对象
        Page<ChatGptSubTypeEntity> pageInfo = new Page<>(page, size);
        Page<ChatGptSubTypeEntity> pages = chatGptSubTypeMapper.selectPageWithTenant(pageInfo, query);
        List<ChatGptSubTypeEntity> records = pages.getRecords();
        List<ChatGptSubTypeEntity> collect = records.stream().peek(e -> {
            if (StrUtil.isNotBlank(e.getRemark())) {
                e.setFeatures(Arrays.asList(e.getRemark().split(",")));
            }
        }).collect(Collectors.toList());
        pageInfo.setRecords(collect);
        return pageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncModelLimits() {
        log.info("开始同步用户模型限制...");
        // 1. 提前获取系统配置和套餐信息 - 这些数据较小，可以全量加载
        String modelLimits = configService.getValueByKey("modelLimits");
        Map<String, ModelLimitDto> sysModelLimit = getSysModelLimitMap(modelLimits);

        // 查询套餐数据
        List<ChatGptSubTypeEntity> subTypeList = this.list();
        Map<Long, Map<String, ModelLimitDto>> subTypeMap = new HashMap<>();
        if (!CollectionUtil.isEmpty(subTypeList)) {
            subTypeMap = subTypeList.stream()
                    .collect(Collectors.toMap(
                            ChatGptSubTypeEntity::getId,
                            ChatGptSubTypeEntity::getModelLimits,
                            (v1, v2) -> v1)); // 处理可能的重复键
        }

        // 2. 预先加载支付记录，使用合并函数处理重复键
        Map<String, String> payLogsMap = new HashMap<>();
        if (!CollectionUtil.isEmpty(subTypeList)) {
            try {
                List<ChatGptEPayLogsEntity> payLogsEntityList = payLogsService.list(
                        new LambdaQueryWrapper<ChatGptEPayLogsEntity>()
                                .eq(ChatGptEPayLogsEntity::getStatus, "success")
                                .inSql(ChatGptEPayLogsEntity::getUpdateTime,
                                        "SELECT MAX(updateTime) FROM chatgpt_epaylogs t2 WHERE t2.userToken = chatgpt_epaylogs.userToken AND t2.status = 'success'")
                );

                if (!CollectionUtil.isEmpty(payLogsEntityList)) {
                    payLogsMap = payLogsEntityList.stream()
                            .filter(log -> log.getUserToken() != null && log.getSubTypeId() != null)
                            .collect(Collectors.toMap(
                                    ChatGptEPayLogsEntity::getUserToken,
                                    ChatGptEPayLogsEntity::getSubTypeId,
                                    (v1, v2) -> v1)); // 如果有重复的userToken，保留第一个
                }
            } catch (Exception e) {
                log.error("加载支付记录失败", e);
                // 继续执行，使用空的payLogsMap
            }
        }
        final Map<String, String> finalPayLogsMap = payLogsMap;
        // 3. 使用ID范围查询代替分页
        final int BATCH_SIZE = 200;
        long lastProcessedId = 0;
        long totalProcessed = 0;
        List<String> failedUserTokens = new ArrayList<>(); // 记录失败的用户

        while (true) {
            try {
                // 使用ID范围查询获取一批用户
                List<ChatGptUserEntity> userBatch = chatGptUserService.list(
                        new LambdaQueryWrapper<ChatGptUserEntity>()
                                .gt(ChatGptUserEntity::getId, lastProcessedId)
                                .orderByAsc(ChatGptUserEntity::getId)
                                .last("LIMIT " + BATCH_SIZE)
                );

                if (CollectionUtil.isEmpty(userBatch)) {
                    break; // 没有更多用户
                }

                // 更新最后处理的ID
                lastProcessedId = userBatch.get(userBatch.size() - 1).getId();

                // 处理当前批次的用户
                List<ChatGptUserEntity> successBatch = new ArrayList<>();
                for (ChatGptUserEntity user : userBatch) {
                    try {
                        // 安全检查
                        if (user == null || user.getUserToken() == null) {
                            continue;
                        }

                        Map<String, ModelLimitDto> updateMap;
                        long subTypeId = 0;
                        try {
                            subTypeId = Long.parseLong(user.getSubTypeId() == null ? "0" : String.valueOf(user.getSubTypeId()));
                        } catch (NumberFormatException e) {
                            log.warn("用户 {} 的套餐ID格式无效: {}", user.getUserToken(), user.getSubTypeId());
                        }

                        // 如果为0，查找最新订单中的数据
                        if (subTypeId == 0 && user.getUserToken() != null) {
                            String paySubTypeId = finalPayLogsMap.get(user.getUserToken());
                            if (paySubTypeId != null) {
                                try {
                                    subTypeId = Long.parseLong(paySubTypeId);
                                } catch (NumberFormatException e) {
                                    log.warn("用户 {} 的支付记录套餐ID格式无效: {}", user.getUserToken(), paySubTypeId);
                                }
                            }
                        }

                        // 确定使用哪个模型限制配置
                        if (subTypeId == 0 || !subTypeMap.containsKey(subTypeId)) {
                            updateMap = sysModelLimit; // 使用默认配置
                        } else {
                            updateMap = subTypeMap.get(subTypeId);
                            if (MapUtil.isEmpty(updateMap)) {
                                updateMap = sysModelLimit;
                            }
                        }
                        // 如果用户模型速率中没有grok-3模型，则set一个
                        if (updateMap != null && !updateMap.containsKey("grok-3")) {
                            updateMap.put("grok-3", sysModelLimit.get("grok-3"));
                        }

                        // 更新用户模型限制
                        user.setModelLimits(updateMap);
                        successBatch.add(user);
                    } catch (Exception e) {
                        log.error("处理用户 {} (ID: {}) 时发生错误", user.getUserToken(), user.getId(), e);
                        failedUserTokens.add(user.getUserToken());
                    }
                }

                // 批量更新处理成功的用户
                if (!successBatch.isEmpty()) {
                    try {
                        // 使用逐个更新而不是批量更新，确保租户插件正常工作
                        int successCount = 0;
                        for (ChatGptUserEntity user : successBatch) {
                            try {
                                boolean updated = chatGptUserService.updateById(user);
                                if (updated) {
                                    successCount++;
                                }
                            } catch (Exception e) {
                                log.error("更新用户 {} 失败: {}", user.getUserToken(), e.getMessage());
                                failedUserTokens.add(user.getUserToken());
                            }
                        }
                        totalProcessed += successCount;
                        log.info("已更新用户批次，共 {} 条记录，成功 {} 条，累计 {} 条",
                                successBatch.size(), successCount, totalProcessed);
                    } catch (Exception e) {
                        log.error("批量更新用户失败，ID范围: {} - {}",
                                successBatch.get(0).getId(), successBatch.get(successBatch.size() - 1).getId(), e);
                        // 记录失败的用户
                        failedUserTokens.addAll(successBatch.stream()
                                .map(ChatGptUserEntity::getUserToken)
                                .toList());
                    }
                }

                // 小休息减轻数据库负担
                Thread.sleep(100);

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("同步过程被中断，已处理 {} 条记录", totalProcessed);
                break;
            } catch (Exception e) {
                log.error("处理ID > {} 的批次时发生错误", lastProcessedId, e);
                // 继续处理下一批
            }
        }

        // 记录同步结果
        log.info("用户模型限制同步完成，总共处理成功 {} 条记录", totalProcessed);
        if (!failedUserTokens.isEmpty()) {
            log.warn("同步过程中有 {} 个用户处理失败，需要手动检查", failedUserTokens.size());
            // 可以将失败的用户token写入文件或数据库表，便于后续处理
        }
    }
}

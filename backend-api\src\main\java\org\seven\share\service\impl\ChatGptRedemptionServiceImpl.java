package org.seven.share.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.enums.SubscriptionType;
import org.seven.share.common.exception.CustomException;
import org.seven.share.mapper.ChatGptRedemptionMapper;
import org.seven.share.mapper.ChatGptSubTypeMapper;
import org.seven.share.common.pojo.dto.ChatGptRedemptionDto;
import org.seven.share.common.pojo.entity.ChatGptRedemptionEntity;
import org.seven.share.common.pojo.entity.ChatGptSubTypeEntity;
import org.seven.share.common.pojo.entity.ChatGptUserEntity;
import org.seven.share.common.pojo.vo.RedemptionHistoryVo;
import org.seven.share.service.ChatGptConfigService;
import org.seven.share.service.ChatGptRedemptionService;
import org.seven.share.service.ChatGptUserService;
import org.seven.share.service.UserDrawingQuotaService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.seven.share.constant.CacheConstant.REDEMPTION_CODE_LOCK;
import static org.seven.share.common.util.ConstantUtil.*;

/**
 * @ClassName: ChatGptRedemptionServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/28
 */
@Slf4j
@Service
public class ChatGptRedemptionServiceImpl extends ServiceImpl<ChatGptRedemptionMapper, ChatGptRedemptionEntity> implements ChatGptRedemptionService {
    @Resource
    private ChatGptSubTypeMapper chatGptSubTypeMapper;

    @Resource
    private ChatGptUserService chatGptUserService;

    @Resource
    private ChatGptRedemptionMapper chatGptRedemptionMapper;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private UserDrawingQuotaService drawingQuotaService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> generateCodes(Long subTypeId, Integer count) {
        ChatGptSubTypeEntity subType = chatGptSubTypeMapper.selectById(subTypeId);
        if (ObjectUtil.isEmpty(subType)) {
            log.error("查询订阅信息失败：订阅id为：{}", subTypeId);
            throw new CustomException("查询订阅信息失败，请先确认订阅是否存在");
        }
        List<ChatGptRedemptionEntity> list = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            ChatGptRedemptionEntity entity = new ChatGptRedemptionEntity();
            entity.setSubTypeId(subTypeId);
            entity.setKey(IdUtil.simpleUUID());
            entity.setStatus(COUPON_HAVE_NO_USED);
            entity.setRemark(subType.getName());
            entity.setIsPlus(subType.getIsPlus());
            entity.setUserId(1L);
            list.add(entity);
        }
        if (CollectionUtil.isNotEmpty(list)) {
            saveBatch(list);
            return list.stream().map(ChatGptRedemptionEntity::getKey).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void redeemCodes(String key, Long userId) {
        if (ObjectUtil.isEmpty(userId)) {
            log.warn("用户id：{}不存在", userId);
            throw new CustomException("请先登录后再进行兑换！");
        }
        String lockKey = REDEMPTION_CODE_LOCK + key;
        Boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1", 10L, TimeUnit.SECONDS);
        if (Boolean.TRUE.equals(locked)) {
            try{
                // 查询激活码信息
                ChatGptRedemptionEntity redemption = this.lambdaQuery()
                        .eq(ChatGptRedemptionEntity::getKey, key).one();
                if (ObjectUtil.isEmpty(redemption)) {
                    log.error("兑换码不存在：{}", key);
                    throw new CustomException("兑换码不存在");
                }

                if (redemption.getStatus() == COUPON_HAVE_USED) {
                    log.error("兑换码已兑换：{}", key);
                    throw new CustomException("兑换码已被兑换");
                }
                // 更新用户权益
                updateUserPrivileges(userId, redemption);

                // 更新兑换码状态和兑换人信息
                updateRedemption(userId, redemption);

            }finally {
                stringRedisTemplate.delete(lockKey);
            }
        } else {
            throw new CustomException("该兑换码正在处理中，请稍后重试");
        }
    }

    private void updateUserPrivileges(Long userId, ChatGptRedemptionEntity redemption) {
        Long subTypeId = redemption.getSubTypeId();
        // 查询订阅信息
        ChatGptSubTypeEntity subType = chatGptSubTypeMapper.getSubtypeByIdWithoutTenant(String.valueOf(subTypeId));
        // 查询用户信息
        ChatGptUserEntity user = chatGptUserService.getByIdWithoutTenant(userId);
        // 计算用户过期数据
        chatGptUserService.calculateExpirationTime(subType, user);
        // 判断用户是否开启了卡密兑换返现
        String enableCalcCarCodes = chatGptConfigService.getValueByKey("enableCalcCarCodes");
        if (Objects.equals(enableCalcCarCodes, "true")) {
            // 更新用户的返现信息
            chatGptUserService.updateUserAffQuota(subType.getMoney(), user, AFF_RECORD_TYPE_EXCHANGE);
        }
        // 如果套餐是独享类型的
        chatGptUserService.dealUserAndGptSessionRelation(subType, user);

        // 更新用户的过期时间和套餐类型
        chatGptUserService.updateByIdWithoutTenant(user);
    }

    private void updateRedemption(Long userId, ChatGptRedemptionEntity redemption) {
        redemption.setStatus(COUPON_HAVE_USED);
        redemption.setUsedUserId(userId);
        redemption.setRedeemedTime(LocalDateTime.now());
        updateById(redemption);
    }

    @Override
    public IPage<ChatGptRedemptionDto> selectPage(Page<ChatGptRedemptionDto> pageInfo,
                                                  String key,
                                                  String userToken,
                                                  Integer status) {
        return chatGptRedemptionMapper.selectPageWithJoin(pageInfo, key, userToken, status);
    }

    @Override
    public void exportCardCodes(HttpServletResponse response, Long subTypeId) {
        try {
            // 设置响应头，指定文件名和类型
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=card-codes.txt");

            // 未兑换，按类型分钟
            List<ChatGptRedemptionEntity> list = this.lambdaQuery()
                    .eq(ChatGptRedemptionEntity::getStatus, 0)
                    .eq(ObjectUtil.isNotEmpty(subTypeId), ChatGptRedemptionEntity::getSubTypeId, subTypeId)
                    .orderByAsc(ChatGptRedemptionEntity::getIsPlus).list();
            StringBuilder csvBuilder = new StringBuilder();
            list.forEach(e -> csvBuilder.append("类型：")
                    .append((PLAN_TYPE_PLUS.equals(e.getIsPlus())? "高级套餐" :"普通套餐"))
                    .append(" 卡密：")
                    .append(e.getKey()).append("\n"));
            response.getWriter().write(csvBuilder.toString());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<RedemptionHistoryVo> listRedemptionHistory(Long userId) {
        return chatGptRedemptionMapper.listRedemptionHistory(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recycleKey(ChatGptRedemptionEntity entity) {
        Integer status = entity.getStatus();
        if (status == COUPON_HAVE_NO_USED) {
            throw new CustomException("回收失败：当前卡密未使用，无需回收");
        }
        Long userId = entity.getUsedUserId();
        if (ObjectUtil.isEmpty(userId)) {
            throw new CustomException("回收失败：当前卡密不存在用户信息，请检查");
        }

        Long subTypeId = entity.getSubTypeId();
        if (ObjectUtil.isEmpty(subTypeId)) {
            throw new CustomException("回收失败：当前卡密的套餐信息不存在，请检查");
        }

        // 查询用户信息
        ChatGptUserEntity user = chatGptUserService.getById(userId);
        // 查询套餐信息
        ChatGptSubTypeEntity subType = chatGptSubTypeMapper.selectById(subTypeId);
        if (ObjectUtil.isNotEmpty(user) && ObjectUtil.isNotEmpty(subType)) {
            SubscriptionType type = SubscriptionType.fromType(subType.getSubType());
            switch (type) {
                case GPT_ONLY:
                    rollbackChatGptOrPlusTime(user, subType);
                    break;
                case CLAUDE_ONLY:
                    rollbackClaudeOrProTime(user, subType);
                    break;
                case GPT_AND_CLAUDE:
                    rollbackChatGptOrPlusTime(user, subType);
                    rollbackClaudeOrProTime(user, subType);
                    break;
                case GROK_ONLY:
                    rollbackGrokOrSuperTime(user, subType);
                    break;
                case GPT_AND_GROK:
                    rollbackChatGptOrPlusTime(user, subType);
                    rollbackGrokOrSuperTime(user, subType);
                    break;
                case CLAUDE_AND_GROK:
                    rollbackClaudeOrProTime(user, subType);
                    rollbackGrokOrSuperTime(user, subType);
                    break;
                case GPT_CLAUDE_AND_GROK:
                    rollbackChatGptOrPlusTime(user, subType);
                    rollbackClaudeOrProTime(user, subType);
                    rollbackGrokOrSuperTime(user, subType);
                    break;
                case DRAW:
                    // 需要做减法
                    drawingQuotaService.rollbackDrawQuota(user.getId(), subType);
                    break;
                default:
                    throw new CustomException("未知的套餐类型：" + type);
            }
            if (!SubscriptionType.DRAW.getType().equals(subType.getSubType())) {
                // 更新用户权益信息
                chatGptUserService.updateById(user);
            }
            // 更新激活码状态
            entity.setStatus(COUPON_HAVE_NO_USED);
            entity.setUserId(null);
            entity.setUsedUserId(null);
            entity.setRedeemedTime(null);
            updateById(entity);
        } else {
            log.error("用户信息或者套餐信息不存在,用户信息：{},套餐信息：{}", user, subType);
            throw new CustomException("回收卡密失败：套餐或者用户信息不存在");
        }
    }

    /**
     * 回收grok权益
     * @param user
     * @param subType
     */
    private void rollbackGrokOrSuperTime(ChatGptUserEntity user, ChatGptSubTypeEntity subType) {
        LocalDateTime grokExpireTime = user.getGrokExpireTime();
        int validDays = subType.getValidDays();
        LocalDateTime now = LocalDateTime.now();

        if (PLAN_TYPE_GROK_SUPER.equals(subType.getIsSuper())) {
            LocalDateTime superExpireTime = user.getGrokSuperExpireTime();
            user.setGrokSuperExpireTime(ObjectUtil.isNotEmpty(superExpireTime)
                    ? superExpireTime.minusDays(validDays)
                    : now);
        }

        user.setGrokExpireTime(ObjectUtil.isNotEmpty(grokExpireTime)
                ? grokExpireTime.minusDays(validDays)
                : now);
    }

    /**
     * 回滚gpt套餐或者plus套餐
     */
    private void rollbackChatGptOrPlusTime(ChatGptUserEntity user, ChatGptSubTypeEntity subType) {
        LocalDateTime expireTime = user.getExpireTime();
        int validDays = subType.getValidDays();
        LocalDateTime now = LocalDateTime.now();

        if (PLAN_TYPE_PLUS.equals(subType.getIsPlus())) {
            LocalDateTime plusExpireTime = user.getPlusExpireTime();
            user.setPlusExpireTime(ObjectUtil.isNotEmpty(plusExpireTime)
                ? plusExpireTime.minusDays(validDays)
                : now);
        }

        user.setExpireTime(ObjectUtil.isNotEmpty(expireTime)
            ? expireTime.minusDays(validDays)
            : now);
    }

    /**
     * 回滚claude套餐或者pro套餐
     */
    private void rollbackClaudeOrProTime(ChatGptUserEntity user, ChatGptSubTypeEntity subType) {
        int validDays = subType.getValidDays();
        LocalDateTime now = LocalDateTime.now();

        if (PLAN_TYPE_CLAUDE_PRO.equals(subType.getIsPro())) {
            LocalDateTime proExpireTime = user.getClaudeProExpireTime();
            user.setClaudeProExpireTime(ObjectUtil.isNotEmpty(proExpireTime)
                ? proExpireTime.minusDays(validDays)
                : now);
        }

        LocalDateTime claudeExpireTime = user.getClaudeExpireTime();
        user.setClaudeExpireTime(ObjectUtil.isNotEmpty(claudeExpireTime)
            ? claudeExpireTime.minusDays(validDays)
            : now);
    }
}

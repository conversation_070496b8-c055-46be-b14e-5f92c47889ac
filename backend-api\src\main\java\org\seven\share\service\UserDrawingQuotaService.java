package org.seven.share.service;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.seven.share.common.enums.QuotaChangeType;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.pojo.entity.ChatGptSubTypeEntity;
import org.seven.share.common.pojo.entity.UserDrawingQuotaEntity;
import org.seven.share.mapper.UserDrawingQuotaMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @InterfaceName: UserDrawingQuotaService
 * @Description:
 * @Author: Seven
 * @Date: 2025/4/25
 */
@Service
public class UserDrawingQuotaService {

    @Resource
    private UserDrawingQuotaMapper quotaMapper;

    @Resource
    private QuotaChangeRecordService quotaChangeRecordService;

    public UserDrawingQuotaEntity queryUserDrawQuotaById(Long uid) {
        return quotaMapper.selectOne(new LambdaQueryWrapper<UserDrawingQuotaEntity>()
                .eq(UserDrawingQuotaEntity::getUserId, uid));
    }

    @Transactional(rollbackFor = Exception.class)
    public void consumeUserDrawQuota(Long uid, int count){
        if (count <= 0) {
            return;
        }
        // 校验 uid
        if (uid == null || uid <= 0) {
            throw new IllegalArgumentException("User ID must be a positive number");
        }
        int rows = quotaMapper.decrementQuota(uid, count);
        if (rows == 0) {
            UserDrawingQuotaEntity quotaEntity = quotaMapper.selectOne(new LambdaQueryWrapper<UserDrawingQuotaEntity>()
                    .eq(UserDrawingQuotaEntity::getUserId, uid));
            if (quotaEntity == null) {
                throw new CustomException("没有可用的绘画额度，请先购买后重试。");
            }
            // 检查是否有足够的额度
            if (quotaEntity.getRemainingQuota() < count) {
                throw new CustomException("您的绘图额度不足，请先购买后重试。");
            }
            throw new CustomException("您没有可用的绘图额度，请先购买后重试。");
        }

        // 新增绘画消费明细,默认消费1次
        quotaChangeRecordService.addQuotaChangeRecord(uid, QuotaChangeType.CONSUME.getCode(), -((long) count), "变更说明：绘图额度消耗");
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateUserDrawQuota(Long uid, Long quota, int days) {
        if (quota < 0 || days <= 0) {
            throw new IllegalArgumentException("Quota and days must be positive");
        }
        // 校验 uid
        if (uid == null || uid <= 0) {
            throw new IllegalArgumentException("User ID must be a positive number");
        }
        // 查询现有配额记录
        UserDrawingQuotaEntity quotaEntity = quotaMapper.selectOne(new LambdaQueryWrapper<UserDrawingQuotaEntity>()
                .eq(UserDrawingQuotaEntity::getUserId, uid));
        if (quotaEntity == null) {
            // 记录不存在，创建新记录
            quotaEntity = new UserDrawingQuotaEntity();
            quotaEntity.setUserId(uid);
            quotaEntity.setRemainingQuota(quota);
            quotaEntity.setTotalQuota(quota);
            quotaEntity.setUsedQuota(0L);
            quotaEntity.setResetAt(LocalDateTime.now().plusDays(days));
            quotaMapper.insert(quotaEntity);
        } else {
            quotaMapper.updateUserQuotaByUid(uid, quota, days);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void rollbackDrawQuota(Long uid, ChatGptSubTypeEntity subType) {
        Long quota = subType.getDrawQuota();
        Integer days = subType.getValidDays();
        if (days <= 0 || quota <= 0) {
            throw new IllegalArgumentException("Quota and days must be positive");
        }
        quotaMapper.rollbackDrawQuotaByUid(uid, quota, days);
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeUserQuota(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        quotaMapper.deleteByIds(ids);
    }

    public IPage<UserDrawingQuotaEntity> pageUserQuota(int current, int size, String username) {
        return quotaMapper.pageUserQuota(new Page<>(current, size), username);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateUserQuota(UserDrawingQuotaEntity quota) {
        Long id = quota.getId();
        UserDrawingQuotaEntity quotaEntity = quotaMapper.selectById(id);
        if (ObjectUtil.isEmpty(quotaEntity)) {
            throw new CustomException("额度信息查询失败");
        }
        // 验证使用额度不能大于总额度
        if (quota.getUsedQuota() > quota.getTotalQuota()) {
            throw new CustomException("使用额度不能大于总额度");
        }
        // 新增一条额度变更记录
        quotaChangeRecordService.addQuotaChangeRecord(quota.getUserId(), QuotaChangeType.MODIFY.getCode(), 0L, "变更说明：管理员手动调整额度");

        quota.setRemainingQuota(quota.getTotalQuota() - quota.getUsedQuota());
        quota.setUpdatedAt(LocalDateTime.now());
        quotaMapper.updateById(quota);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertDrawQuota(List<Long> ids , long count) {
        if (CollectionUtil.isEmpty(ids)) {
            return;
        }
        List<UserDrawingQuotaEntity> quotaEntityList = new ArrayList<>();
        for (Long uid : ids) {
            UserDrawingQuotaEntity quotaEntity = new UserDrawingQuotaEntity();
            quotaEntity.setUserId(uid);
            quotaEntity.setUsedQuota(0L);
            quotaEntity.setTotalQuota(count);
            quotaEntity.setRemainingQuota(count);
            quotaEntity.setCreatedAt(LocalDateTime.now());
            quotaEntity.setUpdatedAt(LocalDateTime.now());
            quotaEntity.setResetAt(LocalDateTime.now());
            quotaEntityList.add(quotaEntity);
        }
        quotaMapper.insert(quotaEntityList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void refundUserDrawQuota(Long userId, int num) {
        quotaMapper.refundUserDrawQuota(userId, num);
    }
}

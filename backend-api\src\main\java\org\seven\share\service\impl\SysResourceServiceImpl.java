package org.seven.share.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import io.netty.util.internal.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.api.Result;
import org.seven.share.common.api.ResultCode;
import org.seven.share.common.enums.DelStatusEnums;
import org.seven.share.common.enums.MenuTypeEnums;
import org.seven.share.common.enums.StatusEnums;
import org.seven.share.common.exception.CustomException;
import org.seven.share.common.exception.ServiceException;
import org.seven.share.common.pojo.dto.Meta;
import org.seven.share.common.pojo.entity.SysResource;
import org.seven.share.common.pojo.entity.SysUser;
import org.seven.share.common.pojo.vo.SysMenuTreeVO;
import org.seven.share.common.pojo.vo.SysMenuVO;
import org.seven.share.common.pojo.vo.SysResourceVO;
import org.seven.share.common.pojo.vo.SysRoutesVO;
import org.seven.share.common.util.*;
import org.seven.share.mapper.CreateTableMapper;
import org.seven.share.mapper.SysResourceMapper;
import org.seven.share.service.SysResourceService;
import org.seven.share.service.SysRoleResourceService;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SysResourceServiceImpl extends ServiceImpl<SysResourceMapper, SysResource>
        implements SysResourceService {

    private final JwtTokenUtil jwtTokenUtil;
    private static final RouteUtil routeUtil = new RouteUtil();

    final UserDetailsService userDetailsService;

    private final JsonUtil jsonUtil;

    @Resource
    private SysRoleResourceService sysRoleResourceService;

    @Resource
    private CreateTableMapper createTableMapper;

    @PostConstruct
    public void init(){
        createTableMapper.createSysResourceTable();
        if (count() == 0) {
            createTableMapper.insertSysResource();
        }

    }

    @Override
    public Result<Map<String, Object>> getUserRoutes(String authorizationHeader) {
        if (authorizationHeader == null || !authorizationHeader.startsWith(jwtTokenUtil.getTokenHead())) {
            throw new ServiceException(401, "登录失效");
        }

        Map<String, Object> map = new HashMap<>();

        try {
            String authToken = authorizationHeader.substring(jwtTokenUtil.getTokenHead().length());
            String username = jwtTokenUtil.getUserNameFromToken(authToken);
            if (username != null) {
                // 从数据库中获取用户信息
                SysUserDetail userDetails = (SysUserDetail) this.userDetailsService
                        .loadUserByUsername(username);

                String id = userDetails.getSysUser()
                        .getId().toString();

                List<SysResource> userRoutes = baseMapper.getUserRoutes(id);

                List<SysRoutesVO> routesVOList = routeUtil.processRoute(userRoutes);

                map.put("home", "home");
                map.put("routes", routesVOList);
                return Result.success(map);
            }
        } catch (Exception e) {
            log.info("获取资源信息异常: {}", e.getMessage());
            throw new CustomException("获取资源失败");
        }

        return Result.success();
    }

    @Override
    public Result<IPage<SysMenuVO>> getMenuList(Map<String, Object> params) {
        int pageNum = 1;
        int pageSize = 10;

        if (ObjectUtil.isNotEmpty(params)) {
            pageSize = Integer.parseInt(String.valueOf(params.get("size")));
            pageNum = Integer.parseInt(String.valueOf(params.get("current")));
        }

        List<SysResource> list = getBaseQueryWrapper();

        List<SysMenuVO> menuVOS = convertSysResourceToMenuVO(list);

        List<SysMenuVO> processedMenuVOS = routeUtil.processMenu(menuVOS);

        List<SysMenuVO> pagedMenuVOS = paginate(processedMenuVOS, pageNum, pageSize);

        IPage<SysMenuVO> sysMenuVOIPage = new Page<>();
        sysMenuVOIPage.setRecords(pagedMenuVOS);
        sysMenuVOIPage.setCurrent(pageNum);
        sysMenuVOIPage.setSize(pageSize);
        sysMenuVOIPage.setTotal(processedMenuVOS.size());

        return Result.success(sysMenuVOIPage);
    }

    // Method to create base LambdaQueryWrapper
    private List<SysResource> getBaseQueryWrapper() {
        LambdaQueryWrapper<SysResource> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysResource::getIsDeleted, DelStatusEnums.DISABLE.getCode());
        lambdaQueryWrapper.notIn(SysResource::getMenuType, MenuTypeEnums.BASIC_MENU.getCode());
        return list(lambdaQueryWrapper);
    }

    // Method to convert SysResource to SysMenuVO
    private List<SysMenuVO> convertSysResourceToMenuVO(List<SysResource> resources) {
        List<SysMenuVO> menuVOList = new ArrayList<>();
        resources.forEach(item -> {
            String meta = item.getMeta();
            SysMenuVO sysMenuVO = new SysMenuVO();
            if(!StrUtil.isEmpty(meta)){
                sysMenuVO = convertMetaJsonToSysMenuVO(meta);
            }
            sysMenuVO.setId(item.getId());
            sysMenuVO.setParentId(item.getParentId());
            sysMenuVO.setUiPath(item.getUiPath());
            sysMenuVO.setMenuType(item.getMenuType());
            sysMenuVO.setStatus(item.getStatus());
            sysMenuVO.setMenuName(item.getMenuName());
            sysMenuVO.setRouteName(item.getRouteName());
            sysMenuVO.setRoutePath(item.getRoutePath());
            sysMenuVO.setComponent(item.getComponent());
            sysMenuVO.setWeight(item.getWeight());
            sysMenuVO.setCreateId(item.getCreateId());
            sysMenuVO.setCreateBy(item.getCreateBy());
            sysMenuVO.setCreateTime(item.getCreateTime());
            sysMenuVO.setUpdateId(item.getUpdateId());
            sysMenuVO.setUpdateBy(item.getUpdateBy());
            sysMenuVO.setUpdateTime(item.getUpdateTime());
            sysMenuVO.setIsDeleted(item.getIsDeleted());
            sysMenuVO.setDeleteTime(item.getDeleteTime());
            menuVOList.add(sysMenuVO);
        });
        return menuVOList;
    }

    // Method to paginate a list
    private <T> List<T> paginate(List<T> list, int pageNum, int pageSize) {
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(start + pageSize, list.size());
        return list.subList(start, end);
    }

    public static SysMenuVO convertMetaJsonToSysMenuVO(String metaJson) {
        // 创建Meta对象并填充属性
        Meta meta = new Meta(metaJson);
        // 创建并初始化SysMenuVO对象
        SysMenuVO sysMenuVO = new SysMenuVO();
        // 将Meta对象的属性赋值给SysMenuVO对象
        BeanUtil.copyProperties(meta, sysMenuVO, false);

        return sysMenuVO;
    }

    @Override
    public Result<List<SysRoutesVO>> getConstantRoutes() {

        LambdaQueryWrapper<SysResource> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysResource::getIsDeleted, DelStatusEnums.DISABLE.getCode());
        lambdaQueryWrapper.eq(SysResource::getMenuType, MenuTypeEnums.BASIC_MENU.getCode());

        List<SysResource> list = list(lambdaQueryWrapper);

        List<SysRoutesVO> routesVOList = routeUtil.processRoute(list);
        return Result.success(routesVOList);
    }

    @Override
    public List<String> getUserPermissions(Long id) {
        return baseMapper.getUserPermissions(id);
    }

    @Override
    public Result<List<String>> getAllPages() {
        List<SysResource> list = getBaseQueryWrapper();

        List<String> routeNames = list.stream()
                .map(SysResource::getRouteName)
                .collect(Collectors.toList());

        return Result.success(routeNames);
    }

    @Override
    public Result<List<SysMenuTreeVO>> getMenuTree() {
        try {
            List<SysResource> userRoutes = getBaseQueryWrapper();

            List<SysMenuTreeVO> menuTree = routeUtil.getMenuTree(userRoutes);

            return Result.success(menuTree);
        } catch (Exception e) {
            log.info("获取菜单权限信息异常: {}", e.getMessage());
            throw new ServiceException(8103, "获取菜单权限异常");
        }
    }

    @Override
    public Result<String> addResource(SysResourceVO sysResourceVO) {
        sysResourceVO.setTitle(sysResourceVO.getMenuName());
        SysResource sysResource = new SysResource();
        BeanUtil.copyProperties(sysResourceVO, sysResource);
        SysUser sysUser = SecurityUtil.getSysUser();
        if (sysUser == null) {
            return Result.failed(ResultCode.FORBIDDEN);
        }

        String meta = jsonUtil.toJsonStringWithFields(sysResourceVO,"title","i18nKey","icon","iconType","order","roles",
                "keepAlive","constant","localIcon","href","hideInMenu","activeMenu","multiTab","fixedIndexInTab","query");
        sysResource.setMeta(meta);
        sysResource.setCreateId(sysUser.getId());
        sysResource.setCreateBy(sysUser.getUserName());
        sysResource.setCreateTime(LocalDateTime.now());
        sysResource.setUpdateId(sysUser.getId());
        sysResource.setUpdateBy(sysUser.getUserName());
        sysResource.setUpdateTime(LocalDateTime.now());
        sysResource.setUiPath(sysResource.getRoutePath());
        sysResource.setStatus(StatusEnums.ENABLE.getCode());
        sysResource.setIsDeleted(DelStatusEnums.DISABLE.getCode());
        log.info("新增资源入参: {}", JSONUtil.parse(sysResource));
        if (sysResourceVO.getId() != null && sysResourceVO.getId()>0) {
            updateById(sysResource);
        } else {
            save(sysResource);
        }
        return Result.success();

    }

    @Override
    public Result<String> updateResource(SysResourceVO sysResourceVO) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteResourceBatch(List<Long> sourceIds) {
        if (CollectionUtil.isEmpty(sourceIds)) {
            return;
        }
        // 删除角色与菜单的关联关系
        sysRoleResourceService.unbindingRoleResource(sourceIds);

        // 删除菜单信息
        LambdaQueryWrapper<SysResource> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SysResource::getId, sourceIds);
        wrapper.or().in(SysResource::getParentId, sourceIds);
        remove(wrapper);
    }

}

package org.seven.share.security.filter;

import cn.hutool.core.util.ObjectUtil;
import org.seven.share.common.annotation.JwtToken;
import org.seven.share.common.exception.ServiceException;
import org.seven.share.common.util.JwtUtil;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

import static org.seven.share.common.util.ConstantUtil.AUTH_HEADER;

/**
 * @ClassName: JwtInterceptor
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/30
 */
public class JwtInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 如果不是映射到方法直接通过
        if(!(handler instanceof HandlerMethod)){
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        Class<?> beanType = handlerMethod.getBeanType(); // 获取控制器类

        String token = request.getHeader(AUTH_HEADER);

        // 检查类级别是否有 @JwtToken 注解
        JwtToken classJwtToken = beanType.getAnnotation(JwtToken.class);
        // 检查方法级别是否有 @JwtToken 注解
        JwtToken methodJwtToken = method.getAnnotation(JwtToken.class);

        // 如果类或方法上有 @JwtToken 注解
        if (classJwtToken != null || methodJwtToken != null) {
            // 优先使用方法级别的注解，如果没有则使用类级别
            JwtToken jwtToken = methodJwtToken != null ? methodJwtToken : classJwtToken;
            if (jwtToken.required()) {
                // 执行认证
                if (ObjectUtil.isEmpty(token)) {
                    throw new ServiceException(401, "Token为空，请重新登录");
                }
                // 验证 token
                JwtUtil.decode(token);
            }
        }
        return true;
    }
}

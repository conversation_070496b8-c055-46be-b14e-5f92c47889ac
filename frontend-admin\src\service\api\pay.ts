import { request } from '../request';

export function fetchPayConfigPage (params) {
    return request({
        url: '/pay/page',
        method: 'get',
        params
    });
}


export function updatePayConfig (data) {
    return request({
        url: '/pay/update',
        method: 'post',
        data,
    });
}

export function addPayConfig (data) {
    return request({
        url: '/pay/add',
        method: 'post',
        data
    });
}

export function removePayConfigBatchByIds (data) {
    return request({
        url: '/pay/delete',
        data,
        method: 'delete',
    });
}


// 以下为支付记录接口
export function fetchPayLogsPage (params) {
    return request({
        url: '/payLogs/page',
        params,
        method: 'get',
    });
}
/**
 * 
 * @param id 手工处理订单状态
 * @returns 
 */
export function handlePayStatus (id) {
    return request({
        url: '/payLogs/change-status/' + id,
        method: 'put',
    });
} 

/**
 * 批量删除支付记录
 * @param data 
 * @returns 
 */
export function removePayLogsBatchByIds (data) {
    return request({
        url: '/payLogs/delete',
        method: 'delete',
        data
    });
}

/**
 * 按天统计
 * @returns 
 */
export function fetchLast15DaysIncome () {
    return request({
        url: '/payLogs/dailyIncome',
        method: 'get',
    });
}

export function fetchLast12MonthsIncome() {
    return request({
      url: '/payLogs/months/income',
      method: 'get'
    })
  }
  
  export function fetchYearlyIncome() {
    return request({
      url: '/payLogs/years/income',
      method: 'get'
    })
  }

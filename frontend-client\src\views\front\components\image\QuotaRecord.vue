<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-lg md:text-xl font-semibold text-slate-800 dark:text-slate-200">{{ t('draw.record.title') }}</h2>
    </div>
    
    <!-- 电脑端表格 -->
    <div class="hidden md:block">
      <el-table
        :data="records"
        style="width: 100%"
        border
        v-loading="loading"
        :empty-text="isLoggedIn ? t('draw.record.noRecord') : t('draw.record.pleaseLogin')"
        class="rounded-lg overflow-hidden"
        :max-height="maxHeight"
      >
        <el-table-column :label="t('draw.record.index')" type="index" width="70" align="center" />
        <el-table-column prop="changeAmount" :label="t('draw.record.amount')" align="center" width="100" />
        <el-table-column prop="changeType" :label="t('draw.record.type')" min-width="120" align="center">
          <template #default="scope">
             <span v-if="scope.row.changeType ==1" class="px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">{{ t('draw.record.types.purchase') }}</span>
             <span v-else-if="scope.row.changeType ==2" class="px-2 py-1 rounded text-xs font-medium bg-black text-white dark:bg-white dark:text-black">{{ t('draw.record.types.consumption') }}</span>
             <span v-else-if="scope.row.changeType ==3" class="px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">{{ t('draw.record.types.refund') }}</span>
             <span v-else-if="scope.row.changeType ==4" class="px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">{{ t('draw.record.types.adminAdjust') }}</span>
             <span v-else class="px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">{{ t('draw.record.types.other') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" :label="t('draw.record.createdAt')" min-width="160" align="center" />
        <el-table-column prop="remark" :label="t('draw.record.remark')" min-width="180" align="center" />
      </el-table>
    </div>
    
    <!-- 移动端卡片式布局 -->
    <div class="md:hidden">
      <div v-if="loading" class="flex justify-center my-8">
        <el-icon class="animate-spin text-black dark:text-white" :size="24"><Refresh /></el-icon>
      </div>
      
      <div v-else-if="records.length === 0" class="text-center py-8 text-slate-500 dark:text-slate-400">
        {{ isLoggedIn ? t('draw.record.noRecord') : t('draw.record.pleaseLogin') }}
      </div>
      
      <div v-else class="space-y-3">
        <div 
          v-for="(record, index) in records" 
          :key="index"
          class="bg-white dark:bg-slate-800 rounded-lg shadow-sm p-3 border border-slate-200 dark:border-slate-700"
        >
          <div class="flex justify-between items-center mb-2">
            <div class="font-semibold text-sm flex items-center">
              <span class="bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 rounded-full h-6 w-6 inline-flex items-center justify-center mr-2 text-xs">
                {{ (currentPage - 1) * pageSize + index + 1 }}
              </span>
              <span v-if="record.changeType == 1" class="text-green-600 dark:text-green-400">{{ t('draw.record.types.purchase') }}</span>
              <span v-else-if="record.changeType == 2" class="text-red-600 dark:text-red-400">{{ t('draw.record.types.consumption') }}</span>
              <span v-else-if="record.changeType == 3" class="text-gray-600 dark:text-gray-400">{{ t('draw.record.types.refund') }}</span>
              <span v-else-if="record.changeType == 4" class="text-yellow-600 dark:text-yellow-400">{{ t('draw.record.types.adminAdjust') }}</span>
              <span v-else>{{ t('draw.record.types.other') }}</span>
            </div>
            <div class="text-right">
              <span :class="[
                'rounded-full px-2 py-0.5 text-xs',
                record.changeType == 1 ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' : 
                record.changeType == 2 ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300' : 
                'bg-slate-100 dark:bg-slate-700 text-slate-800 dark:text-slate-300'
              ]">
                {{ record.changeAmount }}
              </span>
            </div>
          </div>
          <div class="flex justify-between text-xs text-slate-500 dark:text-slate-400">
            <div>{{ record.createdAt }}</div>
            <div>{{ record.remark || t('draw.record.noRemark') }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页控件 - 响应式优化 -->
    <div class="flex justify-center mt-4">
      <!-- 桌面端分页 -->
      <el-pagination
        v-if="total > 0"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="hidden md:flex"
        background
      />
      
      <!-- 移动端简化分页 -->
      <el-pagination
        v-if="total > 0"
        v-model:current-page="currentPage"
        :page-size="pageSize"
        layout="prev, pager, next"
        :total="total"
        @current-change="handleCurrentChange"
        class="md:hidden"
        small
        background
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, onUnmounted } from 'vue';
import { Refresh } from '@element-plus/icons-vue';
import { useUserStore } from '@/store/modules/user';
import { fetchQuotaChangeRecord } from '@/api/image';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const userStore = useUserStore();
const records = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const maxHeight = ref();

const isLoggedIn = computed(() => userStore.isLogin);

// 获取消费记录
const fetchRecords = async () => {
  if (!userStore.isLogin || !userStore.id) return;
  
  loading.value = true;
  try {
    const params = {
      userId: userStore.id,
      current: currentPage.value,
      size: pageSize.value
    };
    
    const res = await fetchQuotaChangeRecord(params);
    records.value = res.records || [];
    total.value = res.total || 0;
  } catch (error) {
    console.error(t('draw.record.errors.fetchFailed'), error);
  } finally {
    loading.value = false;
    maxHeight.value = window.innerHeight - 300;
  }
};

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchRecords();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchRecords();
};

// 监听窗口大小变化
const handleResize = () => {
  maxHeight.value = window.innerHeight - 300;
};

onMounted(() => {
  fetchRecords();
  window.addEventListener('resize', handleResize);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script> 
package org.seven.share.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.pojo.entity.ChatGptSysNoticeEntity;

/**
 * @InterfaceName: ChatGptSysNoticeService
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/1
 */


public interface ChatGptSysNoticeService extends IService<ChatGptSysNoticeEntity> {

    ChatGptSysNoticeEntity  getDomainNotice(String domain);

    Page<ChatGptSysNoticeEntity> selectNoticePage(Page<?> objectPage, Long id);
}

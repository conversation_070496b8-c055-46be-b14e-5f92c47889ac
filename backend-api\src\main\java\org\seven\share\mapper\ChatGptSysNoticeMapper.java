package org.seven.share.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.seven.share.common.pojo.entity.ChatGptSysNoticeEntity;

/**
 * @InterfaceName: ChatGptSysNoticeDao
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/1
 */
@Mapper
public interface ChatGptSysNoticeMapper extends BaseMapper<ChatGptSysNoticeEntity> {
    Page<ChatGptSysNoticeEntity> selectNoticePage(Page<?> page, @Param("id") Long id);
}

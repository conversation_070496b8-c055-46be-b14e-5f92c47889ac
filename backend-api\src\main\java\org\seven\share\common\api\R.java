package org.seven.share.common.api;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: R
 * @Description:
 * @Author: Seven
 * @Date: 2024/6/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class R {
    private Integer code;

    private String msg;

    private Object data;

    public static R ok() {
        R r = new R();
        r.code = 200;
        r.msg = "请求成功";
        return r;
    }

    public static R authSuccess () {
        R r = new R();
        r.code = 1;
        r.msg = "请求成功";
        return r;
    }

    public static R ok(String msg) {
        R r = new R();
        r.code = 200;
        r.msg = msg;
        return r;
    }

    public static R ok(Object data) {
        R r = new R();
        r.code = 200;
        r.msg = "请求成功";
        r.data = data;
        return r;
    }

    public static R error() {
        R r = new R();
        r.code = 500;
        r.msg = "请求失败";
        return r;
    }

    public static R error(String msg) {
        R r = new R();
        r.code = 500;
        r.msg = msg;
        return r;
    }

    public static R error(Integer code, String msg) {
        R r = new R();
        r.code = code;
        r.msg = msg;
        return r;
    }

}

package org.seven.share.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.seven.share.common.pojo.entity.ChatGptConversationEntity;
import org.seven.share.common.pojo.entity.GrokConversationEntity;
import org.seven.share.mapper.ChatGptConversationsMapper;
import org.seven.share.mapper.GrokConversationsMapper;
import org.seven.share.mapper.GrokSessionMapper;
import org.seven.share.service.ChatGptConversationService;
import org.seven.share.service.GrokConversationsService;
import org.springframework.stereotype.Service;

/**
 * @ClassName: GrokConversationServiceImpl
 * @Description:
 * @Author: Seven
 * @Date: 2024/7/11
 */
@Service
public class GrokConversationServiceImpl extends ServiceImpl<GrokConversationsMapper, GrokConversationEntity>
        implements GrokConversationsService {
}

<template>
  <div
    class="sm:mx-2 md:mx-6 lg:mx-8 my-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 p-6 rounded-xl"
  >
    <!-- 分类按钮组 -->
    <div class="px-0 mb-4">
      <div class="-mx-1 flex flex-wrap sm:flex-nowrap">
        <div
          class="w-1/2 sm:w-auto px-1 mb-2 sm:mb-0"
          v-for="category in categories"
          :key="category"
        >
          <el-button
            @click="activeCategory = category"
            class="w-full sm:w-auto !px-2 !py-1 !h-8 !border-0 transition-all duration-300 text-xs sm:text-sm font-medium sm:!px-4 sm:!py-2"
            :class="[
              activeCategory === category
                ? 'bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-amber-500 dark:to-yellow-500 hover:from-blue-400 hover:to-indigo-400 dark:hover:from-amber-400 dark:hover:to-yellow-400 text-white dark:text-gray-900'
                : 'bg-white/60 dark:bg-gray-800/40 text-gray-600 dark:text-gray-300 hover:bg-blue-50/80 dark:hover:bg-gray-700/40',
            ]"
          >
            {{ category }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 套餐卡片 -->
    <el-row :gutter="12">
      <el-col
        :xs="24"
        :sm="12"
        :md="6"
        :lg="6"
        v-for="(plan, index) in filteredPlans"
        :key="index"
        class="mb-3"
      >
        <el-card
          shadow="hover"
          class="h-full flex flex-col transition-all duration-300 hover:transform hover:scale-102 border-0 bg-gradient-to-b from-white/60 to-blue-50/60 dark:from-gray-800/40 dark:to-gray-900/40 backdrop-blur-sm hover:from-white/80 hover:to-blue-50/80 dark:hover:from-gray-800/60 dark:hover:to-gray-900/60"
          :body-style="{ padding: '12px', background: 'transparent' }"
          :header-style="{
            padding: '12px',
            borderBottom: isDark
              ? '1px solid rgba(255,255,255,0.1)'
              : '1px solid rgba(59, 130, 246, 0.1)',
            background: 'transparent',
          }"
        >
          <template #header>
            <div class="flex-shrink-0">
              <div
                class="items-center inline-flex font-medium text-blue-600 dark:text-amber-300"
              >
                <img
                  v-if="plan.subType == '1' || plan.subType == '3'"
                  src="@/assets/chatgpt.png"
                  alt="Logo"
                  class="w-8 h-8"
                />
                <img
                  v-if="plan.subType == '2'"
                  src="@/assets/claude.png"
                  alt="Logo"
                  class="w-8 h-8"
                />
                <img
                  v-if="plan.subType == '3'"
                  src="@/assets/claude.png"
                  alt="Logo"
                  class="ml-0.5 w-8 h-8"
                />
                <span class="ml-2 text-sm font-bold">{{ plan.name }}</span>
              </div>
              <div class="flex items-baseline mt-1.5">
                <span
                  class="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-amber-200 dark:to-yellow-500 bg-clip-text text-transparent"
                >
                  ¥{{ plan.money }}
                </span>
                <span class="text-xs text-gray-500 dark:text-gray-400 ml-1"
                  >/ {{ plan.validDays }}天</span
                >
              </div>
              <p v-if="plan.subType != '2'" class="text-sm mt-1.5 text-gray-500 dark:text-gray-400">
                ChatGPT 次数限制: {{ plan.limit }}次 / {{ convertTimeUnit(plan.per) }}
              </p>
              <p v-if="plan.subType != '1'" class="text-sm mt-1.5 text-gray-500 dark:text-gray-400">
                Claude 次数限制: {{ plan.claudeLimit? plan.claudeLimit : '无限制' }}次 / {{ convertTimeUnit(plan.claudePer? plan.claudePer: '999h') }}
              </p>
              <p class="text-sm mt-1.5 text-gray-500 dark:text-gray-400">
                套餐类型: {{ categoryMap[plan.subType] }}
              </p>
            </div>
          </template>

          <div class="flex-grow py-2">
            <ul class="space-y-2">
              <li
                v-for="(feature, index) in plan.features"
                :key="index"
                class="flex items-start group"
              >
                <Check
                  class="mr-1.5 h-3.5 w-3.5 text-blue-500 dark:text-amber-400 group-hover:text-blue-600 dark:group-hover:text-amber-300 flex-shrink-0 mt-0.5"
                />
                <span
                  class="text-sm text-gray-600 dark:text-gray-300 group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors"
                >
                  {{ feature }}
                </span>
              </li>
            </ul>
          </div>

          <el-button
            class="w-full mt-3 !h-8 !border-0 bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-amber-500 dark:to-yellow-500 hover:from-blue-400 hover:to-indigo-400 dark:hover:from-amber-400 dark:hover:to-yellow-400 transition-all duration-300 text-white dark:text-gray-900 font-medium"
            @click="subscription(plan)"
          >
            立即订阅
          </el-button>
        </el-card>
      </el-col>
    </el-row>
  </div>
  <!-- </el-dialog> -->
  <el-dialog
    title="商品支付"
    v-model="payDialogVisible"
    :width="dialogWidth"
    :before-close="handleClose"
    align-center
  >
    <el-form :model="form">
      <div>
        <!-- 左侧套餐信息 -->
        <div>
          <p class="my-2">
            套餐名称:
            <el-tag class="ml-4" type="primary">{{ form.planName }}</el-tag>
          </p>

          <p class="my-2">
            支付金额:
            <el-tag class="ml-4 text-red-500">
              <div v-if="form.originalMoney != form.money && couponData">
                <del>{{ form.originalMoney }}¥ </del>优惠后：{{ form.money }} ¥
              </div>
              <!-- 如果有折扣，显示原价 -->
              <span v-else>{{ form.originalMoney }}￥</span>
            </el-tag>
          </p>
          <p class="my-2">
            有效天数:
            <el-tag class="ml-4" type="primary">{{ form.validDays }} 天</el-tag>
          </p>
          <p class="my-2">
            次数限制:
            <el-tag class="ml-4" type="primary"
              >{{ form.limit }} / {{ convertTimeUnit(form.per) }}</el-tag
            >
          </p>
        </div>
      </div>
      <el-card class="flex items-center justify-center" v-if="qrCodeData">
        <qrcode
          v-if="qrcodeShow"
          :value="qrCodeData"
          :width="200"
          :height="200"
          :color="{ dark: '#000', light: '#FFF' }"
          type="image/png"
        />
        <div v-if="qrcodeHpShow">
          <img
            class=""
            :src="qrCodeData"
            width="180"
            height="180"
            alt="支付二维码获取失败"
          />
        </div>
        <p class="text-center text-red-500">请打开{{ payName }}扫码支付</p>
      </el-card>
      <!-- 支付方式选择 -->
      <div class="flex items-center justify-center mt-4">
        <el-radio-group v-model="form.payType">
          <el-radio
            v-for="option in paymentOptions"
            :key="option.id"
            :value="option.paymentsType"
            @change="handleRadioChange(option)"
          >
            <template #default>
              <div class="flex items-center">
                <img
                  class="mr-1"
                  v-if="option.iconUrl"
                  :src="option.iconUrl"
                  :alt="option.title"
                />
                {{ option.title }}
              </div>
            </template>
          </el-radio>
        </el-radio-group>
      </div>
      <div class="text-center m-4">
        <el-button
          :loading="buttonLoading"
          class="!h-8 !border-0 bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-amber-500 dark:to-yellow-500 hover:from-blue-400 hover:to-indigo-400 dark:hover:from-amber-400 dark:hover:to-yellow-400 transition-all duration-300 text-gray-900 font-medium"
          @click="goPay"
        >
          立即支付
        </el-button>
        <el-button @click="openCouponDialog">优惠券</el-button>
      </div>
    </el-form>
  </el-dialog>
  <el-dialog
    title="优惠券"
    v-model="couponVisible"
    :width="couponDialogWidth"
    align-center
    @close="handleCouponClose"
  >
    <!-- 输入框 -->
    <el-input
      v-model="couponData"
      placeholder="请输入优惠券（选填）"
      clearable
      @change="handleCouponChange"
    ></el-input>
    <!-- 按钮区域，使用 justify-end 将按钮右对齐 -->
    <div class="flex justify-end mt-4">
      <el-button @click="couponVisible = false">取消</el-button>
      <el-button
        class="!h-8 !border-0 bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-amber-500 dark:to-yellow-500 hover:from-blue-400 hover:to-indigo-400 dark:hover:from-amber-400 dark:hover:to-yellow-400 transition-all duration-300 text-gray-900 font-medium"
        @click="handleCouponClick"
      >
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, onBeforeUnmount } from 'vue';
import { ElNotification, ElMessageBox } from 'element-plus';
import { useI18n } from 'vue-i18n';
import * as api from '@/api/subtype.js';
import {
  createQrCode,
  queryF2FOrderStatus,
  queryEpayOrderStatus,
  queryXhOrderStatus,
  queryLanTuPayStatus,
  queryWxNativeStatus,
  getPayList,
} from '../api/pay';
import Qrcode from 'vue-qrcode';
import { convertTimeUnit } from '@/utils/date.js';
import { useDark, useToggle } from '@vueuse/core';

const categories = ['全部', 'ChatGPT', 'Claude', 'ChatGPT&Claude'];
const activeCategory = ref('全部');
// 套餐类型映射
const categoryMap = {
  1: 'ChatGPT',
  2: 'Claude',
  3: 'ChatGPT&Claude',
};
const filteredPlans = computed(() => {
  if (activeCategory.value === '全部') {
    return plans.value;
  }
  return plans.value.filter((plan) => plan.category === activeCategory.value);
});

const payName = computed(() => {
  // 判断 form.payType 的值并返回对应的支付名称
  if (form.value.payType === 1 || form.value.payType === 3) {
    return '支付宝';
  } else if (form.value.payType === 6) {
    return 'USTD';
  } else {
    return '微信';
  }
});
const isDark = useDark();

const dialogWidth = computed(() => (windowWidth.value < 768 ? '90%' : '40%'));
const couponDialogWidth = computed(() =>
  windowWidth.value < 768 ? '90%' : '30%'
);
const couponVisible = ref(false);
const { t } = useI18n();
const props = defineProps({
  visible: Boolean,
});
const userInfo = JSON.parse(localStorage.getItem('user') || '{}');
const emit = defineEmits(['update:visible']);
const loading = ref(false);
const payDialogVisible = ref(false);
const plans = ref([]);
const form = ref({
  planId: '',
  payType: '',
  coupon: '',
  originalMoney: '',
  money: '',
});
const qrcodeShow = ref(false);
const qrcodeHpShow = ref(false);
const intervalId = ref(null); // 定时器
const windowWidth = ref(window.innerWidth);
const isMobile = computed(() => {
  const userAgent = navigator.userAgent.toLowerCase();
  const mobileKeywords = [
    'android',
    'iphone',
    'ipad',
    'ipod',
    'windows phone',
    'mqqbrowser',
  ];
  const isNarrowScreen = window.innerWidth <= 768;
  return (
    isNarrowScreen ||
    mobileKeywords.some((keyword) => userAgent.includes(keyword))
  );
});
const paymentOptions = ref([]);

const handleResize = () => {
  windowWidth.value = window.innerWidth;
};
const qrCodeData = ref(null);
// 跳转地址
const jumpUrl = ref('');
const payMethod = ref('');
const tradeNo = ref('');
// 启用h5支付
const enableH5 = ref(null);

const couponData = ref('');
// 是否跳转
const jumpFlag = computed(
  () =>
    form.value.payType === 6 ||
    form.value.payType === 7 ||
    (isMobile.value && enableH5.value)
);

const vPayFlag = ref(false);

onMounted(() => {
  if (!isDark.value) {
    useToggle(isDark);
  }
  fetchData();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

const subscription = async (plan) => {
  payDialogVisible.value = true;
  form.value.planId = plan.id;
  form.value.planName = plan.name;
  form.value.originalMoney = plan.money;
  form.value.money = plan.money;
  form.value.validDays = plan.validDays;
  form.value.limit = plan.limit;
  form.value.per = plan.per;
  await fetchPayList();
};

const handleClose = () => {
  ElMessageBox.confirm(t('qr.confirmCloseMessage'), t('qr.tip'), {
    confirmButtonText: t('qr.confirmButton'),
    cancelButtonText: t('qr.cancelButton'),
    type: 'warning',
  })
    .then(() => {
      closeDialog();
    })
    .catch(() => {
      // 用户选择了继续支付，不关闭弹窗
    });
};
const closeDialog = () => {
  payDialogVisible.value = false;
  qrCodeData.value = null;
  form.value = {
    planId: '',
    payType: '',
    coupon: '',
  };
  clearTimers(); // 关闭定时器
};
const fetchQrcode = async () => {
  // 为了避免刷新时，重复请求多个订单号
  await clearTimers();
  try {
    loading.value = true;
    const params = {
      typeId: form.value.planId,
      payType: form.value.payType,
      userToken: userInfo.username,
      coupon: form.value.coupon,
      isMobile: isMobile.value,
    };
    const res = await createQrCode(params);
    if (res != null) {
      tradeNo.value = res.tradeNo;
      payMethod.value = form.value.payType;
      switch (form.value.payType) {
        case 1:
        case 2:
        case 6:
          if (jumpFlag.value || res.payUrl) {
            vPayFlag.value = true;
            jumpUrl.value = res.payUrl;
          } else {
            qrCodeData.value = res.qrcode;
          }
          break;
        case 3:
        case 7:
          // 打开二维码弹窗
          qrCodeData.value = res.qrcode;
          break;
        case 4:
          const isWeixinBrowser = /micromessenger/i.test(navigator.userAgent);
          if (isWeixinBrowser && enableH5.value) {
            vPayFlag.value = true;
            jumpUrl.value = res.url;
          } else {
            // 打开二维码弹窗
            qrCodeData.value = res.url_qrcode;
          }
          break;
        case 5:
          if (jumpFlag.value) {
            vPayFlag.value = true;
            jumpUrl.value = res.payUrl;
          } else {
            qrCodeData.value = res.qrcode;
          }
          break;
        default:
          break;
      }
    }
  } catch (error) {
    console.log('pay error:', error);
  } finally {
    loading.value = false;
  }
};
const clearTimers = () => {
  if (intervalId.value) {
    clearInterval(intervalId.value);
    intervalId.value = null; // 清理后将 intervalId 置空，确保下次定时器重新设置
  }
};

const fetchData = async () => {
  try {
    const res = await api.getSubTypeList();
    plans.value = res.map((plan) => ({
      ...plan,
      category: categoryMap[plan.subType] || '未分类',
    }));
    console.log(plans.value);
  } catch (error) {
    console.error(t('shopDialog.fetchError'));
  }
};

const fetchPayList = async () => {
  const data = await getPayList();
  if (data != null && data.length > 0) {
    paymentOptions.value = data;
    form.value.payType = paymentOptions.value[0].paymentsType;
    enableH5.value = paymentOptions.value[0].enableH5;
  }
};
const getPayStatus = async (tradeNo, time) => {
  // 如果已有定时器，先清除
  if (intervalId.value) {
    clearTimers();
  }
  // 定时器：每隔1秒查询一次订单支付状态
  intervalId.value = setInterval(async () => {
    try {
      let paymentStatus;
      // 查询易支付订单支付状态
      if (form.value.payType == 1 || form.value.payType == 2) {
        paymentStatus = await queryEpayOrderStatus({ tradeNo: tradeNo });
      } else if (form.value.payType == 3) {
        // 查询当面付订单支付状态
        paymentStatus = await queryF2FOrderStatus({ tradeNo: tradeNo });
      } else if (form.value.payType == 4) {
        paymentStatus = await queryXhOrderStatus({ tradeNo: tradeNo });
      } else if (form.value.payType == 5) {
        paymentStatus = await queryLanTuPayStatus({ tradeNo: tradeNo });
      } else if (form.value.payType == 7) {
        paymentStatus = await queryWxNativeStatus({ tradeNo: tradeNo });
      }
      if (paymentStatus === 'TRADE_SUCCESS' || paymentStatus === 'SUCCESS') {
        ElNotification.success(t('shopDialog.paymentSuccess'));
        closeDialog();
      }
      // 其他支付状态处理...
    } catch (error) {
      console.error(error);
      clearTimers(); // 关闭定时器
    }
  }, time); // 2秒查询一次
};
// 在组件卸载时清除定时器
onBeforeUnmount(() => {
  if (intervalId.value) {
    clearInterval(intervalId.value); // 清除定时器
  }
});
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
const buttonLoading = ref(false);
const goPay = async () => {
  try {
    buttonLoading.value = true;
    if (!form.value.payType) {
      ElNotification.error('管理员还未配置支付方式');
      return;
    }
    jumpUrl.value = '';
    qrCodeData.value = null;
    await fetchQrcode(); // 调用支付函数，生成支付跳转链接
    if (form.value.payType === 6) {
      window.open(jumpUrl.value);
    } else {
      if (vPayFlag.value) {
        // 手机端并且启用了 H5 支付时跳转支付
        window.location.href = jumpUrl.value;
      } else if (qrCodeData.value) {
        // 否则展示虎皮二维码
        if (form.value.payType === 4) {
          qrcodeHpShow.value = true;
        } else {
          // 其他二维码
          qrcodeShow.value = true;
        }
        // 监听支付状态
        getPayStatus(tradeNo.value, 2000);
      }
    }
  } finally {
    buttonLoading.value = false;
  }
};
const handleRadioChange = (pay) => {
  clearData();
  enableH5.value = pay.enableH5;
};
const clearData = () => {
  clearTimers();
  qrcodeShow.value = false;
  qrcodeHpShow.value = false;
  qrCodeData.value = null;
};
const handleCouponClose = () => {
  couponVisible.value = false;
};

// 优惠券确定按钮
const handleCouponClick = async () => {
  if (couponData.value) {
    const params = {
      planId: form.value.planId,
      coupon: couponData.value,
      originalMoney: form.value.originalMoney,
    };
    const res = await api.calcOfferAmount(params);
    if (res) {
      form.value.money = res;
      form.value.coupon = couponData.value;
    }
  }
  couponVisible.value = false;
};

const openCouponDialog = () => {
  couponVisible.value = true;
  couponData.value = '';
};

const handleCouponChange = () => {
  form.value.coupon = couponData.value;
  clearData();
};
</script>
<style scoped>
.my-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 16px;
}
</style>

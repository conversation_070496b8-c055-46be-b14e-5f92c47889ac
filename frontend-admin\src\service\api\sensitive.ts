import { request } from '../request';
export function fetchSensitivePage(params: any) {
  return request({
    url: '/word/page',
    method: 'get',
    params
  });
}

export function updateSensitiveWord(data: any) {
  return request({
    url: '/word/update',
    method: 'post',
    data
  });
}

export function addSensitiveWord(data: any) {
  return request({
    url: '/word/add',
    method: 'post',
    data
  });
}

export function removeSensitiveBatchByIds(data: any) {
  return request({
    url: '/word/delete',
    data,
    method: 'delete'
  });
}

export function saveSenstiveBatch(data: any) {
  return request({
    url: '/word/batch',
    method: 'post',
    data
  });
}

package org.seven.share.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.pojo.entity.ClaudeSessionEntity;
import org.seven.share.common.pojo.vo.CarInfoVo;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @InterfaceName: ClaudeSessionService
 * @Description:
 * @Author: Seven
 * @Date: 2024/8/12
 */
public interface ClaudeSessionService extends IService<ClaudeSessionEntity> {
    String authUserInfoAndGetLoginUrl(String userToken, String carId, Integer isPlus) throws IOException;

    String generatorCarID();

    String getClaudeLoginUrl(String username);

    void updateRemaining(Map<String, String> paramMap, HttpServletRequest request);

    List<CarInfoVo> getClaudeList(Integer page, Integer size);

    Map<String, Object> oauthUserInfo(String userToken, String carId, Integer isPlus);

    void updateClaudeSession(ClaudeSessionEntity claudeSession);

    void saveClaudeSession(ClaudeSessionEntity claudeSession);

    void removeCarInfoBatch(List<String> ids);
}

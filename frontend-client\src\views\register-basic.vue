<template>
  <div class="min-h-screen flex items-center justify-center bg-white">
    <div class="w-full max-w-sm p-8">
      <!-- Logo -->
      <h1 class="text-3xl font-bold text-center text-gray-800 mb-2">
        Create your account
      </h1>
      <p class="text-center text-gray-600 mb-8">
        注册账号以继续使用本服务。
      </p>

      <!-- Login Form -->
      <form @submit.prevent="handleSubmit">
        <!-- Email Field -->
        <div class="custom-form-item">
          <div class="custom-input-container">
            <input v-model="form.username" required class="custom-input" :class="{ 'has-content': form.username }"
              @focus="setFocus('username', true)"
              pattern="^[^\u4e00-\u9fa5\s]{3,20}$"
              title="用户名必须是3到20个字符，且不能包含中文字符或空格"
              @blur="setFocus('username', false)" />
            <label :class="{ 'is-focused': usernameFocused || form.username }">用户名</label>
          </div>
        </div>
        <div class="custom-form-item">
          <div class="custom-input-container">
            <input v-model="form.email" required type="email" class="custom-input" :class="{ 'has-content': form.username }"
              @focus="setFocus('email', true)" @blur="setFocus('email', false)" />
            <label :class="{ 'is-focused': emailFocused || form.email }">邮箱地址</label>
          </div>
        </div>
        <!-- Password Field -->
        <div class="custom-form-item">
          <div class="custom-input-container">
            <input v-model="form.password" 
              required 
              autocomplete 
              :type="showPassword ? 'text' : 'password'"
              class="custom-input" :class="{ 'has-content': form.password }" @focus="setFocus('password', true)"
              @blur="setFocus('password', false)"/>
            <label :class="{ 'is-focused': passwordFocused || form.password }">密码</label>
            <el-icon class="password-icon" @click="showPassword = !showPassword">
              <View v-if="showPassword" />
              <Hide v-else />
            </el-icon>
          </div>
        </div>
        <div class="custom-form-item">
          <div class="custom-input-container">
            <input v-model="form.confirmPassword" 
              required 
              autocomplete 
              :type="showPassword ? 'text' : 'password'"
              class="custom-input" :class="{ 'has-content': form.confirmPassword }"
              @focus="setFocus('confirmPassword', true)" 
              @blur="setFocus('confirmPassword', false)" />
            <label :class="{ 'is-focused': confirmPasswordFocused || form.confirmPassword }">确认密码</label>
            <el-icon class="password-icon" @click="showPassword = !showPassword">
              <View v-if="showPassword" />
              <Hide v-else />
            </el-icon>
          </div>
        </div>
        <div class="custom-form-item">
          <div class="custom-input-container">
            <input v-model="form.inviter" :required="enableInviteCode" class="custom-input" :class="{ 'has-content': form.inviter }" :disabled="disableInviter"
              @focus="setFocus('inviter', true)" @blur="setFocus('inviter', false)" />
            <label :class="{ 'is-focused': inviterFocused || form.inviter }">邀请码
              <span v-if="enableInviteCode" class="text-red-500">*</span><span v-else class="text-gray-500">（选填）</span>
            </label>
          </div>
        </div>
        <div class="custom-form-item" v-if="enableEmailCheck">
          <div class="custom-input-container flex">
            <input v-model="form.code" required autocomplete class="custom-input" :class="{ 'has-content': form.code }"
              @focus="setFocus('code', true)" @blur="setFocus('code', false)" />
            <label :class="{ 'is-focused': verificationCodeFocused || form.code }">验证码</label>
            <button type="button"
              class="absolute right-1 top-1/2 -translate-y-1/2 px-4 py-1.5 bg-transparent text-emerald-500 focus:outline-non"
              @click="sendVerificationCode">
              {{ isGettingCode ? `${countdown}s` : '发送' }}
            </button>
          </div>
        </div>

        <!-- Forgot Password Link -->
        <div class="flex justify-center mb-6">
          <el-link type="primary" @click="goLogin" :underline="false">已有账号? <span
              class="ml-1 text-emerald-600">登录</span></el-link>
        </div>

        <!-- Login Button -->
        <el-button type="primary" native-type="submit" :loading="loading" class="w-full mb-6 rounded-[7px]" size="large">
          确定
        </el-button>
      </form>
    </div>
  </div>
</template>
  
<script setup>
import { ref, reactive, onMounted, computed, onUnmounted } from 'vue'
import { View, Hide } from '@element-plus/icons-vue'
import Cookies from 'js-cookie'
import { ElNotification } from 'element-plus'
import * as api from '@/api/user.js'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n';
import { encrypt } from '@/utils/encrypt'
import { useSiteStore } from '@/store/modules/site.js';

const siteStore = useSiteStore();
const enableEmailCheck = computed(() => siteStore.enableEmailCheck === 'true' || siteStore.enableEmailCheck === '');
const enableInviteCode = computed(() => siteStore.enableInviteCode === 'true');

const { t } = useI18n();
const disableInviter = ref(false)
const router = useRouter();  // 使用vue-router
const form = reactive({
  username: '',
  email: '',
  code: '',
  password: '',
  confirmPassword: '',
  inviter: '',
})
const route = useRoute();

const loading = ref(false)
const showPassword = ref(false)
const emailFocused = ref(false)
const usernameFocused = ref(false)
const passwordFocused = ref(false)
const confirmPasswordFocused = ref(false)
const verificationCodeFocused = ref(false)
const inviterFocused = ref(false)
const timer = ref(null)
const setFocus = (field, value) => {
  if (field === 'username') {
    usernameFocused.value = value
  } else if (field === 'email') {
    emailFocused.value = value
  } else if (field === 'password') {
    passwordFocused.value = value
  } else if (field === 'confirmPassword') {
    confirmPasswordFocused.value = value
  } else if (field === 'code') {
    verificationCodeFocused.value = value
  } else if (field === 'inviter') {
    inviterFocused.value = value
  }
}
const validateUsername = (username) => {
  if (username.length < 3 || username.length > 20) {
    return '用户名长度应为 3 到 20 个字符';
  }
  if (/[\u4e00-\u9fa5]/.test(username)) {
    return '用户名不能包含中文字符';
  }
  if (/\s/.test(username)) {
    return '用户名不能包含空格';
  }
  return null; // 无错误
}
const usernameError = ref(null)
const validateEmail = (email) => {
  const regex = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,}$/;
  if (!regex.test(email)) {
    return '请输入有效的邮箱地址';
  }
  return null;
}
const emailError = ref(null)
const handleSubmit = async () => {
  // 用户名验证
  usernameError.value = validateUsername(form.username);
  if (usernameError.value) {
    ElNotification.error(usernameError.value);
    return;
  }

  // 邮箱验证
  emailError.value = validateEmail(form.email);
  if (emailError.value) {
    ElNotification.error(emailError.value);
    return;
  }

  // 密码验证
  if (form.password !== form.confirmPassword) {
    ElNotification.error('密码与确认密码不一致');
    return;
  }
  try {
    loading.value = true
    await api.register(form)
    ElNotification({
      message: t('register.errors.registrationSuccess'),
      type: 'success',
    })
    // 将用户名和密码更新到cookie中
    Cookies.set("username", form.username, { expires: 30 });
    Cookies.set('password', encrypt(form.password), { expires: 30, secure: true });

    router.push('/login');
  } catch (error) {
    console.error('重置密码失败:', error)
  } finally {
    loading.value = false
  }
}
const goLogin = () => {
  router.push('/login')
}

const isGettingCode = ref(false);
const countdown = ref(60);

const sendVerificationCode = async () => {
  if (isGettingCode.value) return;

  // 验证邮箱
  if (!form.email) {
    ElNotification.error(t('register.errors.emailMissing'));
    return;
  }

  // 在这里添加发送验证码的逻辑
  const param = {
    username: form.username,
    email: form.email
  }
  await api.getRegisterCode(param)
  startCountDown()
  ElNotification.success(t('register.errors.codeSent'));
}

const startCountDown = () => {
  // 设置倒计时的持续时间（秒）
  const countdownDuration = 60;

  // 记录倒计时的结束时间
  const endTime = Date.now() + countdownDuration * 1000;
  localStorage.setItem('countdownEndTime', endTime);

  isGettingCode.value = true;

  // 开始倒计时
  const updateCountdown = () => {
    const remainingTime = Math.max(0, Math.floor((endTime - Date.now()) / 1000));
    countdown.value = remainingTime;

    if (remainingTime <= 0) {
      clearInterval(timer);
      isGettingCode.value = false;
      localStorage.removeItem('countdownEndTime'); // 清除存储的倒计时
    }
  };

  timer.value = setInterval(updateCountdown, 1000);

  // 初始化时更新一次倒计时
  updateCountdown();
}

// 页面加载时检查是否有未完成的倒计时
const savedEndTime = localStorage.getItem('countdownEndTime');
if (savedEndTime) {
  const remainingTime = Math.max(0, Math.floor((savedEndTime - Date.now()) / 1000));
  if (remainingTime > 0) {
    isGettingCode.value = true;
    countdown.value = remainingTime;

    timer.value = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer.value);
        isGettingCode.value = false;
        localStorage.removeItem('countdownEndTime');
      }
    }, 1000);
  } else {
    localStorage.removeItem('countdownEndTime');
  }
}
onMounted(() => {
  const inviterParam = route.query.inviter
  if (inviterParam && inviterParam != 'null') {
    form.inviter = inviterParam; // 将参数值填入响应式变量
    disableInviter.value = true
  }
})
onUnmounted(() => {
  localStorage.removeItem('countdownEndTime');
  clearInterval(timer.value);
})
</script>
  
<style scoped>
.custom-form-item {
  margin-bottom: 20px;
}

.custom-input-container {
  position: relative;
}

.custom-input {
  width: 100%;
  height: 56px;
  border: 1px solid #dcdfe6;
  border-radius: 7px;
  padding: 0 15px;
  font-size: 14px;
  transition: all 0.3s;
}

.custom-input:focus {
  border-color: #4caf50;
  outline: none;
}

.custom-input-container label {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  background-color: white;
  padding: 0 5px;
  color: #909399;
  font-size: 14px;
  transition: all 0.3s;
  pointer-events: none;
}

.custom-input-container label.is-focused,
.custom-input.has-content+label {
  top: 0;
  font-size: 12px;
  color: #10a37f;
}

.password-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #909399;
}

:deep(.el-button--primary:not(.is-disabled)) {
  background-color: #000000;
  border-color: #000000;
  color: #ffffff;
}

:deep(.el-button--primary:not(.is-disabled):hover) {
  background-color: #333333;
  border-color: #333333;
}

:deep(.dark .el-button--primary:not(.is-disabled)) {
  background-color: #ffffff;
  border-color: #ffffff;
  color: #000000;
}

:deep(.dark .el-button--primary:not(.is-disabled):hover) {
  background-color: #e5e5e5;
  border-color: #e5e5e5;
}

:deep(.el-link.el-link--primary) {
  color: #000000;
}

:deep(.el-link.el-link--primary:hover) {
  color: #333333;
}

:deep(.dark .el-link.el-link--primary) {
  color: #ffffff;
}

:deep(.dark .el-link.el-link--primary:hover) {
  color: #e5e5e5;
}
</style>
  
<template>
  <div class="instructions-container">
    <h2 class="text-2xl md:text-3xl font-bold mb-6">{{ $t('keyCodePurchase.title') }}</h2>
    <p class="mb-1">{{ $t('keyCodePurchase.description') }}</p>
    <div class="border dark:border-gray-700 border-gray-300 rounded shadow-lg">
      <iframe
        :src="fkAddress" 
        frameborder="0"
        class="w-full iframe-height"
        :title="$t('keyCodePurchase.instructions')"
      ></iframe>
    </div>
  </div>
</template>
<style scoped>
.instructions-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.iframe-height {
  height: calc(100vh - 100px);
  min-height: 500px;
}

@media (max-width: 768px) {
  .iframe-height {
    height: calc(100vh - 150px);
    min-height: 400px;
  }
}
</style>
<script setup>
import { computed } from 'vue';
import { useSiteStore } from '@/store/modules/site';

const siteStore = useSiteStore();
const fkAddress = computed(() => siteStore.fkAddress);
</script>
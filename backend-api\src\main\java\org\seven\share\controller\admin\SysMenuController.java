package org.seven.share.controller.admin;


import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.seven.share.common.annotation.SysLogInterface;
import org.seven.share.common.api.Result;
import org.seven.share.common.enums.BusinessType;
import org.seven.share.common.pojo.vo.SysMenuTreeVO;
import org.seven.share.common.pojo.vo.SysMenuVO;
import org.seven.share.common.pojo.vo.SysResourceVO;
import org.seven.share.service.SysResourceService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "SysMenuController", description = "菜单信息控制层")
//@RequestMapping("/sys/menu")
@RequestMapping("/expander-api/systemManage")
public class SysMenuController {

    @Resource
    private final SysResourceService sysResourceService;

    @Operation(summary = "查询菜单信息")
    @GetMapping(value = "/getMenuList/v2")
    @Parameters({
            @Parameter(name = "current", description = "当前页", required = true, example = "1"),
            @Parameter(name = "size", description = "每页显示条数", required = true, example = "10"),
    })
    @SysLogInterface(title = "查询菜单信息", businessType = BusinessType.OTHER)
    // @PreAuthorize("@pre.hasPermission('sys:menu:list')")
    public Result<IPage<SysMenuVO>> getMenuList(@Parameter(hidden = true) @RequestParam Map<String, Object> params) {
        return sysResourceService.getMenuList(params);
    }

    @Operation(summary = "查询全部页面组件名称")
    @GetMapping(value = "/getAllPages")
    public Result<List<String>> getAllPages() {
        return sysResourceService.getAllPages();
    }

    @Operation(summary = "查询菜单树")
    @GetMapping(value = "/getMenuTree")
    public Result<List<SysMenuTreeVO>> getMenuTree() {
        return sysResourceService.getMenuTree();
    }


    @PostMapping("/addResource")
    @Operation(summary = "添加资源")
    @SysLogInterface(title = "添加资源", businessType = BusinessType.INSERT)
    public Result<String> addResource(@RequestBody SysResourceVO sysResourceVO) {
        return sysResourceService.addResource(sysResourceVO);
    }

    @PutMapping("/updateResource")
    @Operation(summary = "修改资源")
    @SysLogInterface(title = "修改资源", businessType = BusinessType.UPDATE)
    public Result<String> updateResource(@RequestBody SysResourceVO sysResourceVO) {
        return sysResourceService.updateResource(sysResourceVO);
    }

    @PostMapping("/deleteResourceBatch")
    @Operation(summary = "删除资源")
    @SysLogInterface(title = "删除资源", businessType = BusinessType.DELETE)
    public Result<?> deleteResourceBatch(@RequestBody List<Long> sourceIds) {
        sysResourceService.deleteResourceBatch(sourceIds);
        return Result.success();
    }

}

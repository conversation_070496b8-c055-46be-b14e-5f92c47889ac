package org.seven.share.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import io.springboot.captcha.base.Captcha;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.seven.share.common.pojo.vo.CaptchaVo;
import org.seven.share.service.SysCaptchaService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static org.seven.share.constant.CacheConstant.CAPTCHA_CODE_KEY;

@Service
@RequiredArgsConstructor
public class SysCaptchaServiceImpl implements SysCaptchaService {

    private final RedisTemplate<String, String> redisTemplate;

    @Override
    public boolean validate(String uuid, String code) {
        if (StringUtils.isBlank(uuid) || StringUtils.isBlank(code)) {
            return false;
        }
        String cacheCode = redisTemplate.opsForValue().get(CAPTCHA_CODE_KEY + uuid);
        redisTemplate.delete(CAPTCHA_CODE_KEY + uuid);
        if (StringUtils.isBlank(cacheCode)) {
            return false;
        }
        return cacheCode.equals(code);
    }

    @Override
    public CaptchaVo getCaptcha() {
        // 保存验证码信息
        String uuid = IdUtil.simpleUUID();
        String verifyKey = CAPTCHA_CODE_KEY + uuid;
        // 生成验证码
        String code = RandomUtil.randomNumbers(4);
        redisTemplate.opsForValue().set(verifyKey, code, 5, TimeUnit.MINUTES);
        CaptchaVo captchaVo = new CaptchaVo();
        captchaVo.setUuid(uuid);
        captchaVo.setCode(code);
        return captchaVo;
    }
}

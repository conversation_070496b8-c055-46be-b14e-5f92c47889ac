package org.seven.share.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.seven.share.common.api.Result;
import org.seven.share.common.pojo.entity.SysRole;
import org.seven.share.common.pojo.vo.SysRoleVO;

import java.util.List;
import java.util.Map;

public interface SysRoleService extends IService<SysRole> {

    /**
     * 分页获取数据
     *
     * @param params 查询参数
     * @return IPage<SysRole>
     */
    IPage<SysRole> getPage(Map<String, Object> params);

    Result<List<SysRoleVO>> getAllRoles(String authorizationHeader);

    List<String> getUserRole(Long id);

    List<SysRole> queryRoleListByRoleCode(List<String> roleCode);

    Result<Boolean> add(SysRole sysRole);

    Result<List<Long>> getRoleResourceId(Long roleId);

    void bindRoleResource(Long roleId, List<Long> ids);

    List<Long> getRoleResource(Long roleId);

    void deleteRoleBatch(List<Long> ids);
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 取消所有滚动条，内容自动撑开 */
html, body {
  overflow: visible !important;
  height: auto !important;
  min-height: 100vh;
}

body {
  margin: 0;
  padding: 0;
}

/* 主容器自动撑开 */
#app {
  min-height: 100vh;
  height: auto !important;
  overflow: visible !important;
}

/* Element Plus 主容器样式 */
.el-main {
  overflow: visible !important;
  height: auto !important;
  min-height: calc(100vh - 64px);
  padding-bottom: 20px;
}


/* 表格容器自动撑开 */
.el-table {
  height: auto !important;
  max-height: none !important;
}

/* 覆盖Element Plus的紫色主题为黑白主题 */
:root {
  --el-color-primary: #000000;
  --el-color-primary-light-3: #333333;
  --el-color-primary-light-5: #666666;
  --el-color-primary-light-7: #999999;
  --el-color-primary-light-8: #cccccc;
  --el-color-primary-light-9: #f5f5f5;
  --el-color-primary-dark-2: #000000;
}

/* 深色模式下的主题色 */
.dark {
  --el-color-primary: #ffffff;
  --el-color-primary-light-3: #e5e5e5;
  --el-color-primary-light-5: #cccccc;
  --el-color-primary-light-7: #999999;
  --el-color-primary-light-8: #666666;
  --el-color-primary-light-9: #333333;
  --el-color-primary-dark-2: #ffffff;
}

/* 分页组件样式 - 黑白主题 */
.el-pagination .el-pager li.is-active {
  background-color: #000000 !important;
  color: white !important;
  border-color: #000000 !important;
}

.dark .el-pagination .el-pager li.is-active {
  background-color: #ffffff !important;
  color: #000000 !important;
  border-color: #ffffff !important;
}

/* 分页按钮hover样式 */
.el-pagination .el-pager li:hover {
  background-color: #333333 !important;
  color: white !important;
}

.dark .el-pagination .el-pager li:hover {
  background-color: #e5e5e5 !important;
  color: #000000 !important;
}

/* 分页按钮默认样式 */
.el-pagination .el-pager li {
  background-color: transparent !important;
  color: #606266 !important;
  border: 1px solid #dcdfe6 !important;
}

.dark .el-pagination .el-pager li {
  background-color: transparent !important;
  color: #a8abb2 !important;
  border: 1px solid #4c4d4f !important;
}

/* 前进后退按钮样式 */
.el-pagination .btn-prev,
.el-pagination .btn-next {
  background-color: transparent !important;
  color: #606266 !important;
  border: 1px solid #dcdfe6 !important;
}

.dark .el-pagination .btn-prev,
.dark .el-pagination .btn-next {
  background-color: transparent !important;
  color: #a8abb2 !important;
  border: 1px solid #4c4d4f !important;
}

.el-pagination .btn-prev:hover,
.el-pagination .btn-next:hover {
  background-color: #333333 !important;
  color: white !important;
  border-color: #333333 !important;
}

.dark .el-pagination .btn-prev:hover,
.dark .el-pagination .btn-next:hover {
  background-color: #e5e5e5 !important;
  color: #000000 !important;
  border-color: #e5e5e5 !important;
}

/* 禁用状态 */
.el-pagination .btn-prev:disabled,
.el-pagination .btn-next:disabled {
  background-color: #f5f7fa !important;
  color: #c0c4cc !important;
  border-color: #e4e7ed !important;
}

.dark .el-pagination .btn-prev:disabled,
.dark .el-pagination .btn-next:disabled {
  background-color: #2d2d2d !important;
  color: #6c6c6c !important;
  border-color: #4c4d4f !important;
}

/* 分页器容器样式 */
.el-pagination {
  color: #606266 !important;
}

.dark .el-pagination {
  color: #a8abb2 !important;
}

/* 分页器文字 */
.el-pagination .el-pagination__total,
.el-pagination .el-pagination__jump {
  color: #606266 !important;
}

.dark .el-pagination .el-pagination__total,
.dark .el-pagination .el-pagination__jump {
  color: #a8abb2 !important;
}

/* 每页显示条数选择器 */
.el-pagination .el-select .el-input__inner {
  border-color: #dcdfe6 !important;
}

.dark .el-pagination .el-select .el-input__inner {
  border-color: #4c4d4f !important;
  background-color: #2d2d2d !important;
  color: #a8abb2 !important;
}

/* 分页组件更多样式覆盖 */
.el-pagination .el-pager li:not(.is-active):hover {
  color: #000000 !important;
}

.dark .el-pagination .el-pager li:not(.is-active):hover {
  color: #ffffff !important;
}

/* 分页输入框 */
.el-pagination .el-pagination__jump input {
  border-color: #dcdfe6 !important;
}

.dark .el-pagination .el-pagination__jump input {
  border-color: #4c4d4f !important;
  background-color: #2d2d2d !important;
  color: #a8abb2 !important;
}

/* 分页选择器下拉 */
.el-pagination .el-select .el-input.is-focus .el-input__inner {
  border-color: #000000 !important;
}

.dark .el-pagination .el-select .el-input.is-focus .el-input__inner {
  border-color: #ffffff !important;
}

/* 分页器所有按钮的通用样式 */
.el-pagination button {
  background-color: transparent !important;
  color: #606266 !important;
}

.dark .el-pagination button {
  color: #a8abb2 !important;
}

.el-pagination button:hover {
  color: #000000 !important;
}

.dark .el-pagination button:hover {
  color: #ffffff !important;
}

/* 确保所有分页相关元素都不使用蓝色 */
.el-pagination .el-pager li,
.el-pagination .btn-prev,
.el-pagination .btn-next,
.el-pagination .el-pagination__jump input,
.el-pagination .el-select .el-input__inner {
  transition: all 0.2s ease !important;
}

/* 移除任何可能的蓝色焦点样式 */
.el-pagination * {
  outline: none !important;
}

.el-pagination *:focus {
  box-shadow: none !important;
}

/* 按钮样式 */
.el-button--primary {
  background-color: var(--el-color-primary) !important;
  border-color: var(--el-color-primary) !important;
}

.el-button--primary:hover {
  background-color: var(--el-color-primary-light-3) !important;
  border-color: var(--el-color-primary-light-3) !important;
}

/* Tab样式 */
.el-tabs__item.is-active {
  color: var(--el-color-primary) !important;
}

.el-tabs__active-bar {
  background-color: var(--el-color-primary) !important;
}

/* 链接样式 */
.el-link--primary {
  color: var(--el-color-primary) !important;
}

/* 复选框和单选框 */
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--el-color-primary) !important;
  border-color: var(--el-color-primary) !important;
}

.el-radio__input.is-checked .el-radio__inner {
  background-color: var(--el-color-primary) !important;
  border-color: var(--el-color-primary) !important;
}

/* 开关组件 */
.el-switch.is-checked .el-switch__core {
  background-color: var(--el-color-primary) !important;
}

/* 进度条 */
.el-progress-bar__inner {
  background-color: var(--el-color-primary) !important;
}

/* 滑块 */
.el-slider__button {
  border-color: var(--el-color-primary) !important;
}

.el-slider__bar {
  background-color: var(--el-color-primary) !important;
}

/* 确认关闭对话框样式 */
.confirm-close-dialog .el-message-box__btns .el-button--primary {
  background-color: #000000 !important;
  border-color: #000000 !important;
  color: white !important;
}

.dark .confirm-close-dialog .el-message-box__btns .el-button--primary {
  background-color: #ffffff !important;
  border-color: #ffffff !important;
  color: black !important;
}

.confirm-close-dialog .el-message-box__btns .el-button--primary:hover {
  background-color: #333333 !important;
  border-color: #333333 !important;
}

.dark .confirm-close-dialog .el-message-box__btns .el-button--primary:hover {
  background-color: #e5e5e5 !important;
  border-color: #e5e5e5 !important;
}

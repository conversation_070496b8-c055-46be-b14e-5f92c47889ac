package org.seven.share.common.pojo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: BaseSysUser
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/25
 */
@Data
@TableName("base_sys_user")
public class BaseSysUserEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    @TableField(value = "createTime", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "updateTime", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    private String username;

    private String password;

}

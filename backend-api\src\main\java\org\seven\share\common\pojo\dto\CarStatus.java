package org.seven.share.common.pojo.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: CarStatus
 * @Description:
 * @Author: Seven
 * @Date: 2024/9/24
 */
@Data
public class CarStatus implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String accountReady;

    private Integer clears_in;

    private Integer resetTime;

    private Integer count;

    private String isPlus;

    private Integer team_clears_in;

    private String planType;
}

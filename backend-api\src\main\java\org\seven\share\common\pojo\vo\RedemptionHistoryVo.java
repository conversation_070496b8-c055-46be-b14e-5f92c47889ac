package org.seven.share.common.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: RedemptionHistoryVo
 * @Description:
 * @Author: Seven
 * @Date: 2024/12/4
 */
@Data
public class RedemptionHistoryVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    private String key;

    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime redeemedTime;

    private Integer isPlus;
}

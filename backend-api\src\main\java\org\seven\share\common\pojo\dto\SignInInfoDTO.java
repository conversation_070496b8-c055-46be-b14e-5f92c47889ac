package org.seven.share.common.pojo.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: SignInInfoDTO
 * @Description:
 * @Author: Seven
 * @Date: 2024/12/29
 */
@Data
public class SignInInfoDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private boolean hasSignedIn; // 今日签到状态

    private int continuousDays; // 连续签到

    private int reward;

    private List<Integer> signedDates;
}

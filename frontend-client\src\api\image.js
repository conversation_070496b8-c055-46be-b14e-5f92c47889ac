import request from "../utils/request";

export function generateImage (data) {
    return request({
        url: '/images/generate',
        method: 'post',
        data
    });
}

export function fetchImageStatus (id) {
    return request({
        url: '/images/status/' + id,
        method: 'get',
    });
}

export function editImage (data) {
    return request({
        url: '/images/edit',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}

export function fetchImageList (params) {
    return request({
        url: '/images/record/list',
        params,
        method: 'get',
    });
}


export function fetchImagePage (params) {
    return request({
        url: '/images/page',
        method: 'get',
        params
    });
}
 
export function queryQuota (uid) {
    return request({
        url: '/images/quota',
        method: 'get',
        params: {uid: uid}
    });
}

export function fetchQuotaChangeRecord (params) {
    return request({
        url: '/images/quota/change/record',
        method: 'get',
        params
    });
}

export function deleteImageRecord(rid) {
    return request({
        url: '/images/record/delete',
        method: 'get',
        params: { rid }
    });
}
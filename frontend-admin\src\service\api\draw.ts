import { request } from '../request';

export function removeDrawRecordBatch(data: any) {
  return request({
    url: '/draw/recode/delete',
    method: 'post',
    data
  });
}

export function fetchDrawRecordPage(params: any) {
  return request({
    url: '/draw/recode/page',
    method: 'get',
    params
  });
}

export function removeQuotaChangeBatch(data: any) {
  return request({
    url: '/draw/quota/change/delete',
    method: 'post',
    data
  });
}

export function fetchQuotaChangeRecord(params: any) {
  return request({
    url: '/draw/quota/change/page',
    method: 'get',
    params
  });
}

export function removeUserQuotaBatch(data: any) {
  return request({
    url: '/draw/quota/delete',
    method: 'post',
    data
  });
}

export function fetchUserQuotaPage(params: any) {
  return request({
    url: '/draw/quota/page',
    method: 'get',
    params
  });
}

export function fetchUpdateUserDrawQuota(data: any) {
  return request({
    url: '/draw/quota/update',
    method: 'post',
    data
  });
}

<template>
  <div class="personal-center">
    <!-- 移动端菜单按钮 -->
    <div class="flex flex-wrap items-center md:hidden mb-4">
      <el-dropdown @command="handleMenuSelect">
        <el-button type="primary">
          菜单列表<el-icon class="el-icon--right"><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="goHome" :icon="HomeFilled"
              >回到首页</el-dropdown-item
            >
            <el-dropdown-item command="personalInfo" :icon="Avatar"
              >个人信息</el-dropdown-item
            >
            <el-dropdown-item command="changePassword" :icon="Lock"
              >修改密码</el-dropdown-item
            >
            <el-dropdown-item command="invitationInfo" :icon="Money"
              >推广福利</el-dropdown-item
            >
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-row>
      <!-- 左侧菜单导航 -->
      <el-menu
        class="el-menu-vertical hidden md:block"
        :default-active="activeMenu"
        @select="handleMenuSelect"
      >
        <el-menu-item index="goHome">
          <el-icon color="#123123">
            <HomeFilled />
          </el-icon>
          <span>回到首页</span>
        </el-menu-item>
        <el-menu-item index="personalInfo">
          <el-icon>
            <Avatar />
          </el-icon>
          <span>个人信息</span>
        </el-menu-item>
        <el-menu-item index="changePassword">
          <el-icon>
            <Lock />
          </el-icon>
          <span>修改密码</span>
        </el-menu-item>
        <el-menu-item index="invitationInfo">
          <el-icon>
            <Money />
          </el-icon>
          <span>推广福利</span>
        </el-menu-item>
      </el-menu>
      <!-- 右侧内容区 -->
      <el-col :xs="24" :sm="24" :md="18" :lg="18" class="content-col">
        <div v-if="activeMenu === 'personalInfo'">
          <!-- 用户信息组件 -->
          <el-card>
            <template #header>
              <h2>个人信息</h2>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item :label="$t('changePassword.email')"
                >{{ user.email
                }}<el-button
                  v-if="!user.email"
                  type="primary"
                  size="small"
                  @click="bindEmail"
                  >绑定邮箱</el-button
                ></el-descriptions-item
              >
              <el-descriptions-item :label="$t('changePassword.userName')">{{
                user.username
              }}</el-descriptions-item>
              <el-descriptions-item
                :label="$t('changePassword.expirationDateExpired')"
              >
                {{ user.membershipExpiry }}
                <span v-if="isVipExpire && enableSiteShop">
                  <a
                    class="ml-1 cursor-pointer text-red-500 underline"
                    @click="showShop = true"
                    >{{ $t('renewNow') }}</a
                  >
                </span>
              </el-descriptions-item>
              <el-descriptions-item
                :label="$t('changePassword.plusExpirationDateExpired')"
              >
                {{ user.plusMembershipExpiry }}
                <span v-if="isSvipExpire && enableSiteShop">
                  <a
                    class="ml-1 cursor-pointer text-red-500 underline"
                    @click="showShop = true"
                    >{{ $t('renewNow') }}</a
                  >
                </span>
              </el-descriptions-item>
              <el-descriptions-item
                label="Claude 有效期"
              >
                {{ user.claudeExpireTime }}
              </el-descriptions-item>
              <el-descriptions-item
                label="Claude pro 有效期"
              >
                {{ user.claudeProExpireTime }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template #label>
                  {{ $t('changePassword.affCode') }}
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="点击复制按钮，发送链接邀请好友来使用，系统将会为您赠送使用时长。"
                    placement="top"
                  >
                    <el-icon class="mr-2">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </template>
                {{ user.affCode }}
                <el-tooltip content="点我复制邀请链接" placement="top">
                  <el-button
                    plain
                    type="success"
                    size="small"
                    :icon="CopyDocument"
                    @click="handleUrlCopy"
                  />
                </el-tooltip>
              </el-descriptions-item>
              <el-descriptions-item
                :label="$t('changePassword.perLimit')"
                v-if="user.limit > 0"
                >{{ user.limit }}次 /
                {{ convertTimeUnit(user.per) }} ⏲</el-descriptions-item
              >
              <el-descriptions-item
                :label="$t('changePassword.perLimit')"
                v-else
              >
                无限制 ⏲</el-descriptions-item
              >
              <el-descriptions-item
                label="Claude 速率"
                v-if="user.claudeLimit > 0"
                >{{ user.claudeLimit }}次 /
                {{ convertTimeUnit(user.claudePer) }} ⏲</el-descriptions-item
              >
              <el-descriptions-item
                label="claude 速率"
                v-else
              >
                无限制 ⏲</el-descriptions-item
              >
            </el-descriptions>
          </el-card>
        </div>

        <div v-if="activeMenu === 'changePassword'">
          <!-- 修改密码组件 -->
          <el-card>
            <template #header>
              <h2>修改密码</h2>
            </template>
            <el-form
              ref="passwordFormRef"
              :model="passwordForm"
              label-width="auto"
              :rules="rules"
            >
              <el-form-item
                :label="$t('changePassword.currentPassword')"
                prop="currentPassword"
              >
                <el-input
                  v-model="passwordForm.currentPassword"
                  type="password"
                  show-password
                />
              </el-form-item>
              <el-form-item
                :label="$t('changePassword.newPassword')"
                prop="newPassword"
              >
                <el-input
                  v-model="passwordForm.newPassword"
                  type="password"
                  show-password
                />
              </el-form-item>
              <el-form-item
                :label="$t('changePassword.confirmPassword')"
                prop="confirmPassword"
              >
                <el-input
                  v-model="passwordForm.confirmPassword"
                  type="password"
                  show-password
                />
              </el-form-item>
              <div class="p-6 text-center">
                <el-button type="primary" @click="submitForm"
                  >修改密码</el-button
                >
              </div>
            </el-form>
          </el-card>
        </div>
        <div v-if="activeMenu === 'invitationInfo'" class="p-6 shadow-lg">
          <div class="flex flex-wrap -mx-4" v-if="enablePromotion">
            <!-- 左侧二维码和按钮 -->
            <div class="w-full md:w-1/2 px-4 mb-6 md:mb-0">
              <div class="invitation-section">
                <h3 class="text-lg font-semibold mb-4">推广二维码</h3>
                <span class="block my-2 text-sm"
                  >通过分享二维码或者复制邀请链接邀请好友，好友购买后您将获得订单金额
                  <span v-if="affRate" class="text-red-500 font-bold"
                    >{{ affRate }}%</span
                  >
                  的推广现金，<span v-if="thresholdMoney > 0"
                    >满<span class="text-red-500 font-bold"
                      >{{ thresholdMoney }}元</span
                    >可</span
                  >立即提现。</span
                >
                <div class="flex flex-col items-center justify-center">
                  <qrcode
                    :size="200"
                    v-if="inviterUrl"
                    :value="inviterUrl"
                    :color="{ dark: '#000', light: '#FFF' }"
                    type="image/png"
                    class="mb-2 text-center"
                  />
                </div>
                <el-button
                  plain
                  type="success"
                  size="small"
                  :icon="CopyDocument"
                  @click="handleUrlCopy"
                  class="w-full"
                >
                  复制邀请链接
                </el-button>
              </div>
            </div>

            <!-- 右侧统计信息 -->
            <div class="w-full md:w-1/2 px-4">
              <div class="grid grid-cols-2 gap-6 p-6">
                <!-- 邀请总人数 -->
                <div class="p-4 flex flex-col items-center justify-center">
                  <h3 class="text-sm font-semibold">
                    推广总订单数 <i class="el-icon-info"></i>
                  </h3>
                  <p class="text-3xl font-bold text-blue-400 mt-2">
                    {{ user.affCount ? Number(user.affCount) : 0 }}
                  </p>
                </div>

                <!-- 邀请总金额 -->
                <div class="p-4 flex flex-col items-center justify-center">
                  <h3 class="text-sm font-semibold">
                    推广总金额 <i class="el-icon-info"></i>
                  </h3>
                  <p class="text-3xl font-bold text-blue-400 mt-2">
                    {{
                      user.affTotalQuota
                        ? Number(user.affTotalQuota).toFixed(2)
                        : 0.0
                    }}
                  </p>
                </div>

                <!-- 可提现金额 -->
                <div class="p-4 flex flex-col items-center justify-center">
                  <h3 class="text-sm font-semibold">
                    可提现金额 <i class="el-icon-info"></i>
                  </h3>
                  <p class="text-3xl font-bold text-blue-400 mt-2">
                    {{ user.affQuota ? Number(user.affQuota).toFixed(2) : 0.0 }}
                  </p>
                </div>

                <!-- 审核中金额 -->
                <div class="p-4 flex flex-col items-center justify-center">
                  <h3 class="text-sm font-semibold">
                    已提现总金额 <i class="el-icon-info"></i>
                  </h3>
                  <p class="text-3xl font-bold text-blue-400 mt-2">
                    {{
                      user.affHistoryQuota
                        ? Number(user.affHistoryQuota).toFixed(2)
                        : 0.0
                    }}
                  </p>
                </div>
              </div>
              <div class="flex flex-col items-center justify-center">
                <el-button type="primary" class="mt-2" @click="goToTx"
                  >立即提现</el-button
                >
              </div>
            </div>
          </div>

          <!-- Tabs Section -->
          <div class="mt-8">
            <el-tabs
              v-model="activeName"
              class="demo-tabs"
              @tab-click="handleClick"
            >
              <el-tab-pane label="邀请记录" name="first">
                <!-- 邀请记录 -->
                <el-table
                  :data="inviteRecords"
                  v-loading="loading"
                  class="w-full"
                  border
                >
                  <el-table-column
                    type="index"
                    label="序号"
                    width="60"
                    align="center"
                  />
                  <el-table-column
                    prop="userToken"
                    label="用户名"
                    align="center"
                  />
                  <el-table-column prop="email" label="邮箱" align="center" />
                  <el-table-column
                    prop="isPlus"
                    label="会员类型"
                    align="center"
                  >
                    <template #default="scope">
                      <el-tag type="danger" v-if="scope.row.isPlus === 1"
                        >高级会员</el-tag
                      >
                      <el-tag v-else>普通会员</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="createTime"
                    label="邀请时间"
                    align="center"
                  >
                    <template #default="scope">
                      <span>{{ formatDate(scope.row.createTime) }}</span>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 分页 -->
                <el-pagination
                  class="mt-4"
                  :current-page="pageInfo.page"
                  :page-size="pageInfo.pageSize"
                  :total="pageInfo.total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </el-tab-pane>

              <el-tab-pane
                label="提现记录"
                name="second"
                v-if="enablePromotion"
              >
                <el-table
                  :data="withdrawRecords"
                  v-loading="loading"
                  class="w-full"
                  border
                >
                  <el-table-column type="index" label="序号" width="60" />
                  <el-table-column
                    prop="withdrawalMoney"
                    label="提现金额"
                    align="center"
                  />
                  <el-table-column
                    prop="withdrawalTime"
                    label="提现时间"
                    align="center"
                  >
                    <template #default="scope">
                      <span>{{ formatDate(scope.row.withdrawalTime) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="status"
                    label="审批状态"
                    sortable
                    align="center"
                  >
                    <template #default="scope">
                      <el-tag type="success" v-if="scope.row.status === 1"
                        >已审批</el-tag
                      >
                      <el-tag type="primary" v-else-if="scope.row.status === 0"
                        >待审批</el-tag
                      >
                      <el-tag type="warning" v-else-if="scope.row.status === 2"
                        >已驳回</el-tag
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="remark"
                    label="审批结果"
                    align="center"
                  />
                </el-table>

                <!-- 分页 -->
                <el-pagination
                  class="mt-4"
                  :current-page="pageInfo.page"
                  :page-size="pageInfo.pageSize"
                  :total="pageInfo.total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
  <div v-if="enablePromotion">
    <el-dialog
      title="申请提现"
      v-model="applyDialogVisiable"
      :width="dialogWidth"
      @close="handleApplyClose"
    >
      <el-form
        :model="applyWithdrawalDto"
        :rules="applyRules"
        ref="applyFormRef"
      >
        <el-form-item label="提现金额" prop="money">
          <div class="flex items-center space-x-2">
            <!-- 输入框部分 -->
            <el-input
              v-model="applyWithdrawalDto.money"
              placeholder="请输入提现金额"
              class="flex-1"
            ></el-input>
            <!-- 提取全部按钮 -->
            <el-button
              link
              @click="applyWithdrawalDto.money = maxWithdrawAmount"
              ><span class="text-blue-500 font-bold">提取全部</span></el-button
            >
          </div>
        </el-form-item>
        <el-form-item label="登录密码" prop="password">
          <el-input
            v-model="applyWithdrawalDto.password"
            show-password
            placeholder="输入登录密码"
          ></el-input>
        </el-form-item>
        <el-form-item label="联系方式" prop="contact">
          <el-input
            v-model="applyWithdrawalDto.contact"
            placeholder="输入联系方式"
          ></el-input>
        </el-form-item>
        <el-form-item label="收款码" prop="qrcode">
          <el-upload
            :data="{ userId: userStore.id }"
            accept="image/*"
            :limit="1"
            :headers="importHeaders"
            :action="importUrl"
            list-type="picture-card"
            :auto-upload="true"
            :file-list="applyWithdrawalDto.qrcode"
            :on-change="handleFileChange"
            :before-upload="beforeAvatarUpload"
            :on-error="handleFileError"
            :on-success="handleFileSuccess"
            :on-exceed="handleFileExceed"
          >
            <el-icon>
              <Plus />
            </el-icon>
            <template #file="{ file }">
              <div>
                <img
                  class="el-upload-list__item-thumbnail"
                  :src="file.url"
                  alt="收款码"
                />
                <span class="el-upload-list__item-actions">
                  <span
                    v-if="!disabled"
                    class="el-upload-list__item-delete"
                    @click="handleRemove(file)"
                  >
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </span>
                </span>
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <div class="flex justify-end mt-4">
          <el-button @click="applyDialogVisiable = false">取消</el-button>
          <el-button
            type="primary"
            :loading="buttonLoading"
            @click="handleWithdraw"
            >确定</el-button
          >
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  getUser,
  changePassword,
  getInviterPage,
  bindUserEmail,
} from '@/api/user.js';
import { formatDate } from '@/utils/date.js';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import {
  HomeFilled,
  CopyDocument,
  QuestionFilled,
  Money,
  Lock,
  Avatar,
} from '@element-plus/icons-vue';
import { useUserStore } from '@/store/modules/user';
import { useSiteStore } from '@/store/modules/site';
import { useCopyToClipboard } from '@/utils/copyText';
import { convertTimeUnit } from '@/utils/date.js';
import Cookies from 'js-cookie';
import { removeToken, getToken } from '@/utils/auth.js';
import Qrcode from 'vue-qrcode';
import { withdrawPage, fetchApply } from '@/api/withdrawals.js';

const importUrl = import.meta.env.VITE_API_URL + '/common/upload-qrcode';
// 动态计算 headers
const importHeaders = computed(() => {
  return {
    Authorization: `Bearer ${getToken()}`, // 动态获取 token 并返回 Authorization 头
  };
});
const buttonLoading = ref(false);
const disabled = ref(false);
const maxWithdrawAmount = ref();
const applyDialogVisiable = ref(false);
const activeName = ref('first');
const userStore = useUserStore();
const siteStore = useSiteStore();
const affRate = ref();
const thresholdMoney = computed(() => {
  return siteStore.threshold == '' ? 0 : siteStore.threshold;
});
const enableSiteShop = computed(() => siteStore.enableSiteShop === 'true');
const enablePromotion = computed(() => siteStore.enablePromotion === 'true');
const router = useRouter();
const { t } = useI18n();
const showShop = ref(false);
const currentDate = new Date();
const isVipExpire = computed(
  () =>
    user.membershipExpiry != '' &&
    user.membershipExpiry < formatDate(currentDate)
);
const isSvipExpire = computed(
  () =>
    user.plusMembershipExpiry != '' &&
    user.plusMembershipExpiry < formatDate(currentDate)
);
const isSvip = computed(
  () =>
    user.plusMembershipExpiry != '' &&
    user.plusMembershipExpiry > formatDate(currentDate)
);
const applyWithdrawalDto = ref({
  money: 0,
  password: '',
  userId: '',
  qrcode: [],
  contact: '',
});
const user = reactive({
  email: '',
  username: '',
  membershipExpiry: '',
  plusMembershipExpiry: '',
  membershipPlan: '',
  affCode: '',
  limit: '',
  per: '',
  affQuota: '',
  affHistoryQuota: '',
  affTotalQuota: '',
  affCount: '',
  claudeExpireTime:'',
  claudeProExpireTime: '',
  claudeLimit:'',
  claudePer:''
});
const windowWidth = ref(window.innerWidth);
const dialogWidth = computed(() => {
  if (windowWidth.value < 768) return '96%';
  if (windowWidth.value < 1024) return '70%';
  return '40%';
});
const inviterUrl = ref('');
const loading = ref(true);
const inviteRecords = ref([]);
const withdrawRecords = ref([]);
const pageInfo = ref({
  page: 1,
  pageSize: 10,
  total: 0,
});
const passwordFormRef = ref();
const applyFormRef = ref();
const passwordForm = reactive({
  userName: '',
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
});
const activeMenu = ref('personalInfo');
const goToTx = () => {
  maxWithdrawAmount.value = Number(props.user.affQuota);
  applyDialogVisiable.value = true;
};
// 事件处理
const handleMenuSelect = (index) => {
  activeMenu.value = index;
  if (index === 'invitationInfo') {
    fetchInviterPage();
    getInviterUrl();
  } else if (index === 'goHome') {
    goToHome();
  }
};

const beforeAvatarUpload = (rawFile) => {
  if (rawFile.type != 'image/jpeg' && rawFile.type != 'image/png') {
    ElMessage({
      message: '图片格式要以jpg/png结尾',
      type: 'error',
      plain: true,
    });
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage({
      message: '图片大小不能超过2MB，请重新选择!',
      type: 'error',
      plain: true,
    });
    return false;
  }
  return true;
};

const handleFileError = (error, file, fileList) => {
  ElMessage({
    message: `文件上传失败: ${error}`,
    type: 'error',
    plain: true,
  });
  console.error('上传失败的文件:', file);
  console.error('当前文件列表:', fileList);
};

const handleFileExceed = (files, uploadFiles) => {
  ElMessage({
    message: `最多只能上传 ${uploadFiles.length} 个文件`,
    type: 'error',
    plain: true,
  });
};

// 上传成功处理
const handleFileSuccess = (response, file, fileList) => {
  if (response && response.code === 200) {
    // 假设后端返回的响应格式中，code为200表示成功
    file.status = 'success';
    ElMessage({
      message: '文件上传成功',
      type: 'success',
      plain: true,
    });
    console.log(file);
    console.log(fileList);
  } else {
    ElMessage({
      message: `文件上传失败: ${response.message}`,
      type: 'error',
      plain: true,
    });
  }
};

const handleClick = (tab) => {
  pageInfo.value.page = 1;
  pageInfo.value.size = 10;
  const tabFlag = tab.props.name;
  if (tabFlag === 'first') {
    fetchInviterPage();
  } else {
    fetchWithdrawPage();
  }
};
const handleApplyClose = () => {
  applyWithdrawalDto.value = {
    money: 0,
    password: '',
    userId: '',
    qrcode: [],
    contact: '',
  };
  fetchWithdrawPage();
  applyDialogVisiable.value = false;
};
const rules = reactive({
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' },
  ],
});
// 校验收款码是否为空
const validateQrCode = (rule, value, callback) => {
  if (applyWithdrawalDto.value.qrcode.length === 0) {
    callback(new Error('请上传收款码'));
  } else {
    callback();
  }
};
const applyRules = reactive({
  password: [{ required: true, message: '请输入登录密码', trigger: 'blur' }],
  money: [{ required: true, message: '请输入提现金额', trigger: 'blur' }],
  contact: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
  qrcode: [{ validator: validateQrCode, trigger: 'change' }], // 自定义校验收款码
});
// 文件变化时触发的事件，更新 qrcode 列表
const handleFileChange = (file, fileList) => {
  applyWithdrawalDto.value.qrcode = fileList; // 将上传的文件列表保存到 qrcode
};
function validateConfirmPassword(rule, value, callback) {
  if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'));
  } else {
    callback();
  }
}
const handleRemove = (file) => {
  applyWithdrawalDto.value.qrcode = [];
};
const submitForm = async () => {
  passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      await changePassword(passwordForm);
      // 将用户名和密码更新到cookie中
      Cookies.set('username', passwordForm.userName, { expires: 30 });
      Cookies.set('password', passwordForm.newPassword, { expires: 30 });
      // 密码修改成功后，删除token，让用户重新登录。
      removeToken();
      ElMessage({
        message: t('changePassword.success'),
        type: 'success',
        plain: true,
      });
      passwordFormRef.value.resetFields();
    } else {
      return false;
    }
  });
};

async function fetchUserInfo() {
  const username = userStore.username;
  const param = {
    userName: username,
  };
  passwordForm.userName = username;
  const res = await getUser(param);
  user.email = res.email;
  user.username = res.userToken;
  user.membershipExpiry = res.expireTime ? formatDate(res.expireTime) : '';
  user.plusMembershipExpiry = res.plusExpireTime
    ? formatDate(res.plusExpireTime)
    : '';
  user.affCode = res.affCode;
  user.membershipPlan = res.isPlus;
  user.limit = res.limit;
  user.per = res.per;
  user.affCode = res.affCode;
  user.affCount = res.affCount;
  user.affHistoryQuota = res.affHistoryQuota;
  user.affQuota = res.affQuota;
  user.affTotalQuota = res.affTotalQuota;
  user.claudeExpireTime = formatDate(res.claudeExpireTime)
  user.claudeProExpireTime = formatDate(res.claudeProExpireTime)
  user.claudeLimit = res.claudeLimit
  user.claudePer = res.claudePer
  maxWithdrawAmount.value = res.affQuota;
}

const goToHome = () => {
  router.push('/home');
};
const getInviterUrl = () => {
  const currentUrl = window.location.href;
  const hash = window.location.hash;
  const newUrl = currentUrl.replace(hash, '#/register');
  const fullPath = `${newUrl}?inviter=${user.affCode}`;
  inviterUrl.value = fullPath;
};

const handleWithdraw = async () => {
  applyFormRef.value.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      applyWithdrawalDto.value.userId = userStore.id;
      await fetchApply(applyWithdrawalDto.value)
        .then(() => {
          ElMessage({
            message: '提现申请成功',
            type: 'success',
            plain: true,
          });
          applyDialogVisiable.value = false;
          applyFormRef.value.resetFields();
        })
        .finally(() => {
          buttonLoading.value = false;
        });
    } else {
      return false;
    }
  });
};

const handleUrlCopy = async () => {
  getInviterUrl();
  const flag = useCopyToClipboard(inviterUrl.value);
  if (flag) {
    ElMessage({
      message: '复制成功',
      type: 'success',
      plain: true,
    });
  }
};
const fetchInviterPage = async () => {
  loading.value = true;
  const params = {
    page: pageInfo.value.page,
    size: pageInfo.value.pageSize,
    userId: userStore.id,
  };
  const res = await getInviterPage(params);
  inviteRecords.value = res.records;
  pageInfo.value.total = res.total;
  loading.value = false;
};

const fetchWithdrawPage = async () => {
  loading.value = true;
  const params = {
    page: pageInfo.value.page,
    size: pageInfo.value.pageSize,
    userId: userStore.id,
  };
  const res = await withdrawPage(params);
  withdrawRecords.value = res.records;
  pageInfo.value.total = res.total;
  loading.value = false;
};

const handleSizeChange = (val) => {
  pageInfo.value.pageSize = val;
  if (activeName.value === 'first') {
    fetchInviterPage();
  } else {
    fetchWithdrawPage();
  }
};
const handleCurrentChange = (val) => {
  pageInfo.value.page = val;
  if (activeName.value === 'first') {
    fetchInviterPage();
  } else {
    fetchWithdrawPage();
  }
};
const bindEmail = () => {
  ElMessageBox.prompt('请输入邮箱信息', '提示', {
    confirmButtonText: '绑定',
    cancelButtonText: '取消',
    inputPattern:
      /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
    inputErrorMessage: '邮箱无效',
  })
    .then(({ value }) => {
      const params = {
        userId: userStore.id,
        email: value,
      };
      bindUserEmail(params)
        .then((res) => {
          ElMessage({
            type: 'success',
            message: `邮箱绑定成功`,
          });
          fetchUserInfo()
        })
        .catch((err) => {
          console.error(err);
        });
    })
    .catch((error) => {
      console.log('取消', error);
    });
};
onMounted(() => {
  affRate.value = siteStore.affRate * 100;
  fetchUserInfo();
});
</script>

<style scoped>
.personal-center {
  padding: 20px;
}

.back-button {
  margin-bottom: 20px;
}

/* 侧边栏样式 */
.el-menu-vertical {
  border-right: 1px solid #eee;
  height: calc(100vh - 110px);
  width: 220px;
  margin-right: 20px;
}

.dark .el-menu-vertical {
  border-right: 1px solid rgb(94, 92, 92);
  height: calc(100vh - 110px);
  width: 220px;
}

@media (min-width: 992px) {
  .el-menu-vertical {
    height: calc(100vh - 110px);
    width: 220px;
  }
}

.sidebar-col {
  margin-bottom: 20px;
  width: 300px;
}

.content-col {
  padding-left: 10px;
}

/* 手机端布局调整 */
@media (max-width: 768px) {
  .content-col {
    padding-left: 0;
  }

  .el-menu-vertical {
    width: 100%;
    border: none;
    height: auto;
  }
}
</style>

{"name": "@sa/elp", "type": "module", "version": "1.3.10", "description": "A fresh and elegant admin template, based on Vue3、Vite3、TypeScript、ElementPlus and UnoCSS. 一个基于Vue3、Vite3、TypeScript、ElementPlus and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Soybean", "email": "<EMAIL>", "url": "https://github.com/soybeanjs"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin", "repository": {"url": "https://github.com/soybeanjs/soybean-admin.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin/issues"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite5", "TypeScript", "element-plus", "element-plus-admin", "UnoCSS"], "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode test", "dev:prod": "vite --mode prod", "gen-route": "sa gen-route", "lint": "eslint . --fix", "postbuild": "sa print-soybean", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "typecheck": "echo 'Type checking skipped", "update-pkg": "sa update-pkg"}, "dependencies": {"@antv/data-set": "0.11.8", "@antv/g2": "5.2.7", "@antv/g6": "5.0.30", "@better-scroll/core": "2.5.1", "@iconify/vue": "4.1.2", "@sa/alova": "workspace:*", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@visactor/vchart": "1.12.11", "@visactor/vchart-theme": "1.12.2", "@visactor/vtable-editors": "1.10.5", "@visactor/vtable-gantt": "1.10.5", "@visactor/vue-vtable": "1.10.5", "@vueuse/components": "11.2.0", "@vueuse/core": "11.2.0", "clipboard": "2.0.11", "dayjs": "1.11.13", "defu": "^6.1.4", "dhtmlx-gantt": "9.0.2", "dompurify": "3.2.0", "echarts": "5.5.1", "element-plus": "^2.8.7", "jsbarcode": "3.11.6", "json5": "2.2.3", "nprogress": "0.2.0", "pinia": "2.2.6", "pinyin-pro": "3.26.0", "print-js": "1.6.0", "swiper": "11.1.14", "tailwind-merge": "2.5.4", "typeit": "8.8.7", "vditor": "3.10.7", "vue": "3.5.13", "vue-draggable-plus": "0.6.0", "vue-i18n": "10.0.4", "vue-pdf-embed": "2.1.1", "vue-router": "4.4.5", "wangeditor": "4.7.15", "xgplayer": "3.0.20", "xlsx": "0.18.5"}, "devDependencies": {"@amap/amap-jsapi-types": "0.0.15", "@elegant-router/vue": "0.3.8", "@iconify/json": "2.2.273", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.4.2", "@types/bmapgl": "0.0.7", "@types/dompurify": "3.0.5", "@types/node": "22.9.0", "@types/nprogress": "0.2.3", "@unocss/eslint-config": "0.64.1", "@unocss/preset-icons": "0.64.1", "@unocss/preset-uno": "0.64.1", "@unocss/transformer-directives": "0.64.1", "@unocss/transformer-variant-group": "0.64.1", "@unocss/vite": "0.64.1", "@vitejs/plugin-vue": "5.2.0", "@vitejs/plugin-vue-jsx": "4.1.0", "eslint": "9.14.0", "eslint-plugin-vue": "9.31.0", "lint-staged": "15.2.10", "sass": "1.83.0", "simple-git-hooks": "2.11.1", "tsx": "4.19.2", "typescript": "5.6.3", "unplugin-icons": "0.20.1", "unplugin-vue-components": "0.27.4", "vite": "5.4.11", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.6.4", "vue-eslint-parser": "9.4.3", "vue-tsc": "2.1.10"}, "simple-git-hooks": {"commit-msg": "pnpm sa git-commit-verify", "pre-commit": "pnpm typecheck && pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "website": "https://elp.soybeanjs.cn"}
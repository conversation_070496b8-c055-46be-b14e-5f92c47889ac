import { useAuthStore } from '@/store/modules/auth';

export function useAuth() {
  const authStore = useAuthStore();

  function hasAuth(codes: string | string[]) {
    if (!authStore.isLogin) {
      return false;
    }

    if (typeof codes === 'string') {
      return authStore.userInfo.buttons.includes(codes);
    }

    return codes.some(code => authStore.userInfo.buttons.includes(code));
  }

  return {
    hasAuth
  };
}

export function usePermission() {
  const authStore = useAuthStore();

  function hasPermission(permissionValue: string) {
    // 超级角色或管理员直接通过验证
    if (authStore.userInfo?.roles?.includes('SUPER_ADMIN') || authStore.userInfo?.roles?.includes('ADMIN')) {
      return true;
    }
    console.log('authStore.userInfo?.permissions', authStore.userInfo?.permissions);
    console.log('permissionValue', permissionValue);
    // 转换接口路径为权限标识（如将"/expander-api/tenant/update"转为"tenant/update"）
    const hasPerm = authStore.userInfo?.permissions?.some(permission => {
      const parts = permission.split('/').filter(Boolean);
      return parts.slice(-2).join('/') === permissionValue;
    });
    // 普通用户进行权限验证
    return Boolean(hasPerm);
  }

  return {
    hasPermission
  };
}

<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { ElButton, ElImage, ElMessage, ElMessageBox, ElPopconfirm, ElTable, ElTableColumn, ElTag } from 'element-plus';
import { fetchDrawRecordPage, removeDrawRecordBatch } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import DrawRecordSearch from './modules/draw-record-search.vue';

// Define the proper type for your table data
interface DrawRecord {
  id: string;
  prompt: string;
  model: string;
  imageSize: string;
  drawType: string;
  taskStatus: string;
  imageUrl: string;
  description: string;
  response: string;
  createdAt: string;
  updatedAt: string;
  status?: number;
}

const wrapperRef = ref<HTMLElement | null>(null);
const previewVisible = ref(false);
const previewImageUrl = ref('');
const windowWidth = ref(window.innerWidth);
const maxHeight = ref(window.innerHeight - 220);

// Handle window resize
const handleResize = () => {
  windowWidth.value = window.innerWidth;
  maxHeight.value = window.innerHeight - 220;
};

// Get first image URL from comma-separated string
const getFirstImageUrl = (imageUrl: string) => {
  if (!imageUrl) return '';
  const urls = imageUrl.split(',');
  return urls[0].trim();
};

// Get list of image URLs from comma-separated string
const getImageUrlList = (imageUrl: string) => {
  if (!imageUrl) return [];
  return imageUrl.split(',').map(url => url.trim());
};

// For display formatting
const getDrawTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    '1': '生图',
    '2': '改图',
    '3': '其他'
  };
  return typeMap[type] || '未知';
};

const getDrawTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    '1': 'success',
    '2': 'warning',
    '3': 'info'
  };
  return tagMap[type] || 'info';
};

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    PENDING: '处理中',
    COMPLETED: '已完成',
    FAILED: '失败'
  };
  return statusMap[status] || status;
};

const getStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    PENDING: 'info',
    COMPLETED: 'success',
    FAILED: 'danger'
  };
  return tagMap[status] || 'info';
};

// Show image preview
const showPreviewImage = (url: string) => {
  previewImageUrl.value = url;
  previewVisible.value = true;
};
const { columns, columnChecks, data, loading, pagination, getData, searchParams, getDataByPage, resetSearchParams } =
  useTable({
    apiFn: fetchDrawRecordPage,
    apiParams: {
      current: 1,
      size: 10,
      username: undefined
    },
    columns: () => [
      { type: 'selection', width: 48 },
      { prop: 'index', label: $t('common.index'), width: 64 },
      { prop: 'username', label: '用户名', minWidth: 200, showOverflowTooltip: true },
      { prop: 'prompt', label: '提示词', minWidth: 200, align: 'center', showOverflowTooltip: true },
      { prop: 'model', label: '模型', minWidth: 120, align: 'center' },
      { prop: 'imageSize', label: '图片尺寸', minWidth: 120, align: 'center' },
      {
        prop: 'drawType',
        label: '类型',
        width: 100,
        align: 'center',
        formatter: (row: any) => <ElTag type={getDrawTypeTag(row.drawType)}>{getDrawTypeText(row.drawType)}</ElTag>
      },
      {
        prop: 'taskStatus',
        label: '状态',
        width: 100,
        align: 'center',
        formatter: (row: any) => <ElTag type={getStatusTag(row.taskStatus)}>{getStatusText(row.taskStatus)}</ElTag>
      },
      {
        prop: 'imageUrl',
        label: '预览图',
        width: 120,
        align: 'center',
        formatter: (row: any) => (
          <ElImage
            style={{ width: '100px', height: '100px' }}
            src={getFirstImageUrl(row.imageUrl)}
            preview-src-list={getImageUrlList(row.imageUrl)}
            zoom-rate={1.2}
            max-scale={7}
            min-scale={0.2}
            preview-teleported={true}
            fit="cover"
            onClick={() => showPreviewImage(getFirstImageUrl(row.imageUrl))}
          />
        )
      },
      { prop: 'description', label: '描述', width: 150, align: 'center', showOverflowTooltip: true },
      { prop: 'response', label: '接口响应', width: 150, align: 'center', showOverflowTooltip: true },
      { prop: 'createdAt', label: '创建时间', width: 180, align: 'center', sortable: true },
      { prop: 'updatedAt', label: '更新时间', width: 180, align: 'center', sortable: true },
      {
        prop: 'operate',
        label: $t('common.operate'),
        align: 'center',
        fixed: 'right',
        width: 80,
        formatter: (row: any) => (
          <div class="flex-center">
            <ElPopconfirm title="确定删除该记录吗？" onConfirm={() => handleDelete(row.id)}>
              {{
                reference: () => (
                  <ElButton size="small" plain type="danger">
                    删除
                  </ElButton>
                )
              }}
            </ElPopconfirm>
          </div>
        )
      }
    ]
  });

const { checkedRowKeys, onBatchDeleted } = useTableOperate(data as any, getData);

// Handle single record deletion
async function handleDelete(id: string) {
  try {
    await removeDrawRecordBatch([id]);
    ElMessage({
      message: '删除成功',
      type: 'success'
    });
    await getData();
  } catch {
    ElMessage.error('删除失败');
  }
}

// Handle batch deletion
async function handleBatchDelete() {
  if (checkedRowKeys.value.length < 1) {
    ElMessage({
      message: '请先勾选数据',
      type: 'warning'
    });
    return;
  }

  try {
    await ElMessageBox.confirm('确定批量删除选中记录吗？', '注意', {
      confirmButtonText: '确 定',
      cancelButtonText: '取 消',
      type: 'warning'
    });

    await removeDrawRecordBatch(checkedRowKeys.value);
    onBatchDeleted();

    ElMessage({
      message: '删除成功',
      type: 'success'
    });
  } catch {
    // 取消删除或发生错误
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize);
  getData();
});
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <DrawRecordSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>{{ $t('page.manage.menu.title') }}</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            @delete="handleBatchDelete"
            @refresh="getData"
          />
        </div>
      </template>
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          border
          class="sm:h-full"
          :data="data"
          row-key="id"
          :max-height="maxHeight"
          @selection-change="(selection: DrawRecord[]) => checkedRowKeys = selection.map(item => String(item.id))"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
        <div class="mt-20px flex justify-end">
          <ElPagination
            v-if="pagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="pagination"
            @current-change="pagination['current-change']"
            @size-change="pagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>

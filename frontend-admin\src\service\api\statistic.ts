import { request } from '../request';
export function getLast15DaysIncome() {
  return request({
    url: '/payLogs/dailyIncome',
    method: 'get'
  });
}

export function getLast12MonthsIncome() {
  return request({
    url: '/payLogs/months/income',
    method: 'get'
  });
}

export function getYearlyIncome() {
  return request({
    url: '/payLogs/years/income',
    method: 'get'
  });
}

export function statistic() {
  return request({
    url: '/user/statistic',
    method: 'get'
  });
}

// 待审批数量
export function fetchTodoData() {
  return request({
    url: '/withdrawals/todoData',
    method: 'get'
  });
}

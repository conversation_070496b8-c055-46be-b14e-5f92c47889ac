import { request } from '../request';

export function fetchSubTypePage(params: Api.Common.PaginatingCommonParams) {
  return request({
    url: '/subtype/page',
    method: 'get',
    params
  });
}

export function fetchSubTypeList() {
  return request({
    url: '/subtype/list',
    method: 'get'
  });
}

export function updateSubType(data: any) {
  return request({
    url: '/subtype/update',
    method: 'post',
    data
  });
}

export function addSubType(data: any) {
  return request({
    url: '/subtype/add',
    method: 'post',
    data
  });
}

export function removeSubTypeBatchById(data: any) {
  return request({
    url: '/subtype/delete',
    method: 'delete',
    data
  });
}

export function changeSwitch(params: any) {
  return request({
    url: '/subtype/change',
    method: 'put',
    params
  });
}

export function calcOfferAmount(params) {
  return request({
    url: '/subtype/calc-amount',
    method: 'get',
    params
  });
}

export function asyncUserModelLimit() {
  return request({
    url: '/subtype/model/sync',
    method: 'post'
  });
}

// 获取管理员套餐列表
export function getAdminSubTypeList() {
  return request({
    url: '/subType/adminList',
    method: 'get'
  });
}
